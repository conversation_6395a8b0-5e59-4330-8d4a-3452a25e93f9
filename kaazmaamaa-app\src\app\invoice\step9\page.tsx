'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, CheckCircle, Shield, FileCheck, AlertTriangle, Code, Terminal, Award } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep9() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<number[]>([])

  const markSectionComplete = (sectionId: number) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section ${sectionId} completed! ✅`)
    }
  }

  const validationSteps = [
    {
      id: 1,
      title: "CLI Validation Tool",
      description: "Use ZATCA's command-line validation tool",
      icon: <Terminal className="h-5 w-5" />,
      details: [
        "Download ZATCA CLI tool",
        "Install validation dependencies",
        "Configure validation settings",
        "Run invoice validation tests"
      ]
    },
    {
      id: 2,
      title: "QR Code Validation",
      description: "Validate QR code generation and content",
      icon: <FileCheck className="h-5 w-5" />,
      details: [
        "Verify QR code format",
        "Validate TLV structure",
        "Check Base64 encoding",
        "Test QR code scanning"
      ]
    },
    {
      id: 3,
      title: "Digital Signature Verification",
      description: "Validate invoice digital signatures",
      icon: <Shield className="h-5 w-5" />,
      details: [
        "Verify signature algorithm",
        "Check certificate validity",
        "Validate signature format",
        "Test signature verification"
      ]
    },
    {
      id: 4,
      title: "Compliance Certification",
      description: "Final compliance validation and certification",
      icon: <Award className="h-5 w-5" />,
      details: [
        "Complete compliance checklist",
        "Submit for certification",
        "Address validation issues",
        "Obtain compliance certificate"
      ]
    }
  ]

  const validationChecks = [
    {
      category: "XML Structure",
      checks: [
        "Valid XML syntax",
        "Correct namespace declarations",
        "Required elements present",
        "Proper element nesting"
      ],
      color: "blue"
    },
    {
      category: "Business Rules",
      checks: [
        "VAT calculations correct",
        "Invoice totals accurate",
        "Date formats valid",
        "Currency codes proper"
      ],
      color: "green"
    },
    {
      category: "Security",
      checks: [
        "Digital signature valid",
        "Certificate not expired",
        "Hash values correct",
        "Signature algorithm proper"
      ],
      color: "purple"
    },
    {
      category: "QR Code",
      checks: [
        "TLV format correct",
        "Base64 encoding valid",
        "Required fields present",
        "QR code scannable"
      ],
      color: "orange"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-emerald-600">Step 9: Compliance Validation</h1>
              <Badge className="bg-emerald-100 text-emerald-800">
                Validation
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Proceeding to Step 10...')
                  router.push('/invoice/step10')
                }}
                className="bg-emerald-600 hover:bg-emerald-700"
              >
                Next: FAQ & Troubleshooting
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-emerald-50 border-emerald-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-emerald-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-emerald-800 mb-2">✅ Compliance Validation</h3>
              <p className="text-emerald-600 mb-4">
                Validate your ZATCA e-invoicing implementation for compliance certification
              </p>
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-emerald-800">{completedSections.length}</div>
                  <div className="text-sm text-emerald-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{validationSteps.length - completedSections.length}</div>
                  <div className="text-sm text-blue-600">Remaining</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* CLI Validation Tool */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Terminal className="h-5 w-5 mr-2 text-emerald-600" />
              ZATCA CLI Validation Tool
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Download and Installation</h4>
                <code className="text-sm bg-gray-100 p-3 rounded block whitespace-pre">
{`# Download ZATCA CLI tool
wget https://zatca.gov.sa/tools/zatca-cli.zip

# Extract and install
unzip zatca-cli.zip
cd zatca-cli
./install.sh`}
                </code>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Basic Validation Command</h4>
                <code className="text-sm bg-gray-100 p-3 rounded block whitespace-pre">
{`# Validate invoice XML
zatca-cli validate --input invoice.xml --type invoice

# Validate QR code
zatca-cli validate --input qr-data.txt --type qr

# Full compliance check
zatca-cli compliance-check --input invoice.xml`}
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Validation Checks */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {validationChecks.map((category, index) => (
            <Card key={index} className={`bg-${category.color}-50 border-${category.color}-200`}>
              <CardHeader>
                <CardTitle className={`text-${category.color}-800`}>
                  {category.category} Validation
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {category.checks.map((check, checkIndex) => (
                    <div key={checkIndex} className="flex items-center space-x-2">
                      <CheckCircle className={`h-4 w-4 text-${category.color}-600`} />
                      <span className="text-sm text-gray-700">{check}</span>
                    </div>
                  ))}
                </div>
                <Button 
                  size="sm" 
                  className={`mt-4 bg-${category.color}-600 hover:bg-${category.color}-700`}
                  onClick={() => {
                    toast.info(`Running ${category.category} validation...`)
                  }}
                >
                  <FileCheck className="h-4 w-4 mr-2" />
                  Run Validation
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Implementation Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {validationSteps.map((step) => (
            <Card
              key={step.id}
              className={`transition-all hover:shadow-lg ${
                completedSections.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSections.includes(step.id) ? 'bg-green-100 text-green-600' : 'bg-emerald-100 text-emerald-600'
                  }`}>
                    {completedSections.includes(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">
                        {step.title}
                      </h3>
                      {completedSections.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-4">
                  {step.details.map((detail, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{detail}</span>
                    </li>
                  ))}
                </ul>

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast.info(`Learning about ${step.title}...`)
                    }}
                  >
                    <Code className="h-4 w-4 mr-2" />
                    View Guide
                  </Button>

                  {!completedSections.includes(step.id) && (
                    <Button
                      size="sm"
                      onClick={() => markSectionComplete(step.id)}
                      className="bg-emerald-600 hover:bg-emerald-700"
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Compliance Checklist */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="h-5 w-5 mr-2 text-emerald-600" />
              Final Compliance Checklist
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Technical Requirements</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">XML schema validation passes</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Digital signatures valid</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">QR codes generate correctly</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">API integration working</span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Business Requirements</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">VAT calculations accurate</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Invoice formats compliant</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Reporting flows functional</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Error handling robust</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Common Validation Issues */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
              Common Validation Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">Schema Validation Errors</h4>
                <p className="text-red-600 text-sm mb-2">
                  XML structure doesn't match ZATCA schema
                </p>
                <ul className="text-sm text-red-700 space-y-1">
                  <li>• Check element names and namespaces</li>
                  <li>• Verify required fields are present</li>
                  <li>• Validate data types and formats</li>
                  <li>• Ensure proper element ordering</li>
                </ul>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">Business Rule Violations</h4>
                <p className="text-yellow-600 text-sm mb-2">
                  Invoice data doesn't meet business requirements
                </p>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Verify VAT calculation accuracy</li>
                  <li>• Check invoice total calculations</li>
                  <li>• Validate date and time formats</li>
                  <li>• Ensure currency codes are correct</li>
                </ul>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Signature Validation Issues</h4>
                <p className="text-blue-600 text-sm mb-2">
                  Digital signature verification failures
                </p>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Check certificate validity period</li>
                  <li>• Verify signature algorithm</li>
                  <li>• Validate certificate chain</li>
                  <li>• Ensure hash values are correct</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
