'use client'

import { useState, useEffect, useRef } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useBarta } from '@/contexts/BartaContext'
import { useSearchParams } from 'next/navigation'
import { MessageBubble } from '@/components/barta/MessageBubble'
import { ConversationItem } from '@/components/barta/ConversationItem'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { 
  MessageCircle, 
  Search, 
  Send, 
  Phone, 
  Video, 
  MoreVertical,
  ArrowLeft,
  Users,
  Building,
  Wrench,
  Smile,
  Paperclip,
  Image,
  Plus,
  Circle,
  X,
  Reply
} from 'lucide-react'
import { toast } from 'sonner'

export default function BartaPage() {
  const { user, userRole } = useAuth()
  const searchParams = useSearchParams()
  const {
    conversations,
    messages,
    activeConversation,
    onlineUsers,
    sendMessage,
    createConversation,
    setActiveConversation,
    markAsRead,
    searchUsers,
    getConversationWithUser,
    getUserById,
    getUnreadCount,
    addReaction,
    removeReaction
  } = useBarta()

  // State
  const [searchQuery, setSearchQuery] = useState('')
  const [messageInput, setMessageInput] = useState('')
  const [showUserSearch, setShowUserSearch] = useState(false)
  const [searchResults, setSearchResults] = useState(onlineUsers)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [messageSearchQuery, setMessageSearchQuery] = useState('')
  const [showMessageSearch, setShowMessageSearch] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [showFilePreview, setShowFilePreview] = useState(false)
  const [replyingTo, setReplyingTo] = useState<any>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const imageInputRef = useRef<HTMLInputElement>(null)

  // Common emojis for quick access
  const quickEmojis = ['😀', '😂', '❤️', '👍', '👎', '😢', '😮', '😡', '🎉', '🔥', '💯', '👏']

  // Handle URL parameters for direct conversation access
  useEffect(() => {
    const conversationParam = searchParams.get('conversation')
    if (conversationParam && conversations.length > 0) {
      const conversation = conversations.find(conv => conv.id === conversationParam)
      if (conversation) {
        setActiveConversation(conversationParam)
        markAsRead(conversationParam)
        toast.success('Conversation opened! 💬')
      }
    }
  }, [searchParams, conversations, setActiveConversation, markAsRead])

  // Auto-scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, activeConversation])

  // Update search results when query changes
  useEffect(() => {
    setSearchResults(searchUsers(searchQuery))
  }, [searchQuery, searchUsers])

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'worker':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'supplier':
        return <Building className="h-4 w-4 text-green-600" />
      case 'company':
        return <Users className="h-4 w-4 text-purple-600" />
      default:
        return <Circle className="h-4 w-4 text-gray-600" />
    }
  }

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'worker':
        return 'bg-blue-100 text-blue-800'
      case 'supplier':
        return 'bg-green-100 text-green-800'
      case 'company':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Handle sending message
  const handleSendMessage = () => {
    if (!messageInput.trim() || !activeConversation) return

    sendMessage(activeConversation, messageInput)
    setMessageInput('')
    setShowEmojiPicker(false)
    setIsTyping(false)

    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
      typingTimeoutRef.current = null
    }

    toast.success('Message sent! 💬')
  }

  // Handle emoji selection
  const handleEmojiSelect = (emoji: string) => {
    setMessageInput(prev => prev + emoji)
    setShowEmojiPicker(false)
  }

  // Handle typing indicator
  const handleTyping = (value: string) => {
    setMessageInput(value)

    if (!isTyping && value.trim()) {
      setIsTyping(true)
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false)
    }, 2000)
  }

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      setSelectedFiles(files)
      setShowFilePreview(true)
    }
  }

  // Handle image selection
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      setSelectedFiles(files)
      setShowFilePreview(true)
    }
  }

  // Send message with files
  const handleSendWithFiles = () => {
    if (!activeConversation) return

    if (selectedFiles.length > 0) {
      // Create mock URLs for files (in real app, upload to server first)
      const attachments = selectedFiles.map(file => ({
        type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,
        url: URL.createObjectURL(file), // Mock URL
        name: file.name,
        size: file.size
      }))

      const messageType = selectedFiles[0].type.startsWith('image/') ? 'image' : 'file'
      const content = messageInput.trim() || (messageType === 'image' ? '📷 Image' : '📎 File')

      // Send message with attachments and reply
      sendMessage(activeConversation, content, messageType, replyingTo?.id)

      // In a real implementation, you would store attachments in the message
      // For now, we'll just show a success message
      toast.success(`${messageType === 'image' ? 'Image' : 'File'} sent! 📎`)
    } else if (messageInput.trim()) {
      sendMessage(activeConversation, messageInput, 'text', replyingTo?.id)
    }

    // Reset state
    setMessageInput('')
    setSelectedFiles([])
    setShowFilePreview(false)
    setShowEmojiPicker(false)
    setIsTyping(false)
    setReplyingTo(null)

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current)
      typingTimeoutRef.current = null
    }
  }

  // Handle message reply
  const handleReply = (message: any) => {
    setReplyingTo(message)
    toast.info(`Replying to ${message.senderName}`)
  }

  // Handle message reaction
  const handleReaction = (messageId: string, emoji: string) => {
    if (!activeConversation) return
    addReaction(messageId, activeConversation, emoji)
    toast.success(`Reacted with ${emoji}`)
  }

  // Remove selected file
  const removeSelectedFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
    if (selectedFiles.length === 1) {
      setShowFilePreview(false)
    }
  }

  // Handle starting new conversation
  const handleStartConversation = (userId: string) => {
    const conversationId = createConversation(userId)
    if (conversationId) {
      setActiveConversation(conversationId)
      setShowUserSearch(false)
      setSearchQuery('')
      toast.success('Conversation started! 🎉')
    }
  }

  // Handle selecting conversation
  const handleSelectConversation = (conversationId: string) => {
    setActiveConversation(conversationId)
    markAsRead(conversationId)
  }

  // Format message time
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  // Format conversation time
  const formatConversationTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  // Get active conversation details
  const activeConversationDetails = activeConversation
    ? conversations.find(conv => conv.id === activeConversation)
    : null

  const activeConversationMessages = activeConversation
    ? messages[activeConversation] || []
    : []

  // Filter messages based on search query
  const filteredMessages = messageSearchQuery.trim()
    ? activeConversationMessages.filter(msg =>
        msg.content.toLowerCase().includes(messageSearchQuery.toLowerCase())
      )
    : activeConversationMessages

  // Get other participant in conversation
  const getOtherParticipant = (conversation: any) => {
    if (!user?.id) return null
    const otherParticipantId = conversation.participants.find((id: string) => id !== user.id)
    return getUserById(otherParticipantId || '')
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="text-center py-12">
            <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Please Sign In</h3>
            <p className="text-gray-600">You need to be signed in to use Barta messaging.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <MessageCircle className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Barta</h1>
              <p className="text-sm text-gray-600">KAAZMAAMAA Messenger</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              {getUnreadCount()} unread
            </Badge>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              {onlineUsers.length} users
            </Badge>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto h-[calc(100vh-80px)] flex">
        {/* Sidebar - Conversations List */}
        <div className={`${activeConversation ? 'hidden lg:flex' : 'flex'} w-full lg:w-1/3 bg-white border-r border-gray-200 flex-col`}>
          {/* Search and New Chat */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex space-x-2 mb-3">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                onClick={() => setShowUserSearch(!showUserSearch)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            {/* User Search Results */}
            {showUserSearch && (
              <div className="mt-3 max-h-48 overflow-y-auto border border-gray-200 rounded-lg">
                {searchResults.filter(u => u.id !== user?.id).map((searchUser) => (
                  <div
                    key={searchUser.id}
                    className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                    onClick={() => handleStartConversation(searchUser.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-10 w-10 bg-gray-100 flex items-center justify-center">
                        {getRoleIcon(searchUser.role)}
                      </Avatar>
                      <div className="flex-1">
                        <p className="font-medium text-sm">{searchUser.name}</p>
                        <div className="flex items-center space-x-2">
                          <Badge variant="secondary" className={`text-xs ${getRoleColor(searchUser.role)}`}>
                            {searchUser.role}
                          </Badge>
                          {searchUser.isOnline && (
                            <div className="flex items-center">
                              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                              <span className="text-xs text-green-600">Online</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Conversations List */}
          <div className="flex-1 overflow-y-auto">
            {conversations.length === 0 ? (
              <div className="p-8 text-center">
                <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600">No conversations yet</p>
                <p className="text-sm text-gray-500">Start a new conversation above</p>
              </div>
            ) : (
              conversations.map((conversation) => {
                const otherParticipant = getOtherParticipant(conversation)
                return (
                  <ConversationItem
                    key={conversation.id}
                    conversation={conversation}
                    otherParticipant={otherParticipant}
                    isActive={activeConversation === conversation.id}
                    onClick={() => handleSelectConversation(conversation.id)}
                  />
                )
              })
            )}
          </div>
        </div>

        {/* Main Chat Area */}
        <div className={`${activeConversation ? 'flex' : 'hidden lg:flex'} flex-1 flex-col`}>
          {!activeConversation ? (
            /* Welcome Screen */
            <div className="flex-1 flex items-center justify-center bg-gray-50">
              <div className="text-center">
                <MessageCircle className="h-24 w-24 text-gray-400 mx-auto mb-6" />
                <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Barta</h2>
                <p className="text-gray-600 mb-4">Select a conversation to start messaging</p>
                <Button
                  onClick={() => setShowUserSearch(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Start New Conversation
                </Button>
              </div>
            </div>
          ) : (
            /* Active Conversation */
            <>
              {/* Chat Header */}
              {activeConversationDetails && (
                <div className="bg-white border-b border-gray-200 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setActiveConversation(null)}
                        className="lg:hidden"
                        title="Back to conversations"
                      >
                        <ArrowLeft className="h-4 w-4" />
                      </Button>
                      
                      {(() => {
                        const otherParticipant = getOtherParticipant(activeConversationDetails)
                        if (!otherParticipant) return null
                        
                        return (
                          <>
                            <div className="relative">
                              <Avatar className="h-10 w-10 bg-gray-100 flex items-center justify-center">
                                {getRoleIcon(otherParticipant.role)}
                              </Avatar>
                              {otherParticipant.isOnline && (
                                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                              )}
                            </div>
                            
                            <div>
                              <h3 className="font-semibold text-gray-900">{otherParticipant.name}</h3>
                              <div className="flex items-center space-x-2">
                                <Badge variant="secondary" className={`text-xs ${getRoleColor(otherParticipant.role)}`}>
                                  {otherParticipant.role}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {otherParticipant.isOnline ? 'Online' : 'Offline'}
                                </span>
                              </div>
                            </div>
                          </>
                        )
                      })()}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowMessageSearch(!showMessageSearch)}
                        className={showMessageSearch ? 'bg-blue-100 text-blue-600' : ''}
                      >
                        <Search className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" title="Voice Call">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" title="Video Call">
                        <Video className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" title="More Options">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Message Search */}
              {showMessageSearch && (
                <div className="bg-gray-50 border-b border-gray-200 p-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search messages..."
                      value={messageSearchQuery}
                      onChange={(e) => setMessageSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  {messageSearchQuery && (
                    <p className="text-xs text-gray-600 mt-2">
                      Found {filteredMessages.length} message(s) containing "{messageSearchQuery}"
                    </p>
                  )}
                </div>
              )}

              {/* Messages Area */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {filteredMessages.length === 0 ? (
                  <div className="text-center py-12">
                    <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                    {messageSearchQuery ? (
                      <>
                        <p className="text-gray-600">No messages found</p>
                        <p className="text-sm text-gray-500">Try a different search term</p>
                      </>
                    ) : (
                      <>
                        <p className="text-gray-600">No messages yet</p>
                        <p className="text-sm text-gray-500">Send the first message to start the conversation</p>
                      </>
                    )}
                  </div>
                ) : (
                  filteredMessages.map((message) => {
                    const isOwnMessage = message.senderId === user?.id

                    return (
                      <MessageBubble
                        key={message.id}
                        message={message}
                        isOwnMessage={isOwnMessage}
                        showSenderInfo={!isOwnMessage}
                        onReply={handleReply}
                        onReact={handleReaction}
                        onDelete={(messageId) => {
                          // TODO: Implement delete
                          toast.success('Message deleted')
                        }}
                      />
                    )
                  })
                )}
                <div ref={messagesEndRef} />
              </div>

              {/* Message Input */}
              <div className="bg-white border-t border-gray-200 p-4">
                {/* Reply Preview */}
                {replyingTo && (
                  <div className="mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <Reply className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-medium text-blue-800">
                          Replying to {replyingTo.senderName}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setReplyingTo(null)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-blue-700 truncate">{replyingTo.content}</p>
                  </div>
                )}

                {/* Typing Indicator */}
                {isTyping && (
                  <div className="mb-2 text-xs text-gray-500 flex items-center">
                    <div className="flex space-x-1 mr-2">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    You are typing...
                  </div>
                )}

                {/* File Preview */}
                {showFilePreview && selectedFiles.length > 0 && (
                  <div className="mb-3 p-3 bg-gray-50 rounded-lg border">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-700">Selected Files</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedFiles([])
                          setShowFilePreview(false)
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <div className="space-y-2">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className="flex items-center space-x-3 p-2 bg-white rounded border">
                          {file.type.startsWith('image/') ? (
                            <div className="flex-shrink-0">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={file.name}
                                className="w-12 h-12 object-cover rounded"
                              />
                            </div>
                          ) : (
                            <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                              <Paperclip className="h-6 w-6 text-gray-500" />
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">{file.name}</p>
                            <p className="text-xs text-gray-500">{(file.size / 1024).toFixed(1)} KB</p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeSelectedFile(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Emoji Picker */}
                {showEmojiPicker && (
                  <div className="mb-3 p-3 bg-gray-50 rounded-lg border">
                    <div className="flex flex-wrap gap-2">
                      {quickEmojis.map((emoji, index) => (
                        <button
                          key={index}
                          onClick={() => handleEmojiSelect(emoji)}
                          className="text-2xl hover:bg-gray-200 rounded p-1 transition-colors"
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Hidden File Inputs */}
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  className="hidden"
                  accept=".pdf,.doc,.docx,.txt,.zip,.rar"
                />
                <input
                  ref={imageInputRef}
                  type="file"
                  multiple
                  onChange={handleImageSelect}
                  className="hidden"
                  accept="image/*"
                />

                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    title="Attach File"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    title="Send Image"
                    onClick={() => imageInputRef.current?.click()}
                  >
                    <Image className="h-4 w-4" />
                  </Button>

                  <div className="flex-1 relative">
                    <Textarea
                      value={messageInput}
                      onChange={(e) => handleTyping(e.target.value)}
                      placeholder={selectedFiles.length > 0 ? "Add a caption..." : "Type a message..."}
                      rows={1}
                      className="resize-none pr-12"
                      onKeyPress={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault()
                          handleSendWithFiles()
                        }
                      }}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2"
                      title="Add Emoji"
                    >
                      <Smile className="h-4 w-4" />
                    </Button>
                  </div>

                  <Button
                    onClick={handleSendWithFiles}
                    disabled={!messageInput.trim() && selectedFiles.length === 0}
                    className="bg-blue-600 hover:bg-blue-700"
                    title="Send Message"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
