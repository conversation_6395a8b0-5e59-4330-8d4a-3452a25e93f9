globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/feed/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/BartaContext.tsx":{"*":{"id":"(ssr)/./src/contexts/BartaContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/CompaniesContext.tsx":{"*":{"id":"(ssr)/./src/contexts/CompaniesContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/OnlineUsersContext.tsx":{"*":{"id":"(ssr)/./src/contexts/OnlineUsersContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/PaymentContext.tsx":{"*":{"id":"(ssr)/./src/contexts/PaymentContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/PostsContext.tsx":{"*":{"id":"(ssr)/./src/contexts/PostsContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SocialContext.tsx":{"*":{"id":"(ssr)/./src/contexts/SocialContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SuppliersContext.tsx":{"*":{"id":"(ssr)/./src/contexts/SuppliersContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/WorkersContext.tsx":{"*":{"id":"(ssr)/./src/contexts/WorkersContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/feed/page.tsx":{"*":{"id":"(ssr)/./src/app/feed/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\components\\ui\\sonner.tsx":{"id":"(app-pages-browser)/./src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\BartaContext.tsx":{"id":"(app-pages-browser)/./src/contexts/BartaContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\CompaniesContext.tsx":{"id":"(app-pages-browser)/./src/contexts/CompaniesContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\OnlineUsersContext.tsx":{"id":"(app-pages-browser)/./src/contexts/OnlineUsersContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\PaymentContext.tsx":{"id":"(app-pages-browser)/./src/contexts/PaymentContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\PostsContext.tsx":{"id":"(app-pages-browser)/./src/contexts/PostsContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\SocialContext.tsx":{"id":"(app-pages-browser)/./src/contexts/SocialContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\SuppliersContext.tsx":{"id":"(app-pages-browser)/./src/contexts/SuppliersContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\contexts\\WorkersContext.tsx":{"id":"(app-pages-browser)/./src/contexts/WorkersContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\app\\page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\app\\feed\\page.tsx":{"id":"(app-pages-browser)/./src/app/feed/page.tsx","name":"*","chunks":["app/feed/page","static/chunks/app/feed/page.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\":[],"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\KAAZMAAMAA AUGMENT\\kaazmaamaa-app\\src\\app\\feed\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(rsc)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/BartaContext.tsx":{"*":{"id":"(rsc)/./src/contexts/BartaContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/CompaniesContext.tsx":{"*":{"id":"(rsc)/./src/contexts/CompaniesContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/OnlineUsersContext.tsx":{"*":{"id":"(rsc)/./src/contexts/OnlineUsersContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/PaymentContext.tsx":{"*":{"id":"(rsc)/./src/contexts/PaymentContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/PostsContext.tsx":{"*":{"id":"(rsc)/./src/contexts/PostsContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SocialContext.tsx":{"*":{"id":"(rsc)/./src/contexts/SocialContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/SuppliersContext.tsx":{"*":{"id":"(rsc)/./src/contexts/SuppliersContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/WorkersContext.tsx":{"*":{"id":"(rsc)/./src/contexts/WorkersContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(rsc)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/feed/page.tsx":{"*":{"id":"(rsc)/./src/app/feed/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}