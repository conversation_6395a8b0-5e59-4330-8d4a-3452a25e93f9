'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export interface SupplierDocument {
  id: string
  name: string
  type: 'licence' | 'cr' | 'contract' | 'other'
  url: string
  uploadedAt: string
}

export interface JobDemand {
  id: string
  category: string
  position: string
  description: string
  requirements: string[]
  salaryPerHour: number
  currency: 'SAR' | 'USD' | 'EUR'
  paymentDuration: 'daily' | 'weekly' | 'monthly' | 'project_based'
  location: string
  urgency: 'immediate' | 'within_week' | 'within_month'
  documentsRequired: string[]
  createdAt: string
}

export interface SupplierProfile {
  id: string // Must be unique
  userId: string
  personalInfo: {
    supplierName: string
    nationality: string
    kofilName: string
    kofilPhone: string
    profileImage?: string
    address: string
    phone: string
    whatsapp: string
    email: string
  }
  businessInfo: {
    companyName: string
    crNumber: string // Commercial Registration Number
    licenceNumber: string // Must be unique
    businessType: string
    establishedYear: number
    employeeCount: string
  }
  documents: SupplierDocument[]
  jobDemands: JobDemand[]
  videos: {
    id: string
    title: string
    type: 'company_intro' | 'job_requirements' | 'workplace_tour'
    url: string
    thumbnail?: string
    duration?: number
    uploadedAt: string
  }[]
  isLiveStreaming: boolean
  liveStreamUrl?: string
  operatingLocations: string[]
  preferredWorkerCategories: string[]
  rating: number
  totalReviews: number
  isVerified: boolean
  createdAt: string
  updatedAt: string
}

interface SuppliersContextType {
  suppliers: SupplierProfile[]
  currentSupplierProfile?: SupplierProfile
  createSupplierProfile: (profile: Omit<SupplierProfile, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateSupplierProfile: (id: string, updates: Partial<SupplierProfile>) => void
  addDocument: (supplierId: string, document: Omit<SupplierDocument, 'id' | 'uploadedAt'>) => void
  addJobDemand: (supplierId: string, jobDemand: Omit<JobDemand, 'id' | 'createdAt'>) => void
  updateJobDemand: (supplierId: string, jobDemandId: string, updates: Partial<JobDemand>) => void
  deleteJobDemand: (supplierId: string, jobDemandId: string) => void
  startLiveStream: (supplierId: string, streamUrl: string) => void
  stopLiveStream: (supplierId: string) => void
  getSuppliersByLocation: (location: string) => SupplierProfile[]
  getJobDemandsByCategory: (category: string) => { supplier: SupplierProfile, jobDemand: JobDemand }[]
  validateLicenceUniqueness: (licenceNumber: string, excludeId?: string) => boolean
}

const SuppliersContext = createContext<SuppliersContextType | undefined>(undefined)

// Mock data for demonstration
const mockSuppliers: SupplierProfile[] = [
  {
    id: 'supplier-1',
    userId: 'user-supplier-1',
    personalInfo: {
      supplierName: 'Ahmed Construction Supplier',
      nationality: 'Saudi Arabia',
      kofilName: 'Ahmed Al-Rashid',
      kofilPhone: '+966501234567',
      address: 'King Fahd Road, Riyadh 12345',
      phone: '+966501234567',
      whatsapp: '+966501234567',
      email: '<EMAIL>'
    },
    businessInfo: {
      companyName: 'Al-Rashid Construction Supply Co.',
      crNumber: 'CR-1010123456',
      licenceNumber: 'LIC-2024-001',
      businessType: 'Construction Supply',
      establishedYear: 2015,
      employeeCount: '50-100'
    },
    documents: [
      {
        id: 'doc-1',
        name: 'Business Licence',
        type: 'licence',
        url: '/documents/business-licence.pdf',
        uploadedAt: new Date().toISOString()
      },
      {
        id: 'doc-2',
        name: 'Commercial Registration',
        type: 'cr',
        url: '/documents/cr-certificate.pdf',
        uploadedAt: new Date().toISOString()
      }
    ],
    jobDemands: [
      {
        id: 'job-1',
        category: 'Electrical Work',
        position: 'Senior Electrician',
        description: 'Looking for experienced electricians for large construction project',
        requirements: ['5+ years experience', 'Electrical license', 'Safety certification'],
        salaryPerHour: 85,
        currency: 'SAR',
        paymentDuration: 'weekly',
        location: 'Riyadh',
        urgency: 'immediate',
        documentsRequired: ['Iqama', 'Electrical License', 'Medical Certificate'],
        createdAt: new Date().toISOString()
      }
    ],
    videos: [
      {
        id: 'video-1',
        title: 'Company Introduction',
        type: 'company_intro',
        url: '/videos/company-intro.mp4',
        duration: 180,
        uploadedAt: new Date().toISOString()
      }
    ],
    isLiveStreaming: false,
    operatingLocations: ['Riyadh', 'Jeddah', 'Dammam'],
    preferredWorkerCategories: ['Electrical Work', 'Plumbing', 'Construction'],
    rating: 4.7,
    totalReviews: 23,
    isVerified: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export function SuppliersProvider({ children }: { children: ReactNode }) {
  const [suppliers, setSuppliers] = useState<SupplierProfile[]>([])
  const [currentSupplierProfile, setCurrentSupplierProfile] = useState<SupplierProfile>()

  useEffect(() => {
    // Load suppliers from localStorage or use mock data
    const savedSuppliers = localStorage.getItem('kaazmaamaa-suppliers')
    if (savedSuppliers) {
      setSuppliers(JSON.parse(savedSuppliers))
    } else {
      setSuppliers(mockSuppliers)
      localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(mockSuppliers))
    }
  }, [])

  const saveToStorage = (updatedSuppliers: SupplierProfile[]) => {
    localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(updatedSuppliers))
  }

  const createSupplierProfile = (profile: Omit<SupplierProfile, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProfile: SupplierProfile = {
      ...profile,
      id: `supplier-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const updatedSuppliers = [...suppliers, newProfile]
    setSuppliers(updatedSuppliers)
    saveToStorage(updatedSuppliers)
    setCurrentSupplierProfile(newProfile)
  }

  const updateSupplierProfile = (id: string, updates: Partial<SupplierProfile>) => {
    const updatedSuppliers = suppliers.map(supplier =>
      supplier.id === id
        ? { ...supplier, ...updates, updatedAt: new Date().toISOString() }
        : supplier
    )
    setSuppliers(updatedSuppliers)
    saveToStorage(updatedSuppliers)
  }

  const addDocument = (supplierId: string, document: Omit<SupplierDocument, 'id' | 'uploadedAt'>) => {
    const newDocument: SupplierDocument = {
      ...document,
      id: Date.now().toString(),
      uploadedAt: new Date().toISOString()
    }

    updateSupplierProfile(supplierId, {
      documents: [...(suppliers.find(s => s.id === supplierId)?.documents || []), newDocument]
    })
  }

  const addJobDemand = (supplierId: string, jobDemand: Omit<JobDemand, 'id' | 'createdAt'>) => {
    const newJobDemand: JobDemand = {
      ...jobDemand,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    }

    updateSupplierProfile(supplierId, {
      jobDemands: [...(suppliers.find(s => s.id === supplierId)?.jobDemands || []), newJobDemand]
    })
  }

  const updateJobDemand = (supplierId: string, jobDemandId: string, updates: Partial<JobDemand>) => {
    const supplier = suppliers.find(s => s.id === supplierId)
    if (supplier) {
      const updatedJobDemands = supplier.jobDemands.map(job =>
        job.id === jobDemandId ? { ...job, ...updates } : job
      )
      updateSupplierProfile(supplierId, { jobDemands: updatedJobDemands })
    }
  }

  const deleteJobDemand = (supplierId: string, jobDemandId: string) => {
    const supplier = suppliers.find(s => s.id === supplierId)
    if (supplier) {
      const updatedJobDemands = supplier.jobDemands.filter(job => job.id !== jobDemandId)
      updateSupplierProfile(supplierId, { jobDemands: updatedJobDemands })
    }
  }

  const startLiveStream = (supplierId: string, streamUrl: string) => {
    updateSupplierProfile(supplierId, {
      isLiveStreaming: true,
      liveStreamUrl: streamUrl
    })
  }

  const stopLiveStream = (supplierId: string) => {
    updateSupplierProfile(supplierId, {
      isLiveStreaming: false,
      liveStreamUrl: undefined
    })
  }

  const getSuppliersByLocation = (location: string) => {
    return suppliers.filter(supplier => 
      supplier.operatingLocations.some(loc => 
        loc.toLowerCase().includes(location.toLowerCase())
      )
    )
  }

  const getJobDemandsByCategory = (category: string) => {
    const results: { supplier: SupplierProfile, jobDemand: JobDemand }[] = []
    suppliers.forEach(supplier => {
      supplier.jobDemands.forEach(jobDemand => {
        if (jobDemand.category.toLowerCase().includes(category.toLowerCase())) {
          results.push({ supplier, jobDemand })
        }
      })
    })
    return results
  }

  const validateLicenceUniqueness = (licenceNumber: string, excludeId?: string) => {
    return !suppliers.some(supplier => 
      supplier.businessInfo.licenceNumber === licenceNumber && supplier.id !== excludeId
    )
  }

  const value = {
    suppliers,
    currentSupplierProfile,
    createSupplierProfile,
    updateSupplierProfile,
    addDocument,
    addJobDemand,
    updateJobDemand,
    deleteJobDemand,
    startLiveStream,
    stopLiveStream,
    getSuppliersByLocation,
    getJobDemandsByCategory,
    validateLicenceUniqueness
  }

  return (
    <SuppliersContext.Provider value={value}>
      {children}
    </SuppliersContext.Provider>
  )
}

export function useSuppliers() {
  const context = useContext(SuppliersContext)
  if (context === undefined) {
    throw new Error('useSuppliers must be used within a SuppliersProvider')
  }
  return context
}
