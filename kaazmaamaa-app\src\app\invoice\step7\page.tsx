'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Users, Building, CheckCircle, AlertTriangle, FileText, Calculator } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep7() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<number[]>([])

  const markSectionComplete = (sectionId: number) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section ${sectionId} completed! ✅`)
    }
  }

  const vatGroupSteps = [
    {
      id: 1,
      title: "VAT Group Identification",
      description: "Identify and configure VAT group scenarios",
      icon: <Users className="h-5 w-5" />,
      details: [
        "Define VAT group structure",
        "Identify representative member",
        "Configure group relationships",
        "Set up group VAT numbers"
      ]
    },
    {
      id: 2,
      title: "Representative Member Setup",
      description: "Configure the representative member for VAT group",
      icon: <Building className="h-5 w-5" />,
      details: [
        "Designate representative entity",
        "Configure VAT registration",
        "Set up invoicing authority",
        "Establish reporting responsibility"
      ]
    },
    {
      id: 3,
      title: "Group Invoice Processing",
      description: "Handle invoices within VAT group context",
      icon: <FileText className="h-5 w-5" />,
      details: [
        "Process intra-group transactions",
        "Handle external invoices",
        "Apply group VAT rules",
        "Manage consolidated reporting"
      ]
    },
    {
      id: 4,
      title: "Edge Case Handling",
      description: "Manage complex VAT group scenarios",
      icon: <AlertTriangle className="h-5 w-5" />,
      details: [
        "Handle member changes",
        "Process partial group transactions",
        "Manage cross-border scenarios",
        "Handle group dissolution"
      ]
    }
  ]

  const vatGroupScenarios = [
    {
      title: "Standard VAT Group",
      description: "Multiple entities under single VAT registration",
      example: "Parent company with subsidiaries",
      considerations: [
        "Single VAT number for group",
        "Representative member invoicing",
        "Consolidated VAT reporting",
        "Intra-group transaction handling"
      ],
      color: "blue"
    },
    {
      title: "Partial VAT Group",
      description: "Some entities participate in VAT group",
      example: "Mixed group with VAT and non-VAT entities",
      considerations: [
        "Separate VAT treatment",
        "Individual entity invoicing",
        "Split reporting requirements",
        "Complex transaction flows"
      ],
      color: "yellow"
    },
    {
      title: "Cross-Border VAT Group",
      description: "International entities in VAT group",
      example: "Multinational corporation structure",
      considerations: [
        "Multiple jurisdiction compliance",
        "Currency conversion handling",
        "Different VAT rates",
        "Regulatory coordination"
      ],
      color: "green"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-orange-600">Step 7: VAT Groups & Edge Cases</h1>
              <Badge className="bg-orange-100 text-orange-800">
                Advanced
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Proceeding to Step 8...')
                  router.push('/invoice/step8')
                }}
                className="bg-orange-600 hover:bg-orange-700"
              >
                Next: Sandbox Testing
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-orange-50 border-orange-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <Users className="h-16 w-16 text-orange-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-orange-800 mb-2">👥 VAT Groups & Edge Cases</h3>
              <p className="text-orange-600 mb-4">
                Handle complex VAT Group scenarios and edge cases in ZATCA compliance
              </p>
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-800">{completedSections.length}</div>
                  <div className="text-sm text-orange-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{vatGroupSteps.length - completedSections.length}</div>
                  <div className="text-sm text-blue-600">Remaining</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VAT Group Scenarios */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {vatGroupScenarios.map((scenario, index) => (
            <Card key={index} className={`bg-${scenario.color}-50 border-${scenario.color}-200`}>
              <CardHeader>
                <CardTitle className={`text-${scenario.color}-800 text-lg`}>
                  {scenario.title}
                </CardTitle>
                <p className={`text-${scenario.color}-600 text-sm`}>
                  {scenario.description}
                </p>
                <Badge variant="outline" className={`text-${scenario.color}-700 border-${scenario.color}-300 w-fit`}>
                  {scenario.example}
                </Badge>
              </CardHeader>
              <CardContent>
                <h4 className="font-semibold text-gray-800 mb-2">Key Considerations:</h4>
                <ul className="space-y-1">
                  {scenario.considerations.map((consideration, idx) => (
                    <li key={idx} className="flex items-start space-x-2 text-sm">
                      <div className={`w-1.5 h-1.5 bg-${scenario.color}-400 rounded-full mt-2 flex-shrink-0`}></div>
                      <span className="text-gray-600">{consideration}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Implementation Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {vatGroupSteps.map((step) => (
            <Card
              key={step.id}
              className={`transition-all hover:shadow-lg ${
                completedSections.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSections.includes(step.id) ? 'bg-green-100 text-green-600' : 'bg-orange-100 text-orange-600'
                  }`}>
                    {completedSections.includes(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">
                        {step.title}
                      </h3>
                      {completedSections.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-4">
                  {step.details.map((detail, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-orange-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{detail}</span>
                    </li>
                  ))}
                </ul>

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast.info(`Learning about ${step.title}...`)
                    }}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    View Guide
                  </Button>

                  {!completedSections.includes(step.id) && (
                    <Button
                      size="sm"
                      onClick={() => markSectionComplete(step.id)}
                      className="bg-orange-600 hover:bg-orange-700"
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* VAT Group Configuration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2 text-orange-600" />
              VAT Group Configuration Example
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Group Structure Configuration</h4>
                <code className="text-sm bg-gray-100 p-3 rounded block whitespace-pre">
{`{
  "vatGroup": {
    "groupVATNumber": "300000000000003",
    "representativeMember": {
      "name": "KAAZMAAMAA Holdings Ltd",
      "vatNumber": "300000000000003",
      "role": "representative"
    },
    "members": [
      {
        "name": "KAAZMAAMAA Trading Co",
        "vatNumber": "300000000000004",
        "role": "member"
      },
      {
        "name": "KAAZMAAMAA Services LLC",
        "vatNumber": "300000000000005",
        "role": "member"
      }
    ]
  }
}`}
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Edge Cases & Solutions */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
              Common Edge Cases & Solutions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-red-50 rounded-lg">
                  <h4 className="font-semibold text-red-800 mb-2">Member Entity Changes</h4>
                  <p className="text-red-600 text-sm mb-2">
                    When entities join or leave the VAT group
                  </p>
                  <ul className="text-sm text-red-700 space-y-1">
                    <li>• Update group registration</li>
                    <li>• Adjust invoicing procedures</li>
                    <li>• Handle transition periods</li>
                    <li>• Maintain audit trails</li>
                  </ul>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h4 className="font-semibold text-yellow-800 mb-2">Cross-Border Transactions</h4>
                  <p className="text-yellow-600 text-sm mb-2">
                    International VAT group transactions
                  </p>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Apply correct VAT rates</li>
                    <li>• Handle currency conversion</li>
                    <li>• Comply with local regulations</li>
                    <li>• Manage reporting requirements</li>
                  </ul>
                </div>
              </div>

              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">Partial Group Participation</h4>
                  <p className="text-blue-600 text-sm mb-2">
                    Some entities not in VAT group
                  </p>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Separate VAT treatment</li>
                    <li>• Individual invoicing</li>
                    <li>• Split reporting</li>
                    <li>• Complex reconciliation</li>
                  </ul>
                </div>

                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">Group Dissolution</h4>
                  <p className="text-green-600 text-sm mb-2">
                    When VAT group is dissolved
                  </p>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Final group reporting</li>
                    <li>• Individual registrations</li>
                    <li>• Asset transfer handling</li>
                    <li>• Compliance transition</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Best Practices */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              VAT Group Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Documentation</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Maintain group structure records</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Document member relationships</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Track configuration changes</span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Compliance</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Regular compliance reviews</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Automated validation checks</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Audit trail maintenance</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
