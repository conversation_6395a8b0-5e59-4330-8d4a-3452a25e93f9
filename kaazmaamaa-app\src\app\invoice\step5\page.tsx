'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, QrCode, FileText, CheckCircle, Code, Download, Eye } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep5() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<number[]>([])

  const markSectionComplete = (sectionId: number) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section ${sectionId} completed! ✅`)
    }
  }

  const qrSteps = [
    {
      id: 1,
      title: "TLV Format Structure",
      description: "Tag-Length-Value encoding for QR data",
      icon: <Code className="h-5 w-5" />,
      details: [
        "Tag: Data type identifier",
        "Length: Data field length",
        "Value: Actual data content",
        "Sequential TLV blocks"
      ]
    },
    {
      id: 2,
      title: "Required QR Fields",
      description: "ZATCA mandatory fields for QR code",
      icon: <FileText className="h-5 w-5" />,
      details: [
        "Seller Name (Tag 1)",
        "VAT Registration Number (Tag 2)",
        "Invoice Date & Time (Tag 3)",
        "Invoice Total (Tag 4)",
        "VAT Total (Tag 5)"
      ]
    },
    {
      id: 3,
      title: "Base64 Encoding",
      description: "Convert TLV data to Base64 format",
      icon: <QrCode className="h-5 w-5" />,
      details: [
        "Concatenate all TLV blocks",
        "Apply Base64 encoding",
        "Generate QR code string",
        "Validate format compliance"
      ]
    },
    {
      id: 4,
      title: "QR Code Generation",
      description: "Create visual QR code from encoded data",
      icon: <Eye className="h-5 w-5" />,
      details: [
        "Use QR code library",
        "Set appropriate error correction",
        "Generate visual representation",
        "Embed in invoice document"
      ]
    }
  ]

  const sampleQRData = {
    sellerName: "KAAZMAAMAA Company",
    vatNumber: "300000000000003",
    timestamp: "2024-01-15T10:30:00Z",
    invoiceTotal: "2035.50",
    vatTotal: "265.50"
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Step 5: QR Code Generation</h1>
              <Badge className="bg-blue-100 text-blue-800">
                TLV Format
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Proceeding to Step 6...')
                  router.push('/invoice/step6')
                }}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Next: Clearance APIs
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <QrCode className="h-16 w-16 text-blue-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-blue-800 mb-2">📱 QR Code Generation</h3>
              <p className="text-blue-600 mb-4">
                Generate ZATCA-compliant QR codes using TLV format and Base64 encoding
              </p>
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{completedSections.length}</div>
                  <div className="text-sm text-blue-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-800">{qrSteps.length - completedSections.length}</div>
                  <div className="text-sm text-purple-600">Remaining</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* TLV Format Example */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="h-5 w-5 mr-2 text-blue-600" />
              TLV Format Example
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">Sample QR Data Structure</h4>
                <div className="space-y-2 text-sm font-mono">
                  <div className="flex items-center space-x-2">
                    <span className="bg-red-100 px-2 py-1 rounded">Tag 1</span>
                    <span className="bg-yellow-100 px-2 py-1 rounded">Length: 16</span>
                    <span className="bg-green-100 px-2 py-1 rounded">Value: {sampleQRData.sellerName}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-red-100 px-2 py-1 rounded">Tag 2</span>
                    <span className="bg-yellow-100 px-2 py-1 rounded">Length: 15</span>
                    <span className="bg-green-100 px-2 py-1 rounded">Value: {sampleQRData.vatNumber}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-red-100 px-2 py-1 rounded">Tag 3</span>
                    <span className="bg-yellow-100 px-2 py-1 rounded">Length: 20</span>
                    <span className="bg-green-100 px-2 py-1 rounded">Value: {sampleQRData.timestamp}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-red-100 px-2 py-1 rounded">Tag 4</span>
                    <span className="bg-yellow-100 px-2 py-1 rounded">Length: 7</span>
                    <span className="bg-green-100 px-2 py-1 rounded">Value: {sampleQRData.invoiceTotal}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-red-100 px-2 py-1 rounded">Tag 5</span>
                    <span className="bg-yellow-100 px-2 py-1 rounded">Length: 6</span>
                    <span className="bg-green-100 px-2 py-1 rounded">Value: {sampleQRData.vatTotal}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {qrSteps.map((step) => (
            <Card
              key={step.id}
              className={`transition-all hover:shadow-lg ${
                completedSections.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSections.includes(step.id) ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                  }`}>
                    {completedSections.includes(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">
                        {step.title}
                      </h3>
                      {completedSections.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-4">
                  {step.details.map((detail, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{detail}</span>
                    </li>
                  ))}
                </ul>

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast.info(`Learning about ${step.title}...`)
                    }}
                  >
                    <Code className="h-4 w-4 mr-2" />
                    View Code
                  </Button>

                  {!completedSections.includes(step.id) && (
                    <Button
                      size="sm"
                      onClick={() => markSectionComplete(step.id)}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Code Implementation */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="h-5 w-5 mr-2 text-blue-600" />
              Implementation Code
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">TLV Encoding Function</h4>
                <code className="text-sm bg-gray-100 p-2 rounded block whitespace-pre">
{`function encodeTLV(tag, value) {
  const length = value.length
  return tag + String.fromCharCode(length) + value
}`}
                </code>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">QR Data Generation</h4>
                <code className="text-sm bg-gray-100 p-2 rounded block whitespace-pre">
{`function generateQRData(invoice) {
  let tlvData = ''
  tlvData += encodeTLV(1, invoice.sellerName)
  tlvData += encodeTLV(2, invoice.vatNumber)
  tlvData += encodeTLV(3, invoice.timestamp)
  tlvData += encodeTLV(4, invoice.total)
  tlvData += encodeTLV(5, invoice.vatTotal)
  return btoa(tlvData) // Base64 encode
}`}
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* QR Code Preview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <QrCode className="h-5 w-5 mr-2 text-blue-600" />
              QR Code Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <div className="inline-block p-4 bg-white border-2 border-gray-200 rounded-lg">
                <div className="w-32 h-32 bg-gray-100 flex items-center justify-center rounded">
                  <QrCode className="h-16 w-16 text-gray-400" />
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-2">
                QR Code will be generated based on invoice data
              </p>
              <Button
                className="mt-4 bg-blue-600 hover:bg-blue-700"
                onClick={() => {
                  toast.success('QR Code generated successfully!')
                }}
              >
                <Download className="h-4 w-4 mr-2" />
                Generate QR Code
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
