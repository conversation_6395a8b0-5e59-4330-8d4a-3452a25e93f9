{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        onChange={props.onChange}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACL,UAAU,MAAM,QAAQ;QACvB,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/companies/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useCompanies } from '@/contexts/CompaniesContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Avatar } from '@/components/ui/avatar'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Building,\n  Search,\n  MessageCircle,\n  Phone,\n  ArrowLeft,\n  MapPin,\n  Star,\n  Plus,\n  FileText,\n  Video,\n  Calendar,\n  Briefcase,\n  Eye,\n  Download,\n  Play,\n  Radio,\n  CheckCircle,\n  Clock,\n  DollarSign,\n  Globe,\n  Users,\n  Award,\n  Factory,\n  TrendingUp,\n  Target,\n  Heart,\n  UserCheck\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\nexport default function CompaniesPage() {\n  const { companies } = useCompanies()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedLocation, setSelectedLocation] = useState('all')\n  const [selectedIndustry, setSelectedIndustry] = useState('all')\n  const [showHiringOnly, setShowHiringOnly] = useState(false)\n\n  // Debug logging\n  console.log('🔍 Companies page - companies:', companies)\n  console.log('🔍 Companies page - companies type:', typeof companies)\n  console.log('🔍 Companies page - companies is array:', Array.isArray(companies))\n\n  useEffect(() => {\n    // Wait for companies data to be available or timeout after 2 seconds\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 2000)\n\n    // If companies data is available, stop loading immediately\n    if (companies !== undefined) {\n      setIsLoading(false)\n    }\n\n    return () => clearTimeout(timer)\n  }, [companies])\n\n  const handleWhatsAppContact = (whatsapp: string, companyName: string) => {\n    const message = `Hello ${companyName}, I found your company on KAAZMAAMAA Companies Block and would like to discuss career opportunities.`\n    const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`\n    window.open(whatsappUrl, '_blank')\n  }\n\n  // Filter companies based on search term (with null check)\n  const filteredCompanies = (companies || []).filter(company =>\n    company.companyInfo?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    company.companyInfo?.industry?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    company.companyInfo?.headquarters?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  if (isLoading || companies === undefined) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4\"></div>\n          <h2 className=\"text-xl font-semibold text-gray-700\">Loading Companies...</h2>\n          <p className=\"text-gray-500 mt-2\">Please wait while we fetch the companies data</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  toast.info('Navigating back to Feed...')\n                  router.push('/feed')\n                }}\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Feed\n              </Button>\n              <h1 className=\"text-2xl font-bold text-purple-600\">Companies Block</h1>\n              <Badge className=\"bg-purple-100 text-purple-800\">\n                {filteredCompanies.length} Companies Available\n              </Badge>\n            </div>\n            <Button\n              onClick={() => router.push('/companies/create')}\n              className=\"bg-purple-600 hover:bg-purple-700\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Create Company Profile\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 py-6\">\n        {/* Quick Stats */}\n        <Card className=\"mb-6 bg-purple-50 border-purple-200\">\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold text-purple-800 mb-2\">🏢 Professional Companies Directory</h3>\n              <p className=\"text-purple-600 mb-4\">Browse {(companies || []).length} verified companies across different industries</p>\n              <div className=\"grid grid-cols-3 gap-4 max-w-md mx-auto\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-800\">{(companies || []).filter(c => c.isHiring && (c.jobOpenings?.length || 0) > 0).length}</div>\n                  <div className=\"text-sm text-purple-600\">Hiring Now</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-800\">{(companies || []).filter(c => c.isVerified).length}</div>\n                  <div className=\"text-sm text-blue-600\">Verified</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-800\">{(companies || []).filter(c => c.isLiveStreaming).length}</div>\n                  <div className=\"text-sm text-green-600\">Live</div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Search */}\n        <Card className=\"mb-6\">\n          <CardContent className=\"pt-6\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"Search by company name, industry, or location...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Companies Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {(filteredCompanies || []).map((company) => (\n            <Card key={company.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"relative\">\n                    <Avatar className=\"h-16 w-16 bg-purple-100 flex items-center justify-center\">\n                      <Building className=\"h-8 w-8 text-purple-600\" />\n                    </Avatar>\n                    {company.isLiveStreaming && (\n                      <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center\">\n                        <Radio className=\"h-3 w-3 mr-1\" />\n                        LIVE\n                      </div>\n                    )}\n                    {company.isVerified && (\n                      <div className=\"absolute -bottom-1 -right-1 bg-purple-500 text-white rounded-full p-1\">\n                        <CheckCircle className=\"h-3 w-3\" />\n                      </div>\n                    )}\n                    {company.isHiring && (\n                      <div className=\"absolute -top-1 -left-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full\">\n                        HIRING\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <h3 className=\"font-semibold text-lg truncate\">{company.companyInfo?.companyName || 'Unknown Company'}</h3>\n                    <p className=\"text-purple-600 font-medium\">{company.companyInfo?.industry || 'No Industry'}</p>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <Badge className=\"bg-purple-100 text-purple-800\">\n                        {company.companyInfo?.companySize || 'Unknown Size'}\n                      </Badge>\n                      <div className=\"flex items-center text-yellow-500\">\n                        <Star className=\"h-4 w-4 fill-current\" />\n                        <span className=\"text-sm ml-1\">{company.rating || 0}</span>\n                        <span className=\"text-gray-500 text-sm ml-1\">({company.totalReviews || 0})</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardHeader>\n\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <MapPin className=\"h-4 w-4 mr-2\" />\n                    {company.companyInfo?.headquarters || 'Location not specified'}\n                  </div>\n\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <Users className=\"h-4 w-4 mr-2\" />\n                    {company.totalEmployees || 0} employees\n                  </div>\n\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <Calendar className=\"h-4 w-4 mr-2\" />\n                    Founded: {company.companyInfo?.foundedYear || 'Unknown'}\n                  </div>\n\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <Globe className=\"h-4 w-4 mr-2\" />\n                    {company.companyInfo?.website || 'No website'}\n                  </div>\n\n                  {/* Job Openings */}\n                  {(company.jobOpenings || []).length > 0 && (\n                    <div className=\"pt-2 border-t\">\n                      <p className=\"text-sm font-medium text-gray-700 mb-2\">Open Positions:</p>\n                      {(company.jobOpenings || []).slice(0, 2).map((job) => (\n                        <div key={job.id} className=\"bg-gray-50 rounded-lg p-2 mb-2\">\n                          <p className=\"font-medium text-sm\">{job.title}</p>\n                          <p className=\"text-xs text-gray-600\">{job.department}</p>\n                          <div className=\"flex items-center justify-between mt-1\">\n                            <span className=\"text-xs text-purple-600\">\n                              {job.salaryRange.min}-{job.salaryRange.max} {job.salaryRange.currency}\n                            </span>\n                            <Badge variant=\"secondary\" className=\"text-xs\">\n                              {job.urgency.replace('_', ' ')}\n                            </Badge>\n                          </div>\n                        </div>\n                      ))}\n                      {(company.jobOpenings || []).length > 2 && (\n                        <p className=\"text-xs text-gray-500\">+{(company.jobOpenings || []).length - 2} more positions</p>\n                      )}\n                    </div>\n                  )}\n\n                  {/* Company Benefits */}\n                  <div className=\"flex flex-wrap gap-1 mt-2\">\n                    {(company.companyBenefits || []).slice(0, 3).map((benefit, index) => (\n                      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                        {benefit}\n                      </Badge>\n                    ))}\n                    {(company.companyBenefits || []).length > 3 && (\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        +{(company.companyBenefits || []).length - 3} more\n                      </Badge>\n                    )}\n                  </div>\n\n                  {/* Work Environment Indicators */}\n                  <div className=\"flex items-center space-x-3 text-xs text-gray-500 pt-2 border-t\">\n                    {company.workEnvironment?.isRemoteFriendly && (\n                      <div className=\"flex items-center\">\n                        <Globe className=\"h-3 w-3 mr-1\" />\n                        Remote\n                      </div>\n                    )}\n                    {company.workEnvironment?.hasFlexibleHours && (\n                      <div className=\"flex items-center\">\n                        <Clock className=\"h-3 w-3 mr-1\" />\n                        Flexible\n                      </div>\n                    )}\n                    {company.workEnvironment?.providesTraining && (\n                      <div className=\"flex items-center\">\n                        <Award className=\"h-3 w-3 mr-1\" />\n                        Training\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Media Indicators */}\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-500 pt-2 border-t\">\n                    <div className=\"flex items-center\">\n                      <FileText className=\"h-4 w-4 mr-1\" />\n                      {(company.documents || []).length} docs\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Video className=\"h-4 w-4 mr-1\" />\n                      {(company.videos || []).length} videos\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Briefcase className=\"h-4 w-4 mr-1\" />\n                      {(company.jobOpenings || []).length} jobs\n                    </div>\n                  </div>\n\n                  <div className=\"flex gap-2 pt-3\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => router.push(`/companies/${company.id}`)}\n                      className=\"flex-1 bg-purple-600 hover:bg-purple-700\"\n                    >\n                      <Eye className=\"h-4 w-4 mr-2\" />\n                      View Profile\n                    </Button>\n\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleWhatsAppContact(company.contactInfo?.hrWhatsapp || '', company.companyInfo?.companyName || '')}\n                      className=\"border-purple-300 text-purple-600 hover:bg-purple-50\"\n                    >\n                      <MessageCircle className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {filteredCompanies.length === 0 && (\n          <Card>\n            <CardContent className=\"text-center py-12\">\n              <Building className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">No Companies Found</h3>\n              <p className=\"text-gray-600 mb-4\">\n                Try adjusting your search criteria or filters to find companies.\n              </p>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSearchTerm('')\n                  setSelectedLocation('all')\n                  setSelectedIndustry('all')\n                  setShowHiringOnly(false)\n                }}\n              >\n                Clear All Filters\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA;;;AAxCA;;;;;;;;;;;AA0Ce,SAAS;;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,gBAAgB;IAChB,QAAQ,GAAG,CAAC,kCAAkC;IAC9C,QAAQ,GAAG,CAAC,uCAAuC,OAAO;IAC1D,QAAQ,GAAG,CAAC,2CAA2C,MAAM,OAAO,CAAC;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,qEAAqE;YACrE,MAAM,QAAQ;iDAAW;oBACvB,aAAa;gBACf;gDAAG;YAEH,2DAA2D;YAC3D,IAAI,cAAc,WAAW;gBAC3B,aAAa;YACf;YAEA;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;KAAU;IAEd,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,UAAU,CAAC,MAAM,EAAE,YAAY,oGAAoG,CAAC;QAC1I,MAAM,cAAc,CAAC,cAAc,EAAE,SAAS,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,mBAAmB,UAAU;QAC1G,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,0DAA0D;IAC1D,MAAM,oBAAoB,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAA,UACjD,QAAQ,WAAW,EAAE,aAAa,cAAc,SAAS,WAAW,WAAW,OAC/E,QAAQ,WAAW,EAAE,UAAU,cAAc,SAAS,WAAW,WAAW,OAC5E,QAAQ,WAAW,EAAE,cAAc,cAAc,SAAS,WAAW,WAAW;IAGlF,IAAI,aAAa,cAAc,WAAW;QACxC,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;4CACX,OAAO,IAAI,CAAC;wCACd;;0DAEA,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,kBAAkB,MAAM;4CAAC;;;;;;;;;;;;;0CAG9B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA6C;;;;;;kDAC3D,6LAAC;wCAAE,WAAU;;4CAAuB;4CAAQ,CAAC,aAAa,EAAE,EAAE,MAAM;4CAAC;;;;;;;kDACrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,CAAC,EAAE,WAAW,EAAE,UAAU,CAAC,IAAI,GAAG,MAAM;;;;;;kEACzI,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;0DAE3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;kEACrG,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,CAAC,aAAa,EAAE,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;;;;;;kEAC3G,6LAAC;wDAAI,WAAU;kEAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOlB,6LAAC;wBAAI,WAAU;kCACZ,CAAC,qBAAqB,EAAE,EAAE,GAAG,CAAC,CAAC,wBAC9B,6LAAC,mIAAA,CAAA,OAAI;gCAAkB,WAAU;;kDAC/B,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;wDAErB,QAAQ,eAAe,kBACtB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIrC,QAAQ,UAAU,kBACjB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;wDAG1B,QAAQ,QAAQ,kBACf,6LAAC;4DAAI,WAAU;sEAAqF;;;;;;;;;;;;8DAKxG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAkC,QAAQ,WAAW,EAAE,eAAe;;;;;;sEACpF,6LAAC;4DAAE,WAAU;sEAA+B,QAAQ,WAAW,EAAE,YAAY;;;;;;sEAC7E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EACd,QAAQ,WAAW,EAAE,eAAe;;;;;;8EAEvC,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFAAgB,QAAQ,MAAM,IAAI;;;;;;sFAClD,6LAAC;4EAAK,WAAU;;gFAA6B;gFAAE,QAAQ,YAAY,IAAI;gFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOnF,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,QAAQ,WAAW,EAAE,gBAAgB;;;;;;;8DAGxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,QAAQ,cAAc,IAAI;wDAAE;;;;;;;8DAG/B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;wDAC3B,QAAQ,WAAW,EAAE,eAAe;;;;;;;8DAGhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,QAAQ,WAAW,EAAE,WAAW;;;;;;;gDAIlC,CAAC,QAAQ,WAAW,IAAI,EAAE,EAAE,MAAM,GAAG,mBACpC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC;;;;;;wDACrD,CAAC,QAAQ,WAAW,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC5C,6LAAC;gEAAiB,WAAU;;kFAC1B,6LAAC;wEAAE,WAAU;kFAAuB,IAAI,KAAK;;;;;;kFAC7C,6LAAC;wEAAE,WAAU;kFAAyB,IAAI,UAAU;;;;;;kFACpD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;oFACb,IAAI,WAAW,CAAC,GAAG;oFAAC;oFAAE,IAAI,WAAW,CAAC,GAAG;oFAAC;oFAAE,IAAI,WAAW,CAAC,QAAQ;;;;;;;0FAEvE,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;0FAClC,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;+DARtB,IAAI,EAAE;;;;;wDAajB,CAAC,QAAQ,WAAW,IAAI,EAAE,EAAE,MAAM,GAAG,mBACpC,6LAAC;4DAAE,WAAU;;gEAAwB;gEAAE,CAAC,QAAQ,WAAW,IAAI,EAAE,EAAE,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAMpF,6LAAC;oDAAI,WAAU;;wDACZ,CAAC,QAAQ,eAAe,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzD,6LAAC,oIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAY,WAAU;0EAC9C;+DADS;;;;;wDAIb,CAAC,QAAQ,eAAe,IAAI,EAAE,EAAE,MAAM,GAAG,mBACxC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAAU;gEAC3C,CAAC,QAAQ,eAAe,IAAI,EAAE,EAAE,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAMnD,6LAAC;oDAAI,WAAU;;wDACZ,QAAQ,eAAe,EAAE,kCACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIrC,QAAQ,eAAe,EAAE,kCACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIrC,QAAQ,eAAe,EAAE,kCACxB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAOxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,CAAC,QAAQ,SAAS,IAAI,EAAE,EAAE,MAAM;gEAAC;;;;;;;sEAEpC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,CAAC,QAAQ,MAAM,IAAI,EAAE,EAAE,MAAM;gEAAC;;;;;;;sEAEjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEACpB,CAAC,QAAQ,WAAW,IAAI,EAAE,EAAE,MAAM;gEAAC;;;;;;;;;;;;;8DAIxC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE,EAAE;4DACrD,WAAU;;8EAEV,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAIlC,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,sBAAsB,QAAQ,WAAW,EAAE,cAAc,IAAI,QAAQ,WAAW,EAAE,eAAe;4DAChH,WAAU;sEAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA3JxB,QAAQ,EAAE;;;;;;;;;;oBAoKxB,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,cAAc;wCACd,oBAAoB;wCACpB,oBAAoB;wCACpB,kBAAkB;oCACpB;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA/TwB;;QACA,uIAAA,CAAA,eAAY;QACnB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}