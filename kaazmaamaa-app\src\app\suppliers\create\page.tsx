'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Upload, 
  FileText, 
  CheckCircle, 
  User,
  Building,
  FileCheck,
  CreditCard,
  Award
} from 'lucide-react'
import { toast } from 'sonner'

export default function CreateSupplierProfilePage() {
  const router = useRouter()
  const { user } = useAuth()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Supplier Information
  const [supplierInfo, setSupplierInfo] = useState({
    supplierName: '',
    nationality: '',
    kofilName: '',
    crNumber: '',
    license: '',
    profileDescription: '',
    address: '',
    email: '',
    phone: '',
    whatsapp: ''
  })

  // Job Demands & Salary
  const [jobInfo, setJobInfo] = useState({
    jobDemands: [] as string[],
    salaryOffered: '',
    currency: 'SAR' as 'SAR' | 'USD' | 'EUR',
    paymentFrequency: 'monthly' as 'daily' | 'weekly' | 'monthly' | 'hourly',
    benefits: [] as string[],
    workingHours: '',
    location: ''
  })

  // Document uploads
  const [documents, setDocuments] = useState<File[]>([])

  const handleSupplierInfoChange = (field: string, value: string) => {
    setSupplierInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleJobInfoChange = (field: string, value: string) => {
    if (field === 'jobDemands' || field === 'benefits') {
      // Convert comma-separated string to array
      const arrayValue = value.split(',').map(item => item.trim()).filter(item => item.length > 0)
      setJobInfo(prev => ({ ...prev, [field]: arrayValue }))
    } else {
      setJobInfo(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleDocumentUpload = (files: FileList | null) => {
    if (!files) return

    const validFiles = Array.from(files).filter(file => {
      if (file.type !== 'application/pdf') {
        toast.error(`${file.name} is not a PDF file`)
        return false
      }
      if (file.size > 10 * 1024 * 1024) {
        toast.error(`${file.name} is too large (max 10MB)`)
        return false
      }
      return true
    })

    setDocuments(prev => [...prev, ...validFiles])
    toast.success(`${validFiles.length} document(s) uploaded successfully!`)
  }

  const removeDocument = (index: number) => {
    setDocuments(prev => prev.filter((_, i) => i !== index))
    toast.info('Document removed')
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(supplierInfo.supplierName && supplierInfo.phone && supplierInfo.email)
      case 2:
        return !!(jobInfo.jobDemands.length > 0 && jobInfo.salaryOffered)
      case 3:
        return documents.length > 0
      default:
        return false
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3))
    } else {
      toast.error('Please fill in all required fields before proceeding')
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(3)) {
      toast.error('Please complete all required sections')
      return
    }

    setIsSubmitting(true)

    try {
      // In a real app, this would save to database
      const supplierProfile = {
        userId: user?.id || 'current-user',
        supplierInfo,
        jobInfo,
        documents: documents.map(doc => ({
          name: doc.name,
          size: doc.size,
          type: doc.type,
          url: URL.createObjectURL(doc)
        })),
        createdAt: new Date().toISOString(),
        isVerified: false
      }

      console.log('Supplier profile created:', supplierProfile)
      toast.success('Supplier profile created successfully! 🎉')
      
      // Redirect to user's own profile page
      router.push(`/profile/${user?.id || 'current-user'}`)
    } catch (error) {
      console.error('Error creating profile:', error)
      toast.error('Failed to create profile. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Supplier Information'
      case 2: return 'Job Demands & Salary'
      case 3: return 'Document Upload'
      default: return 'Profile Setup'
    }
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return Building
      case 2: return Award
      case 3: return FileText
      default: return User
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/suppliers')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Suppliers
              </Button>
              <h1 className="text-2xl font-bold text-green-600">Create Supplier Profile</h1>
            </div>
            <Badge variant="outline" className="text-green-600">
              Step {currentStep} of 3
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {[1, 2, 3].map((step) => {
              const StepIcon = getStepIcon(step)
              const isActive = step === currentStep
              const isCompleted = step < currentStep
              const isValid = validateStep(step)

              return (
                <div key={step} className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                      isCompleted
                        ? 'bg-green-600 border-green-600 text-white'
                        : isActive
                        ? 'bg-green-600 border-green-600 text-white'
                        : isValid
                        ? 'bg-gray-100 border-gray-300 text-gray-600'
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <StepIcon className="h-6 w-6" />
                    )}
                  </div>
                  <span className={`text-xs mt-2 text-center ${isActive ? 'text-green-600 font-medium' : 'text-gray-500'}`}>
                    {getStepTitle(step)}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Form Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {(() => {
                const StepIcon = getStepIcon(currentStep)
                return <StepIcon className="h-5 w-5 text-green-600" />
              })()}
              <span>{getStepTitle(currentStep)}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Step 1: Supplier Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="supplierName">Supplier Name *</Label>
                    <Input
                      id="supplierName"
                      value={supplierInfo.supplierName}
                      onChange={(e) => handleSupplierInfoChange('supplierName', e.target.value)}
                      placeholder="Your company/supplier name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationality">Nationality *</Label>
                    <Input
                      id="nationality"
                      value={supplierInfo.nationality}
                      onChange={(e) => handleSupplierInfoChange('nationality', e.target.value)}
                      placeholder="Your nationality"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="kofilName">Kofil Name</Label>
                    <Input
                      id="kofilName"
                      value={supplierInfo.kofilName}
                      onChange={(e) => handleSupplierInfoChange('kofilName', e.target.value)}
                      placeholder="Kofil sponsor name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="crNumber">C.R. Number *</Label>
                    <Input
                      id="crNumber"
                      value={supplierInfo.crNumber}
                      onChange={(e) => handleSupplierInfoChange('crNumber', e.target.value)}
                      placeholder="Commercial registration number"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="license">License</Label>
                    <Input
                      id="license"
                      value={supplierInfo.license}
                      onChange={(e) => handleSupplierInfoChange('license', e.target.value)}
                      placeholder="Business license number"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={supplierInfo.email}
                      onChange={(e) => handleSupplierInfoChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={supplierInfo.phone}
                      onChange={(e) => handleSupplierInfoChange('phone', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp">WhatsApp Number</Label>
                    <Input
                      id="whatsapp"
                      value={supplierInfo.whatsapp}
                      onChange={(e) => handleSupplierInfoChange('whatsapp', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={supplierInfo.address}
                    onChange={(e) => handleSupplierInfoChange('address', e.target.value)}
                    placeholder="Your complete business address"
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="profileDescription">Profile Description</Label>
                  <Textarea
                    id="profileDescription"
                    value={supplierInfo.profileDescription}
                    onChange={(e) => handleSupplierInfoChange('profileDescription', e.target.value)}
                    placeholder="Describe your business and services"
                    rows={4}
                  />
                </div>
              </div>
            )}

            {/* Step 2: Job Demands & Salary */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="jobDemands">Job Demands *</Label>
                    <Input
                      id="jobDemands"
                      value={Array.isArray(jobInfo.jobDemands) ? jobInfo.jobDemands.join(', ') : ''}
                      onChange={(e) => handleJobInfoChange('jobDemands', e.target.value)}
                      placeholder="e.g., Construction, IT, Healthcare (comma separated)"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="salaryOffered">Salary Offered *</Label>
                    <Input
                      id="salaryOffered"
                      value={jobInfo.salaryOffered}
                      onChange={(e) => handleJobInfoChange('salaryOffered', e.target.value)}
                      placeholder="e.g., 5000-8000"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <select
                      id="currency"
                      value={jobInfo.currency}
                      onChange={(e) => handleJobInfoChange('currency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="SAR">SAR</option>
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="paymentFrequency">Payment Frequency</Label>
                    <select
                      id="paymentFrequency"
                      value={jobInfo.paymentFrequency}
                      onChange={(e) => handleJobInfoChange('paymentFrequency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                    >
                      <option value="hourly">Hourly</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="workingHours">Working Hours</Label>
                    <Input
                      id="workingHours"
                      value={jobInfo.workingHours}
                      onChange={(e) => handleJobInfoChange('workingHours', e.target.value)}
                      placeholder="e.g., 8 AM - 5 PM"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="location">Work Location</Label>
                    <Input
                      id="location"
                      value={jobInfo.location}
                      onChange={(e) => handleJobInfoChange('location', e.target.value)}
                      placeholder="e.g., Riyadh, Jeddah"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="benefits">Benefits & Perks</Label>
                  <Input
                    id="benefits"
                    value={Array.isArray(jobInfo.benefits) ? jobInfo.benefits.join(', ') : ''}
                    onChange={(e) => handleJobInfoChange('benefits', e.target.value)}
                    placeholder="e.g., Health insurance, Transportation, Housing (comma separated)"
                  />
                </div>
              </div>
            )}

            {/* Step 3: Document Upload */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-green-800 mb-2">📄 Document Upload</h3>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Upload business documents (PDF format only)</li>
                    <li>• Maximum file size: 10MB per document</li>
                    <li>• Include C.R., license, and other relevant documents</li>
                  </ul>
                </div>

                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                  <input
                    type="file"
                    multiple
                    accept=".pdf"
                    onChange={(e) => handleDocumentUpload(e.target.files)}
                    className="hidden"
                    id="document-upload"
                  />
                  <label htmlFor="document-upload" className="cursor-pointer">
                    <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Upload Documents</h3>
                    <p className="text-gray-500">Click to select PDF files or drag and drop</p>
                  </label>
                </div>

                {documents.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-gray-900">Uploaded Documents:</h4>
                    {documents.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <FileText className="h-5 w-5 text-green-600" />
                          <span className="text-sm font-medium">{doc.name}</span>
                          <span className="text-xs text-gray-500">
                            ({(doc.size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeDocument(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
              >
                Previous
              </Button>

              {currentStep < 3 ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep(currentStep)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !validateStep(3)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? 'Creating Profile...' : 'Create Profile'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
