'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { toast } from 'sonner'
import { Users, Building, Wrench } from 'lucide-react'

export default function ProfileSetupPage() {
  const { user, userRole } = useAuth()
  const router = useRouter()
  
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    nationality: '',
    iqama: '',
    visa: '',
    passport: '',
    chamber: '',
    crNumber: '',
    license: '',
    companyInfo: '',
    workCategory: '',
    preferredLocations: '',
    salaryRange: '',
    whatsappLink: ''
  })
  
  const [loading, setLoading] = useState(false)

  // Handle redirect in useEffect to avoid render-time side effects
  useEffect(() => {
    if (!user) {
      router.push('/')
    }
  }, [user, router])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Mock profile creation - in real app, this would save to database
      console.log('Profile data:', formData)
      toast.success('Profile created successfully!')
      router.push('/feed')
    } catch (error) {
      toast.error('Failed to create profile')
    } finally {
      setLoading(false)
    }
  }

  const getRoleIcon = () => {
    switch (userRole) {
      case 'worker':
        return <Wrench className="h-6 w-6 text-blue-600" />
      case 'supplier':
        return <Users className="h-6 w-6 text-green-600" />
      case 'company':
        return <Building className="h-6 w-6 text-purple-600" />
      default:
        return <Users className="h-6 w-6 text-gray-600" />
    }
  }

  const getRoleColor = () => {
    switch (userRole) {
      case 'worker':
        return 'from-blue-50 to-blue-100'
      case 'supplier':
        return 'from-green-50 to-green-100'
      case 'company':
        return 'from-purple-50 to-purple-100'
      default:
        return 'from-gray-50 to-gray-100'
    }
  }

  // Show loading or nothing while redirecting
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Redirecting...</h2>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br ${getRoleColor()} p-4`}>
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-white rounded-full w-16 h-16 flex items-center justify-center shadow-md">
              {getRoleIcon()}
            </div>
            <CardTitle className="text-2xl font-bold">Complete Your Profile</CardTitle>
            <CardDescription>
              Set up your {userRole} profile to start connecting with others
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      placeholder="Enter your full name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      placeholder="+966 XXX XXX XXX"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nationality">Nationality</Label>
                  <Input
                    id="nationality"
                    placeholder="Your nationality"
                    value={formData.nationality}
                    onChange={(e) => handleInputChange('nationality', e.target.value)}
                  />
                </div>
              </div>

              {/* Worker-specific fields */}
              {userRole === 'worker' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Worker Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="iqama">IQAMA Number</Label>
                      <Input
                        id="iqama"
                        placeholder="IQAMA number"
                        value={formData.iqama}
                        onChange={(e) => handleInputChange('iqama', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="visa">Visa Status</Label>
                      <Input
                        id="visa"
                        placeholder="Visa status"
                        value={formData.visa}
                        onChange={(e) => handleInputChange('visa', e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="workCategory">Work Category</Label>
                    <Input
                      id="workCategory"
                      placeholder="e.g., Construction, Electrical, Plumbing"
                      value={formData.workCategory}
                      onChange={(e) => handleInputChange('workCategory', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="preferredLocations">Preferred Work Locations</Label>
                    <Input
                      id="preferredLocations"
                      placeholder="e.g., Riyadh, Jeddah, Dammam"
                      value={formData.preferredLocations}
                      onChange={(e) => handleInputChange('preferredLocations', e.target.value)}
                    />
                  </div>
                </div>
              )}

              {/* Supplier-specific fields */}
              {userRole === 'supplier' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Supplier Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="license">License Number</Label>
                      <Input
                        id="license"
                        placeholder="Business license number"
                        value={formData.license}
                        onChange={(e) => handleInputChange('license', e.target.value)}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="crNumber">C.R. Number</Label>
                      <Input
                        id="crNumber"
                        placeholder="Commercial registration number"
                        value={formData.crNumber}
                        onChange={(e) => handleInputChange('crNumber', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Company-specific fields */}
              {userRole === 'company' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Company Information</h3>
                  
                  <div className="space-y-2">
                    <Label htmlFor="companyInfo">Company Description</Label>
                    <Input
                      id="companyInfo"
                      placeholder="Brief description of your company"
                      value={formData.companyInfo}
                      onChange={(e) => handleInputChange('companyInfo', e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="crNumber">C.R. Number</Label>
                    <Input
                      id="crNumber"
                      placeholder="Commercial registration number"
                      value={formData.crNumber}
                      onChange={(e) => handleInputChange('crNumber', e.target.value)}
                    />
                  </div>
                </div>
              )}

              {/* WhatsApp Contact */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Contact Information</h3>
                
                <div className="space-y-2">
                  <Label htmlFor="whatsappLink">WhatsApp Number</Label>
                  <Input
                    id="whatsappLink"
                    placeholder="+966 XXX XXX XXX"
                    value={formData.whatsappLink}
                    onChange={(e) => handleInputChange('whatsappLink', e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="flex-1"
                  onClick={() => router.push('/feed')}
                >
                  Skip for Now
                </Button>
                <Button 
                  type="submit" 
                  className="flex-1"
                  disabled={loading}
                >
                  {loading ? 'Creating Profile...' : 'Complete Profile'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
