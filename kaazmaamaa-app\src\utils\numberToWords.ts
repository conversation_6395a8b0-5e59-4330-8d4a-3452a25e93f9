// Number to Words Converter for Invoice Amounts

const ones = [
  '', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine',
  'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen',
  'Seventeen', 'Eighteen', 'Nineteen'
];

const tens = [
  '', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'
];

const scales = [
  '', 'Thousand', 'Million', 'Billion', 'Trillion'
];

/**
 * Convert a number (0-999) to words
 * @param num - Number to convert
 * @returns String representation
 */
function convertHundreds(num: number): string {
  let result = '';
  
  if (num >= 100) {
    result += ones[Math.floor(num / 100)] + ' Hundred';
    num %= 100;
    if (num > 0) result += ' ';
  }
  
  if (num >= 20) {
    result += tens[Math.floor(num / 10)];
    num %= 10;
    if (num > 0) result += ' ';
  }
  
  if (num > 0) {
    result += ones[num];
  }
  
  return result;
}

/**
 * Convert a number to words
 * @param num - Number to convert
 * @returns String representation
 */
export function numberToWords(num: number): string {
  if (num === 0) return 'Zero';
  
  let result = '';
  let scaleIndex = 0;
  
  while (num > 0) {
    const chunk = num % 1000;
    if (chunk !== 0) {
      const chunkWords = convertHundreds(chunk);
      if (scaleIndex > 0) {
        result = chunkWords + ' ' + scales[scaleIndex] + (result ? ' ' + result : '');
      } else {
        result = chunkWords;
      }
    }
    num = Math.floor(num / 1000);
    scaleIndex++;
  }
  
  return result;
}

/**
 * Convert currency amount to words with currency name
 * @param amount - Amount to convert
 * @param currency - Currency code (SAR, USD, etc.)
 * @returns Formatted string with currency
 */
export function amountToWords(amount: number, currency: string = 'SAR'): string {
  const wholePart = Math.floor(amount);
  const decimalPart = Math.round((amount - wholePart) * 100);
  
  let result = numberToWords(wholePart);
  
  // Add currency name
  const currencyNames: { [key: string]: { singular: string; plural: string; subunit: string } } = {
    'SAR': { singular: 'Riyal', plural: 'Riyals', subunit: 'Halala' },
    'USD': { singular: 'Dollar', plural: 'Dollars', subunit: 'Cent' },
    'EUR': { singular: 'Euro', plural: 'Euros', subunit: 'Cent' },
    'AED': { singular: 'Dirham', plural: 'Dirhams', subunit: 'Fils' }
  };
  
  const currencyInfo = currencyNames[currency] || { singular: currency, plural: currency + 's', subunit: 'Cent' };
  
  if (wholePart === 1) {
    result += ' ' + currencyInfo.singular;
  } else {
    result += ' ' + currencyInfo.plural;
  }
  
  // Add decimal part if exists
  if (decimalPart > 0) {
    result += ' and ' + numberToWords(decimalPart);
    if (decimalPart === 1) {
      result += ' ' + currencyInfo.subunit;
    } else {
      result += ' ' + currencyInfo.subunit + 's';
    }
  }
  
  return result + ' Only';
}

/**
 * Format amount in words for invoice display
 * @param amount - Amount to convert
 * @param currency - Currency code
 * @returns Formatted string for invoice
 */
export function formatAmountInWords(amount: number, currency: string = 'SAR'): string {
  return amountToWords(amount, currency);
}

// Export utility functions
export const NumberUtils = {
  numberToWords,
  amountToWords,
  formatAmountInWords
}
