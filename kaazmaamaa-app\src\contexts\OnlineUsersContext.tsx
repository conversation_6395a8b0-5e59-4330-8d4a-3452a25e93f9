'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'

export interface OnlineUser {
  id: string
  name: string
  email: string
  role: 'worker' | 'supplier' | 'company'
  lastSeen: string
  isOnline: boolean
  avatar?: string
}

interface OnlineUsersContextType {
  onlineUsers: OnlineUser[]
  totalOnlineCount: number
  setUserOnline: (user: { id: string, name: string, email: string, role: 'worker' | 'supplier' | 'company' }) => void
  setUserOffline: (userId: string) => void
  isUserOnline: (userId: string) => boolean
}

const OnlineUsersContext = createContext<OnlineUsersContextType | undefined>(undefined)

// Mock online users data
const mockOnlineUsers: OnlineUser[] = [
  {
    id: 'user-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'worker',
    lastSeen: new Date().toISOString(),
    isOnline: true
  },
  {
    id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'company',
    lastSeen: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    isOnline: true
  },
  {
    id: 'user-3',
    name: 'Gulf Manpower',
    email: '<EMAIL>',
    role: 'supplier',
    lastSeen: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago
    isOnline: true
  },
  {
    id: 'user-4',
    name: 'Mohammed Hassan',
    email: '<EMAIL>',
    role: 'worker',
    lastSeen: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago
    isOnline: true
  },
  {
    id: 'user-5',
    name: 'Riyadh Builders',
    email: '<EMAIL>',
    role: 'company',
    lastSeen: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago
    isOnline: true
  },
  {
    id: 'user-6',
    name: 'Ali Construction',
    email: '<EMAIL>',
    role: 'worker',
    lastSeen: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
    isOnline: false
  }
]

export function OnlineUsersProvider({ children }: { children: ReactNode }) {
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([])

  useEffect(() => {
    // Load initial online users
    setOnlineUsers(mockOnlineUsers)

    // Simulate real-time updates
    const interval = setInterval(() => {
      setOnlineUsers(prevUsers => {
        return prevUsers.map(user => {
          // Randomly update online status for demo
          const shouldToggle = Math.random() < 0.1 // 10% chance to toggle
          if (shouldToggle) {
            return {
              ...user,
              isOnline: !user.isOnline,
              lastSeen: new Date().toISOString()
            }
          }
          return user
        })
      })
    }, 30000) // Update every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const setUserOnline = useCallback((user: { id: string, name: string, email: string, role: 'worker' | 'supplier' | 'company' }) => {
    setOnlineUsers(prevUsers => {
      const existingUserIndex = prevUsers.findIndex(u => u.id === user.id)
      const onlineUser: OnlineUser = {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        lastSeen: new Date().toISOString(),
        isOnline: true
      }

      if (existingUserIndex >= 0) {
        // Update existing user only if status changed
        const existingUser = prevUsers[existingUserIndex]
        if (existingUser.isOnline) {
          // User is already online, no need to update
          return prevUsers
        }
        const updatedUsers = [...prevUsers]
        updatedUsers[existingUserIndex] = onlineUser
        return updatedUsers
      } else {
        // Add new user
        return [...prevUsers, onlineUser]
      }
    })
  }, [])

  const setUserOffline = useCallback((userId: string) => {
    setOnlineUsers(prevUsers =>
      prevUsers.map(user =>
        user.id === userId
          ? { ...user, isOnline: false, lastSeen: new Date().toISOString() }
          : user
      )
    )
  }, [])

  const isUserOnline = useCallback((userId: string) => {
    const user = onlineUsers.find(u => u.id === userId)
    return user?.isOnline || false
  }, [onlineUsers])

  const totalOnlineCount = onlineUsers.filter(user => user.isOnline).length

  const value = {
    onlineUsers,
    totalOnlineCount,
    setUserOnline,
    setUserOffline,
    isUserOnline
  }

  return (
    <OnlineUsersContext.Provider value={value}>
      {children}
    </OnlineUsersContext.Provider>
  )
}

export function useOnlineUsers() {
  const context = useContext(OnlineUsersContext)
  if (context === undefined) {
    throw new Error('useOnlineUsers must be used within an OnlineUsersProvider')
  }
  return context
}

// Utility function to format last seen time
export function formatLastSeen(lastSeenString: string): string {
  const now = new Date()
  const lastSeen = new Date(lastSeenString)
  const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`

  return lastSeen.toLocaleDateString()
}
