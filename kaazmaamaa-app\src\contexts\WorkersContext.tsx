'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export interface WorkerDocument {
  id: string
  name: string
  type: 'absher_iqama' | 'muqeem' | 'medical' | 'insurance' | 'chamber' | 'ajeer' | 'experience' | 'fitness' | 'passport' | 'visa' | 'gosi' | 'other'
  url: string
  uploadedAt: string
  isValidated?: boolean
  fileSize?: number
  fileName?: string
}

export interface WorkerVideo {
  id: string
  title: string
  type: 'introduction' | 'skills' | 'portfolio'
  url: string
  thumbnail?: string
  duration?: number
  uploadedAt: string
}

export interface WorkExperience {
  id: string
  company: string
  position: string
  startDate: string
  endDate?: string
  description: string
  location: string
  isCurrentJob: boolean
}

export interface WorkerLifecycle {
  joiningDate?: string
  vacationStart?: string
  vacationEnd?: string
  leavingDate?: string
  status: 'available' | 'working' | 'on_vacation' | 'left' | 'seeking'
  currentEmployer?: string
  notes?: string
}

export interface WorkerProfile {
  id: string // Must be unique
  userId: string
  personalInfo: {
    workerName: string
    nationality: string
    kofilName: string
    kofilPhone: string
    email: string
    phone: string
    whatsapp: string
    address: string
    age?: number
    profileImage?: string
  }
  identificationInfo: {
    iqamaNumber: string
    passportNumber: string
    crNumber?: string // Optional for workers
  }
  professionalInfo: {
    workerCategory: string
    title: string
    specialty: string
    experience: number
    skills: string[]
    languages: string[]
    expectedSalaryPerHour: number
    currency: 'SAR' | 'USD' | 'EUR'
    salaryPayingDuration: 'daily' | 'weekly' | 'monthly' | 'hourly'
    availability: 'immediate' | 'within_week' | 'within_month' | 'negotiable'
  }
  documents: WorkerDocument[]
  videos: WorkerVideo[]
  workExperience: WorkExperience[]
  lifecycle: WorkerLifecycle
  preferences: {
    workType: 'full_time' | 'part_time' | 'contract' | 'freelance'
    sideLocationInterest: string[]
    companyNameInterest: string[]
    preferredLocations?: string[]
    willingToRelocate: boolean
    remoteWork: boolean
    neededDocuments: string[]
    otherRequirements: string
  }
  isLiveStreaming: boolean
  liveStreamUrl?: string
  createdAt: string
  updatedAt: string
  isVerified: boolean
  rating: number
  totalReviews: number
}

interface WorkersContextType {
  workers: WorkerProfile[]
  currentWorkerProfile?: WorkerProfile
  createWorkerProfile: (profile: Omit<WorkerProfile, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateWorkerProfile: (id: string, updates: Partial<WorkerProfile>) => void
  updateWorker: (profile: WorkerProfile) => void
  addDocument: (workerId: string, document: Omit<WorkerDocument, 'id' | 'uploadedAt'>) => void
  addVideo: (workerId: string, video: Omit<WorkerVideo, 'id' | 'uploadedAt'>) => void
  updateLifecycle: (workerId: string, lifecycle: Partial<WorkerLifecycle>) => void
  startLiveStream: (workerId: string, streamUrl: string) => void
  stopLiveStream: (workerId: string) => void
  getWorkersBySpecialty: (specialty: string) => WorkerProfile[]
  getWorkersByCategory: (category: string) => WorkerProfile[]
  getAvailableWorkers: () => WorkerProfile[]
  validateWorkerIdUniqueness: (workerId: string, excludeId?: string) => boolean
  validateIqamaDocument: (iqamaNumber: string) => Promise<boolean>
}

const WorkersContext = createContext<WorkersContextType | undefined>(undefined)

// Mock data for demonstration
const mockWorkers: WorkerProfile[] = [
  {
    id: 'worker-1', // Must be unique
    userId: 'user-1',
    personalInfo: {
      workerName: 'Ahmed Al-Rashid',
      nationality: 'Saudi Arabia',
      kofilName: 'Ahmed Mohammed Al-Rashid',
      kofilPhone: '+966501234567',
      email: '<EMAIL>',
      phone: '+966501234567',
      whatsapp: '+966501234567',
      address: 'King Fahd Road, Riyadh 12345, Saudi Arabia'
    },
    identificationInfo: {
      iqamaNumber: '2345678901',
      passportNumber: 'A12345678',
      crNumber: 'CR-1010987654'
    },
    professionalInfo: {
      workerCategory: 'Electrical Technician',
      title: 'Senior Electrical Technician',
      specialty: 'Electrical Work',
      experience: 5,
      skills: ['Electrical Installation', 'Maintenance', 'Troubleshooting', 'Safety Protocols'],
      languages: ['Arabic', 'English'],
      expectedSalaryPerHour: 85,
      currency: 'SAR',
      salaryPayingDuration: 'weekly',
      availability: 'immediate'
    },
    documents: [
      {
        id: 'doc-1',
        name: 'Iqama Document',
        type: 'iqama',
        url: '/documents/iqama.pdf',
        uploadedAt: new Date().toISOString(),
        isValidated: true
      },
      {
        id: 'doc-2',
        name: 'Visa Document',
        type: 'visa',
        url: '/documents/visa.pdf',
        uploadedAt: new Date().toISOString()
      },
      {
        id: 'doc-3',
        name: 'Passport Copy',
        type: 'passport',
        url: '/documents/passport.pdf',
        uploadedAt: new Date().toISOString()
      },
      {
        id: 'doc-4',
        name: 'Medical Certificate',
        type: 'medical',
        url: '/documents/medical.pdf',
        uploadedAt: new Date().toISOString()
      },
      {
        id: 'doc-5',
        name: 'Fitness Certificate',
        type: 'fitness',
        url: '/documents/fitness.pdf',
        uploadedAt: new Date().toISOString()
      }
    ],
    videos: [
      {
        id: 'video-1',
        title: 'Professional Introduction',
        type: 'introduction',
        url: '/videos/intro.mp4',
        duration: 120,
        uploadedAt: new Date().toISOString()
      }
    ],
    workExperience: [
      {
        id: 'exp-1',
        company: 'Saudi Electric Company',
        position: 'Electrical Technician',
        startDate: '2020-01-01',
        endDate: '2023-12-31',
        description: 'Responsible for electrical installations and maintenance',
        location: 'Riyadh',
        isCurrentJob: false
      }
    ],
    lifecycle: {
      status: 'seeking',
      notes: 'Looking for new opportunities in electrical field'
    },
    preferences: {
      workType: 'full_time',
      sideLocationInterest: ['Riyadh', 'Jeddah', 'Dammam'],
      companyNameInterest: ['Saudi Electric Company', 'ARAMCO', 'SABIC'],
      willingToRelocate: true,
      remoteWork: false,
      neededDocuments: ['Work Permit', 'Safety Certificate', 'Medical Clearance'],
      otherRequirements: 'Prefer companies with good safety standards and career growth opportunities'
    },
    isLiveStreaming: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isVerified: true,
    rating: 4.8,
    totalReviews: 15
  },
  {
    id: 'worker-2',
    userId: 'user-2',
    personalInfo: {
      workerName: 'Mohammed Al-Zahrani',
      nationality: 'Saudi Arabia',
      kofilName: 'Mohammed Abdullah Al-Zahrani',
      kofilPhone: '+966502345678',
      email: '<EMAIL>',
      phone: '+966502345678',
      whatsapp: '+966502345678',
      address: 'Prince Sultan Road, Jeddah 21455, Saudi Arabia'
    },
    identificationInfo: {
      iqamaNumber: '**********',
      passportNumber: 'B23456789',
      crNumber: 'CR-**********'
    },
    professionalInfo: {
      workerCategory: 'Plumber',
      title: 'Senior Plumber',
      specialty: 'Plumbing & Water Systems',
      experience: 8,
      skills: ['Pipe Installation', 'Water System Repair', 'Drainage', 'Emergency Repairs'],
      languages: ['Arabic', 'English', 'Urdu'],
      expectedSalaryPerHour: 75,
      currency: 'SAR',
      salaryPayingDuration: 'daily',
      availability: 'within_week'
    },
    documents: [
      {
        id: 'doc-6',
        name: 'Iqama Document',
        type: 'iqama',
        url: '/documents/iqama2.pdf',
        uploadedAt: new Date().toISOString(),
        isValidated: true
      },
      {
        id: 'doc-7',
        name: 'Plumbing Certificate',
        type: 'experience',
        url: '/documents/plumbing-cert.pdf',
        uploadedAt: new Date().toISOString()
      }
    ],
    videos: [
      {
        id: 'video-2',
        title: 'Plumbing Skills Demo',
        type: 'skills',
        url: '/videos/plumbing-demo.mp4',
        duration: 180,
        uploadedAt: new Date().toISOString()
      }
    ],
    workExperience: [
      {
        id: 'exp-2',
        company: 'Saudi Water Company',
        position: 'Senior Plumber',
        startDate: '2018-03-01',
        endDate: '2024-01-01',
        description: 'Specialized in water system installations and repairs',
        location: 'Jeddah',
        isCurrentJob: false
      }
    ],
    lifecycle: {
      status: 'available',
      notes: 'Available for immediate plumbing work'
    },
    preferences: {
      workType: 'contract',
      sideLocationInterest: ['Jeddah', 'Mecca', 'Taif'],
      companyNameInterest: ['Saudi Water Company', 'National Water Company'],
      willingToRelocate: false,
      remoteWork: false,
      neededDocuments: ['Work Permit', 'Plumbing License'],
      otherRequirements: 'Prefer emergency repair contracts'
    },
    isLiveStreaming: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isVerified: true,
    rating: 4.6,
    totalReviews: 23
  },
  {
    id: 'worker-3',
    userId: 'user-3',
    personalInfo: {
      workerName: 'Khalid Al-Mutairi',
      nationality: 'Saudi Arabia',
      kofilName: 'Khalid Saad Al-Mutairi',
      kofilPhone: '+966503456789',
      email: '<EMAIL>',
      phone: '+966503456789',
      whatsapp: '+966503456789',
      address: 'King Abdul Aziz Road, Dammam 31411, Saudi Arabia'
    },
    identificationInfo: {
      iqamaNumber: '**********',
      passportNumber: '*********',
      crNumber: 'CR-**********'
    },
    professionalInfo: {
      workerCategory: 'Carpenter',
      title: 'Master Carpenter',
      specialty: 'Woodworking & Furniture',
      experience: 12,
      skills: ['Custom Furniture', 'Cabinet Making', 'Wood Finishing', 'Restoration'],
      languages: ['Arabic', 'English'],
      expectedSalaryPerHour: 95,
      currency: 'SAR',
      salaryPayingDuration: 'weekly',
      availability: 'immediate'
    },
    documents: [
      {
        id: 'doc-8',
        name: 'Iqama Document',
        type: 'iqama',
        url: '/documents/iqama3.pdf',
        uploadedAt: new Date().toISOString(),
        isValidated: true
      },
      {
        id: 'doc-9',
        name: 'Carpentry Master Certificate',
        type: 'experience',
        url: '/documents/carpentry-master.pdf',
        uploadedAt: new Date().toISOString()
      }
    ],
    videos: [
      {
        id: 'video-3',
        title: 'Custom Furniture Portfolio',
        type: 'portfolio',
        url: '/videos/furniture-portfolio.mp4',
        duration: 300,
        uploadedAt: new Date().toISOString()
      }
    ],
    workExperience: [
      {
        id: 'exp-3',
        company: 'Royal Furniture Workshop',
        position: 'Master Carpenter',
        startDate: '2015-01-01',
        description: 'Creating custom furniture for luxury homes and offices',
        location: 'Dammam',
        isCurrentJob: true
      }
    ],
    lifecycle: {
      status: 'seeking',
      notes: 'Looking for high-end furniture projects'
    },
    preferences: {
      workType: 'freelance',
      sideLocationInterest: ['Dammam', 'Khobar', 'Dhahran'],
      companyNameInterest: ['Royal Furniture', 'Elite Woodworks'],
      willingToRelocate: true,
      remoteWork: false,
      neededDocuments: ['Work Permit', 'Carpentry License', 'Portfolio'],
      otherRequirements: 'Prefer luxury furniture projects'
    },
    isLiveStreaming: true,
    liveStreamUrl: 'https://stream.example.com/khalid-live',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isVerified: true,
    rating: 4.9,
    totalReviews: 31
  }
]

export function WorkersProvider({ children }: { children: ReactNode }) {
  const [workers, setWorkers] = useState<WorkerProfile[]>([])
  const [currentWorkerProfile, setCurrentWorkerProfile] = useState<WorkerProfile>()

  useEffect(() => {
    try {
      // Load workers from localStorage or use mock data
      const savedWorkers = localStorage.getItem('kaazmaamaa-workers')
      if (savedWorkers) {
        const parsedWorkers = JSON.parse(savedWorkers)
        // Validate the data structure
        if (Array.isArray(parsedWorkers) && parsedWorkers.length > 0) {
          setWorkers(parsedWorkers)
        } else {
          // Use mock data if saved data is invalid
          setWorkers(mockWorkers)
          localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))
        }
      } else {
        // Use mock data for first time
        setWorkers(mockWorkers)
        localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))
      }
    } catch (error) {
      console.error('Error loading workers:', error)
      // Fallback to mock data if there's an error
      setWorkers(mockWorkers)
      localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))
    }
  }, [])

  const saveToStorage = (updatedWorkers: WorkerProfile[]) => {
    localStorage.setItem('kaazmaamaa-workers', JSON.stringify(updatedWorkers))
  }

  const createWorkerProfile = (profile: Omit<WorkerProfile, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProfile: WorkerProfile = {
      ...profile,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const updatedWorkers = [...workers, newProfile]
    setWorkers(updatedWorkers)
    saveToStorage(updatedWorkers)
    setCurrentWorkerProfile(newProfile)
  }

  const updateWorkerProfile = (id: string, updates: Partial<WorkerProfile>) => {
    const updatedWorkers = workers.map(worker =>
      worker.id === id
        ? { ...worker, ...updates, updatedAt: new Date().toISOString() }
        : worker
    )
    setWorkers(updatedWorkers)
    saveToStorage(updatedWorkers)
  }

  const updateWorker = (profile: WorkerProfile) => {
    const existingWorkerIndex = workers.findIndex(w => w.userId === profile.userId)

    if (existingWorkerIndex >= 0) {
      // Update existing worker
      const updatedWorkers = [...workers]
      updatedWorkers[existingWorkerIndex] = {
        ...profile,
        updatedAt: new Date().toISOString()
      }
      setWorkers(updatedWorkers)
      saveToStorage(updatedWorkers)
    } else {
      // Create new worker if doesn't exist
      const newProfile: WorkerProfile = {
        ...profile,
        id: profile.id || Date.now().toString(),
        createdAt: profile.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      const updatedWorkers = [...workers, newProfile]
      setWorkers(updatedWorkers)
      saveToStorage(updatedWorkers)
    }
  }

  const addDocument = (workerId: string, document: Omit<WorkerDocument, 'id' | 'uploadedAt'>) => {
    const newDocument: WorkerDocument = {
      ...document,
      id: Date.now().toString(),
      uploadedAt: new Date().toISOString()
    }

    updateWorkerProfile(workerId, {
      documents: [...(workers.find(w => w.id === workerId)?.documents || []), newDocument]
    })
  }

  const addVideo = (workerId: string, video: Omit<WorkerVideo, 'id' | 'uploadedAt'>) => {
    const newVideo: WorkerVideo = {
      ...video,
      id: Date.now().toString(),
      uploadedAt: new Date().toISOString()
    }

    updateWorkerProfile(workerId, {
      videos: [...(workers.find(w => w.id === workerId)?.videos || []), newVideo]
    })
  }

  const updateLifecycle = (workerId: string, lifecycle: Partial<WorkerLifecycle>) => {
    const worker = workers.find(w => w.id === workerId)
    if (worker) {
      updateWorkerProfile(workerId, {
        lifecycle: { ...worker.lifecycle, ...lifecycle }
      })
    }
  }

  const startLiveStream = (workerId: string, streamUrl: string) => {
    updateWorkerProfile(workerId, {
      isLiveStreaming: true,
      liveStreamUrl: streamUrl
    })
  }

  const stopLiveStream = (workerId: string) => {
    updateWorkerProfile(workerId, {
      isLiveStreaming: false,
      liveStreamUrl: undefined
    })
  }

  const getWorkersBySpecialty = (specialty: string) => {
    return workers.filter(worker =>
      worker.professionalInfo.specialty.toLowerCase().includes(specialty.toLowerCase())
    )
  }

  const getAvailableWorkers = () => {
    return workers.filter(worker =>
      worker.lifecycle.status === 'available' || worker.lifecycle.status === 'seeking'
    )
  }

  const getWorkersByCategory = (category: string) => {
    return workers.filter(worker =>
      worker.professionalInfo.workerCategory.toLowerCase().includes(category.toLowerCase())
    )
  }

  const validateWorkerIdUniqueness = (workerId: string, excludeId?: string) => {
    return !workers.some(worker =>
      worker.id === workerId && worker.id !== excludeId
    )
  }

  const validateIqamaDocument = async (iqamaNumber: string): Promise<boolean> => {
    // Mock validation - in real app, this would call government API
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simple validation: check if it's 10 digits
        const isValid = /^\d{10}$/.test(iqamaNumber)
        resolve(isValid)
      }, 1000)
    })
  }

  const value = {
    workers,
    currentWorkerProfile,
    createWorkerProfile,
    updateWorkerProfile,
    updateWorker,
    addDocument,
    addVideo,
    updateLifecycle,
    startLiveStream,
    stopLiveStream,
    getWorkersBySpecialty,
    getWorkersByCategory,
    getAvailableWorkers,
    validateWorkerIdUniqueness,
    validateIqamaDocument
  }

  return (
    <WorkersContext.Provider value={value}>
      {children}
    </WorkersContext.Provider>
  )
}

export function useWorkers() {
  const context = useContext(WorkersContext)
  if (context === undefined) {
    throw new Error('useWorkers must be used within a WorkersProvider')
  }
  return context
}
