'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Upload, TestTube, CheckCircle, AlertCircle, Code, Play, Settings, Database } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep8() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<number[]>([])

  const markSectionComplete = (sectionId: number) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section ${sectionId} completed! ✅`)
    }
  }

  const sandboxSteps = [
    {
      id: 1,
      title: "Sandbox Environment Setup",
      description: "Configure connection to ZATCA sandbox",
      icon: <Settings className="h-5 w-5" />,
      details: [
        "Obtain sandbox credentials",
        "Configure API endpoints",
        "Set up authentication",
        "Verify connectivity"
      ]
    },
    {
      id: 2,
      title: "Test Data Preparation",
      description: "Prepare test invoices and scenarios",
      icon: <Database className="h-5 w-5" />,
      details: [
        "Create test invoice data",
        "Generate test certificates",
        "Prepare edge case scenarios",
        "Set up validation datasets"
      ]
    },
    {
      id: 3,
      title: "API Testing",
      description: "Test all ZATCA API endpoints",
      icon: <TestTube className="h-5 w-5" />,
      details: [
        "Test clearance APIs",
        "Test reporting APIs",
        "Test status endpoints",
        "Validate error handling"
      ]
    },
    {
      id: 4,
      title: "End-to-End Testing",
      description: "Complete invoice lifecycle testing",
      icon: <Play className="h-5 w-5" />,
      details: [
        "Full invoice submission flow",
        "QR code validation",
        "Signature verification",
        "Compliance validation"
      ]
    }
  ]

  const testScenarios = [
    {
      title: "Standard B2B Invoice",
      description: "Basic business-to-business invoice clearance",
      status: "ready",
      color: "blue"
    },
    {
      title: "B2C Simplified Invoice",
      description: "Consumer invoice reporting flow",
      status: "ready", 
      color: "green"
    },
    {
      title: "Credit Note Processing",
      description: "Credit note and debit note handling",
      status: "pending",
      color: "yellow"
    },
    {
      title: "VAT Group Scenarios",
      description: "Complex VAT group invoice testing",
      status: "pending",
      color: "purple"
    },
    {
      title: "Error Handling",
      description: "Invalid data and error response testing",
      status: "pending",
      color: "red"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-indigo-600">Step 8: Sandbox Testing</h1>
              <Badge className="bg-indigo-100 text-indigo-800">
                Testing
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Proceeding to Step 9...')
                  router.push('/invoice/step9')
                }}
                className="bg-indigo-600 hover:bg-indigo-700"
              >
                Next: Compliance Validation
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-indigo-50 border-indigo-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <Upload className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-indigo-800 mb-2">🧪 Sandbox Testing</h3>
              <p className="text-indigo-600 mb-4">
                Test your ZATCA e-invoicing implementation in the sandbox environment
              </p>
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-800">{completedSections.length}</div>
                  <div className="text-sm text-indigo-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{sandboxSteps.length - completedSections.length}</div>
                  <div className="text-sm text-blue-600">Remaining</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sandbox Environment Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Settings className="h-5 w-5 mr-2 text-indigo-600" />
              ZATCA Sandbox Environment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">Sandbox Base URL</h4>
                  <code className="text-sm bg-blue-100 p-2 rounded block">
                    https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal
                  </code>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">Authentication</h4>
                  <p className="text-green-600 text-sm">
                    Use sandbox credentials and certificates for testing
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h4 className="font-semibold text-yellow-800 mb-2">Rate Limits</h4>
                  <p className="text-yellow-600 text-sm">
                    Sandbox has relaxed rate limits for testing purposes
                  </p>
                </div>
                <div className="p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-semibold text-purple-800 mb-2">Test Data</h4>
                  <p className="text-purple-600 text-sm">
                    Use predefined test scenarios and sample data
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Scenarios */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TestTube className="h-5 w-5 mr-2 text-indigo-600" />
              Test Scenarios
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testScenarios.map((scenario, index) => (
                <div key={index} className={`p-4 bg-${scenario.color}-50 border border-${scenario.color}-200 rounded-lg`}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-semibold text-${scenario.color}-800`}>
                      {scenario.title}
                    </h4>
                    <Badge 
                      variant={scenario.status === 'ready' ? 'default' : 'secondary'}
                      className={scenario.status === 'ready' ? `bg-${scenario.color}-100 text-${scenario.color}-800` : ''}
                    >
                      {scenario.status}
                    </Badge>
                  </div>
                  <p className={`text-${scenario.color}-600 text-sm mb-3`}>
                    {scenario.description}
                  </p>
                  <Button 
                    size="sm" 
                    variant="outline"
                    className={`border-${scenario.color}-300 text-${scenario.color}-700 hover:bg-${scenario.color}-100`}
                    onClick={() => {
                      toast.info(`Starting ${scenario.title} test...`)
                    }}
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Run Test
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Implementation Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {sandboxSteps.map((step) => (
            <Card
              key={step.id}
              className={`transition-all hover:shadow-lg ${
                completedSections.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSections.includes(step.id) ? 'bg-green-100 text-green-600' : 'bg-indigo-100 text-indigo-600'
                  }`}>
                    {completedSections.includes(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">
                        {step.title}
                      </h3>
                      {completedSections.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-4">
                  {step.details.map((detail, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{detail}</span>
                    </li>
                  ))}
                </ul>

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast.info(`Learning about ${step.title}...`)
                    }}
                  >
                    <Code className="h-4 w-4 mr-2" />
                    View Guide
                  </Button>

                  {!completedSections.includes(step.id) && (
                    <Button
                      size="sm"
                      onClick={() => markSectionComplete(step.id)}
                      className="bg-indigo-600 hover:bg-indigo-700"
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testing Checklist */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
              Testing Checklist
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">API Testing</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Test invoice clearance API</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Test invoice reporting API</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Test status check API</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Test error scenarios</span>
                  </div>
                </div>
              </div>
              <div className="space-y-3">
                <h4 className="font-semibold text-gray-800">Validation Testing</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">QR code validation</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Digital signature verification</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">XML schema validation</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Business rule validation</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Common Issues */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-yellow-600" />
              Common Testing Issues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">Authentication Errors</h4>
                <p className="text-red-600 text-sm mb-2">
                  Invalid certificates or expired tokens
                </p>
                <ul className="text-sm text-red-700 space-y-1">
                  <li>• Verify certificate validity</li>
                  <li>• Check token expiration</li>
                  <li>• Validate certificate chain</li>
                </ul>
              </div>

              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">Validation Failures</h4>
                <p className="text-yellow-600 text-sm mb-2">
                  Schema or business rule violations
                </p>
                <ul className="text-sm text-yellow-700 space-y-1">
                  <li>• Check XML schema compliance</li>
                  <li>• Validate business rules</li>
                  <li>• Verify required fields</li>
                </ul>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Network Issues</h4>
                <p className="text-blue-600 text-sm mb-2">
                  Connectivity and timeout problems
                </p>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Check network connectivity</li>
                  <li>• Verify firewall settings</li>
                  <li>• Implement retry logic</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
