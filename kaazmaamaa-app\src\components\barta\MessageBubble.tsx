'use client'

import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { BartaMessage } from '@/contexts/BartaContext'
import { Users, Building, Wrench, MoreVertical, Reply, Heart, Copy } from 'lucide-react'
import { useState } from 'react'

interface MessageBubbleProps {
  message: BartaMessage
  isOwnMessage: boolean
  showSenderInfo?: boolean
  onReply?: (message: BartaMessage) => void
  onReact?: (messageId: string, reaction: string) => void
  onDelete?: (messageId: string) => void
}

export function MessageBubble({ 
  message, 
  isOwnMessage, 
  showSenderInfo = true,
  onReply,
  onReact,
  onDelete 
}: MessageBubbleProps) {
  const [showActions, setShowActions] = useState(false)

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'worker':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'supplier':
        return <Building className="h-4 w-4 text-green-600" />
      case 'company':
        return <Users className="h-4 w-4 text-purple-600" />
      default:
        return <Users className="h-4 w-4 text-gray-600" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'worker':
        return 'bg-blue-100 text-blue-800'
      case 'supplier':
        return 'bg-green-100 text-green-800'
      case 'company':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const handleCopyMessage = () => {
    navigator.clipboard.writeText(message.content)
    setShowActions(false)
  }

  return (
    <div
      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} group`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>
        {/* Sender Info */}
        {!isOwnMessage && showSenderInfo && (
          <div className="flex items-center space-x-2 mb-1">
            <Avatar className="h-6 w-6 bg-gray-100 flex items-center justify-center">
              {getRoleIcon(message.senderRole)}
            </Avatar>
            <span className="text-xs font-medium text-gray-700">{message.senderName}</span>
            <Badge variant="secondary" className={`text-xs ${getRoleColor(message.senderRole)}`}>
              {message.senderRole}
            </Badge>
          </div>
        )}
        
        {/* Message Bubble */}
        <div className="relative">
          <div
            className={`rounded-lg px-4 py-2 ${
              isOwnMessage
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-900'
            }`}
          >
            {/* Reply Reference */}
            {message.replyToMessage && (
              <div className={`text-xs mb-2 p-2 rounded border-l-2 ${
                isOwnMessage
                  ? 'bg-blue-500 border-blue-300 text-blue-100'
                  : 'bg-gray-50 border-gray-300 text-gray-600'
              }`}>
                <p className="font-medium">Replying to {message.replyToMessage.senderName}</p>
                <p className="truncate">{message.replyToMessage.content}</p>
              </div>
            )}
            
            {/* Message Content */}
            <div className="space-y-1">
              {message.type === 'text' && (
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              )}
              
              {message.type === 'emoji' && (
                <p className="text-2xl">{message.content}</p>
              )}
              
              {message.type === 'image' && (
                <div className="space-y-2">
                  {message.content && <p className="text-sm">{message.content}</p>}
                  {message.attachments?.map((attachment, index) => (
                    <img
                      key={index}
                      src={attachment.url}
                      alt={attachment.name}
                      className="max-w-full h-auto rounded"
                    />
                  ))}
                </div>
              )}
              
              {message.type === 'file' && (
                <div className="space-y-2">
                  {message.content && <p className="text-sm">{message.content}</p>}
                  {message.attachments?.map((attachment, index) => (
                    <div
                      key={index}
                      className={`flex items-center space-x-2 p-2 rounded ${
                        isOwnMessage ? 'bg-blue-500' : 'bg-gray-200'
                      }`}
                    >
                      <div className="flex-1">
                        <p className="text-sm font-medium">{attachment.name}</p>
                        {attachment.size && (
                          <p className="text-xs opacity-75">
                            {(attachment.size / 1024).toFixed(1)} KB
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            {/* Message Time & Status */}
            <div className={`flex items-center justify-between mt-1 text-xs ${
              isOwnMessage ? 'text-blue-100' : 'text-gray-500'
            }`}>
              <div className="flex items-center space-x-2">
                <span>{formatMessageTime(message.timestamp)}</span>
                {message.isEdited && <span className="italic">(edited)</span>}
              </div>
              {isOwnMessage && (
                <div className="flex items-center space-x-1">
                  {message.isDelivered && <span>✓</span>}
                  {message.isRead && <span>✓</span>}
                </div>
              )}
            </div>
          </div>

          {/* Message Reactions */}
          {message.reactions && Object.keys(message.reactions).length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {Object.entries(message.reactions).map(([emoji, reaction]) => (
                <button
                  key={emoji}
                  onClick={() => onReact?.(message.id, emoji)}
                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs border transition-colors ${
                    isOwnMessage
                      ? 'bg-blue-100 border-blue-200 hover:bg-blue-200'
                      : 'bg-gray-100 border-gray-200 hover:bg-gray-200'
                  }`}
                >
                  <span>{emoji}</span>
                  <span>{reaction.count}</span>
                </button>
              ))}
            </div>
          )}
          
          {/* Message Actions */}
          {showActions && (
            <div className={`absolute top-0 ${isOwnMessage ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} flex items-center space-x-1 bg-white shadow-lg rounded-lg p-1 border`}>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onReact?.(message.id, '❤️')}
                className="h-8 w-8 p-0"
                title="React with heart"
              >
                <Heart className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onReply?.(message)}
                className="h-8 w-8 p-0"
                title="Reply to message"
              >
                <Reply className="h-3 w-3" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyMessage}
                className="h-8 w-8 p-0"
                title="Copy message"
              >
                <Copy className="h-3 w-3" />
              </Button>
              
              {isOwnMessage && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete?.(message.id)}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                  title="Delete message"
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
