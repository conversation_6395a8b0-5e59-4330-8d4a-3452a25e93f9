"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PostsContext */ \"(app-pages-browser)/./src/contexts/PostsContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FeedPage() {\n    _s();\n    const { user, userRole, getUserProfile, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { posts, addPost, likePost, addComment, sharePost } = (0,_contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Create Post States\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postText, setPostText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFeeling, setSelectedFeeling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taggedUsers, setTaggedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postPrivacy, setPostPrivacy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('public');\n    // Comments States\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [commentTexts, setCommentTexts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAllComments, setShowAllComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Share States\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shareText, setShareText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Other States\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeStory, setActiveStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLocationPicker, setShowLocationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTagPicker, setShowTagPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (user && userRole) {\n                const profile = getUserProfile();\n                setUserProfile(profile);\n                console.log('User profile loaded:', profile);\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        userRole,\n        getUserProfile\n    ]);\n    // Redirect to home if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/');\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Helper function to get user's display name\n    const getUserDisplayName = ()=>{\n        if (!userProfile) return 'User';\n        if (userRole === 'worker') {\n            var _userProfile_personalInfo;\n            return ((_userProfile_personalInfo = userProfile.personalInfo) === null || _userProfile_personalInfo === void 0 ? void 0 : _userProfile_personalInfo.workerName) || 'Worker';\n        } else if (userRole === 'supplier') {\n            var _userProfile_personalInfo1;\n            return ((_userProfile_personalInfo1 = userProfile.personalInfo) === null || _userProfile_personalInfo1 === void 0 ? void 0 : _userProfile_personalInfo1.supplierName) || 'Supplier';\n        } else if (userRole === 'company') {\n            var _userProfile_companyInfo;\n            return ((_userProfile_companyInfo = userProfile.companyInfo) === null || _userProfile_companyInfo === void 0 ? void 0 : _userProfile_companyInfo.companyName) || 'Company';\n        }\n        return 'User';\n    };\n    // Helper function to get user's avatar/initial\n    const getUserInitial = ()=>{\n        const name = getUserDisplayName();\n        return name.charAt(0).toUpperCase();\n    };\n    // Helper function to get user's role emoji\n    const getUserRoleEmoji = ()=>{\n        switch(userRole){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Helper functions for Facebook-style features\n    const handleLikePost = (postId)=>{\n        likePost(postId);\n    };\n    const handleCommentSubmit = (postId)=>{\n        const commentText = commentTexts[postId];\n        if (!(commentText === null || commentText === void 0 ? void 0 : commentText.trim()) || !user) return;\n        addComment(postId, commentText, {\n            id: user.email,\n            name: getUserDisplayName(),\n            role: userRole || 'worker'\n        });\n        setCommentTexts((prev)=>({\n                ...prev,\n                [postId]: ''\n            }));\n    };\n    const handleSharePost = (postId)=>{\n        if (!shareText.trim()) {\n            sharePost(postId);\n        } else {\n            // Create a new post that shares the original\n            const originalPost = posts.find((p)=>p.id === postId);\n            if (originalPost) {\n                const newPost = {\n                    user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                    user_name: getUserDisplayName(),\n                    user_role: userRole || 'worker',\n                    content: shareText,\n                    post_type: 'shared',\n                    visibility: 'public'\n                };\n                addPost(newPost);\n            }\n        }\n        setShowShareModal(null);\n        setShareText('');\n    };\n    const toggleComments = (postId)=>{\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !prev[postId]\n            }));\n    };\n    const formatTimeAgo = (dateString)=>{\n        const now = new Date();\n        const postDate = new Date(dateString);\n        const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d\");\n        const diffInWeeks = Math.floor(diffInDays / 7);\n        return \"\".concat(diffInWeeks, \"w\");\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'worker':\n                return '#2563eb';\n            case 'supplier':\n                return '#059669';\n            case 'company':\n                return '#7c3aed';\n            default:\n                return '#6b7280';\n        }\n    };\n    const getRoleEmoji = (role)=>{\n        switch(role){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Show loading if still authenticating\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#f0f2f5'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '2rem',\n                            marginBottom: '1rem'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#65676b'\n                        },\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: '#f0f2f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: '#1877f2',\n                    padding: '0.75rem 1rem',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: 'white',\n                                        margin: 0\n                                    },\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'rgba(255,255,255,0.2)',\n                                        borderRadius: '20px',\n                                        padding: '0.5rem 1rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search KAAZMAAMAA...\",\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            color: 'white',\n                                            outline: 'none',\n                                            fontSize: '0.875rem'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.75rem',\n                                        color: 'white'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                borderRadius: '50%',\n                                                width: '2rem',\n                                                height: '2rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '0.875rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: getUserInitial()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const profilePath = userRole === 'worker' ? '/workers/profile' : userRole === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                router.push(profilePath);\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Go Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                await signOut();\n                                                router.push('/');\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 2fr 1fr',\n                    gap: '1rem',\n                    padding: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Quick Access\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/workers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDD27\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Workers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/suppliers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e8f5e8',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Suppliers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/companies\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#f3e8ff',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFE2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/barta\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#fff3cd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Barta Messenger\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem',\n                                        overflowX: 'auto',\n                                        paddingBottom: '0.5rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: '120px',\n                                                height: '200px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(45deg, #1877f2, #42a5f5)',\n                                                position: 'relative',\n                                                cursor: 'pointer',\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                justifyContent: 'flex-end',\n                                                padding: '1rem',\n                                                color: 'white'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: 'absolute',\n                                                        top: '1rem',\n                                                        left: '1rem',\n                                                        backgroundColor: 'rgba(255,255,255,0.3)',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '0.875rem',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"Create Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            'Ahmed Al-Rashid',\n                                            'Saudi Building Co.',\n                                            'Tech Solutions',\n                                            'Construction Pro'\n                                        ].map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    minWidth: '120px',\n                                                    height: '200px',\n                                                    borderRadius: '12px',\n                                                    background: \"linear-gradient(45deg, \".concat([\n                                                        '#ff6b6b',\n                                                        '#4ecdc4',\n                                                        '#45b7d1',\n                                                        '#96ceb4'\n                                                    ][index], \", \").concat([\n                                                        '#ffa726',\n                                                        '#26a69a',\n                                                        '#42a5f5',\n                                                        '#81c784'\n                                                    ][index], \")\"),\n                                                    position: 'relative',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    justifyContent: 'space-between',\n                                                    padding: '1rem',\n                                                    color: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: 'rgba(255,255,255,0.3)',\n                                                            borderRadius: '50%',\n                                                            width: '2.5rem',\n                                                            height: '2.5rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: '600'\n                                                        },\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.75rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    color: 'white',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: getUserInitial()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"What's on your mind, \".concat(getUserDisplayName(), \"?\"),\n                                                onClick: ()=>setShowCreatePost(true),\n                                                readOnly: true,\n                                                style: {\n                                                    flex: 1,\n                                                    backgroundColor: '#f0f2f5',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '0.75rem 1rem',\n                                                    outline: 'none',\n                                                    fontSize: '1rem',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-around',\n                                            paddingTop: '0.75rem',\n                                            borderTop: '1px solid #e4e6ea'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDCF7 Photo/Video\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDE0A Feeling/Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            posts.map((post)=>{\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '8px',\n                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(post.user_role),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: post.user_name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // Navigate to user's profile based on their role\n                                                                const profilePath = post.user_role === 'worker' ? '/workers/profile' : post.user_role === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                                router.push(\"\".concat(profilePath, \"?user=\").concat(encodeURIComponent(post.user_id)));\n                                                            },\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                padding: 0,\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21',\n                                                                cursor: 'pointer',\n                                                                fontSize: 'inherit',\n                                                                textAlign: 'left'\n                                                            },\n                                                            onMouseEnter: (e)=>e.target.style.textDecoration = 'underline',\n                                                            onMouseLeave: (e)=>e.target.style.textDecoration = 'none',\n                                                            children: post.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                formatTimeAgo(post.created_at),\n                                                                \" • \",\n                                                                getRoleEmoji(post.user_role),\n                                                                \" \",\n                                                                post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: '1rem',\n                                                paddingRight: '1rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#1c1e21',\n                                                    lineHeight: '1.5',\n                                                    margin: 0\n                                                },\n                                                children: post.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this),\n                                        post.media_urls && post.media_urls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f2f5',\n                                                height: '250px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                color: '#65676b',\n                                                fontSize: '3rem'\n                                            },\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '0.75rem 1rem'\n                                            },\n                                            children: [\n                                                (post.likes > 0 || post.comments > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '0.75rem'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem'\n                                                            },\n                                                            children: post.likes > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: '0.25rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: 'flex',\n                                                                            gap: '0.125rem'\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#1877f2',\n                                                                                    borderRadius: '50%',\n                                                                                    width: '1.25rem',\n                                                                                    height: '1.25rem',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    justifyContent: 'center',\n                                                                                    fontSize: '0.75rem'\n                                                                                },\n                                                                                children: \"\\uD83D\\uDC4D\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 501,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#e74c3c',\n                                                                                    borderRadius: '50%',\n                                                                                    width: '1.25rem',\n                                                                                    height: '1.25rem',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    justifyContent: 'center',\n                                                                                    fontSize: '0.75rem'\n                                                                                },\n                                                                                children: \"❤️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 502,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#f39c12',\n                                                                                    borderRadius: '50%',\n                                                                                    width: '1.25rem',\n                                                                                    height: '1.25rem',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    justifyContent: 'center',\n                                                                                    fontSize: '0.75rem'\n                                                                                },\n                                                                                children: \"\\uD83D\\uDE02\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 503,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 500,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#65676b',\n                                                                            fontSize: '0.875rem'\n                                                                        },\n                                                                        children: post.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 505,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '1rem',\n                                                                color: '#65676b',\n                                                                fontSize: '0.875rem'\n                                                            },\n                                                            children: [\n                                                                post.comments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>toggleComments(post.id),\n                                                                    style: {\n                                                                        background: 'none',\n                                                                        border: 'none',\n                                                                        color: '#65676b',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.875rem'\n                                                                    },\n                                                                    children: [\n                                                                        post.comments.length,\n                                                                        \" comments\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 511,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                post.shares > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        post.shares,\n                                                                        \" shares\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 45\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-around',\n                                                        paddingTop: '0.75rem',\n                                                        borderTop: '1px solid #e4e6ea'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleLikePost(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: post.liked_by_user ? '#1877f2' : '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDC4D\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Like\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>toggleComments(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Comment\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowShareModal(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCE4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 586,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Share\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: '1rem',\n                                                        borderTop: '1px solid #e4e6ea',\n                                                        paddingTop: '1rem'\n                                                    },\n                                                    children: [\n                                                        post.comments.slice(0, showAllComments[post.id] ? undefined : 3).map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    gap: '0.75rem',\n                                                                    marginBottom: '1rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundColor: getRoleColor(comment.user_role),\n                                                                            borderRadius: '50%',\n                                                                            width: '2rem',\n                                                                            height: '2rem',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            justifyContent: 'center',\n                                                                            color: 'white',\n                                                                            fontSize: '0.875rem',\n                                                                            fontWeight: 'bold',\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        children: comment.user_name.charAt(0).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 596,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            flex: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#f0f2f5',\n                                                                                    borderRadius: '1rem',\n                                                                                    padding: '0.75rem 1rem'\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            fontWeight: '600',\n                                                                                            fontSize: '0.875rem',\n                                                                                            marginBottom: '0.25rem'\n                                                                                        },\n                                                                                        children: comment.user_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 613,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            fontSize: '0.875rem',\n                                                                                            lineHeight: '1.4'\n                                                                                        },\n                                                                                        children: comment.content\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 616,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 612,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    display: 'flex',\n                                                                                    gap: '1rem',\n                                                                                    marginTop: '0.25rem',\n                                                                                    paddingLeft: '1rem'\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        style: {\n                                                                                            background: 'none',\n                                                                                            border: 'none',\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem',\n                                                                                            cursor: 'pointer',\n                                                                                            fontWeight: '600'\n                                                                                        },\n                                                                                        children: \"Like\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 621,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        style: {\n                                                                                            background: 'none',\n                                                                                            border: 'none',\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem',\n                                                                                            cursor: 'pointer',\n                                                                                            fontWeight: '600'\n                                                                                        },\n                                                                                        children: \"Reply\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 624,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem'\n                                                                                        },\n                                                                                        children: formatTimeAgo(comment.created_at)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 627,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 611,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, comment.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        post.comments.length > 3 && !showAllComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAllComments((prev)=>({\n                                                                        ...prev,\n                                                                        [post.id]: true\n                                                                    })),\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                color: '#65676b',\n                                                                fontSize: '0.875rem',\n                                                                cursor: 'pointer',\n                                                                marginBottom: '1rem'\n                                                            },\n                                                            children: [\n                                                                \"View \",\n                                                                post.comments.length - 3,\n                                                                \" more comments\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 637,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '0.75rem',\n                                                                alignItems: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        backgroundColor: getRoleColor(userRole || 'worker'),\n                                                                        borderRadius: '50%',\n                                                                        width: '2rem',\n                                                                        height: '2rem',\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        justifyContent: 'center',\n                                                                        color: 'white',\n                                                                        fontSize: '0.875rem',\n                                                                        fontWeight: 'bold',\n                                                                        flexShrink: 0\n                                                                    },\n                                                                    children: getUserInitial()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        flex: 1,\n                                                                        position: 'relative'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"Write a comment...\",\n                                                                            value: commentTexts[post.id] || '',\n                                                                            onChange: (e)=>setCommentTexts((prev)=>({\n                                                                                        ...prev,\n                                                                                        [post.id]: e.target.value\n                                                                                    })),\n                                                                            onKeyPress: (e)=>{\n                                                                                if (e.key === 'Enter') {\n                                                                                    handleCommentSubmit(post.id);\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                width: '100%',\n                                                                                backgroundColor: '#f0f2f5',\n                                                                                border: 'none',\n                                                                                borderRadius: '1rem',\n                                                                                padding: '0.75rem 1rem',\n                                                                                outline: 'none',\n                                                                                fontSize: '0.875rem'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 663,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        commentTexts[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleCommentSubmit(post.id),\n                                                                            style: {\n                                                                                position: 'absolute',\n                                                                                right: '0.5rem',\n                                                                                top: '50%',\n                                                                                transform: 'translateY(-50%)',\n                                                                                background: 'none',\n                                                                                border: 'none',\n                                                                                color: '#1877f2',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.875rem',\n                                                                                fontWeight: '600'\n                                                                            },\n                                                                            children: \"Post\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 662,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this);\n                            }),\n                            posts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\n                                    padding: '3rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 714,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#1c1e21',\n                                            marginBottom: '0.5rem'\n                                        },\n                                        children: \"No posts yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Be the first to share something with your professional network!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Online Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#7c3aed',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 732,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Modern Tech Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#2563eb',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 738,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Fatima Al-Zahra\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 744,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 737,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#059669',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 748,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Khalid Construction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 747,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '2rem',\n                                    paddingTop: '1rem',\n                                    borderTop: '1px solid #e4e6ea'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    style: {\n                                        color: '#1877f2',\n                                        textDecoration: 'none',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            showCreatePost && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreatePost(false),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 797,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.75rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        color: 'white',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: getUserInitial()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                color: '#65676b',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                getUserRoleEmoji(),\n                                                \" \",\n                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                \" • \\uD83C\\uDF0D Public\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 832,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: postText,\n                                onChange: (e)=>setPostText(e.target.value),\n                                placeholder: \"What's on your mind?\",\n                                style: {\n                                    width: '100%',\n                                    minHeight: '120px',\n                                    border: 'none',\n                                    outline: 'none',\n                                    fontSize: '1.5rem',\n                                    color: '#1c1e21',\n                                    resize: 'none',\n                                    fontFamily: 'inherit'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 842,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 13\n                        }, this),\n                        selectedFeeling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '20px',\n                                    padding: '0.5rem 1rem',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: selectedFeeling\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedFeeling(''),\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            color: '#65676b'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 862,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 861,\n                            columnNumber: 15\n                        }, this),\n                        selectedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'relative',\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '8px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 896,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: [\n                                            \"Image Preview (\",\n                                            selectedImages.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 897,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedImages([]),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '0.5rem',\n                                            right: '0.5rem',\n                                            backgroundColor: 'rgba(0,0,0,0.5)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '2rem',\n                                            height: '2rem',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderTop: '1px solid #e4e6ea'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: \"Add to your post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 922,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '0.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImages([\n                                                            'image'\n                                                        ]),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Photo/Video\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedFeeling('😊 feeling happy'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Feeling/Activity\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Tag People\",\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Check In\",\n                                                    children: \"\\uD83D\\uDCCD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 923,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (!postText.trim()) return;\n                                        // Create the post using the posts context\n                                        const newPost = {\n                                            user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                                            user_name: getUserDisplayName(),\n                                            user_role: userRole || 'worker',\n                                            content: postText,\n                                            media_urls: selectedImages.length > 0 ? selectedImages : undefined,\n                                            post_type: selectedImages.length > 0 ? 'image' : 'text',\n                                            visibility: 'public'\n                                        };\n                                        addPost(newPost);\n                                        // Reset form\n                                        setPostText('');\n                                        setSelectedFeeling('');\n                                        setSelectedImages([]);\n                                        setSelectedLocation('');\n                                        setTaggedUsers([]);\n                                        setShowCreatePost(false);\n                                        // Show success message\n                                        alert('Post created successfully!');\n                                    },\n                                    disabled: !postText.trim(),\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                                        color: postText.trim() ? 'white' : '#bcc0c4',\n                                        border: 'none',\n                                        borderRadius: '6px',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '600',\n                                        cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                                    },\n                                    children: \"Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 920,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 778,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 766,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"mG1is2bfnznMFmJKz7POH9TiagY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZmVlZC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFa0Q7QUFDRjtBQUNFO0FBQ1A7QUFFNUIsU0FBU007O0lBQ3RCLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxRQUFRLEVBQUVDLGNBQWMsRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQUUsR0FBR1IsOERBQU9BO0lBQ3BFLE1BQU0sRUFBRVMsS0FBSyxFQUFFQyxPQUFPLEVBQUVDLFFBQVEsRUFBRUMsVUFBVSxFQUFFQyxTQUFTLEVBQUUsR0FBR1osZ0VBQVFBO0lBQ3BFLE1BQU1hLFNBQVNaLDBEQUFTQTtJQUV4QixxQkFBcUI7SUFDckIsTUFBTSxDQUFDYSxnQkFBZ0JDLGtCQUFrQixHQUFHbEIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDbUIsVUFBVUMsWUFBWSxHQUFHcEIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDcUIsaUJBQWlCQyxtQkFBbUIsR0FBR3RCLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ3VCLGdCQUFnQkMsa0JBQWtCLEdBQUd4QiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ2pFLE1BQU0sQ0FBQ3lCLGtCQUFrQkMsb0JBQW9CLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUMyQixhQUFhQyxlQUFlLEdBQUc1QiwrQ0FBUUEsQ0FBVyxFQUFFO0lBQzNELE1BQU0sQ0FBQzZCLGFBQWFDLGVBQWUsR0FBRzlCLCtDQUFRQSxDQUFtQztJQUVqRixrQkFBa0I7SUFDbEIsTUFBTSxDQUFDK0IsY0FBY0MsZ0JBQWdCLEdBQUdoQywrQ0FBUUEsQ0FBMkIsQ0FBQztJQUM1RSxNQUFNLENBQUNpQyxjQUFjQyxnQkFBZ0IsR0FBR2xDLCtDQUFRQSxDQUEwQixDQUFDO0lBQzNFLE1BQU0sQ0FBQ21DLGlCQUFpQkMsbUJBQW1CLEdBQUdwQywrQ0FBUUEsQ0FBMkIsQ0FBQztJQUVsRixlQUFlO0lBQ2YsTUFBTSxDQUFDcUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3RDLCtDQUFRQSxDQUFnQjtJQUNwRSxNQUFNLENBQUN1QyxXQUFXQyxhQUFhLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUUzQyxlQUFlO0lBQ2YsTUFBTSxDQUFDeUMsYUFBYUMsZUFBZSxHQUFHMUMsK0NBQVFBLENBQU07SUFDcEQsTUFBTSxDQUFDMkMsYUFBYUMsZUFBZSxHQUFHNUMsK0NBQVFBLENBQWdCO0lBQzlELE1BQU0sQ0FBQzZDLGlCQUFpQkMsbUJBQW1CLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUN2RCxNQUFNLENBQUMrQyxvQkFBb0JDLHNCQUFzQixHQUFHaEQsK0NBQVFBLENBQUM7SUFDN0QsTUFBTSxDQUFDaUQsZUFBZUMsaUJBQWlCLEdBQUdsRCwrQ0FBUUEsQ0FBQztJQUVuRCx3QkFBd0I7SUFDeEJDLGdEQUFTQTs4QkFBQztZQUNSLElBQUlLLFFBQVFDLFVBQVU7Z0JBQ3BCLE1BQU00QyxVQUFVM0M7Z0JBQ2hCa0MsZUFBZVM7Z0JBQ2ZDLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JGO1lBQ3RDO1FBQ0Y7NkJBQUc7UUFBQzdDO1FBQU1DO1FBQVVDO0tBQWU7SUFFbkMsd0NBQXdDO0lBQ3hDUCxnREFBU0E7OEJBQUM7WUFDUixJQUFJLENBQUNRLFdBQVcsQ0FBQ0gsTUFBTTtnQkFDckJVLE9BQU9zQyxJQUFJLENBQUM7WUFDZDtRQUNGOzZCQUFHO1FBQUNoRDtRQUFNRztRQUFTTztLQUFPO0lBRTFCLDZDQUE2QztJQUM3QyxNQUFNdUMscUJBQXFCO1FBQ3pCLElBQUksQ0FBQ2QsYUFBYSxPQUFPO1FBRXpCLElBQUlsQyxhQUFhLFVBQVU7Z0JBQ2xCa0M7WUFBUCxPQUFPQSxFQUFBQSw0QkFBQUEsWUFBWWUsWUFBWSxjQUF4QmYsZ0RBQUFBLDBCQUEwQmdCLFVBQVUsS0FBSTtRQUNqRCxPQUFPLElBQUlsRCxhQUFhLFlBQVk7Z0JBQzNCa0M7WUFBUCxPQUFPQSxFQUFBQSw2QkFBQUEsWUFBWWUsWUFBWSxjQUF4QmYsaURBQUFBLDJCQUEwQmlCLFlBQVksS0FBSTtRQUNuRCxPQUFPLElBQUluRCxhQUFhLFdBQVc7Z0JBQzFCa0M7WUFBUCxPQUFPQSxFQUFBQSwyQkFBQUEsWUFBWWtCLFdBQVcsY0FBdkJsQiwrQ0FBQUEseUJBQXlCbUIsV0FBVyxLQUFJO1FBQ2pEO1FBQ0EsT0FBTztJQUNUO0lBRUEsK0NBQStDO0lBQy9DLE1BQU1DLGlCQUFpQjtRQUNyQixNQUFNQyxPQUFPUDtRQUNiLE9BQU9PLEtBQUtDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXO0lBQ25DO0lBRUEsMkNBQTJDO0lBQzNDLE1BQU1DLG1CQUFtQjtRQUN2QixPQUFRMUQ7WUFDTixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBWSxPQUFPO1lBQ3hCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTTJELGlCQUFpQixDQUFDQztRQUN0QnRELFNBQVNzRDtJQUNYO0lBRUEsTUFBTUMsc0JBQXNCLENBQUNEO1FBQzNCLE1BQU1FLGNBQWNwQyxZQUFZLENBQUNrQyxPQUFPO1FBQ3hDLElBQUksRUFBQ0Usd0JBQUFBLGtDQUFBQSxZQUFhQyxJQUFJLE9BQU0sQ0FBQ2hFLE1BQU07UUFFbkNRLFdBQVdxRCxRQUFRRSxhQUFhO1lBQzlCRSxJQUFJakUsS0FBS2tFLEtBQUs7WUFDZFYsTUFBTVA7WUFDTmtCLE1BQU1sRSxZQUFZO1FBQ3BCO1FBRUEyQixnQkFBZ0J3QyxDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ1AsT0FBTyxFQUFFO1lBQUc7SUFDbkQ7SUFFQSxNQUFNUSxrQkFBa0IsQ0FBQ1I7UUFDdkIsSUFBSSxDQUFDNUIsVUFBVStCLElBQUksSUFBSTtZQUNyQnZELFVBQVVvRDtRQUNaLE9BQU87WUFDTCw2Q0FBNkM7WUFDN0MsTUFBTVMsZUFBZWpFLE1BQU1rRSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVQLEVBQUUsS0FBS0o7WUFDOUMsSUFBSVMsY0FBYztnQkFDaEIsTUFBTUcsVUFBVTtvQkFDZEMsU0FBUzFFLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWtFLEtBQUssS0FBSTtvQkFDeEJTLFdBQVcxQjtvQkFDWDJCLFdBQVkzRSxZQUFZO29CQUN4QjRFLFNBQVM1QztvQkFDVDZDLFdBQVc7b0JBQ1hDLFlBQVk7Z0JBQ2Q7Z0JBQ0F6RSxRQUFRbUU7WUFDVjtRQUNGO1FBQ0F6QyxrQkFBa0I7UUFDbEJFLGFBQWE7SUFDZjtJQUVBLE1BQU04QyxpQkFBaUIsQ0FBQ25CO1FBQ3RCbkMsZ0JBQWdCMEMsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNQLE9BQU8sRUFBRSxDQUFDTyxJQUFJLENBQUNQLE9BQU87WUFBQztJQUM5RDtJQUVBLE1BQU1vQixnQkFBZ0IsQ0FBQ0M7UUFDckIsTUFBTUMsTUFBTSxJQUFJQztRQUNoQixNQUFNQyxXQUFXLElBQUlELEtBQUtGO1FBQzFCLE1BQU1JLGdCQUFnQkMsS0FBS0MsS0FBSyxDQUFDLENBQUNMLElBQUlNLE9BQU8sS0FBS0osU0FBU0ksT0FBTyxFQUFDLElBQU0sUUFBTyxFQUFDO1FBRWpGLElBQUlILGdCQUFnQixHQUFHLE9BQU87UUFDOUIsSUFBSUEsZ0JBQWdCLElBQUksT0FBTyxHQUFpQixPQUFkQSxlQUFjO1FBRWhELE1BQU1JLGNBQWNILEtBQUtDLEtBQUssQ0FBQ0YsZ0JBQWdCO1FBQy9DLElBQUlJLGNBQWMsSUFBSSxPQUFPLEdBQWUsT0FBWkEsYUFBWTtRQUU1QyxNQUFNQyxhQUFhSixLQUFLQyxLQUFLLENBQUNFLGNBQWM7UUFDNUMsSUFBSUMsYUFBYSxHQUFHLE9BQU8sR0FBYyxPQUFYQSxZQUFXO1FBRXpDLE1BQU1DLGNBQWNMLEtBQUtDLEtBQUssQ0FBQ0csYUFBYTtRQUM1QyxPQUFPLEdBQWUsT0FBWkMsYUFBWTtJQUN4QjtJQUVBLE1BQU1DLGVBQWUsQ0FBQzFCO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQVksT0FBTztZQUN4QixLQUFLO2dCQUFXLE9BQU87WUFDdkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTTJCLGVBQWUsQ0FBQzNCO1FBQ3BCLE9BQVFBO1lBQ04sS0FBSztnQkFBVSxPQUFPO1lBQ3RCLEtBQUs7Z0JBQVksT0FBTztZQUN4QixLQUFLO2dCQUFXLE9BQU87WUFDdkI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsdUNBQXVDO0lBQ3ZDLElBQUloRSxTQUFTO1FBQ1gscUJBQ0UsOERBQUM0RjtZQUFJQyxPQUFPO2dCQUFFQyxXQUFXO2dCQUFTQyxTQUFTO2dCQUFRQyxZQUFZO2dCQUFVQyxnQkFBZ0I7Z0JBQVVDLGlCQUFpQjtZQUFVO3NCQUM1SCw0RUFBQ047Z0JBQUlDLE9BQU87b0JBQUVNLFdBQVc7Z0JBQVM7O2tDQUNoQyw4REFBQ1A7d0JBQUlDLE9BQU87NEJBQUVPLFVBQVU7NEJBQVFDLGNBQWM7d0JBQU87a0NBQUc7Ozs7OztrQ0FDeEQsOERBQUNUO3dCQUFJQyxPQUFPOzRCQUFFUyxPQUFPO3dCQUFVO2tDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztJQUkxQztJQUVBLGdDQUFnQztJQUNoQyxJQUFJLENBQUN6RyxNQUFNO1FBQ1QsT0FBTztJQUNUO0lBQ0EscUJBQ0UsOERBQUMrRjtRQUFJQyxPQUFPO1lBQUVDLFdBQVc7WUFBU0ksaUJBQWlCO1FBQVU7OzBCQUUzRCw4REFBQ047Z0JBQUlDLE9BQU87b0JBQUVLLGlCQUFpQjtvQkFBV0ssU0FBUztvQkFBZ0JDLFdBQVc7Z0JBQTRCOzBCQUN4Ryw0RUFBQ1o7b0JBQUlDLE9BQU87d0JBQUVZLFVBQVU7d0JBQVVDLFFBQVE7d0JBQVVYLFNBQVM7d0JBQVFFLGdCQUFnQjt3QkFBaUJELFlBQVk7b0JBQVM7O3NDQUN6SCw4REFBQ0o7NEJBQUlDLE9BQU87Z0NBQUVFLFNBQVM7Z0NBQVFDLFlBQVk7Z0NBQVVXLEtBQUs7NEJBQU87OzhDQUMvRCw4REFBQ0M7b0NBQUdmLE9BQU87d0NBQUVPLFVBQVU7d0NBQVVTLFlBQVk7d0NBQVFQLE9BQU87d0NBQVNJLFFBQVE7b0NBQUU7OENBQUc7Ozs7Ozs4Q0FHbEYsOERBQUNkO29DQUFJQyxPQUFPO3dDQUFFSyxpQkFBaUI7d0NBQXlCWSxjQUFjO3dDQUFRUCxTQUFTO29DQUFjOzhDQUNuRyw0RUFBQ1E7d0NBQ0NDLE1BQUs7d0NBQ0xDLGFBQVk7d0NBQ1pwQixPQUFPOzRDQUNMSyxpQkFBaUI7NENBQ2pCZ0IsUUFBUTs0Q0FDUlosT0FBTzs0Q0FDUGEsU0FBUzs0Q0FDVGYsVUFBVTt3Q0FDWjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS04sOERBQUNSOzRCQUFJQyxPQUFPO2dDQUFFRSxTQUFTO2dDQUFRWSxLQUFLO2dDQUFRWCxZQUFZOzRCQUFTOzs4Q0FDL0QsOERBQUNKO29DQUFJQyxPQUFPO3dDQUFFRSxTQUFTO3dDQUFRQyxZQUFZO3dDQUFVVyxLQUFLO3dDQUFXTCxPQUFPO29DQUFROztzREFDbEYsOERBQUNWOzRDQUFJQyxPQUFPO2dEQUNWSyxpQkFBaUI7Z0RBQ2pCWSxjQUFjO2dEQUNkTSxPQUFPO2dEQUNQQyxRQUFRO2dEQUNSdEIsU0FBUztnREFDVEMsWUFBWTtnREFDWkMsZ0JBQWdCO2dEQUNoQkcsVUFBVTtnREFDVlMsWUFBWTs0Q0FDZDtzREFDR3pEOzs7Ozs7c0RBRUgsOERBQUNrRTs0Q0FBS3pCLE9BQU87Z0RBQUVPLFVBQVU7Z0RBQVlTLFlBQVk7NENBQU07c0RBQ3BEL0Q7Ozs7Ozs7Ozs7Ozs4Q0FJTCw4REFBQzhDO29DQUFJQyxPQUFPO3dDQUFFRSxTQUFTO3dDQUFRWSxLQUFLO29DQUFVOztzREFDNUMsOERBQUNZOzRDQUNDQyxTQUFTO2dEQUNQLE1BQU1DLGNBQWMzSCxhQUFhLFdBQVcscUJBQzFCQSxhQUFhLGFBQWEsdUJBQzFCO2dEQUNsQlMsT0FBT3NDLElBQUksQ0FBQzRFOzRDQUNkOzRDQUNBNUIsT0FBTztnREFDTEssaUJBQWlCO2dEQUNqQkksT0FBTztnREFDUFksUUFBUTtnREFDUlgsU0FBUztnREFDVE8sY0FBYztnREFDZFYsVUFBVTtnREFDVlMsWUFBWTtnREFDWmEsUUFBUTs0Q0FDVjtzREFDRDs7Ozs7O3NEQUlELDhEQUFDSDs0Q0FDQ0MsU0FBUztnREFDUCxNQUFNdkg7Z0RBQ05NLE9BQU9zQyxJQUFJLENBQUM7NENBQ2Q7NENBQ0FnRCxPQUFPO2dEQUNMSyxpQkFBaUI7Z0RBQ2pCSSxPQUFPO2dEQUNQWSxRQUFRO2dEQUNSWCxTQUFTO2dEQUNUTyxjQUFjO2dEQUNkVixVQUFVO2dEQUNWUyxZQUFZO2dEQUNaYSxRQUFROzRDQUNWO3NEQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFTVCw4REFBQzlCO2dCQUFJQyxPQUFPO29CQUFFWSxVQUFVO29CQUFVQyxRQUFRO29CQUFVWCxTQUFTO29CQUFRNEIscUJBQXFCO29CQUFlaEIsS0FBSztvQkFBUUosU0FBUztnQkFBTzs7a0NBR3BJLDhEQUFDWDt3QkFBSUMsT0FBTzs0QkFBRUssaUJBQWlCOzRCQUFTWSxjQUFjOzRCQUFPUCxTQUFTOzRCQUFRYyxRQUFROzRCQUFlYixXQUFXO3dCQUE0Qjs7MENBQzFJLDhEQUFDb0I7Z0NBQUcvQixPQUFPO29DQUFFTyxVQUFVO29DQUFZUyxZQUFZO29DQUFPUCxPQUFPO29DQUFXRCxjQUFjO2dDQUFPOzBDQUFHOzs7Ozs7MENBR2hHLDhEQUFDVDtnQ0FBSUMsT0FBTztvQ0FBRUUsU0FBUztvQ0FBUThCLGVBQWU7b0NBQVVsQixLQUFLO2dDQUFVOztrREFDckUsOERBQUNtQjt3Q0FBRUMsTUFBSzt3Q0FBV2xDLE9BQU87NENBQUVFLFNBQVM7NENBQVFDLFlBQVk7NENBQVVXLEtBQUs7NENBQVdxQixnQkFBZ0I7NENBQVExQixPQUFPOzRDQUFXQyxTQUFTOzRDQUFVTyxjQUFjO3dDQUFNOzswREFDbEssOERBQUNsQjtnREFBSUMsT0FBTztvREFBRUssaUJBQWlCO29EQUFXWSxjQUFjO29EQUFPTSxPQUFPO29EQUFVQyxRQUFRO29EQUFVdEIsU0FBUztvREFBUUMsWUFBWTtvREFBVUMsZ0JBQWdCO2dEQUFTOzBEQUFHOzs7Ozs7MERBR3JLLDhEQUFDcUI7Z0RBQUt6QixPQUFPO29EQUFFZ0IsWUFBWTtnREFBTTswREFBRzs7Ozs7Ozs7Ozs7O2tEQUd0Qyw4REFBQ2lCO3dDQUFFQyxNQUFLO3dDQUFhbEMsT0FBTzs0Q0FBRUUsU0FBUzs0Q0FBUUMsWUFBWTs0Q0FBVVcsS0FBSzs0Q0FBV3FCLGdCQUFnQjs0Q0FBUTFCLE9BQU87NENBQVdDLFNBQVM7NENBQVVPLGNBQWM7d0NBQU07OzBEQUNwSyw4REFBQ2xCO2dEQUFJQyxPQUFPO29EQUFFSyxpQkFBaUI7b0RBQVdZLGNBQWM7b0RBQU9NLE9BQU87b0RBQVVDLFFBQVE7b0RBQVV0QixTQUFTO29EQUFRQyxZQUFZO29EQUFVQyxnQkFBZ0I7Z0RBQVM7MERBQUc7Ozs7OzswREFHckssOERBQUNxQjtnREFBS3pCLE9BQU87b0RBQUVnQixZQUFZO2dEQUFNOzBEQUFHOzs7Ozs7Ozs7Ozs7a0RBR3RDLDhEQUFDaUI7d0NBQUVDLE1BQUs7d0NBQWFsQyxPQUFPOzRDQUFFRSxTQUFTOzRDQUFRQyxZQUFZOzRDQUFVVyxLQUFLOzRDQUFXcUIsZ0JBQWdCOzRDQUFRMUIsT0FBTzs0Q0FBV0MsU0FBUzs0Q0FBVU8sY0FBYzt3Q0FBTTs7MERBQ3BLLDhEQUFDbEI7Z0RBQUlDLE9BQU87b0RBQUVLLGlCQUFpQjtvREFBV1ksY0FBYztvREFBT00sT0FBTztvREFBVUMsUUFBUTtvREFBVXRCLFNBQVM7b0RBQVFDLFlBQVk7b0RBQVVDLGdCQUFnQjtnREFBUzswREFBRzs7Ozs7OzBEQUdySyw4REFBQ3FCO2dEQUFLekIsT0FBTztvREFBRWdCLFlBQVk7Z0RBQU07MERBQUc7Ozs7Ozs7Ozs7OztrREFHdEMsOERBQUNpQjt3Q0FBRUMsTUFBSzt3Q0FBU2xDLE9BQU87NENBQUVFLFNBQVM7NENBQVFDLFlBQVk7NENBQVVXLEtBQUs7NENBQVdxQixnQkFBZ0I7NENBQVExQixPQUFPOzRDQUFXQyxTQUFTOzRDQUFVTyxjQUFjO3dDQUFNOzswREFDaEssOERBQUNsQjtnREFBSUMsT0FBTztvREFBRUssaUJBQWlCO29EQUFXWSxjQUFjO29EQUFPTSxPQUFPO29EQUFVQyxRQUFRO29EQUFVdEIsU0FBUztvREFBUUMsWUFBWTtvREFBVUMsZ0JBQWdCO2dEQUFTOzBEQUFHOzs7Ozs7MERBR3JLLDhEQUFDcUI7Z0RBQUt6QixPQUFPO29EQUFFZ0IsWUFBWTtnREFBTTswREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU0xQyw4REFBQ2pCO3dCQUFJQyxPQUFPOzRCQUFFRSxTQUFTOzRCQUFROEIsZUFBZTs0QkFBVWxCLEtBQUs7d0JBQU87OzBDQUdsRSw4REFBQ2Y7Z0NBQUlDLE9BQU87b0NBQUVLLGlCQUFpQjtvQ0FBU1ksY0FBYztvQ0FBT1AsU0FBUztvQ0FBUUMsV0FBVztnQ0FBNEI7MENBQ25ILDRFQUFDWjtvQ0FBSUMsT0FBTzt3Q0FBRUUsU0FBUzt3Q0FBUVksS0FBSzt3Q0FBV3NCLFdBQVc7d0NBQVFDLGVBQWU7b0NBQVM7O3NEQUV4Riw4REFBQ3RDOzRDQUFJQyxPQUFPO2dEQUNWc0MsVUFBVTtnREFDVmQsUUFBUTtnREFDUlAsY0FBYztnREFDZHNCLFlBQVk7Z0RBQ1pDLFVBQVU7Z0RBQ1ZYLFFBQVE7Z0RBQ1IzQixTQUFTO2dEQUNUOEIsZUFBZTtnREFDZjVCLGdCQUFnQjtnREFDaEJNLFNBQVM7Z0RBQ1RELE9BQU87NENBQ1Q7OzhEQUNFLDhEQUFDVjtvREFBSUMsT0FBTzt3REFDVndDLFVBQVU7d0RBQ1ZDLEtBQUs7d0RBQ0xDLE1BQU07d0RBQ05yQyxpQkFBaUI7d0RBQ2pCWSxjQUFjO3dEQUNkTSxPQUFPO3dEQUNQQyxRQUFRO3dEQUNSdEIsU0FBUzt3REFDVEMsWUFBWTt3REFDWkMsZ0JBQWdCO3dEQUNoQkcsVUFBVTtvREFDWjs4REFBRzs7Ozs7OzhEQUdILDhEQUFDUjtvREFBSUMsT0FBTzt3REFBRU8sVUFBVTt3REFBWVMsWUFBWTtvREFBTTs4REFBRzs7Ozs7Ozs7Ozs7O3dDQUkxRDs0Q0FBQzs0Q0FBbUI7NENBQXNCOzRDQUFrQjt5Q0FBbUIsQ0FBQzJCLEdBQUcsQ0FBQyxDQUFDbkYsTUFBTW9GLHNCQUMxRiw4REFBQzdDO2dEQUFnQkMsT0FBTztvREFDdEJzQyxVQUFVO29EQUNWZCxRQUFRO29EQUNSUCxjQUFjO29EQUNkc0IsWUFBWSwwQkFBa0YsT0FBeEQ7d0RBQUM7d0RBQVc7d0RBQVc7d0RBQVc7cURBQVUsQ0FBQ0ssTUFBTSxFQUFDLE1BQXdELE9BQXBEO3dEQUFDO3dEQUFXO3dEQUFXO3dEQUFXO3FEQUFVLENBQUNBLE1BQU0sRUFBQztvREFDbEpKLFVBQVU7b0RBQ1ZYLFFBQVE7b0RBQ1IzQixTQUFTO29EQUNUOEIsZUFBZTtvREFDZjVCLGdCQUFnQjtvREFDaEJNLFNBQVM7b0RBQ1RELE9BQU87Z0RBQ1Q7O2tFQUNFLDhEQUFDVjt3REFBSUMsT0FBTzs0REFDVkssaUJBQWlCOzREQUNqQlksY0FBYzs0REFDZE0sT0FBTzs0REFDUEMsUUFBUTs0REFDUnRCLFNBQVM7NERBQ1RDLFlBQVk7NERBQ1pDLGdCQUFnQjs0REFDaEJHLFVBQVU7NERBQ1ZTLFlBQVk7d0RBQ2Q7a0VBQ0d4RCxLQUFLQyxNQUFNLENBQUM7Ozs7OztrRUFFZiw4REFBQ3NDO3dEQUFJQyxPQUFPOzREQUFFTyxVQUFVOzREQUFZUyxZQUFZO3dEQUFNO2tFQUFJeEQ7Ozs7Ozs7K0NBMUJsRG9GOzs7Ozs7Ozs7Ozs7Ozs7OzBDQWlDaEIsOERBQUM3QztnQ0FBSUMsT0FBTztvQ0FBRUssaUJBQWlCO29DQUFTWSxjQUFjO29DQUFPUCxTQUFTO29DQUFRQyxXQUFXO2dDQUE0Qjs7a0RBQ25ILDhEQUFDWjt3Q0FBSUMsT0FBTzs0Q0FBRUUsU0FBUzs0Q0FBUVksS0FBSzs0Q0FBV04sY0FBYzt3Q0FBTzs7MERBQ2xFLDhEQUFDVDtnREFBSUMsT0FBTztvREFDVkssaUJBQWlCcEcsYUFBYSxXQUFXLFlBQVlBLGFBQWEsYUFBYSxZQUFZO29EQUMzRmdILGNBQWM7b0RBQ2RNLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1J0QixTQUFTO29EQUNUQyxZQUFZO29EQUNaQyxnQkFBZ0I7b0RBQ2hCSyxPQUFPO29EQUNQTyxZQUFZO2dEQUNkOzBEQUNHekQ7Ozs7OzswREFFSCw4REFBQzJEO2dEQUNDQyxNQUFLO2dEQUNMQyxhQUFhLHdCQUE2QyxPQUFyQm5FLHNCQUFxQjtnREFDMUQwRSxTQUFTLElBQU0vRyxrQkFBa0I7Z0RBQ2pDaUksUUFBUTtnREFDUjdDLE9BQU87b0RBQ0w4QyxNQUFNO29EQUNOekMsaUJBQWlCO29EQUNqQmdCLFFBQVE7b0RBQ1JKLGNBQWM7b0RBQ2RQLFNBQVM7b0RBQ1RZLFNBQVM7b0RBQ1RmLFVBQVU7b0RBQ1ZzQixRQUFRO2dEQUNWOzs7Ozs7Ozs7Ozs7a0RBR0osOERBQUM5Qjt3Q0FBSUMsT0FBTzs0Q0FBRUUsU0FBUzs0Q0FBUUUsZ0JBQWdCOzRDQUFnQjJDLFlBQVk7NENBQVdDLFdBQVc7d0NBQW9COzswREFDbkgsOERBQUN0QjtnREFDQ0MsU0FBUyxJQUFNL0csa0JBQWtCO2dEQUNqQ29GLE9BQU87b0RBQUVFLFNBQVM7b0RBQVFDLFlBQVk7b0RBQVVXLEtBQUs7b0RBQVVULGlCQUFpQjtvREFBZWdCLFFBQVE7b0RBQVFYLFNBQVM7b0RBQWVPLGNBQWM7b0RBQU9ZLFFBQVE7b0RBQVdwQixPQUFPO2dEQUFVOzBEQUNqTTs7Ozs7OzBEQUdELDhEQUFDaUI7Z0RBQ0NDLFNBQVMsSUFBTS9HLGtCQUFrQjtnREFDakNvRixPQUFPO29EQUFFRSxTQUFTO29EQUFRQyxZQUFZO29EQUFVVyxLQUFLO29EQUFVVCxpQkFBaUI7b0RBQWVnQixRQUFRO29EQUFRWCxTQUFTO29EQUFlTyxjQUFjO29EQUFPWSxRQUFRO29EQUFXcEIsT0FBTztnREFBVTswREFDak07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs0QkFPSnBHLE1BQU1zSSxHQUFHLENBQUMsQ0FBQ007Z0NBRVYscUJBQ0UsOERBQUNsRDtvQ0FBa0JDLE9BQU87d0NBQUVLLGlCQUFpQjt3Q0FBU1ksY0FBYzt3Q0FBT04sV0FBVztvQ0FBNEI7O3NEQUVoSCw4REFBQ1o7NENBQUlDLE9BQU87Z0RBQUVVLFNBQVM7Z0RBQVFSLFNBQVM7Z0RBQVFDLFlBQVk7Z0RBQVVXLEtBQUs7NENBQVU7OzhEQUNuRiw4REFBQ2Y7b0RBQUlDLE9BQU87d0RBQ1ZLLGlCQUFpQlIsYUFBYW9ELEtBQUtyRSxTQUFTO3dEQUM1Q3FDLGNBQWM7d0RBQ2RNLE9BQU87d0RBQ1BDLFFBQVE7d0RBQ1J0QixTQUFTO3dEQUNUQyxZQUFZO3dEQUNaQyxnQkFBZ0I7d0RBQ2hCSyxPQUFPO3dEQUNQTyxZQUFZO29EQUNkOzhEQUNHaUMsS0FBS3RFLFNBQVMsQ0FBQ2xCLE1BQU0sQ0FBQyxHQUFHQyxXQUFXOzs7Ozs7OERBRXZDLDhEQUFDcUM7O3NFQUNDLDhEQUFDMkI7NERBQ0NDLFNBQVM7Z0VBQ1AsaURBQWlEO2dFQUNqRCxNQUFNQyxjQUFjcUIsS0FBS3JFLFNBQVMsS0FBSyxXQUFXLHFCQUNoQ3FFLEtBQUtyRSxTQUFTLEtBQUssYUFBYSx1QkFDaEM7Z0VBQ2xCbEUsT0FBT3NDLElBQUksQ0FBQyxHQUF1QmtHLE9BQXBCdEIsYUFBWSxVQUF5QyxPQUFqQ3NCLG1CQUFtQkQsS0FBS3ZFLE9BQU87NERBQ3BFOzREQUNBc0IsT0FBTztnRUFDTHVDLFlBQVk7Z0VBQ1psQixRQUFRO2dFQUNSWCxTQUFTO2dFQUNUTSxZQUFZO2dFQUNaUCxPQUFPO2dFQUNQb0IsUUFBUTtnRUFDUnRCLFVBQVU7Z0VBQ1ZELFdBQVc7NERBQ2I7NERBQ0E2QyxjQUFjLENBQUNDLElBQU0sRUFBR0MsTUFBTSxDQUFpQnJELEtBQUssQ0FBQ21DLGNBQWMsR0FBRzs0REFDdEVtQixjQUFjLENBQUNGLElBQU0sRUFBR0MsTUFBTSxDQUFpQnJELEtBQUssQ0FBQ21DLGNBQWMsR0FBRztzRUFFckVjLEtBQUt0RSxTQUFTOzs7Ozs7c0VBRWpCLDhEQUFDb0I7NERBQUlDLE9BQU87Z0VBQUVPLFVBQVU7Z0VBQVVFLE9BQU87NERBQVU7O2dFQUNoRHhCLGNBQWNnRSxLQUFLTSxVQUFVO2dFQUFFO2dFQUFJekQsYUFBYW1ELEtBQUtyRSxTQUFTO2dFQUFFO2dFQUFFcUUsS0FBS3JFLFNBQVMsQ0FBQ25CLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUt1RixLQUFLckUsU0FBUyxDQUFDNEUsS0FBSyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQU12SSw4REFBQ3pEOzRDQUFJQyxPQUFPO2dEQUFFeUQsYUFBYTtnREFBUUMsY0FBYztnREFBUWxELGNBQWM7NENBQU87c0RBQzVFLDRFQUFDaEM7Z0RBQUV3QixPQUFPO29EQUFFUyxPQUFPO29EQUFXa0QsWUFBWTtvREFBTzlDLFFBQVE7Z0RBQUU7MERBQ3hEb0MsS0FBS3BFLE9BQU87Ozs7Ozs7Ozs7O3dDQUtoQm9FLEtBQUtXLFVBQVUsSUFBSVgsS0FBS1csVUFBVSxDQUFDQyxNQUFNLEdBQUcsbUJBQzNDLDhEQUFDOUQ7NENBQUlDLE9BQU87Z0RBQUVLLGlCQUFpQjtnREFBV21CLFFBQVE7Z0RBQVN0QixTQUFTO2dEQUFRQyxZQUFZO2dEQUFVQyxnQkFBZ0I7Z0RBQVVLLE9BQU87Z0RBQVdGLFVBQVU7NENBQU87c0RBQUc7Ozs7OztzREFNcEssOERBQUNSOzRDQUFJQyxPQUFPO2dEQUFFVSxTQUFTOzRDQUFlOztnREFDbEN1QyxDQUFBQSxLQUFLYSxLQUFLLEdBQUcsS0FBS2IsS0FBS2MsUUFBUSxHQUFHLG9CQUNsQyw4REFBQ2hFO29EQUFJQyxPQUFPO3dEQUFFRSxTQUFTO3dEQUFRRSxnQkFBZ0I7d0RBQWlCRCxZQUFZO3dEQUFVSyxjQUFjO29EQUFVOztzRUFDNUcsOERBQUNUOzREQUFJQyxPQUFPO2dFQUFFRSxTQUFTO2dFQUFRQyxZQUFZO2dFQUFVVyxLQUFLOzREQUFTO3NFQUNoRW1DLEtBQUthLEtBQUssR0FBRyxtQkFDWiw4REFBQy9EO2dFQUFJQyxPQUFPO29FQUFFRSxTQUFTO29FQUFRQyxZQUFZO29FQUFVVyxLQUFLO2dFQUFVOztrRkFDbEUsOERBQUNmO3dFQUFJQyxPQUFPOzRFQUFFRSxTQUFTOzRFQUFRWSxLQUFLO3dFQUFXOzswRkFDN0MsOERBQUNmO2dGQUFJQyxPQUFPO29GQUFFSyxpQkFBaUI7b0ZBQVdZLGNBQWM7b0ZBQU9NLE9BQU87b0ZBQVdDLFFBQVE7b0ZBQVd0QixTQUFTO29GQUFRQyxZQUFZO29GQUFVQyxnQkFBZ0I7b0ZBQVVHLFVBQVU7Z0ZBQVU7MEZBQUc7Ozs7OzswRkFDNUwsOERBQUNSO2dGQUFJQyxPQUFPO29GQUFFSyxpQkFBaUI7b0ZBQVdZLGNBQWM7b0ZBQU9NLE9BQU87b0ZBQVdDLFFBQVE7b0ZBQVd0QixTQUFTO29GQUFRQyxZQUFZO29GQUFVQyxnQkFBZ0I7b0ZBQVVHLFVBQVU7Z0ZBQVU7MEZBQUc7Ozs7OzswRkFDNUwsOERBQUNSO2dGQUFJQyxPQUFPO29GQUFFSyxpQkFBaUI7b0ZBQVdZLGNBQWM7b0ZBQU9NLE9BQU87b0ZBQVdDLFFBQVE7b0ZBQVd0QixTQUFTO29GQUFRQyxZQUFZO29GQUFVQyxnQkFBZ0I7b0ZBQVVHLFVBQVU7Z0ZBQVU7MEZBQUc7Ozs7Ozs7Ozs7OztrRkFFOUwsOERBQUNrQjt3RUFBS3pCLE9BQU87NEVBQUVTLE9BQU87NEVBQVdGLFVBQVU7d0VBQVc7a0ZBQUkwQyxLQUFLYSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OztzRUFJMUUsOERBQUMvRDs0REFBSUMsT0FBTztnRUFBRUUsU0FBUztnRUFBUVksS0FBSztnRUFBUUwsT0FBTztnRUFBV0YsVUFBVTs0REFBVzs7Z0VBQ2hGMEMsS0FBS2MsUUFBUSxDQUFDRixNQUFNLEdBQUcsbUJBQ3RCLDhEQUFDbkM7b0VBQ0NDLFNBQVMsSUFBTTNDLGVBQWVpRSxLQUFLaEYsRUFBRTtvRUFDckMrQixPQUFPO3dFQUFFdUMsWUFBWTt3RUFBUWxCLFFBQVE7d0VBQVFaLE9BQU87d0VBQVdvQixRQUFRO3dFQUFXdEIsVUFBVTtvRUFBVzs7d0VBRXRHMEMsS0FBS2MsUUFBUSxDQUFDRixNQUFNO3dFQUFDOzs7Ozs7O2dFQUd6QlosS0FBS2UsTUFBTSxHQUFHLG1CQUFLLDhEQUFDdkM7O3dFQUFNd0IsS0FBS2UsTUFBTTt3RUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNN0MsOERBQUNqRTtvREFBSUMsT0FBTzt3REFBRUUsU0FBUzt3REFBUUUsZ0JBQWdCO3dEQUFnQjJDLFlBQVk7d0RBQVdDLFdBQVc7b0RBQW9COztzRUFDbkgsOERBQUN0Qjs0REFDQ0MsU0FBUyxJQUFNL0QsZUFBZXFGLEtBQUtoRixFQUFFOzREQUNyQytCLE9BQU87Z0VBQ0xFLFNBQVM7Z0VBQ1RDLFlBQVk7Z0VBQ1pXLEtBQUs7Z0VBQ0xULGlCQUFpQjtnRUFDakJnQixRQUFRO2dFQUNSWCxTQUFTO2dFQUNUTyxjQUFjO2dFQUNkWSxRQUFRO2dFQUNScEIsT0FBT3dDLEtBQUtnQixhQUFhLEdBQUcsWUFBWTtnRUFDeENqRCxZQUFZO2dFQUNaOEIsTUFBTTtnRUFDTjFDLGdCQUFnQjs0REFDbEI7NERBQ0ErQyxjQUFjLENBQUNDLElBQU1BLEVBQUVjLGFBQWEsQ0FBQ2xFLEtBQUssQ0FBQ0ssZUFBZSxHQUFHOzREQUM3RGlELGNBQWMsQ0FBQ0YsSUFBTUEsRUFBRWMsYUFBYSxDQUFDbEUsS0FBSyxDQUFDSyxlQUFlLEdBQUc7OzhFQUU3RCw4REFBQ29CO29FQUFLekIsT0FBTzt3RUFBRU8sVUFBVTtvRUFBVTs4RUFBRzs7Ozs7O2dFQUFTOzs7Ozs7O3NFQUVqRCw4REFBQ21COzREQUNDQyxTQUFTLElBQU0zQyxlQUFlaUUsS0FBS2hGLEVBQUU7NERBQ3JDK0IsT0FBTztnRUFDTEUsU0FBUztnRUFDVEMsWUFBWTtnRUFDWlcsS0FBSztnRUFDTFQsaUJBQWlCO2dFQUNqQmdCLFFBQVE7Z0VBQ1JYLFNBQVM7Z0VBQ1RPLGNBQWM7Z0VBQ2RZLFFBQVE7Z0VBQ1JwQixPQUFPO2dFQUNQTyxZQUFZO2dFQUNaOEIsTUFBTTtnRUFDTjFDLGdCQUFnQjs0REFDbEI7NERBQ0ErQyxjQUFjLENBQUNDLElBQU1BLEVBQUVjLGFBQWEsQ0FBQ2xFLEtBQUssQ0FBQ0ssZUFBZSxHQUFHOzREQUM3RGlELGNBQWMsQ0FBQ0YsSUFBTUEsRUFBRWMsYUFBYSxDQUFDbEUsS0FBSyxDQUFDSyxlQUFlLEdBQUc7OzhFQUU3RCw4REFBQ29CO29FQUFLekIsT0FBTzt3RUFBRU8sVUFBVTtvRUFBVTs4RUFBRzs7Ozs7O2dFQUFTOzs7Ozs7O3NFQUVqRCw4REFBQ21COzREQUNDQyxTQUFTLElBQU0zRixrQkFBa0JpSCxLQUFLaEYsRUFBRTs0REFDeEMrQixPQUFPO2dFQUNMRSxTQUFTO2dFQUNUQyxZQUFZO2dFQUNaVyxLQUFLO2dFQUNMVCxpQkFBaUI7Z0VBQ2pCZ0IsUUFBUTtnRUFDUlgsU0FBUztnRUFDVE8sY0FBYztnRUFDZFksUUFBUTtnRUFDUnBCLE9BQU87Z0VBQ1BPLFlBQVk7Z0VBQ1o4QixNQUFNO2dFQUNOMUMsZ0JBQWdCOzREQUNsQjs0REFDQStDLGNBQWMsQ0FBQ0MsSUFBTUEsRUFBRWMsYUFBYSxDQUFDbEUsS0FBSyxDQUFDSyxlQUFlLEdBQUc7NERBQzdEaUQsY0FBYyxDQUFDRixJQUFNQSxFQUFFYyxhQUFhLENBQUNsRSxLQUFLLENBQUNLLGVBQWUsR0FBRzs7OEVBRTdELDhEQUFDb0I7b0VBQUt6QixPQUFPO3dFQUFFTyxVQUFVO29FQUFVOzhFQUFHOzs7Ozs7Z0VBQVM7Ozs7Ozs7Ozs7Ozs7Z0RBS2xEOUUsWUFBWSxDQUFDd0gsS0FBS2hGLEVBQUUsQ0FBQyxrQkFDcEIsOERBQUM4QjtvREFBSUMsT0FBTzt3REFBRW1FLFdBQVc7d0RBQVFuQixXQUFXO3dEQUFxQkQsWUFBWTtvREFBTzs7d0RBRWpGRSxLQUFLYyxRQUFRLENBQUNQLEtBQUssQ0FBQyxHQUFHM0gsZUFBZSxDQUFDb0gsS0FBS2hGLEVBQUUsQ0FBQyxHQUFHbUcsWUFBWSxHQUFHekIsR0FBRyxDQUFDLENBQUMwQix3QkFDckUsOERBQUN0RTtnRUFBcUJDLE9BQU87b0VBQUVFLFNBQVM7b0VBQVFZLEtBQUs7b0VBQVdOLGNBQWM7Z0VBQU87O2tGQUNuRiw4REFBQ1Q7d0VBQUlDLE9BQU87NEVBQ1ZLLGlCQUFpQlIsYUFBYXdFLFFBQVF6RixTQUFTOzRFQUMvQ3FDLGNBQWM7NEVBQ2RNLE9BQU87NEVBQ1BDLFFBQVE7NEVBQ1J0QixTQUFTOzRFQUNUQyxZQUFZOzRFQUNaQyxnQkFBZ0I7NEVBQ2hCSyxPQUFPOzRFQUNQRixVQUFVOzRFQUNWUyxZQUFZOzRFQUNac0QsWUFBWTt3RUFDZDtrRkFDR0QsUUFBUTFGLFNBQVMsQ0FBQ2xCLE1BQU0sQ0FBQyxHQUFHQyxXQUFXOzs7Ozs7a0ZBRTFDLDhEQUFDcUM7d0VBQUlDLE9BQU87NEVBQUU4QyxNQUFNO3dFQUFFOzswRkFDcEIsOERBQUMvQztnRkFBSUMsT0FBTztvRkFBRUssaUJBQWlCO29GQUFXWSxjQUFjO29GQUFRUCxTQUFTO2dGQUFlOztrR0FDdEYsOERBQUNYO3dGQUFJQyxPQUFPOzRGQUFFZ0IsWUFBWTs0RkFBT1QsVUFBVTs0RkFBWUMsY0FBYzt3RkFBVTtrR0FDNUU2RCxRQUFRMUYsU0FBUzs7Ozs7O2tHQUVwQiw4REFBQ29CO3dGQUFJQyxPQUFPOzRGQUFFTyxVQUFVOzRGQUFZb0QsWUFBWTt3RkFBTTtrR0FDbkRVLFFBQVF4RixPQUFPOzs7Ozs7Ozs7Ozs7MEZBR3BCLDhEQUFDa0I7Z0ZBQUlDLE9BQU87b0ZBQUVFLFNBQVM7b0ZBQVFZLEtBQUs7b0ZBQVFxRCxXQUFXO29GQUFXVixhQUFhO2dGQUFPOztrR0FDcEYsOERBQUMvQjt3RkFBTzFCLE9BQU87NEZBQUV1QyxZQUFZOzRGQUFRbEIsUUFBUTs0RkFBUVosT0FBTzs0RkFBV0YsVUFBVTs0RkFBV3NCLFFBQVE7NEZBQVdiLFlBQVk7d0ZBQU07a0dBQUc7Ozs7OztrR0FHcEksOERBQUNVO3dGQUFPMUIsT0FBTzs0RkFBRXVDLFlBQVk7NEZBQVFsQixRQUFROzRGQUFRWixPQUFPOzRGQUFXRixVQUFVOzRGQUFXc0IsUUFBUTs0RkFBV2IsWUFBWTt3RkFBTTtrR0FBRzs7Ozs7O2tHQUdwSSw4REFBQ1M7d0ZBQUt6QixPQUFPOzRGQUFFUyxPQUFPOzRGQUFXRixVQUFVO3dGQUFVO2tHQUNsRHRCLGNBQWNvRixRQUFRZCxVQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OytEQWpDL0JjLFFBQVFwRyxFQUFFOzs7Ozt3REF5Q3JCZ0YsS0FBS2MsUUFBUSxDQUFDRixNQUFNLEdBQUcsS0FBSyxDQUFDaEksZUFBZSxDQUFDb0gsS0FBS2hGLEVBQUUsQ0FBQyxrQkFDcEQsOERBQUN5RDs0REFDQ0MsU0FBUyxJQUFNN0YsbUJBQW1Cc0MsQ0FBQUEsT0FBUzt3RUFBRSxHQUFHQSxJQUFJO3dFQUFFLENBQUM2RSxLQUFLaEYsRUFBRSxDQUFDLEVBQUU7b0VBQUs7NERBQ3RFK0IsT0FBTztnRUFBRXVDLFlBQVk7Z0VBQVFsQixRQUFRO2dFQUFRWixPQUFPO2dFQUFXRixVQUFVO2dFQUFZc0IsUUFBUTtnRUFBV3JCLGNBQWM7NERBQU87O2dFQUM5SDtnRUFDT3lDLEtBQUtjLFFBQVEsQ0FBQ0YsTUFBTSxHQUFHO2dFQUFFOzs7Ozs7O3NFQUtuQyw4REFBQzlEOzREQUFJQyxPQUFPO2dFQUFFRSxTQUFTO2dFQUFRWSxLQUFLO2dFQUFXWCxZQUFZOzREQUFTOzs4RUFDbEUsOERBQUNKO29FQUFJQyxPQUFPO3dFQUNWSyxpQkFBaUJSLGFBQWE1RixZQUFZO3dFQUMxQ2dILGNBQWM7d0VBQ2RNLE9BQU87d0VBQ1BDLFFBQVE7d0VBQ1J0QixTQUFTO3dFQUNUQyxZQUFZO3dFQUNaQyxnQkFBZ0I7d0VBQ2hCSyxPQUFPO3dFQUNQRixVQUFVO3dFQUNWUyxZQUFZO3dFQUNac0QsWUFBWTtvRUFDZDs4RUFDRy9HOzs7Ozs7OEVBRUgsOERBQUN3QztvRUFBSUMsT0FBTzt3RUFBRThDLE1BQU07d0VBQUdOLFVBQVU7b0VBQVc7O3NGQUMxQyw4REFBQ3RCOzRFQUNDQyxNQUFLOzRFQUNMQyxhQUFZOzRFQUNabUQsT0FBTzVJLFlBQVksQ0FBQ3NILEtBQUtoRixFQUFFLENBQUMsSUFBSTs0RUFDaEN1RyxVQUFVLENBQUNwQixJQUFNeEgsZ0JBQWdCd0MsQ0FBQUEsT0FBUzt3RkFBRSxHQUFHQSxJQUFJO3dGQUFFLENBQUM2RSxLQUFLaEYsRUFBRSxDQUFDLEVBQUVtRixFQUFFQyxNQUFNLENBQUNrQixLQUFLO29GQUFDOzRFQUMvRUUsWUFBWSxDQUFDckI7Z0ZBQ1gsSUFBSUEsRUFBRXNCLEdBQUcsS0FBSyxTQUFTO29GQUNyQjVHLG9CQUFvQm1GLEtBQUtoRixFQUFFO2dGQUM3Qjs0RUFDRjs0RUFDQStCLE9BQU87Z0ZBQ0x1QixPQUFPO2dGQUNQbEIsaUJBQWlCO2dGQUNqQmdCLFFBQVE7Z0ZBQ1JKLGNBQWM7Z0ZBQ2RQLFNBQVM7Z0ZBQ1RZLFNBQVM7Z0ZBQ1RmLFVBQVU7NEVBQ1o7Ozs7Ozt3RUFFRDVFLFlBQVksQ0FBQ3NILEtBQUtoRixFQUFFLENBQUMsa0JBQ3BCLDhEQUFDeUQ7NEVBQ0NDLFNBQVMsSUFBTTdELG9CQUFvQm1GLEtBQUtoRixFQUFFOzRFQUMxQytCLE9BQU87Z0ZBQ0x3QyxVQUFVO2dGQUNWbUMsT0FBTztnRkFDUGxDLEtBQUs7Z0ZBQ0xtQyxXQUFXO2dGQUNYckMsWUFBWTtnRkFDWmxCLFFBQVE7Z0ZBQ1JaLE9BQU87Z0ZBQ1BvQixRQUFRO2dGQUNSdEIsVUFBVTtnRkFDVlMsWUFBWTs0RUFDZDtzRkFDRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQ0F6UUxpQyxLQUFLaEYsRUFBRTs7Ozs7NEJBb1JyQjs0QkFHQzVELE1BQU13SixNQUFNLEtBQUssbUJBQ2hCLDhEQUFDOUQ7Z0NBQUlDLE9BQU87b0NBQUVLLGlCQUFpQjtvQ0FBU1ksY0FBYztvQ0FBT04sV0FBVztvQ0FBNkJELFNBQVM7b0NBQVFKLFdBQVc7Z0NBQVM7O2tEQUN4SSw4REFBQ1A7d0NBQUlDLE9BQU87NENBQUVPLFVBQVU7NENBQVFDLGNBQWM7d0NBQU87a0RBQUc7Ozs7OztrREFDeEQsOERBQUN1Qjt3Q0FBRy9CLE9BQU87NENBQUVTLE9BQU87NENBQVdELGNBQWM7d0NBQVM7a0RBQUc7Ozs7OztrREFDekQsOERBQUNoQzt3Q0FBRXdCLE9BQU87NENBQUVTLE9BQU87d0NBQVU7a0RBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNdEMsOERBQUNWO3dCQUFJQyxPQUFPOzRCQUFFSyxpQkFBaUI7NEJBQVNZLGNBQWM7NEJBQU9QLFNBQVM7NEJBQVFjLFFBQVE7NEJBQWViLFdBQVc7d0JBQTRCOzswQ0FDMUksOERBQUNvQjtnQ0FBRy9CLE9BQU87b0NBQUVPLFVBQVU7b0NBQVlTLFlBQVk7b0NBQU9QLE9BQU87b0NBQVdELGNBQWM7Z0NBQU87MENBQUc7Ozs7OzswQ0FHaEcsOERBQUNUO2dDQUFJQyxPQUFPO29DQUFFRSxTQUFTO29DQUFROEIsZUFBZTtvQ0FBVWxCLEtBQUs7Z0NBQVU7O2tEQUNyRSw4REFBQ2Y7d0NBQUlDLE9BQU87NENBQUVFLFNBQVM7NENBQVFDLFlBQVk7NENBQVVXLEtBQUs7d0NBQVU7OzBEQUNsRSw4REFBQ2Y7Z0RBQUlDLE9BQU87b0RBQUV3QyxVQUFVO2dEQUFXOztrRUFDakMsOERBQUN6Qzt3REFBSUMsT0FBTzs0REFBRUssaUJBQWlCOzREQUFXWSxjQUFjOzREQUFPTSxPQUFPOzREQUFRQyxRQUFROzREQUFRdEIsU0FBUzs0REFBUUMsWUFBWTs0REFBVUMsZ0JBQWdCOzREQUFVSyxPQUFPOzREQUFTRixVQUFVOzREQUFZUyxZQUFZO3dEQUFPO2tFQUFHOzs7Ozs7a0VBRzNOLDhEQUFDakI7d0RBQUlDLE9BQU87NERBQUV3QyxVQUFVOzREQUFZcUMsUUFBUTs0REFBUUYsT0FBTzs0REFBUXRFLGlCQUFpQjs0REFBV1ksY0FBYzs0REFBT00sT0FBTzs0REFBV0MsUUFBUTs0REFBV0gsUUFBUTt3REFBa0I7Ozs7Ozs7Ozs7OzswREFFckwsOERBQUNJO2dEQUFLekIsT0FBTztvREFBRU8sVUFBVTtvREFBWUUsT0FBTztnREFBVTswREFBRzs7Ozs7Ozs7Ozs7O2tEQUczRCw4REFBQ1Y7d0NBQUlDLE9BQU87NENBQUVFLFNBQVM7NENBQVFDLFlBQVk7NENBQVVXLEtBQUs7d0NBQVU7OzBEQUNsRSw4REFBQ2Y7Z0RBQUlDLE9BQU87b0RBQUV3QyxVQUFVO2dEQUFXOztrRUFDakMsOERBQUN6Qzt3REFBSUMsT0FBTzs0REFBRUssaUJBQWlCOzREQUFXWSxjQUFjOzREQUFPTSxPQUFPOzREQUFRQyxRQUFROzREQUFRdEIsU0FBUzs0REFBUUMsWUFBWTs0REFBVUMsZ0JBQWdCOzREQUFVSyxPQUFPOzREQUFTRixVQUFVOzREQUFZUyxZQUFZO3dEQUFPO2tFQUFHOzs7Ozs7a0VBRzNOLDhEQUFDakI7d0RBQUlDLE9BQU87NERBQUV3QyxVQUFVOzREQUFZcUMsUUFBUTs0REFBUUYsT0FBTzs0REFBUXRFLGlCQUFpQjs0REFBV1ksY0FBYzs0REFBT00sT0FBTzs0REFBV0MsUUFBUTs0REFBV0gsUUFBUTt3REFBa0I7Ozs7Ozs7Ozs7OzswREFFckwsOERBQUNJO2dEQUFLekIsT0FBTztvREFBRU8sVUFBVTtvREFBWUUsT0FBTztnREFBVTswREFBRzs7Ozs7Ozs7Ozs7O2tEQUczRCw4REFBQ1Y7d0NBQUlDLE9BQU87NENBQUVFLFNBQVM7NENBQVFDLFlBQVk7NENBQVVXLEtBQUs7d0NBQVU7OzBEQUNsRSw4REFBQ2Y7Z0RBQUlDLE9BQU87b0RBQUV3QyxVQUFVO2dEQUFXOztrRUFDakMsOERBQUN6Qzt3REFBSUMsT0FBTzs0REFBRUssaUJBQWlCOzREQUFXWSxjQUFjOzREQUFPTSxPQUFPOzREQUFRQyxRQUFROzREQUFRdEIsU0FBUzs0REFBUUMsWUFBWTs0REFBVUMsZ0JBQWdCOzREQUFVSyxPQUFPOzREQUFTRixVQUFVOzREQUFZUyxZQUFZO3dEQUFPO2tFQUFHOzs7Ozs7a0VBRzNOLDhEQUFDakI7d0RBQUlDLE9BQU87NERBQUV3QyxVQUFVOzREQUFZcUMsUUFBUTs0REFBUUYsT0FBTzs0REFBUXRFLGlCQUFpQjs0REFBV1ksY0FBYzs0REFBT00sT0FBTzs0REFBV0MsUUFBUTs0REFBV0gsUUFBUTt3REFBa0I7Ozs7Ozs7Ozs7OzswREFFckwsOERBQUNJO2dEQUFLekIsT0FBTztvREFBRU8sVUFBVTtvREFBWUUsT0FBTztnREFBVTswREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUk3RCw4REFBQ1Y7Z0NBQUlDLE9BQU87b0NBQUVtRSxXQUFXO29DQUFRcEIsWUFBWTtvQ0FBUUMsV0FBVztnQ0FBb0I7MENBQ2xGLDRFQUFDZjtvQ0FBRUMsTUFBSztvQ0FBSWxDLE9BQU87d0NBQUVTLE9BQU87d0NBQVcwQixnQkFBZ0I7d0NBQVE1QixVQUFVO29DQUFXOzhDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU01RjVGLGdDQUNDLDhEQUFDb0Y7Z0JBQUlDLE9BQU87b0JBQ1Z3QyxVQUFVO29CQUNWQyxLQUFLO29CQUNMQyxNQUFNO29CQUNOaUMsT0FBTztvQkFDUEUsUUFBUTtvQkFDUnhFLGlCQUFpQjtvQkFDakJILFNBQVM7b0JBQ1RDLFlBQVk7b0JBQ1pDLGdCQUFnQjtvQkFDaEIwRSxRQUFRO2dCQUNWOzBCQUNFLDRFQUFDL0U7b0JBQUlDLE9BQU87d0JBQ1ZLLGlCQUFpQjt3QkFDakJZLGNBQWM7d0JBQ2ROLFdBQVc7d0JBQ1hZLE9BQU87d0JBQ1B3RCxXQUFXO3dCQUNYQyxVQUFVO29CQUNaOztzQ0FFRSw4REFBQ2pGOzRCQUFJQyxPQUFPO2dDQUNWVSxTQUFTO2dDQUNUdUUsY0FBYztnQ0FDZC9FLFNBQVM7Z0NBQ1RFLGdCQUFnQjtnQ0FDaEJELFlBQVk7NEJBQ2Q7OzhDQUNFLDhEQUFDK0U7b0NBQUdsRixPQUFPO3dDQUFFTyxVQUFVO3dDQUFXUyxZQUFZO3dDQUFPUCxPQUFPO3dDQUFXSSxRQUFRO29DQUFFOzhDQUFHOzs7Ozs7OENBR3BGLDhEQUFDYTtvQ0FDQ0MsU0FBUyxJQUFNL0csa0JBQWtCO29DQUNqQ29GLE9BQU87d0NBQ0xLLGlCQUFpQjt3Q0FDakJnQixRQUFRO3dDQUNSSixjQUFjO3dDQUNkTSxPQUFPO3dDQUNQQyxRQUFRO3dDQUNSSyxRQUFRO3dDQUNSM0IsU0FBUzt3Q0FDVEMsWUFBWTt3Q0FDWkMsZ0JBQWdCO3dDQUNoQkcsVUFBVTt3Q0FDVkUsT0FBTztvQ0FDVDs4Q0FDRDs7Ozs7Ozs7Ozs7O3NDQU1ILDhEQUFDVjs0QkFBSUMsT0FBTztnQ0FBRVUsU0FBUztnQ0FBUVIsU0FBUztnQ0FBUUMsWUFBWTtnQ0FBVVcsS0FBSzs0QkFBVTs7OENBQ25GLDhEQUFDZjtvQ0FBSUMsT0FBTzt3Q0FDVkssaUJBQWlCcEcsYUFBYSxXQUFXLFlBQVlBLGFBQWEsYUFBYSxZQUFZO3dDQUMzRmdILGNBQWM7d0NBQ2RNLE9BQU87d0NBQ1BDLFFBQVE7d0NBQ1J0QixTQUFTO3dDQUNUQyxZQUFZO3dDQUNaQyxnQkFBZ0I7d0NBQ2hCSyxPQUFPO3dDQUNQTyxZQUFZO29DQUNkOzhDQUNHekQ7Ozs7Ozs4Q0FFSCw4REFBQ3dDOztzREFDQyw4REFBQ0E7NENBQUlDLE9BQU87Z0RBQUVnQixZQUFZO2dEQUFPUCxPQUFPOzRDQUFVO3NEQUFJeEQ7Ozs7OztzREFDdEQsOERBQUM4Qzs0Q0FBSUMsT0FBTztnREFBRU8sVUFBVTtnREFBWUUsT0FBTztnREFBV1AsU0FBUztnREFBUUMsWUFBWTtnREFBVVcsS0FBSzs0Q0FBVTs7Z0RBQ3pHbkQ7Z0RBQW1CO2dEQUFFMUQsV0FBV0EsU0FBU3dELE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUt6RCxTQUFTdUosS0FBSyxDQUFDLEtBQUs7Z0RBQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXJHLDhEQUFDekQ7NEJBQUlDLE9BQU87Z0NBQUVVLFNBQVM7NEJBQVM7c0NBQzlCLDRFQUFDeUU7Z0NBQ0NaLE9BQU8xSjtnQ0FDUDJKLFVBQVUsQ0FBQ3BCLElBQU10SSxZQUFZc0ksRUFBRUMsTUFBTSxDQUFDa0IsS0FBSztnQ0FDM0NuRCxhQUFZO2dDQUNacEIsT0FBTztvQ0FDTHVCLE9BQU87b0NBQ1B0QixXQUFXO29DQUNYb0IsUUFBUTtvQ0FDUkMsU0FBUztvQ0FDVGYsVUFBVTtvQ0FDVkUsT0FBTztvQ0FDUDJFLFFBQVE7b0NBQ1JDLFlBQVk7Z0NBQ2Q7Ozs7Ozs7Ozs7O3dCQUtIdEssaUNBQ0MsOERBQUNnRjs0QkFBSUMsT0FBTztnQ0FBRVUsU0FBUztnQ0FBVUYsY0FBYzs0QkFBTztzQ0FDcEQsNEVBQUNUO2dDQUFJQyxPQUFPO29DQUNWSyxpQkFBaUI7b0NBQ2pCWSxjQUFjO29DQUNkUCxTQUFTO29DQUNUUixTQUFTO29DQUNUQyxZQUFZO29DQUNaVyxLQUFLO2dDQUNQOztrREFDRSw4REFBQ1c7a0RBQU0xRzs7Ozs7O2tEQUNQLDhEQUFDMkc7d0NBQ0NDLFNBQVMsSUFBTTNHLG1CQUFtQjt3Q0FDbENnRixPQUFPOzRDQUNMSyxpQkFBaUI7NENBQ2pCZ0IsUUFBUTs0Q0FDUlEsUUFBUTs0Q0FDUnBCLE9BQU87d0NBQ1Q7a0RBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7O3dCQVFOeEYsZUFBZTRJLE1BQU0sR0FBRyxtQkFDdkIsOERBQUM5RDs0QkFBSUMsT0FBTztnQ0FBRVUsU0FBUztnQ0FBVUYsY0FBYzs0QkFBTztzQ0FDcEQsNEVBQUNUO2dDQUFJQyxPQUFPO29DQUNWd0MsVUFBVTtvQ0FDVm5DLGlCQUFpQjtvQ0FDakJZLGNBQWM7b0NBQ2RQLFNBQVM7b0NBQ1RKLFdBQVc7Z0NBQ2I7O2tEQUNFLDhEQUFDUDt3Q0FBSUMsT0FBTzs0Q0FBRU8sVUFBVTs0Q0FBUUMsY0FBYzt3Q0FBTztrREFBRzs7Ozs7O2tEQUN4RCw4REFBQ1Q7d0NBQUlDLE9BQU87NENBQUVTLE9BQU87d0NBQVU7OzRDQUFHOzRDQUFnQnhGLGVBQWU0SSxNQUFNOzRDQUFDOzs7Ozs7O2tEQUN4RSw4REFBQ25DO3dDQUNDQyxTQUFTLElBQU16RyxrQkFBa0IsRUFBRTt3Q0FDbkM4RSxPQUFPOzRDQUNMd0MsVUFBVTs0Q0FDVkMsS0FBSzs0Q0FDTGtDLE9BQU87NENBQ1B0RSxpQkFBaUI7NENBQ2pCSSxPQUFPOzRDQUNQWSxRQUFROzRDQUNSSixjQUFjOzRDQUNkTSxPQUFPOzRDQUNQQyxRQUFROzRDQUNSSyxRQUFRO3dDQUNWO2tEQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRUCw4REFBQzlCOzRCQUFJQyxPQUFPO2dDQUFFVSxTQUFTO2dDQUFRc0MsV0FBVzs0QkFBb0I7OzhDQUM1RCw4REFBQ2pEO29DQUFJQyxPQUFPO3dDQUFFRSxTQUFTO3dDQUFRRSxnQkFBZ0I7d0NBQWlCRCxZQUFZO3dDQUFVSyxjQUFjO29DQUFPOztzREFDekcsOERBQUNpQjs0Q0FBS3pCLE9BQU87Z0RBQUVnQixZQUFZO2dEQUFPUCxPQUFPOzRDQUFVO3NEQUFHOzs7Ozs7c0RBQ3RELDhEQUFDVjs0Q0FBSUMsT0FBTztnREFBRUUsU0FBUztnREFBUVksS0FBSzs0Q0FBUzs7OERBQzNDLDhEQUFDWTtvREFDQ0MsU0FBUyxJQUFNekcsa0JBQWtCOzREQUFDO3lEQUFRO29EQUMxQzhFLE9BQU87d0RBQ0xLLGlCQUFpQjt3REFDakJnQixRQUFRO3dEQUNSSixjQUFjO3dEQUNkTSxPQUFPO3dEQUNQQyxRQUFRO3dEQUNSSyxRQUFRO3dEQUNSM0IsU0FBUzt3REFDVEMsWUFBWTt3REFDWkMsZ0JBQWdCO3dEQUNoQkcsVUFBVTtvREFDWjtvREFDQStFLE9BQU07OERBQ1A7Ozs7Ozs4REFHRCw4REFBQzVEO29EQUNDQyxTQUFTLElBQU0zRyxtQkFBbUI7b0RBQ2xDZ0YsT0FBTzt3REFDTEssaUJBQWlCO3dEQUNqQmdCLFFBQVE7d0RBQ1JKLGNBQWM7d0RBQ2RNLE9BQU87d0RBQ1BDLFFBQVE7d0RBQ1JLLFFBQVE7d0RBQ1IzQixTQUFTO3dEQUNUQyxZQUFZO3dEQUNaQyxnQkFBZ0I7d0RBQ2hCRyxVQUFVO29EQUNaO29EQUNBK0UsT0FBTTs4REFDUDs7Ozs7OzhEQUdELDhEQUFDNUQ7b0RBQ0MxQixPQUFPO3dEQUNMSyxpQkFBaUI7d0RBQ2pCZ0IsUUFBUTt3REFDUkosY0FBYzt3REFDZE0sT0FBTzt3REFDUEMsUUFBUTt3REFDUkssUUFBUTt3REFDUjNCLFNBQVM7d0RBQ1RDLFlBQVk7d0RBQ1pDLGdCQUFnQjt3REFDaEJHLFVBQVU7b0RBQ1o7b0RBQ0ErRSxPQUFNOzhEQUNQOzs7Ozs7OERBR0QsOERBQUM1RDtvREFDQzFCLE9BQU87d0RBQ0xLLGlCQUFpQjt3REFDakJnQixRQUFRO3dEQUNSSixjQUFjO3dEQUNkTSxPQUFPO3dEQUNQQyxRQUFRO3dEQUNSSyxRQUFRO3dEQUNSM0IsU0FBUzt3REFDVEMsWUFBWTt3REFDWkMsZ0JBQWdCO3dEQUNoQkcsVUFBVTtvREFDWjtvREFDQStFLE9BQU07OERBQ1A7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPTCw4REFBQzVEO29DQUNDQyxTQUFTO3dDQUNQLElBQUksQ0FBQzlHLFNBQVNtRCxJQUFJLElBQUk7d0NBRXRCLDBDQUEwQzt3Q0FDMUMsTUFBTVMsVUFBVTs0Q0FDZEMsU0FBUzFFLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTWtFLEtBQUssS0FBSTs0Q0FDeEJTLFdBQVcxQjs0Q0FDWDJCLFdBQVkzRSxZQUFZOzRDQUN4QjRFLFNBQVNoRTs0Q0FDVCtJLFlBQVkzSSxlQUFlNEksTUFBTSxHQUFHLElBQUk1SSxpQkFBaUJtSjs0Q0FDekR0RixXQUFZN0QsZUFBZTRJLE1BQU0sR0FBRyxJQUFJLFVBQVU7NENBQ2xEOUUsWUFBWTt3Q0FDZDt3Q0FFQXpFLFFBQVFtRTt3Q0FFUixhQUFhO3dDQUNiM0QsWUFBWTt3Q0FDWkUsbUJBQW1CO3dDQUNuQkUsa0JBQWtCLEVBQUU7d0NBQ3BCRSxvQkFBb0I7d0NBQ3BCRSxlQUFlLEVBQUU7d0NBQ2pCVixrQkFBa0I7d0NBRWxCLHVCQUF1Qjt3Q0FDdkIySyxNQUFNO29DQUNSO29DQUNBQyxVQUFVLENBQUMzSyxTQUFTbUQsSUFBSTtvQ0FDeEJnQyxPQUFPO3dDQUNMdUIsT0FBTzt3Q0FDUGxCLGlCQUFpQnhGLFNBQVNtRCxJQUFJLEtBQUssWUFBWTt3Q0FDL0N5QyxPQUFPNUYsU0FBU21ELElBQUksS0FBSyxVQUFVO3dDQUNuQ3FELFFBQVE7d0NBQ1JKLGNBQWM7d0NBQ2RQLFNBQVM7d0NBQ1RILFVBQVU7d0NBQ1ZTLFlBQVk7d0NBQ1phLFFBQVFoSCxTQUFTbUQsSUFBSSxLQUFLLFlBQVk7b0NBQ3hDOzhDQUNEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNmO0dBL2dDd0JqRTs7UUFDdUNILDBEQUFPQTtRQUNSQyw0REFBUUE7UUFDckRDLHNEQUFTQTs7O0tBSEZDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxLQUFaTUFBTUFBIEFVR01FTlRcXGthYXptYWFtYWEtYXBwXFxzcmNcXGFwcFxcZmVlZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IHVzZVBvc3RzIH0gZnJvbSAnQC9jb250ZXh0cy9Qb3N0c0NvbnRleHQnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZlZWRQYWdlKCkge1xuICBjb25zdCB7IHVzZXIsIHVzZXJSb2xlLCBnZXRVc2VyUHJvZmlsZSwgbG9hZGluZywgc2lnbk91dCB9ID0gdXNlQXV0aCgpXG4gIGNvbnN0IHsgcG9zdHMsIGFkZFBvc3QsIGxpa2VQb3N0LCBhZGRDb21tZW50LCBzaGFyZVBvc3QgfSA9IHVzZVBvc3RzKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICAvLyBDcmVhdGUgUG9zdCBTdGF0ZXNcbiAgY29uc3QgW3Nob3dDcmVhdGVQb3N0LCBzZXRTaG93Q3JlYXRlUG9zdF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3Bvc3RUZXh0LCBzZXRQb3N0VGV4dF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW3NlbGVjdGVkRmVlbGluZywgc2V0U2VsZWN0ZWRGZWVsaW5nXSA9IHVzZVN0YXRlKCcnKVxuICBjb25zdCBbc2VsZWN0ZWRJbWFnZXMsIHNldFNlbGVjdGVkSW1hZ2VzXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSlcbiAgY29uc3QgW3NlbGVjdGVkTG9jYXRpb24sIHNldFNlbGVjdGVkTG9jYXRpb25dID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFt0YWdnZWRVc2Vycywgc2V0VGFnZ2VkVXNlcnNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKVxuICBjb25zdCBbcG9zdFByaXZhY3ksIHNldFBvc3RQcml2YWN5XSA9IHVzZVN0YXRlPCdwdWJsaWMnIHwgJ2ZyaWVuZHMnIHwgJ3ByaXZhdGUnPigncHVibGljJylcblxuICAvLyBDb21tZW50cyBTdGF0ZXNcbiAgY29uc3QgW3Nob3dDb21tZW50cywgc2V0U2hvd0NvbW1lbnRzXSA9IHVzZVN0YXRlPHtba2V5OiBzdHJpbmddOiBib29sZWFufT4oe30pXG4gIGNvbnN0IFtjb21tZW50VGV4dHMsIHNldENvbW1lbnRUZXh0c10gPSB1c2VTdGF0ZTx7W2tleTogc3RyaW5nXTogc3RyaW5nfT4oe30pXG4gIGNvbnN0IFtzaG93QWxsQ29tbWVudHMsIHNldFNob3dBbGxDb21tZW50c10gPSB1c2VTdGF0ZTx7W2tleTogc3RyaW5nXTogYm9vbGVhbn0+KHt9KVxuXG4gIC8vIFNoYXJlIFN0YXRlc1xuICBjb25zdCBbc2hvd1NoYXJlTW9kYWwsIHNldFNob3dTaGFyZU1vZGFsXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtzaGFyZVRleHQsIHNldFNoYXJlVGV4dF0gPSB1c2VTdGF0ZSgnJylcblxuICAvLyBPdGhlciBTdGF0ZXNcbiAgY29uc3QgW3VzZXJQcm9maWxlLCBzZXRVc2VyUHJvZmlsZV0gPSB1c2VTdGF0ZTxhbnk+KG51bGwpXG4gIGNvbnN0IFthY3RpdmVTdG9yeSwgc2V0QWN0aXZlU3RvcnldID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3Nob3dFbW9qaVBpY2tlciwgc2V0U2hvd0Vtb2ppUGlja2VyXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd0xvY2F0aW9uUGlja2VyLCBzZXRTaG93TG9jYXRpb25QaWNrZXJdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzaG93VGFnUGlja2VyLCBzZXRTaG93VGFnUGlja2VyXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIEdldCB1c2VyIHByb2ZpbGUgZGF0YVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyICYmIHVzZXJSb2xlKSB7XG4gICAgICBjb25zdCBwcm9maWxlID0gZ2V0VXNlclByb2ZpbGUoKVxuICAgICAgc2V0VXNlclByb2ZpbGUocHJvZmlsZSlcbiAgICAgIGNvbnNvbGUubG9nKCdVc2VyIHByb2ZpbGUgbG9hZGVkOicsIHByb2ZpbGUpXG4gICAgfVxuICB9LCBbdXNlciwgdXNlclJvbGUsIGdldFVzZXJQcm9maWxlXSlcblxuICAvLyBSZWRpcmVjdCB0byBob21lIGlmIG5vdCBhdXRoZW50aWNhdGVkXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFsb2FkaW5nICYmICF1c2VyKSB7XG4gICAgICByb3V0ZXIucHVzaCgnLycpXG4gICAgfVxuICB9LCBbdXNlciwgbG9hZGluZywgcm91dGVyXSlcblxuICAvLyBIZWxwZXIgZnVuY3Rpb24gdG8gZ2V0IHVzZXIncyBkaXNwbGF5IG5hbWVcbiAgY29uc3QgZ2V0VXNlckRpc3BsYXlOYW1lID0gKCkgPT4ge1xuICAgIGlmICghdXNlclByb2ZpbGUpIHJldHVybiAnVXNlcidcblxuICAgIGlmICh1c2VyUm9sZSA9PT0gJ3dvcmtlcicpIHtcbiAgICAgIHJldHVybiB1c2VyUHJvZmlsZS5wZXJzb25hbEluZm8/Lndvcmtlck5hbWUgfHwgJ1dvcmtlcidcbiAgICB9IGVsc2UgaWYgKHVzZXJSb2xlID09PSAnc3VwcGxpZXInKSB7XG4gICAgICByZXR1cm4gdXNlclByb2ZpbGUucGVyc29uYWxJbmZvPy5zdXBwbGllck5hbWUgfHwgJ1N1cHBsaWVyJ1xuICAgIH0gZWxzZSBpZiAodXNlclJvbGUgPT09ICdjb21wYW55Jykge1xuICAgICAgcmV0dXJuIHVzZXJQcm9maWxlLmNvbXBhbnlJbmZvPy5jb21wYW55TmFtZSB8fCAnQ29tcGFueSdcbiAgICB9XG4gICAgcmV0dXJuICdVc2VyJ1xuICB9XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGdldCB1c2VyJ3MgYXZhdGFyL2luaXRpYWxcbiAgY29uc3QgZ2V0VXNlckluaXRpYWwgPSAoKSA9PiB7XG4gICAgY29uc3QgbmFtZSA9IGdldFVzZXJEaXNwbGF5TmFtZSgpXG4gICAgcmV0dXJuIG5hbWUuY2hhckF0KDApLnRvVXBwZXJDYXNlKClcbiAgfVxuXG4gIC8vIEhlbHBlciBmdW5jdGlvbiB0byBnZXQgdXNlcidzIHJvbGUgZW1vamlcbiAgY29uc3QgZ2V0VXNlclJvbGVFbW9qaSA9ICgpID0+IHtcbiAgICBzd2l0Y2ggKHVzZXJSb2xlKSB7XG4gICAgICBjYXNlICd3b3JrZXInOiByZXR1cm4gJ/CflKcnXG4gICAgICBjYXNlICdzdXBwbGllcic6IHJldHVybiAn8J+PrSdcbiAgICAgIGNhc2UgJ2NvbXBhbnknOiByZXR1cm4gJ/Cfj6InXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ/CfkaQnXG4gICAgfVxuICB9XG5cbiAgLy8gSGVscGVyIGZ1bmN0aW9ucyBmb3IgRmFjZWJvb2stc3R5bGUgZmVhdHVyZXNcbiAgY29uc3QgaGFuZGxlTGlrZVBvc3QgPSAocG9zdElkOiBzdHJpbmcpID0+IHtcbiAgICBsaWtlUG9zdChwb3N0SWQpXG4gIH1cblxuICBjb25zdCBoYW5kbGVDb21tZW50U3VibWl0ID0gKHBvc3RJZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgY29tbWVudFRleHQgPSBjb21tZW50VGV4dHNbcG9zdElkXVxuICAgIGlmICghY29tbWVudFRleHQ/LnRyaW0oKSB8fCAhdXNlcikgcmV0dXJuXG5cbiAgICBhZGRDb21tZW50KHBvc3RJZCwgY29tbWVudFRleHQsIHtcbiAgICAgIGlkOiB1c2VyLmVtYWlsLFxuICAgICAgbmFtZTogZ2V0VXNlckRpc3BsYXlOYW1lKCksXG4gICAgICByb2xlOiB1c2VyUm9sZSB8fCAnd29ya2VyJ1xuICAgIH0pXG5cbiAgICBzZXRDb21tZW50VGV4dHMocHJldiA9PiAoeyAuLi5wcmV2LCBbcG9zdElkXTogJycgfSkpXG4gIH1cblxuICBjb25zdCBoYW5kbGVTaGFyZVBvc3QgPSAocG9zdElkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXNoYXJlVGV4dC50cmltKCkpIHtcbiAgICAgIHNoYXJlUG9zdChwb3N0SWQpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENyZWF0ZSBhIG5ldyBwb3N0IHRoYXQgc2hhcmVzIHRoZSBvcmlnaW5hbFxuICAgICAgY29uc3Qgb3JpZ2luYWxQb3N0ID0gcG9zdHMuZmluZChwID0+IHAuaWQgPT09IHBvc3RJZClcbiAgICAgIGlmIChvcmlnaW5hbFBvc3QpIHtcbiAgICAgICAgY29uc3QgbmV3UG9zdCA9IHtcbiAgICAgICAgICB1c2VyX2lkOiB1c2VyPy5lbWFpbCB8fCAnJyxcbiAgICAgICAgICB1c2VyX25hbWU6IGdldFVzZXJEaXNwbGF5TmFtZSgpLFxuICAgICAgICAgIHVzZXJfcm9sZTogKHVzZXJSb2xlIHx8ICd3b3JrZXInKSBhcyAnd29ya2VyJyB8ICdzdXBwbGllcicgfCAnY29tcGFueScsXG4gICAgICAgICAgY29udGVudDogc2hhcmVUZXh0LFxuICAgICAgICAgIHBvc3RfdHlwZTogJ3NoYXJlZCcgYXMgY29uc3QsXG4gICAgICAgICAgdmlzaWJpbGl0eTogJ3B1YmxpYycgYXMgY29uc3RcbiAgICAgICAgfVxuICAgICAgICBhZGRQb3N0KG5ld1Bvc3QpXG4gICAgICB9XG4gICAgfVxuICAgIHNldFNob3dTaGFyZU1vZGFsKG51bGwpXG4gICAgc2V0U2hhcmVUZXh0KCcnKVxuICB9XG5cbiAgY29uc3QgdG9nZ2xlQ29tbWVudHMgPSAocG9zdElkOiBzdHJpbmcpID0+IHtcbiAgICBzZXRTaG93Q29tbWVudHMocHJldiA9PiAoeyAuLi5wcmV2LCBbcG9zdElkXTogIXByZXZbcG9zdElkXSB9KSlcbiAgfVxuXG4gIGNvbnN0IGZvcm1hdFRpbWVBZ28gPSAoZGF0ZVN0cmluZzogc3RyaW5nKSA9PiB7XG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICAgIGNvbnN0IHBvc3REYXRlID0gbmV3IERhdGUoZGF0ZVN0cmluZylcbiAgICBjb25zdCBkaWZmSW5NaW51dGVzID0gTWF0aC5mbG9vcigobm93LmdldFRpbWUoKSAtIHBvc3REYXRlLmdldFRpbWUoKSkgLyAoMTAwMCAqIDYwKSlcblxuICAgIGlmIChkaWZmSW5NaW51dGVzIDwgMSkgcmV0dXJuICdKdXN0IG5vdydcbiAgICBpZiAoZGlmZkluTWludXRlcyA8IDYwKSByZXR1cm4gYCR7ZGlmZkluTWludXRlc31tYFxuXG4gICAgY29uc3QgZGlmZkluSG91cnMgPSBNYXRoLmZsb29yKGRpZmZJbk1pbnV0ZXMgLyA2MClcbiAgICBpZiAoZGlmZkluSG91cnMgPCAyNCkgcmV0dXJuIGAke2RpZmZJbkhvdXJzfWhgXG5cbiAgICBjb25zdCBkaWZmSW5EYXlzID0gTWF0aC5mbG9vcihkaWZmSW5Ib3VycyAvIDI0KVxuICAgIGlmIChkaWZmSW5EYXlzIDwgNykgcmV0dXJuIGAke2RpZmZJbkRheXN9ZGBcblxuICAgIGNvbnN0IGRpZmZJbldlZWtzID0gTWF0aC5mbG9vcihkaWZmSW5EYXlzIC8gNylcbiAgICByZXR1cm4gYCR7ZGlmZkluV2Vla3N9d2BcbiAgfVxuXG4gIGNvbnN0IGdldFJvbGVDb2xvciA9IChyb2xlOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHJvbGUpIHtcbiAgICAgIGNhc2UgJ3dvcmtlcic6IHJldHVybiAnIzI1NjNlYidcbiAgICAgIGNhc2UgJ3N1cHBsaWVyJzogcmV0dXJuICcjMDU5NjY5J1xuICAgICAgY2FzZSAnY29tcGFueSc6IHJldHVybiAnIzdjM2FlZCdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnIzZiNzI4MCdcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRSb2xlRW1vamkgPSAocm9sZTogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChyb2xlKSB7XG4gICAgICBjYXNlICd3b3JrZXInOiByZXR1cm4gJ/CflKcnXG4gICAgICBjYXNlICdzdXBwbGllcic6IHJldHVybiAn8J+PrSdcbiAgICAgIGNhc2UgJ2NvbXBhbnknOiByZXR1cm4gJ/Cfj6InXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ/CfkaQnXG4gICAgfVxuICB9XG5cbiAgLy8gU2hvdyBsb2FkaW5nIGlmIHN0aWxsIGF1dGhlbnRpY2F0aW5nXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgc3R5bGU9e3sgbWluSGVpZ2h0OiAnMTAwdmgnLCBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsIGJhY2tncm91bmRDb2xvcjogJyNmMGYyZjUnIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzJyZW0nLCBtYXJnaW5Cb3R0b206ICcxcmVtJyB9fT7ij7M8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnIzY1Njc2YicgfX0+TG9hZGluZyB5b3VyIGZlZWQuLi48L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICAvLyBSZWRpcmVjdCBpZiBub3QgYXV0aGVudGljYXRlZFxuICBpZiAoIXVzZXIpIHtcbiAgICByZXR1cm4gbnVsbFxuICB9XG4gIHJldHVybiAoXG4gICAgPGRpdiBzdHlsZT17eyBtaW5IZWlnaHQ6ICcxMDB2aCcsIGJhY2tncm91bmRDb2xvcjogJyNmMGYyZjUnIH19PlxuICAgICAgey8qIEZhY2Vib29rLXN0eWxlIEhlYWRlciAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnIzE4NzdmMicsIHBhZGRpbmc6ICcwLjc1cmVtIDFyZW0nLCBib3hTaGFkb3c6ICcwIDJweCA0cHggcmdiYSgwLDAsMCwwLjEpJyB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17eyBtYXhXaWR0aDogJzEyMDBweCcsIG1hcmdpbjogJzAgYXV0bycsIGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcxcmVtJyB9fT5cbiAgICAgICAgICAgIDxoMSBzdHlsZT17eyBmb250U2l6ZTogJzEuNXJlbScsIGZvbnRXZWlnaHQ6ICdib2xkJywgY29sb3I6ICd3aGl0ZScsIG1hcmdpbjogMCB9fT5cbiAgICAgICAgICAgICAgS0FBWk1BQU1BQVxuICAgICAgICAgICAgPC9oMT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjIpJywgYm9yZGVyUmFkaXVzOiAnMjBweCcsIHBhZGRpbmc6ICcwLjVyZW0gMXJlbScgfX0+XG4gICAgICAgICAgICAgIDxpbnB1dCBcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiIFxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VhcmNoIEtBQVpNQUFNQUEuLi5cIiBcbiAgICAgICAgICAgICAgICBzdHlsZT17eyBcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywgXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJywgXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJywgXG4gICAgICAgICAgICAgICAgICBvdXRsaW5lOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuODc1cmVtJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMXJlbScsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjc1cmVtJywgY29sb3I6ICd3aGl0ZScgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuMiknLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgd2lkdGg6ICcycmVtJyxcbiAgICAgICAgICAgICAgICBoZWlnaHQ6ICcycmVtJyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7Z2V0VXNlckluaXRpYWwoKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMC44NzVyZW0nLCBmb250V2VpZ2h0OiAnNTAwJyB9fT5cbiAgICAgICAgICAgICAgICB7Z2V0VXNlckRpc3BsYXlOYW1lKCl9XG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMC43NXJlbScgfX0+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBwcm9maWxlUGF0aCA9IHVzZXJSb2xlID09PSAnd29ya2VyJyA/ICcvd29ya2Vycy9wcm9maWxlJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyUm9sZSA9PT0gJ3N1cHBsaWVyJyA/ICcvc3VwcGxpZXJzL3Byb2ZpbGUnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcvY29tcGFuaWVzL3Byb2ZpbGUnXG4gICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChwcm9maWxlUGF0aClcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuMiknLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjMpJyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0gMXJlbScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjI1cmVtJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBHbyBQcm9maWxlXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXthc3luYyAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBhd2FpdCBzaWduT3V0KClcbiAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvJylcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDI1NSwyNTUsMjU1LDAuMiknLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgcmdiYSgyNTUsMjU1LDI1NSwwLjMpJyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0gMXJlbScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcwLjI1cmVtJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBTaWduIE91dFxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogTWFpbiBGZWVkIExheW91dCAqL31cbiAgICAgIDxkaXYgc3R5bGU9e3sgbWF4V2lkdGg6ICcxMjAwcHgnLCBtYXJnaW46ICcwIGF1dG8nLCBkaXNwbGF5OiAnZ3JpZCcsIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcxZnIgMmZyIDFmcicsIGdhcDogJzFyZW0nLCBwYWRkaW5nOiAnMXJlbScgfX0+XG4gICAgICAgIFxuICAgICAgICB7LyogTGVmdCBTaWRlYmFyICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJ3doaXRlJywgYm9yZGVyUmFkaXVzOiAnOHB4JywgcGFkZGluZzogJzFyZW0nLCBoZWlnaHQ6ICdmaXQtY29udGVudCcsIGJveFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsMCwwLDAuMSknIH19PlxuICAgICAgICAgIDxoMyBzdHlsZT17eyBmb250U2l6ZTogJzEuMTI1cmVtJywgZm9udFdlaWdodDogJzYwMCcsIGNvbG9yOiAnIzFjMWUyMScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgUXVpY2sgQWNjZXNzXG4gICAgICAgICAgPC9oMz5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsIGdhcDogJzAuNzVyZW0nIH19PlxuICAgICAgICAgICAgPGEgaHJlZj1cIi93b3JrZXJzXCIgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC43NXJlbScsIHRleHREZWNvcmF0aW9uOiAnbm9uZScsIGNvbG9yOiAnIzFjMWUyMScsIHBhZGRpbmc6ICcwLjVyZW0nLCBib3JkZXJSYWRpdXM6ICc2cHgnIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNlM2YyZmQnLCBib3JkZXJSYWRpdXM6ICc1MCUnLCB3aWR0aDogJzIuNXJlbScsIGhlaWdodDogJzIuNXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgICAgICDwn5SnXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJyB9fT5Xb3JrZXJzPC9zcGFuPlxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8YSBocmVmPVwiL3N1cHBsaWVyc1wiIHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNzVyZW0nLCB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnLCBjb2xvcjogJyMxYzFlMjEnLCBwYWRkaW5nOiAnMC41cmVtJywgYm9yZGVyUmFkaXVzOiAnNnB4JyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZThmNWU4JywgYm9yZGVyUmFkaXVzOiAnNTAlJywgd2lkdGg6ICcyLjVyZW0nLCBoZWlnaHQ6ICcyLjVyZW0nLCBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgICAg8J+PrVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFdlaWdodDogJzUwMCcgfX0+U3VwcGxpZXJzPC9zcGFuPlxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8YSBocmVmPVwiL2NvbXBhbmllc1wiIHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNzVyZW0nLCB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnLCBjb2xvcjogJyMxYzFlMjEnLCBwYWRkaW5nOiAnMC41cmVtJywgYm9yZGVyUmFkaXVzOiAnNnB4JyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZjNlOGZmJywgYm9yZGVyUmFkaXVzOiAnNTAlJywgd2lkdGg6ICcyLjVyZW0nLCBoZWlnaHQ6ICcyLjVyZW0nLCBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgICAg8J+PolxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFdlaWdodDogJzUwMCcgfX0+Q29tcGFuaWVzPC9zcGFuPlxuICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8YSBocmVmPVwiL2JhcnRhXCIgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC43NXJlbScsIHRleHREZWNvcmF0aW9uOiAnbm9uZScsIGNvbG9yOiAnIzFjMWUyMScsIHBhZGRpbmc6ICcwLjVyZW0nLCBib3JkZXJSYWRpdXM6ICc2cHgnIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNmZmYzY2QnLCBib3JkZXJSYWRpdXM6ICc1MCUnLCB3aWR0aDogJzIuNXJlbScsIGhlaWdodDogJzIuNXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgICAgICDwn5KsXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNTAwJyB9fT5CYXJ0YSBNZXNzZW5nZXI8L3NwYW4+XG4gICAgICAgICAgICA8L2E+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDZW50ZXIgRmVlZCAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBnYXA6ICcxcmVtJyB9fT5cblxuICAgICAgICAgIHsvKiBTdG9yaWVzIFNlY3Rpb24gKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScsIGJvcmRlclJhZGl1czogJzhweCcsIHBhZGRpbmc6ICcxcmVtJywgYm94U2hhZG93OiAnMCAxcHggMnB4IHJnYmEoMCwwLDAsMC4xKScgfX0+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMC43NXJlbScsIG92ZXJmbG93WDogJ2F1dG8nLCBwYWRkaW5nQm90dG9tOiAnMC41cmVtJyB9fT5cbiAgICAgICAgICAgICAgey8qIENyZWF0ZSBTdG9yeSAqL31cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIG1pbldpZHRoOiAnMTIwcHgnLFxuICAgICAgICAgICAgICAgIGhlaWdodDogJzIwMHB4JyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMTg3N2YyLCAjNDJhNWY1KScsXG4gICAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnZmxleC1lbmQnLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICAgIHRvcDogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgbGVmdDogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAncmdiYSgyNTUsMjU1LDI1NSwwLjMpJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzIuNXJlbScsXG4gICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcyLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMjVyZW0nXG4gICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICArXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzAuODc1cmVtJywgZm9udFdlaWdodDogJzYwMCcgfX0+Q3JlYXRlIFN0b3J5PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTYW1wbGUgU3RvcmllcyAqL31cbiAgICAgICAgICAgICAge1snQWhtZWQgQWwtUmFzaGlkJywgJ1NhdWRpIEJ1aWxkaW5nIENvLicsICdUZWNoIFNvbHV0aW9ucycsICdDb25zdHJ1Y3Rpb24gUHJvJ10ubWFwKChuYW1lLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIG1pbldpZHRoOiAnMTIwcHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMjAwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBgbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAke1snI2ZmNmI2YicsICcjNGVjZGM0JywgJyM0NWI3ZDEnLCAnIzk2Y2ViNCddW2luZGV4XX0sICR7WycjZmZhNzI2JywgJyMyNmE2OWEnLCAnIzQyYTVmNScsICcjODFjNzg0J11baW5kZXhdfSlgLFxuICAgICAgICAgICAgICAgICAgcG9zaXRpb246ICdyZWxhdGl2ZScsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnXG4gICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMjU1LDI1NSwyNTUsMC4zKScsXG4gICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICB7bmFtZS5jaGFyQXQoMCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICcwLjg3NXJlbScsIGZvbnRXZWlnaHQ6ICc2MDAnIH19PntuYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIENyZWF0ZSBQb3N0ICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLCBib3JkZXJSYWRpdXM6ICc4cHgnLCBwYWRkaW5nOiAnMXJlbScsIGJveFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsMCwwLDAuMSknIH19PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzAuNzVyZW0nLCBtYXJnaW5Cb3R0b206ICcxcmVtJyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdXNlclJvbGUgPT09ICd3b3JrZXInID8gJyMyNTYzZWInIDogdXNlclJvbGUgPT09ICdzdXBwbGllcicgPyAnIzA1OTY2OScgOiAnIzdjM2FlZCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICB3aWR0aDogJzIuNXJlbScsXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7Z2V0VXNlckluaXRpYWwoKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17YFdoYXQncyBvbiB5b3VyIG1pbmQsICR7Z2V0VXNlckRpc3BsYXlOYW1lKCl9P2B9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0NyZWF0ZVBvc3QodHJ1ZSl9XG4gICAgICAgICAgICAgICAgcmVhZE9ubHlcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZmxleDogMSxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmMGYyZjUnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcyMHB4JyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjc1cmVtIDFyZW0nLFxuICAgICAgICAgICAgICAgICAgb3V0bGluZTogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYXJvdW5kJywgcGFkZGluZ1RvcDogJzAuNzVyZW0nLCBib3JkZXJUb3A6ICcxcHggc29saWQgI2U0ZTZlYScgfX0+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlUG9zdCh0cnVlKX1cbiAgICAgICAgICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjVyZW0nLCBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsIGJvcmRlcjogJ25vbmUnLCBwYWRkaW5nOiAnMC41cmVtIDFyZW0nLCBib3JkZXJSYWRpdXM6ICc2cHgnLCBjdXJzb3I6ICdwb2ludGVyJywgY29sb3I6ICcjNjU2NzZiJyB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg8J+TtyBQaG90by9WaWRlb1xuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dDcmVhdGVQb3N0KHRydWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNXJlbScsIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JywgYm9yZGVyOiAnbm9uZScsIHBhZGRpbmc6ICcwLjVyZW0gMXJlbScsIGJvcmRlclJhZGl1czogJzZweCcsIGN1cnNvcjogJ3BvaW50ZXInLCBjb2xvcjogJyM2NTY3NmInIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDwn5iKIEZlZWxpbmcvQWN0aXZpdHlcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBEeW5hbWljIFBvc3RzIGZyb20gQ29udGV4dCAqL31cbiAgICAgICAgICB7cG9zdHMubWFwKChwb3N0KSA9PiB7XG5cbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxkaXYga2V5PXtwb3N0LmlkfSBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScsIGJvcmRlclJhZGl1czogJzhweCcsIGJveFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsMCwwLDAuMSknIH19PlxuICAgICAgICAgICAgICAgIHsvKiBQb3N0IEhlYWRlciAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6ICcxcmVtJywgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC43NXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogZ2V0Um9sZUNvbG9yKHBvc3QudXNlcl9yb2xlKSxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcyLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcyLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCdcbiAgICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgICB7cG9zdC51c2VyX25hbWUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBOYXZpZ2F0ZSB0byB1c2VyJ3MgcHJvZmlsZSBiYXNlZCBvbiB0aGVpciByb2xlXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBwcm9maWxlUGF0aCA9IHBvc3QudXNlcl9yb2xlID09PSAnd29ya2VyJyA/ICcvd29ya2Vycy9wcm9maWxlJyA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3N0LnVzZXJfcm9sZSA9PT0gJ3N1cHBsaWVyJyA/ICcvc3VwcGxpZXJzL3Byb2ZpbGUnIDpcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICcvY29tcGFuaWVzL3Byb2ZpbGUnXG4gICAgICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChgJHtwcm9maWxlUGF0aH0/dXNlcj0ke2VuY29kZVVSSUNvbXBvbmVudChwb3N0LnVzZXJfaWQpfWApXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzYwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyMxYzFlMjEnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJ2luaGVyaXQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGV4dEFsaWduOiAnbGVmdCdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IChlLnRhcmdldCBhcyBIVE1MRWxlbWVudCkuc3R5bGUudGV4dERlY29yYXRpb24gPSAndW5kZXJsaW5lJ31cbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiAoZS50YXJnZXQgYXMgSFRNTEVsZW1lbnQpLnN0eWxlLnRleHREZWNvcmF0aW9uID0gJ25vbmUnfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAge3Bvc3QudXNlcl9uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzAuOHJlbScsIGNvbG9yOiAnIzY1Njc2YicgfX0+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVBZ28ocG9zdC5jcmVhdGVkX2F0KX0g4oCiIHtnZXRSb2xlRW1vamkocG9zdC51c2VyX3JvbGUpfSB7cG9zdC51c2VyX3JvbGUuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBwb3N0LnVzZXJfcm9sZS5zbGljZSgxKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIHsvKiBQb3N0IENvbnRlbnQgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBwYWRkaW5nTGVmdDogJzFyZW0nLCBwYWRkaW5nUmlnaHQ6ICcxcmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogJyMxYzFlMjEnLCBsaW5lSGVpZ2h0OiAnMS41JywgbWFyZ2luOiAwIH19PlxuICAgICAgICAgICAgICAgICAgICB7cG9zdC5jb250ZW50fVxuICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIFBvc3QgTWVkaWEgKGlmIGFueSkgKi99XG4gICAgICAgICAgICAgICAge3Bvc3QubWVkaWFfdXJscyAmJiBwb3N0Lm1lZGlhX3VybHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNmMGYyZjUnLCBoZWlnaHQ6ICcyNTBweCcsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgY29sb3I6ICcjNjU2NzZiJywgZm9udFNpemU6ICczcmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAg8J+Tt1xuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICAgIHsvKiBQb3N0IFJlYWN0aW9ucyBTdW1tYXJ5ICovfVxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzAuNzVyZW0gMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICB7KHBvc3QubGlrZXMgPiAwIHx8IHBvc3QuY29tbWVudHMgPiAwKSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgbWFyZ2luQm90dG9tOiAnMC43NXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjVyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAge3Bvc3QubGlrZXMgPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjI1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMC4xMjVyZW0nIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjMTg3N2YyJywgYm9yZGVyUmFkaXVzOiAnNTAlJywgd2lkdGg6ICcxLjI1cmVtJywgaGVpZ2h0OiAnMS4yNXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fT7wn5GNPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyNlNzRjM2MnLCBib3JkZXJSYWRpdXM6ICc1MCUnLCB3aWR0aDogJzEuMjVyZW0nLCBoZWlnaHQ6ICcxLjI1cmVtJywgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLCBmb250U2l6ZTogJzAuNzVyZW0nIH19PuKdpO+4jzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjZjM5YzEyJywgYm9yZGVyUmFkaXVzOiAnNTAlJywgd2lkdGg6ICcxLjI1cmVtJywgaGVpZ2h0OiAnMS4yNXJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fT7wn5iCPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6ICcjNjU2NzZiJywgZm9udFNpemU6ICcwLjg3NXJlbScgfX0+e3Bvc3QubGlrZXN9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzFyZW0nLCBjb2xvcjogJyM2NTY3NmInLCBmb250U2l6ZTogJzAuODc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LmNvbW1lbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gdG9nZ2xlQ29tbWVudHMocG9zdC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZDogJ25vbmUnLCBib3JkZXI6ICdub25lJywgY29sb3I6ICcjNjU2NzZiJywgY3Vyc29yOiAncG9pbnRlcicsIGZvbnRTaXplOiAnMC44NzVyZW0nIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cG9zdC5jb21tZW50cy5sZW5ndGh9IGNvbW1lbnRzXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwb3N0LnNoYXJlcyA+IDAgJiYgPHNwYW4+e3Bvc3Quc2hhcmVzfSBzaGFyZXM8L3NwYW4+fVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWFyb3VuZCcsIHBhZGRpbmdUb3A6ICcwLjc1cmVtJywgYm9yZGVyVG9wOiAnMXB4IHNvbGlkICNlNGU2ZWEnIH19PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTGlrZVBvc3QocG9zdC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgZ2FwOiAnMC41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNXJlbSAxcmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBwb3N0Lmxpa2VkX2J5X3VzZXIgPyAnIzE4NzdmMicgOiAnIzY1Njc2YicsXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNTAwJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcidcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAnI2YwZjJmNSd9XG4gICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4gZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmRDb2xvciA9ICd0cmFuc3BhcmVudCd9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250U2l6ZTogJzEuMjVyZW0nIH19PvCfkY08L3NwYW4+IExpa2VcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB0b2dnbGVDb21tZW50cyhwb3N0LmlkKX1cbiAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBnYXA6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMC41cmVtIDFyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjNjU2NzZiJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc1MDAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgZmxleDogMSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgb25Nb3VzZUVudGVyPXsoZSkgPT4gZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJhY2tncm91bmRDb2xvciA9ICcjZjBmMmY1J31cbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlTGVhdmU9eyhlKSA9PiBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJ3RyYW5zcGFyZW50J31cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMS4yNXJlbScgfX0+8J+SrDwvc3Bhbj4gQ29tbWVudFxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dTaGFyZU1vZGFsKHBvc3QuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGdhcDogJzAuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcwLjVyZW0gMXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyM2NTY3NmInLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJzUwMCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBmbGV4OiAxLFxuICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICBvbk1vdXNlRW50ZXI9eyhlKSA9PiBlLmN1cnJlbnRUYXJnZXQuc3R5bGUuYmFja2dyb3VuZENvbG9yID0gJyNmMGYyZjUnfVxuICAgICAgICAgICAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IGUuY3VycmVudFRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAndHJhbnNwYXJlbnQnfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFNpemU6ICcxLjI1cmVtJyB9fT7wn5OkPC9zcGFuPiBTaGFyZVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7LyogQ29tbWVudHMgU2VjdGlvbiAqL31cbiAgICAgICAgICAgICAgICAgIHtzaG93Q29tbWVudHNbcG9zdC5pZF0gJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IG1hcmdpblRvcDogJzFyZW0nLCBib3JkZXJUb3A6ICcxcHggc29saWQgI2U0ZTZlYScsIHBhZGRpbmdUb3A6ICcxcmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogRXhpc3RpbmcgQ29tbWVudHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAge3Bvc3QuY29tbWVudHMuc2xpY2UoMCwgc2hvd0FsbENvbW1lbnRzW3Bvc3QuaWRdID8gdW5kZWZpbmVkIDogMykubWFwKChjb21tZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17Y29tbWVudC5pZH0gc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcwLjc1cmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGdldFJvbGVDb2xvcihjb21tZW50LnVzZXJfcm9sZSksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzJyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzJyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsZXhTaHJpbms6IDBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbW1lbnQudXNlcl9uYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmbGV4OiAxIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnI2YwZjJmNScsIGJvcmRlclJhZGl1czogJzFyZW0nLCBwYWRkaW5nOiAnMC43NXJlbSAxcmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFdlaWdodDogJzYwMCcsIGZvbnRTaXplOiAnMC44NzVyZW0nLCBtYXJnaW5Cb3R0b206ICcwLjI1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbW1lbnQudXNlcl9uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMC44NzVyZW0nLCBsaW5lSGVpZ2h0OiAnMS40JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2NvbW1lbnQuY29udGVudH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6ICcxcmVtJywgbWFyZ2luVG9wOiAnMC4yNXJlbScsIHBhZGRpbmdMZWZ0OiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIHN0eWxlPXt7IGJhY2tncm91bmQ6ICdub25lJywgYm9yZGVyOiAnbm9uZScsIGNvbG9yOiAnIzY1Njc2YicsIGZvbnRTaXplOiAnMC43NXJlbScsIGN1cnNvcjogJ3BvaW50ZXInLCBmb250V2VpZ2h0OiAnNjAwJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTGlrZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIHN0eWxlPXt7IGJhY2tncm91bmQ6ICdub25lJywgYm9yZGVyOiAnbm9uZScsIGNvbG9yOiAnIzY1Njc2YicsIGZvbnRTaXplOiAnMC43NXJlbScsIGN1cnNvcjogJ3BvaW50ZXInLCBmb250V2VpZ2h0OiAnNjAwJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgUmVwbHlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgY29sb3I6ICcjNjU2NzZiJywgZm9udFNpemU6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFRpbWVBZ28oY29tbWVudC5jcmVhdGVkX2F0KX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cblxuICAgICAgICAgICAgICAgICAgICAgIHsvKiBTaG93IE1vcmUgQ29tbWVudHMgKi99XG4gICAgICAgICAgICAgICAgICAgICAge3Bvc3QuY29tbWVudHMubGVuZ3RoID4gMyAmJiAhc2hvd0FsbENvbW1lbnRzW3Bvc3QuaWRdICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0FsbENvbW1lbnRzKHByZXYgPT4gKHsgLi4ucHJldiwgW3Bvc3QuaWRdOiB0cnVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgYmFja2dyb3VuZDogJ25vbmUnLCBib3JkZXI6ICdub25lJywgY29sb3I6ICcjNjU2NzZiJywgZm9udFNpemU6ICcwLjg3NXJlbScsIGN1cnNvcjogJ3BvaW50ZXInLCBtYXJnaW5Cb3R0b206ICcxcmVtJyB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICBWaWV3IHtwb3N0LmNvbW1lbnRzLmxlbmd0aCAtIDN9IG1vcmUgY29tbWVudHNcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAgICB7LyogQWRkIENvbW1lbnQgKi99XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzAuNzVyZW0nLCBhbGlnbkl0ZW1zOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBnZXRSb2xlQ29sb3IodXNlclJvbGUgfHwgJ3dvcmtlcicpLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzJyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcycmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZmxleFNocmluazogMFxuICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRVc2VySW5pdGlhbCgpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZsZXg6IDEsIHBvc2l0aW9uOiAncmVsYXRpdmUnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJXcml0ZSBhIGNvbW1lbnQuLi5cIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb21tZW50VGV4dHNbcG9zdC5pZF0gfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb21tZW50VGV4dHMocHJldiA9PiAoeyAuLi5wcmV2LCBbcG9zdC5pZF06IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbktleVByZXNzPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKGUua2V5ID09PSAnRW50ZXInKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhhbmRsZUNvbW1lbnRTdWJtaXQocG9zdC5pZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2YwZjJmNScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNzVyZW0gMXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvdXRsaW5lOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuODc1cmVtJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtjb21tZW50VGV4dHNbcG9zdC5pZF0gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUNvbW1lbnRTdWJtaXQocG9zdC5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmlnaHQ6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3A6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC01MCUpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMTg3N2YyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBQb3N0XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIClcbiAgICAgICAgICB9KX1cblxuICAgICAgICAgIHsvKiBObyBQb3N0cyBNZXNzYWdlICovfVxuICAgICAgICAgIHtwb3N0cy5sZW5ndGggPT09IDAgJiYgKFxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICd3aGl0ZScsIGJvcmRlclJhZGl1czogJzhweCcsIGJveFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsMCwwLDAuMSknLCBwYWRkaW5nOiAnM3JlbScsIHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFNpemU6ICczcmVtJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+8J+TnTwvZGl2PlxuICAgICAgICAgICAgICA8aDMgc3R5bGU9e3sgY29sb3I6ICcjMWMxZTIxJywgbWFyZ2luQm90dG9tOiAnMC41cmVtJyB9fT5ObyBwb3N0cyB5ZXQ8L2gzPlxuICAgICAgICAgICAgICA8cCBzdHlsZT17eyBjb2xvcjogJyM2NTY3NmInIH19PkJlIHRoZSBmaXJzdCB0byBzaGFyZSBzb21ldGhpbmcgd2l0aCB5b3VyIHByb2Zlc3Npb25hbCBuZXR3b3JrITwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBSaWdodCBTaWRlYmFyICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJ3doaXRlJywgYm9yZGVyUmFkaXVzOiAnOHB4JywgcGFkZGluZzogJzFyZW0nLCBoZWlnaHQ6ICdmaXQtY29udGVudCcsIGJveFNoYWRvdzogJzAgMXB4IDJweCByZ2JhKDAsMCwwLDAuMSknIH19PlxuICAgICAgICAgIDxoMyBzdHlsZT17eyBmb250U2l6ZTogJzEuMTI1cmVtJywgZm9udFdlaWdodDogJzYwMCcsIGNvbG9yOiAnIzFjMWUyMScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgT25saW5lIE5vd1xuICAgICAgICAgIDwvaDM+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLCBnYXA6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC43NXJlbScgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcG9zaXRpb246ICdyZWxhdGl2ZScgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBiYWNrZ3JvdW5kQ29sb3I6ICcjN2MzYWVkJywgYm9yZGVyUmFkaXVzOiAnNTAlJywgd2lkdGg6ICcycmVtJywgaGVpZ2h0OiAnMnJlbScsIGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgY29sb3I6ICd3aGl0ZScsIGZvbnRTaXplOiAnMC44NzVyZW0nLCBmb250V2VpZ2h0OiAnYm9sZCcgfX0+XG4gICAgICAgICAgICAgICAgICBNXG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBwb3NpdGlvbjogJ2Fic29sdXRlJywgYm90dG9tOiAnLTJweCcsIHJpZ2h0OiAnLTJweCcsIGJhY2tncm91bmRDb2xvcjogJyM0MmI4ODMnLCBib3JkZXJSYWRpdXM6ICc1MCUnLCB3aWR0aDogJzAuNzVyZW0nLCBoZWlnaHQ6ICcwLjc1cmVtJywgYm9yZGVyOiAnMnB4IHNvbGlkIHdoaXRlJyB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxzcGFuIHN0eWxlPXt7IGZvbnRTaXplOiAnMC44NzVyZW0nLCBjb2xvcjogJyMxYzFlMjEnIH19Pk1vZGVybiBUZWNoIFNvbHV0aW9uczwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzAuNzVyZW0nIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHBvc2l0aW9uOiAncmVsYXRpdmUnIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgYmFja2dyb3VuZENvbG9yOiAnIzI1NjNlYicsIGJvcmRlclJhZGl1czogJzUwJScsIHdpZHRoOiAnMnJlbScsIGhlaWdodDogJzJyZW0nLCBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsIGNvbG9yOiAnd2hpdGUnLCBmb250U2l6ZTogJzAuODc1cmVtJywgZm9udFdlaWdodDogJ2JvbGQnIH19PlxuICAgICAgICAgICAgICAgICAgRlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcG9zaXRpb246ICdhYnNvbHV0ZScsIGJvdHRvbTogJy0ycHgnLCByaWdodDogJy0ycHgnLCBiYWNrZ3JvdW5kQ29sb3I6ICcjNDJiODgzJywgYm9yZGVyUmFkaXVzOiAnNTAlJywgd2lkdGg6ICcwLjc1cmVtJywgaGVpZ2h0OiAnMC43NXJlbScsIGJvcmRlcjogJzJweCBzb2xpZCB3aGl0ZScgfX0+PC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250U2l6ZTogJzAuODc1cmVtJywgY29sb3I6ICcjMWMxZTIxJyB9fT5GYXRpbWEgQWwtWmFocmE8L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBwb3NpdGlvbjogJ3JlbGF0aXZlJyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGJhY2tncm91bmRDb2xvcjogJyMwNTk2NjknLCBib3JkZXJSYWRpdXM6ICc1MCUnLCB3aWR0aDogJzJyZW0nLCBoZWlnaHQ6ICcycmVtJywgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLCBjb2xvcjogJ3doaXRlJywgZm9udFNpemU6ICcwLjg3NXJlbScsIGZvbnRXZWlnaHQ6ICdib2xkJyB9fT5cbiAgICAgICAgICAgICAgICAgIEtcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHBvc2l0aW9uOiAnYWJzb2x1dGUnLCBib3R0b206ICctMnB4JywgcmlnaHQ6ICctMnB4JywgYmFja2dyb3VuZENvbG9yOiAnIzQyYjg4MycsIGJvcmRlclJhZGl1czogJzUwJScsIHdpZHRoOiAnMC43NXJlbScsIGhlaWdodDogJzAuNzVyZW0nLCBib3JkZXI6ICcycHggc29saWQgd2hpdGUnIH19PjwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFNpemU6ICcwLjg3NXJlbScsIGNvbG9yOiAnIzFjMWUyMScgfX0+S2hhbGlkIENvbnN0cnVjdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMnJlbScsIHBhZGRpbmdUb3A6ICcxcmVtJywgYm9yZGVyVG9wOiAnMXB4IHNvbGlkICNlNGU2ZWEnIH19PlxuICAgICAgICAgICAgPGEgaHJlZj1cIi9cIiBzdHlsZT17eyBjb2xvcjogJyMxODc3ZjInLCB0ZXh0RGVjb3JhdGlvbjogJ25vbmUnLCBmb250U2l6ZTogJzAuODc1cmVtJyB9fT7ihpAgQmFjayB0byBIb21lPC9hPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQ3JlYXRlIFBvc3QgTW9kYWwgKi99XG4gICAgICB7c2hvd0NyZWF0ZVBvc3QgJiYgKFxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgIGxlZnQ6IDAsXG4gICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgYm90dG9tOiAwLFxuICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC44KScsXG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICB6SW5kZXg6IDEwMDBcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgIGJveFNoYWRvdzogJzAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgOHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcbiAgICAgICAgICAgIHdpZHRoOiAnNTAwcHgnLFxuICAgICAgICAgICAgbWF4SGVpZ2h0OiAnOTB2aCcsXG4gICAgICAgICAgICBvdmVyZmxvdzogJ2F1dG8nXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICB7LyogTW9kYWwgSGVhZGVyICovfVxuICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICBwYWRkaW5nOiAnMXJlbScsXG4gICAgICAgICAgICAgIGJvcmRlckJvdHRvbTogJzFweCBzb2xpZCAjZTRlNmVhJyxcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJ1xuICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgIDxoMiBzdHlsZT17eyBmb250U2l6ZTogJzEuMjVyZW0nLCBmb250V2VpZ2h0OiAnNjAwJywgY29sb3I6ICcjMWMxZTIxJywgbWFyZ2luOiAwIH19PlxuICAgICAgICAgICAgICAgIENyZWF0ZSBQb3N0XG4gICAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlUG9zdChmYWxzZSl9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmMGYyZjUnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgd2lkdGg6ICcyLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMjVyZW0nLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjNjU2NzZiJ1xuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDinJVcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFVzZXIgSW5mbyAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzFyZW0nLCBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICcwLjc1cmVtJyB9fT5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdXNlclJvbGUgPT09ICd3b3JrZXInID8gJyMyNTYzZWInIDogdXNlclJvbGUgPT09ICdzdXBwbGllcicgPyAnIzA1OTY2OScgOiAnIzdjM2FlZCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICB3aWR0aDogJzIuNXJlbScsXG4gICAgICAgICAgICAgICAgaGVpZ2h0OiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7Z2V0VXNlckluaXRpYWwoKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250V2VpZ2h0OiAnNjAwJywgY29sb3I6ICcjMWMxZTIxJyB9fT57Z2V0VXNlckRpc3BsYXlOYW1lKCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzAuODc1cmVtJywgY29sb3I6ICcjNjU2NzZiJywgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMC4yNXJlbScgfX0+XG4gICAgICAgICAgICAgICAgICB7Z2V0VXNlclJvbGVFbW9qaSgpfSB7dXNlclJvbGUgPyB1c2VyUm9sZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHVzZXJSb2xlLnNsaWNlKDEpIDogJ1VzZXInfSDigKIg8J+MjSBQdWJsaWNcbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFBvc3QgQ29udGVudCAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzAgMXJlbScgfX0+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIHZhbHVlPXtwb3N0VGV4dH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBvc3RUZXh0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIldoYXQncyBvbiB5b3VyIG1pbmQ/XCJcbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgIG1pbkhlaWdodDogJzEyMHB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgb3V0bGluZTogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMWMxZTIxJyxcbiAgICAgICAgICAgICAgICAgIHJlc2l6ZTogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgZm9udEZhbWlseTogJ2luaGVyaXQnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRmVlbGluZy9BY3Rpdml0eSAqL31cbiAgICAgICAgICAgIHtzZWxlY3RlZEZlZWxpbmcgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHBhZGRpbmc6ICcwIDFyZW0nLCBtYXJnaW5Cb3R0b206ICcxcmVtJyB9fT5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjBmMmY1JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzIwcHgnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNXJlbSAxcmVtJyxcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdpbmxpbmUtZmxleCcsXG4gICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgIGdhcDogJzAuNXJlbSdcbiAgICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICAgIDxzcGFuPntzZWxlY3RlZEZlZWxpbmd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZEZlZWxpbmcoJycpfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyM2NTY3NmInXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIOKclVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgey8qIEltYWdlIFByZXZpZXcgKi99XG4gICAgICAgICAgICB7c2VsZWN0ZWRJbWFnZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzAgMXJlbScsIG1hcmdpbkJvdHRvbTogJzFyZW0nIH19PlxuICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiAncmVsYXRpdmUnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2YwZjJmNScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzJyZW0nLFxuICAgICAgICAgICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJ1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzNyZW0nLCBtYXJnaW5Cb3R0b206ICcxcmVtJyB9fT7wn5O3PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnIzY1Njc2YicgfX0+SW1hZ2UgUHJldmlldyAoe3NlbGVjdGVkSW1hZ2VzLmxlbmd0aH0pPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkSW1hZ2VzKFtdKX1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICB0b3A6ICcwLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIHJpZ2h0OiAnMC41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICdyZ2JhKDAsMCwwLDAuNSknLFxuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcycmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcycmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJ1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDinJVcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHsvKiBBZGQgdG8gUG9zdCAqL31cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgcGFkZGluZzogJzFyZW0nLCBib3JkZXJUb3A6ICcxcHggc29saWQgI2U0ZTZlYScgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgbWFyZ2luQm90dG9tOiAnMXJlbScgfX0+XG4gICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFdlaWdodDogJzYwMCcsIGNvbG9yOiAnIzFjMWUyMScgfX0+QWRkIHRvIHlvdXIgcG9zdDwvc3Bhbj5cbiAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZ2FwOiAnMC41cmVtJyB9fT5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2VsZWN0ZWRJbWFnZXMoWydpbWFnZSddKX1cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzIuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4yNXJlbSdcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJQaG90by9WaWRlb1wiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIPCfk7dcbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTZWxlY3RlZEZlZWxpbmcoJ/CfmIogZmVlbGluZyBoYXBweScpfVxuICAgICAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6ICcyLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjI1cmVtJ1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkZlZWxpbmcvQWN0aXZpdHlcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDwn5iKXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNTAlJyxcbiAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogJzIuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnMi41cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4yNXJlbSdcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJUYWcgUGVvcGxlXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAg8J+RpVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgICAgICAgICAgd2lkdGg6ICcyLjVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogJzIuNXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMjVyZW0nXG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ2hlY2sgSW5cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICDwn5ONXG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFBvc3QgQnV0dG9uICovfVxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKCFwb3N0VGV4dC50cmltKCkpIHJldHVyblxuXG4gICAgICAgICAgICAgICAgICAvLyBDcmVhdGUgdGhlIHBvc3QgdXNpbmcgdGhlIHBvc3RzIGNvbnRleHRcbiAgICAgICAgICAgICAgICAgIGNvbnN0IG5ld1Bvc3QgPSB7XG4gICAgICAgICAgICAgICAgICAgIHVzZXJfaWQ6IHVzZXI/LmVtYWlsIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICB1c2VyX25hbWU6IGdldFVzZXJEaXNwbGF5TmFtZSgpLFxuICAgICAgICAgICAgICAgICAgICB1c2VyX3JvbGU6ICh1c2VyUm9sZSB8fCAnd29ya2VyJykgYXMgJ3dvcmtlcicgfCAnc3VwcGxpZXInIHwgJ2NvbXBhbnknLFxuICAgICAgICAgICAgICAgICAgICBjb250ZW50OiBwb3N0VGV4dCxcbiAgICAgICAgICAgICAgICAgICAgbWVkaWFfdXJsczogc2VsZWN0ZWRJbWFnZXMubGVuZ3RoID4gMCA/IHNlbGVjdGVkSW1hZ2VzIDogdW5kZWZpbmVkLFxuICAgICAgICAgICAgICAgICAgICBwb3N0X3R5cGU6IChzZWxlY3RlZEltYWdlcy5sZW5ndGggPiAwID8gJ2ltYWdlJyA6ICd0ZXh0JykgYXMgJ3RleHQnIHwgJ2ltYWdlJyB8ICd2aWRlbycgfCAnbGl2ZScgfCAnZG9jdW1lbnQnIHwgJ21peGVkJyxcbiAgICAgICAgICAgICAgICAgICAgdmlzaWJpbGl0eTogJ3B1YmxpYycgYXMgJ3B1YmxpYycgfCAnYmxvY2tfb25seSdcbiAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgYWRkUG9zdChuZXdQb3N0KVxuXG4gICAgICAgICAgICAgICAgICAvLyBSZXNldCBmb3JtXG4gICAgICAgICAgICAgICAgICBzZXRQb3N0VGV4dCgnJylcbiAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkRmVlbGluZygnJylcbiAgICAgICAgICAgICAgICAgIHNldFNlbGVjdGVkSW1hZ2VzKFtdKVxuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRMb2NhdGlvbignJylcbiAgICAgICAgICAgICAgICAgIHNldFRhZ2dlZFVzZXJzKFtdKVxuICAgICAgICAgICAgICAgICAgc2V0U2hvd0NyZWF0ZVBvc3QoZmFsc2UpXG5cbiAgICAgICAgICAgICAgICAgIC8vIFNob3cgc3VjY2VzcyBtZXNzYWdlXG4gICAgICAgICAgICAgICAgICBhbGVydCgnUG9zdCBjcmVhdGVkIHN1Y2Nlc3NmdWxseSEnKVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9eyFwb3N0VGV4dC50cmltKCl9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHBvc3RUZXh0LnRyaW0oKSA/ICcjMTg3N2YyJyA6ICcjZTRlNmVhJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBwb3N0VGV4dC50cmltKCkgPyAnd2hpdGUnIDogJyNiY2MwYzQnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc2cHgnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICc2MDAnLFxuICAgICAgICAgICAgICAgICAgY3Vyc29yOiBwb3N0VGV4dC50cmltKCkgPyAncG9pbnRlcicgOiAnbm90LWFsbG93ZWQnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIFBvc3RcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZUF1dGgiLCJ1c2VQb3N0cyIsInVzZVJvdXRlciIsIkZlZWRQYWdlIiwidXNlciIsInVzZXJSb2xlIiwiZ2V0VXNlclByb2ZpbGUiLCJsb2FkaW5nIiwic2lnbk91dCIsInBvc3RzIiwiYWRkUG9zdCIsImxpa2VQb3N0IiwiYWRkQ29tbWVudCIsInNoYXJlUG9zdCIsInJvdXRlciIsInNob3dDcmVhdGVQb3N0Iiwic2V0U2hvd0NyZWF0ZVBvc3QiLCJwb3N0VGV4dCIsInNldFBvc3RUZXh0Iiwic2VsZWN0ZWRGZWVsaW5nIiwic2V0U2VsZWN0ZWRGZWVsaW5nIiwic2VsZWN0ZWRJbWFnZXMiLCJzZXRTZWxlY3RlZEltYWdlcyIsInNlbGVjdGVkTG9jYXRpb24iLCJzZXRTZWxlY3RlZExvY2F0aW9uIiwidGFnZ2VkVXNlcnMiLCJzZXRUYWdnZWRVc2VycyIsInBvc3RQcml2YWN5Iiwic2V0UG9zdFByaXZhY3kiLCJzaG93Q29tbWVudHMiLCJzZXRTaG93Q29tbWVudHMiLCJjb21tZW50VGV4dHMiLCJzZXRDb21tZW50VGV4dHMiLCJzaG93QWxsQ29tbWVudHMiLCJzZXRTaG93QWxsQ29tbWVudHMiLCJzaG93U2hhcmVNb2RhbCIsInNldFNob3dTaGFyZU1vZGFsIiwic2hhcmVUZXh0Iiwic2V0U2hhcmVUZXh0IiwidXNlclByb2ZpbGUiLCJzZXRVc2VyUHJvZmlsZSIsImFjdGl2ZVN0b3J5Iiwic2V0QWN0aXZlU3RvcnkiLCJzaG93RW1vamlQaWNrZXIiLCJzZXRTaG93RW1vamlQaWNrZXIiLCJzaG93TG9jYXRpb25QaWNrZXIiLCJzZXRTaG93TG9jYXRpb25QaWNrZXIiLCJzaG93VGFnUGlja2VyIiwic2V0U2hvd1RhZ1BpY2tlciIsInByb2ZpbGUiLCJjb25zb2xlIiwibG9nIiwicHVzaCIsImdldFVzZXJEaXNwbGF5TmFtZSIsInBlcnNvbmFsSW5mbyIsIndvcmtlck5hbWUiLCJzdXBwbGllck5hbWUiLCJjb21wYW55SW5mbyIsImNvbXBhbnlOYW1lIiwiZ2V0VXNlckluaXRpYWwiLCJuYW1lIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJnZXRVc2VyUm9sZUVtb2ppIiwiaGFuZGxlTGlrZVBvc3QiLCJwb3N0SWQiLCJoYW5kbGVDb21tZW50U3VibWl0IiwiY29tbWVudFRleHQiLCJ0cmltIiwiaWQiLCJlbWFpbCIsInJvbGUiLCJwcmV2IiwiaGFuZGxlU2hhcmVQb3N0Iiwib3JpZ2luYWxQb3N0IiwiZmluZCIsInAiLCJuZXdQb3N0IiwidXNlcl9pZCIsInVzZXJfbmFtZSIsInVzZXJfcm9sZSIsImNvbnRlbnQiLCJwb3N0X3R5cGUiLCJ2aXNpYmlsaXR5IiwidG9nZ2xlQ29tbWVudHMiLCJmb3JtYXRUaW1lQWdvIiwiZGF0ZVN0cmluZyIsIm5vdyIsIkRhdGUiLCJwb3N0RGF0ZSIsImRpZmZJbk1pbnV0ZXMiLCJNYXRoIiwiZmxvb3IiLCJnZXRUaW1lIiwiZGlmZkluSG91cnMiLCJkaWZmSW5EYXlzIiwiZGlmZkluV2Vla3MiLCJnZXRSb2xlQ29sb3IiLCJnZXRSb2xlRW1vamkiLCJkaXYiLCJzdHlsZSIsIm1pbkhlaWdodCIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJ0ZXh0QWxpZ24iLCJmb250U2l6ZSIsIm1hcmdpbkJvdHRvbSIsImNvbG9yIiwicGFkZGluZyIsImJveFNoYWRvdyIsIm1heFdpZHRoIiwibWFyZ2luIiwiZ2FwIiwiaDEiLCJmb250V2VpZ2h0IiwiYm9yZGVyUmFkaXVzIiwiaW5wdXQiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJib3JkZXIiLCJvdXRsaW5lIiwid2lkdGgiLCJoZWlnaHQiLCJzcGFuIiwiYnV0dG9uIiwib25DbGljayIsInByb2ZpbGVQYXRoIiwiY3Vyc29yIiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsImgzIiwiZmxleERpcmVjdGlvbiIsImEiLCJocmVmIiwidGV4dERlY29yYXRpb24iLCJvdmVyZmxvd1giLCJwYWRkaW5nQm90dG9tIiwibWluV2lkdGgiLCJiYWNrZ3JvdW5kIiwicG9zaXRpb24iLCJ0b3AiLCJsZWZ0IiwibWFwIiwiaW5kZXgiLCJyZWFkT25seSIsImZsZXgiLCJwYWRkaW5nVG9wIiwiYm9yZGVyVG9wIiwicG9zdCIsImVuY29kZVVSSUNvbXBvbmVudCIsIm9uTW91c2VFbnRlciIsImUiLCJ0YXJnZXQiLCJvbk1vdXNlTGVhdmUiLCJjcmVhdGVkX2F0Iiwic2xpY2UiLCJwYWRkaW5nTGVmdCIsInBhZGRpbmdSaWdodCIsImxpbmVIZWlnaHQiLCJtZWRpYV91cmxzIiwibGVuZ3RoIiwibGlrZXMiLCJjb21tZW50cyIsInNoYXJlcyIsImxpa2VkX2J5X3VzZXIiLCJjdXJyZW50VGFyZ2V0IiwibWFyZ2luVG9wIiwidW5kZWZpbmVkIiwiY29tbWVudCIsImZsZXhTaHJpbmsiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwib25LZXlQcmVzcyIsImtleSIsInJpZ2h0IiwidHJhbnNmb3JtIiwiYm90dG9tIiwiekluZGV4IiwibWF4SGVpZ2h0Iiwib3ZlcmZsb3ciLCJib3JkZXJCb3R0b20iLCJoMiIsInRleHRhcmVhIiwicmVzaXplIiwiZm9udEZhbWlseSIsInRpdGxlIiwiYWxlcnQiLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});