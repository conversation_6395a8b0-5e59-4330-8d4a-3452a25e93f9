module.exports = {

"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/utils.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-ssr] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
}}),
"[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardAction": (()=>CardAction),
    "CardContent": (()=>CardContent),
    "CardDescription": (()=>CardDescription),
    "CardFooter": (()=>CardFooter),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
function Card({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
function CardHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
function CardTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
function CardDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
function CardAction({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-action",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("col-start-2 row-span-2 row-start-1 self-start justify-self-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
function CardContent({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-content",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("px-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
function CardFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center px-6 [.border-t]:pt-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/components/ui/input.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Input = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, type, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/input.tsx",
        lineNumber: 11,
        columnNumber: 7
    }, this);
});
Input.displayName = "Input";
;
}}),
"[project]/src/components/ui/textarea.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 11,
        columnNumber: 7
    }, this);
});
Textarea.displayName = "Textarea";
;
}}),
"[project]/src/utils/zatcaQR.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// ZATCA QR Code Generation Utility
// Following ZATCA TLV (Tag-Length-Value) format specifications
__turbopack_context__.s({
    "ZATCAUtils": (()=>ZATCAUtils),
    "ZATCA_TAGS": (()=>ZATCA_TAGS),
    "formatCurrencyForZATCA": (()=>formatCurrencyForZATCA),
    "generateInvoiceHash": (()=>generateInvoiceHash),
    "generateQRCodeURL": (()=>generateQRCodeURL),
    "generateSampleZATCAQR": (()=>generateSampleZATCAQR),
    "generateTLV": (()=>generateTLV),
    "generateZATCAQRCode": (()=>generateZATCAQRCode),
    "generateZATCATimestamp": (()=>generateZATCATimestamp),
    "validateZATCAQRData": (()=>validateZATCAQRData)
});
const ZATCA_TAGS = {
    SELLER_NAME: 1,
    VAT_NUMBER: 2,
    TIMESTAMP: 3,
    INVOICE_TOTAL: 4,
    VAT_TOTAL: 5,
    INVOICE_HASH: 6
};
function generateTLV(tag, value) {
    const valueBytes = new TextEncoder().encode(value);
    const length = valueBytes.length;
    // Create TLV structure: [Tag][Length][Value]
    const tlv = new Uint8Array(2 + length);
    tlv[0] = tag;
    tlv[1] = length;
    tlv.set(valueBytes, 2);
    return tlv;
}
function generateZATCAQRCode(data) {
    try {
        // Generate TLV for each required field
        const sellerNameTLV = generateTLV(ZATCA_TAGS.SELLER_NAME, data.sellerName);
        const vatNumberTLV = generateTLV(ZATCA_TAGS.VAT_NUMBER, data.vatNumber);
        const timestampTLV = generateTLV(ZATCA_TAGS.TIMESTAMP, data.timestamp);
        const invoiceTotalTLV = generateTLV(ZATCA_TAGS.INVOICE_TOTAL, data.invoiceTotal);
        const vatTotalTLV = generateTLV(ZATCA_TAGS.VAT_TOTAL, data.vatTotal);
        // Calculate total length
        let totalLength = sellerNameTLV.length + vatNumberTLV.length + timestampTLV.length + invoiceTotalTLV.length + vatTotalTLV.length;
        // Add invoice hash if provided
        let invoiceHashTLV = null;
        if (data.invoiceHash) {
            invoiceHashTLV = generateTLV(ZATCA_TAGS.INVOICE_HASH, data.invoiceHash);
            totalLength += invoiceHashTLV.length;
        }
        // Combine all TLV data
        const qrData = new Uint8Array(totalLength);
        let offset = 0;
        qrData.set(sellerNameTLV, offset);
        offset += sellerNameTLV.length;
        qrData.set(vatNumberTLV, offset);
        offset += vatNumberTLV.length;
        qrData.set(timestampTLV, offset);
        offset += timestampTLV.length;
        qrData.set(invoiceTotalTLV, offset);
        offset += invoiceTotalTLV.length;
        qrData.set(vatTotalTLV, offset);
        offset += vatTotalTLV.length;
        if (invoiceHashTLV) {
            qrData.set(invoiceHashTLV, offset);
        }
        // Convert to Base64
        return btoa(String.fromCharCode(...qrData));
    } catch (error) {
        console.error('Error generating ZATCA QR code:', error);
        throw new Error('Failed to generate ZATCA QR code');
    }
}
async function generateInvoiceHash(invoiceData) {
    try {
        // Convert invoice data to string (in production, use XML canonicalization)
        const dataString = JSON.stringify(invoiceData);
        // Generate SHA-256 hash
        const encoder = new TextEncoder();
        const data = encoder.encode(dataString);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        // Convert to Base64
        const hashArray = new Uint8Array(hashBuffer);
        return btoa(String.fromCharCode(...hashArray));
    } catch (error) {
        console.error('Error generating invoice hash:', error);
        throw new Error('Failed to generate invoice hash');
    }
}
function validateZATCAQRData(data) {
    const errors = [];
    // Validate seller name
    if (!data.sellerName || data.sellerName.trim().length === 0) {
        errors.push('Seller name is required');
    }
    // Validate VAT number (should be 15 digits)
    if (!data.vatNumber || !/^\d{15}$/.test(data.vatNumber)) {
        errors.push('VAT number must be exactly 15 digits');
    }
    // Validate timestamp (ISO format)
    if (!data.timestamp || isNaN(Date.parse(data.timestamp))) {
        errors.push('Valid timestamp is required');
    }
    // Validate invoice total (should be numeric)
    if (!data.invoiceTotal || isNaN(parseFloat(data.invoiceTotal))) {
        errors.push('Valid invoice total is required');
    }
    // Validate VAT total (should be numeric)
    if (!data.vatTotal || isNaN(parseFloat(data.vatTotal))) {
        errors.push('Valid VAT total is required');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
function generateQRCodeURL(qrData, size = 200) {
    // Using QR Server API (in production, consider using a local QR generator)
    const encodedData = encodeURIComponent(qrData);
    return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodedData}`;
}
function formatCurrencyForZATCA(amount, currency = 'SAR') {
    return amount.toFixed(2);
}
function generateZATCATimestamp(date = new Date()) {
    return date.toISOString();
}
function generateSampleZATCAQR() {
    const sampleData = {
        sellerName: 'KAAZMAAMAA Company',
        vatNumber: '301234567890003',
        timestamp: generateZATCATimestamp(),
        invoiceTotal: '1150.00',
        vatTotal: '150.00'
    };
    return generateZATCAQRCode(sampleData);
}
const ZATCAUtils = {
    generateTLV,
    generateZATCAQRCode,
    generateInvoiceHash,
    validateZATCAQRData,
    generateQRCodeURL,
    formatCurrencyForZATCA,
    generateZATCATimestamp,
    generateSampleZATCAQR,
    ZATCA_TAGS
};
}}),
"[project]/src/utils/numberToWords.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Number to Words Converter for Invoice Amounts
__turbopack_context__.s({
    "NumberUtils": (()=>NumberUtils),
    "amountToWords": (()=>amountToWords),
    "formatAmountInWords": (()=>formatAmountInWords),
    "numberToWords": (()=>numberToWords)
});
const ones = [
    '',
    'One',
    'Two',
    'Three',
    'Four',
    'Five',
    'Six',
    'Seven',
    'Eight',
    'Nine',
    'Ten',
    'Eleven',
    'Twelve',
    'Thirteen',
    'Fourteen',
    'Fifteen',
    'Sixteen',
    'Seventeen',
    'Eighteen',
    'Nineteen'
];
const tens = [
    '',
    '',
    'Twenty',
    'Thirty',
    'Forty',
    'Fifty',
    'Sixty',
    'Seventy',
    'Eighty',
    'Ninety'
];
const scales = [
    '',
    'Thousand',
    'Million',
    'Billion',
    'Trillion'
];
/**
 * Convert a number (0-999) to words
 * @param num - Number to convert
 * @returns String representation
 */ function convertHundreds(num) {
    let result = '';
    if (num >= 100) {
        result += ones[Math.floor(num / 100)] + ' Hundred';
        num %= 100;
        if (num > 0) result += ' ';
    }
    if (num >= 20) {
        result += tens[Math.floor(num / 10)];
        num %= 10;
        if (num > 0) result += ' ';
    }
    if (num > 0) {
        result += ones[num];
    }
    return result;
}
function numberToWords(num) {
    if (num === 0) return 'Zero';
    let result = '';
    let scaleIndex = 0;
    while(num > 0){
        const chunk = num % 1000;
        if (chunk !== 0) {
            const chunkWords = convertHundreds(chunk);
            if (scaleIndex > 0) {
                result = chunkWords + ' ' + scales[scaleIndex] + (result ? ' ' + result : '');
            } else {
                result = chunkWords;
            }
        }
        num = Math.floor(num / 1000);
        scaleIndex++;
    }
    return result;
}
function amountToWords(amount, currency = 'SAR') {
    const wholePart = Math.floor(amount);
    const decimalPart = Math.round((amount - wholePart) * 100);
    let result = numberToWords(wholePart);
    // Add currency name
    const currencyNames = {
        'SAR': {
            singular: 'Riyal',
            plural: 'Riyals',
            subunit: 'Halala'
        },
        'USD': {
            singular: 'Dollar',
            plural: 'Dollars',
            subunit: 'Cent'
        },
        'EUR': {
            singular: 'Euro',
            plural: 'Euros',
            subunit: 'Cent'
        },
        'AED': {
            singular: 'Dirham',
            plural: 'Dirhams',
            subunit: 'Fils'
        }
    };
    const currencyInfo = currencyNames[currency] || {
        singular: currency,
        plural: currency + 's',
        subunit: 'Cent'
    };
    if (wholePart === 1) {
        result += ' ' + currencyInfo.singular;
    } else {
        result += ' ' + currencyInfo.plural;
    }
    // Add decimal part if exists
    if (decimalPart > 0) {
        result += ' and ' + numberToWords(decimalPart);
        if (decimalPart === 1) {
            result += ' ' + currencyInfo.subunit;
        } else {
            result += ' ' + currencyInfo.subunit + 's';
        }
    }
    return result + ' Only';
}
function formatAmountInWords(amount, currency = 'SAR') {
    return amountToWords(amount, currency);
}
const NumberUtils = {
    numberToWords,
    amountToWords,
    formatAmountInWords
};
}}),
"[project]/src/utils/pdfGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// PDF Generation Utility for ZATCA Invoices - HD Quality Version
// Enhanced with professional styling and textbox system
__turbopack_context__.s({
    "PDFUtils": (()=>PDFUtils),
    "downloadInvoicePDF": (()=>downloadInvoicePDF),
    "generateInvoiceHTML": (()=>generateInvoiceHTML),
    "generateInvoicePDFBlob": (()=>generateInvoicePDFBlob),
    "printInvoice": (()=>printInvoice)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$numberToWords$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/numberToWords.ts [app-ssr] (ecmascript)");
;
function generateInvoiceHTML(data) {
    const discountAmount = data.subtotal * data.discount / 100;
    const subtotalAfterDiscount = data.subtotal - discountAmount;
    // NEW: Use the passed subtotalAfterDeduction which includes deduction amount logic
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${data.invoiceNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Segoe UI', 'Arial', sans-serif;
          font-size: 11px;
          line-height: 1.5;
          color: #1f2937;
          background: white;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          print-color-adjust: exact;
        }

        .invoice-container {
          max-width: 850px;
          margin: 0 auto;
          padding: 30px;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          border: 1px solid #e5e7eb;
          width: 100%;
          box-sizing: border-box;
          overflow-x: hidden;
        }

        .header {
          display: grid;
          grid-template-columns: 1fr auto 1fr;
          align-items: center;
          margin-bottom: 35px;
          padding: 25px;
          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
          border-radius: 12px;
          gap: 25px;
          box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);
        }

        .qr-header {
          text-align: center;
          justify-self: center;
        }

        .qr-code-top {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .logo-section h1 {
          color: white;
          font-size: 26px;
          font-weight: 700;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .logo-section p {
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          font-weight: 500;
        }

        .invoice-title {
          text-align: right;
        }

        .invoice-title h2 {
          color: white;
          font-size: 32px;
          font-weight: 800;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          letter-spacing: 1px;
        }

        .invoice-number {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          font-weight: 600;
        }

        .details-section {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 30px;
          margin-bottom: 30px;
        }

        .detail-card {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #A78BFA;
          border: 1px solid #e5e7eb;
          overflow: hidden;
          word-wrap: break-word;
        }

        .detail-card h3 {
          color: #A78BFA;
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 15px;
          text-align: center;
          background: #A78BFA;
          color: white;
          padding: 8px;
          margin: -15px -15px 15px -15px;
          border-radius: 7px 7px 0 0;
        }

        .textbox-container {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          margin-bottom: 10px;
        }

        .textbox-full {
          grid-column: 1 / -1;
        }

        .textbox {
          border: 1.5px solid #d1d5db;
          border-radius: 6px;
          padding: 10px 12px;
          background: #ffffff;
          font-size: 11px;
          min-height: 24px;
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
          font-weight: 500;
          color: #374151;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
        }

        .textbox-label {
          font-size: 9px;
          color: #6b7280;
          font-weight: 600;
          margin-bottom: 4px;
          display: block;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .textbox-large {
          min-height: 50px;
          line-height: 1.4;
          padding: 12px;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
          max-height: 80px;
        }

        .textbox-address {
          min-height: 45px;
          line-height: 1.3;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
          max-height: 75px;
          font-size: 10px;
          padding: 10px;
        }

        .textbox-amount {
          font-weight: 700;
          color: #059669;
          font-size: 12px;
          text-align: right;
        }

        .seller-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 15px;
        }

        .seller-info {
          flex: 1;
        }

        .seller-logo {
          width: 80px;
          text-align: center;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          background: white;
          border: 1px solid #e5e7eb;
        }

        .items-table th {
          background: #A78BFA;
          color: white;
          padding: 12px 8px;
          text-align: left;
          font-weight: bold;
          font-size: 11px;
        }

        .items-table td {
          padding: 10px 8px;
          border-bottom: 1px solid #e5e7eb;
          font-size: 11px;
        }

        .items-table tr:nth-child(even) {
          background: #f9fafb;
        }

        .summary-section {
          display: grid;
          grid-template-columns: 280px 1fr 1fr;
          gap: 20px;
          margin-bottom: 30px;
          max-width: 100%;
          overflow: hidden;
        }

        .notes-section, .bank-section {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }

        .summary-card {
          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
          color: white;
          padding: 15px;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
          max-width: 280px;
          width: 100%;
          box-sizing: border-box;
        }

        .summary-card h4 {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 12px;
        }

        .summary-total {
          border-top: 2px solid rgba(255,255,255,0.3);
          padding-top: 10px;
          margin-top: 10px;
          font-size: 16px;
          font-weight: bold;
        }



        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 10px;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }

        @media print {
          body { margin: 0; }
          .invoice-container { padding: 0; }
          .summary-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .summary-card {
            max-width: 100%;
          }
        }

        @media (max-width: 768px) {
          .summary-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .summary-card {
            max-width: 100%;
          }
          .invoice-container {
            padding: 15px;
          }
          .details-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .textbox-address {
            font-size: 9px;
            max-height: 60px;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header with QR Code -->
        <div class="header">
          <div class="logo-section">
            <h1>🧾 ZATCA Invoice Generator</h1>
            <p>Create and manage your invoices with ease</p>
          </div>

          <!-- QR Code in Center Top -->
          <div class="qr-header">
            <div class="qr-code-top">
              <img src="https://api.qrserver.com/v1/create-qr-code/?size=130x130&data=${encodeURIComponent(data.qrCode)}"
                   alt="ZATCA QR Code" style="width: 130px; height: 130px; border: 3px solid white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);" />
            </div>
            <p style="font-size: 11px; color: rgba(255, 255, 255, 0.9); margin-top: 8px; text-align: center; font-weight: 600;">ZATCA QR Code</p>
          </div>

          <div class="invoice-title">
            <h2>INVOICE</h2>
            <p class="invoice-number">${data.invoiceNumber}</p>
          </div>
        </div>

        <!-- Details Section -->
        <div class="details-section">
          <div class="detail-card">
            <h3>📋 Invoice Details</h3>
            <div class="textbox-container">
              <div>
                <span class="textbox-label">Invoice Number</span>
                <div class="textbox">${data.invoiceNumber}</div>
              </div>
              <div>
                <span class="textbox-label">Currency</span>
                <div class="textbox">${data.currency}</div>
              </div>
              <div>
                <span class="textbox-label">Invoice Date</span>
                <div class="textbox">${new Date(data.invoiceDate).toLocaleDateString()}</div>
              </div>
              <div>
                <span class="textbox-label">Due Date</span>
                <div class="textbox">${data.dueDate ? new Date(data.dueDate).toLocaleDateString() : 'N/A'}</div>
              </div>
              ${data.discount > 0 ? `
                <div class="textbox-full">
                  <span class="textbox-label">Discount Applied</span>
                  <div class="textbox">${data.discount}% discount applied to subtotal</div>
                </div>
              ` : ''}
              ${data.deductionAmount > 0 ? `
                <div class="textbox-full">
                  <span class="textbox-label">⚠️ Deduction Amount</span>
                  <div class="textbox" style="color: #ea580c; font-weight: 600; background: #fef3f2; border: 1px solid #fecaca;">${data.currency} ${data.deductionAmount.toFixed(2)} deducted before VAT calculation</div>
                </div>
                ${data.deductionReason ? `
                  <div class="textbox-full">
                    <span class="textbox-label">📝 Deduction Reason</span>
                    <div class="textbox" style="color: #374151; font-weight: 500; background: #fffbeb; border: 1px solid #fed7aa; padding: 12px; line-height: 1.4;">${data.deductionReason}</div>
                  </div>
                ` : ''}
              ` : ''}
            </div>
          </div>

          <div class="detail-card">
            <h3>🏢 Billed From (Seller)</h3>
            <div class="seller-container">
              <div class="seller-info">
                <div class="textbox-container">
                  <div class="textbox-full">
                    <span class="textbox-label">Company Name</span>
                    <div class="textbox">${data.sellerName}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">Company Address</span>
                    <div class="textbox textbox-address">${data.sellerAddress.replace(/\n/g, '<br>')}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">VAT Registration Number</span>
                    <div class="textbox">${data.sellerVAT}</div>
                  </div>
                </div>
              </div>
              ${data.sellerLogo ? `
                <div class="seller-logo">
                  <span class="textbox-label">Company Logo</span>
                  <div class="textbox" style="text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;">
                    <img src="${data.sellerLogo}" alt="Company Logo"
                         style="max-width: 70px; max-height: 60px; object-fit: contain;" />
                  </div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="detail-card">
            <h3>👤 Billed To (Client)</h3>
            <div class="seller-container">
              <div class="seller-info">
                <div class="textbox-container">
                  <div class="textbox-full">
                    <span class="textbox-label">Client Name</span>
                    <div class="textbox">${data.clientName}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">Client Address</span>
                    <div class="textbox textbox-address">${data.clientAddress.replace(/\n/g, '<br>')}</div>
                  </div>
                  ${data.clientVAT ? `
                    <div class="textbox-full">
                      <span class="textbox-label">Client VAT Registration Number</span>
                      <div class="textbox">${data.clientVAT}</div>
                    </div>
                  ` : `
                    <div class="textbox-full">
                      <span class="textbox-label">Client VAT Registration Number</span>
                      <div class="textbox" style="color: #9ca3af; font-style: italic;">Not provided</div>
                    </div>
                  `}
                </div>
              </div>
              ${data.clientLogo ? `
                <div class="seller-logo">
                  <span class="textbox-label">Client Logo</span>
                  <div class="textbox" style="text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;">
                    <img src="${data.clientLogo}" alt="Client Logo"
                         style="max-width: 70px; max-height: 60px; object-fit: contain;" />
                  </div>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40%">Description</th>
              <th style="width: 10%">Qty</th>
              <th style="width: 15%">Rate</th>
              <th style="width: 10%">VAT %</th>
              <th style="width: 15%">Amount (${data.currency})</th>
            </tr>
          </thead>
          <tbody>
            ${data.items.map((item)=>`
              <tr>
                <td>${item.description}</td>
                <td>${item.quantity}</td>
                <td>${item.rate.toFixed(2)}</td>
                <td>${item.vatPercent}%</td>
                <td>${item.amount.toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Summary Section -->
        <div class="summary-section">
          <div class="summary-card">
            <h4 style="color: white; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: rgba(255,255,255,0.25); padding: 8px; margin: -15px -15px 15px -15px; border-radius: 8px 8px 0 0;">💰 Invoice Summary</h4>
            <div class="textbox-container" style="gap: 8px; grid-template-columns: 1fr;">
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Subtotal</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;">${data.currency} ${data.subtotal.toFixed(2)}</div>
              </div>
              ${data.discount > 0 ? `
                <div>
                  <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Discount (${data.discount}%)</span>
                  <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #dc2626; padding: 8px; font-size: 10px;">-${data.currency} ${discountAmount.toFixed(2)}</div>
                </div>
              ` : ''}
              ${data.deductionAmount > 0 ? `
                <div>
                  <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Deduction Amount</span>
                  <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #ea580c; padding: 8px; font-size: 10px;">-${data.currency} ${data.deductionAmount.toFixed(2)}</div>
                </div>
                ${data.deductionReason ? `
                  <div>
                    <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Deduction Reason</span>
                    <div class="textbox" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #6b7280; font-style: italic; padding: 8px; font-size: 9px; line-height: 1.3;">${data.deductionReason}</div>
                  </div>
                ` : ''}
              ` : ''}
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Amount Before VAT</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px; font-weight: 600;">${data.currency} ${data.subtotalAfterDeduction.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Total VAT (15%)</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;">${data.currency} ${data.totalVAT.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Total Amount</span>
                <div class="textbox" style="background: #10b981; border: 2px solid #059669; color: white; font-weight: 700; font-size: 14px; text-align: center; padding: 10px;">${data.currency} ${data.totalAmount.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Amount in Words</span>
                <div class="textbox" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); font-style: italic; color: #374151; text-align: center; font-weight: 600; padding: 8px; font-size: 9px; line-height: 1.3;">${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$numberToWords$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["formatAmountInWords"])(data.totalAmount, data.currency)}</div>
              </div>
            </div>
          </div>

          <div class="bank-section">
            <h4 style="color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;">🏦 Bank Details</h4>
            <div class="textbox-container">
              ${data.accountName ? `
                <div>
                  <span class="textbox-label">Account Name</span>
                  <div class="textbox">${data.accountName}</div>
                </div>
              ` : ''}
              ${data.bankName ? `
                <div>
                  <span class="textbox-label">Bank Name</span>
                  <div class="textbox">${data.bankName}</div>
                </div>
              ` : ''}
              ${data.accountNumber ? `
                <div>
                  <span class="textbox-label">Account Number</span>
                  <div class="textbox">${data.accountNumber}</div>
                </div>
              ` : ''}
              ${data.iban ? `
                <div>
                  <span class="textbox-label">IBAN</span>
                  <div class="textbox">${data.iban}</div>
                </div>
              ` : ''}
              ${!data.accountName && !data.bankName && !data.accountNumber && !data.iban ? `
                <div class="textbox-full">
                  <span class="textbox-label">Payment Information</span>
                  <div class="textbox" style="color: #9ca3af; font-style: italic;">No bank details provided</div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="notes-section">
            <h4 style="color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;">📝 Notes & Terms</h4>
            <div class="textbox-container" style="grid-template-columns: 1fr;">
              ${data.deductionAmount > 0 && data.deductionReason ? `
                <div class="textbox-full">
                  <span class="textbox-label">⚠️ Important: Deduction Applied</span>
                  <div class="textbox" style="background: #fef3f2; border: 2px solid #fecaca; color: #dc2626; font-weight: 600; padding: 12px; line-height: 1.4;">
                    A deduction of ${data.currency} ${data.deductionAmount.toFixed(2)} has been applied to this invoice.<br>
                    <strong>Reason:</strong> ${data.deductionReason}
                  </div>
                </div>
              ` : ''}
              ${data.notes ? `
                <div class="textbox-full">
                  <span class="textbox-label">Notes</span>
                  <div class="textbox textbox-large">${data.notes}</div>
                </div>
              ` : `
                <div class="textbox-full">
                  <span class="textbox-label">Notes</span>
                  <div class="textbox" style="color: #6b7280; font-style: italic; min-height: 30px; padding: 12px;">Thank you for your business!</div>
                </div>
              `}
              ${data.terms ? `
                <div class="textbox-full">
                  <span class="textbox-label">Terms & Conditions</span>
                  <div class="textbox textbox-large">${data.terms}</div>
                </div>
              ` : `
                <div class="textbox-full">
                  <span class="textbox-label">Terms & Conditions</span>
                  <div class="textbox textbox-large" style="color: #6b7280; font-style: normal; line-height: 1.6; padding: 15px;">
                    • Payment due within 30 days of invoice date<br>
                    • Late payments may incur additional charges<br>
                    • All prices are inclusive of applicable taxes<br>
                    • This invoice is computer generated and valid without signature
                  </div>
                </div>
              `}
            </div>
          </div>
        </div>



        <!-- Footer -->
        <div class="footer">
          <p>This invoice is generated by KAAZMAAMAA ZATCA Invoice System</p>
          <p>Compliant with Saudi Arabia's ZATCA e-invoicing regulations</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
function downloadInvoicePDF(data, filename) {
    const html = generateInvoiceHTML(data);
    const pdfFilename = filename || `invoice-${data.invoiceNumber}.pdf`;
    // Create a new window for PDF generation
    const printWindow = window.open('', '_blank');
    if (printWindow) {
        printWindow.document.write(html);
        printWindow.document.close();
        // Wait for content to load, then print
        printWindow.onload = ()=>{
            setTimeout(()=>{
                printWindow.print();
            // Don't close the window automatically - let user close it
            // printWindow.close()
            }, 500);
        };
    }
}
async function generateInvoicePDFBlob(data) {
    // This is a simplified version
    // In production, use libraries like jsPDF or Puppeteer for proper PDF generation
    const html = generateInvoiceHTML(data);
    const blob = new Blob([
        html
    ], {
        type: 'text/html'
    });
    return blob;
}
function printInvoice(data) {
    const html = generateInvoiceHTML(data);
    // Create a hidden iframe for printing
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);
    const doc = iframe.contentDocument || iframe.contentWindow?.document;
    if (doc) {
        doc.write(html);
        doc.close();
        iframe.onload = ()=>{
            iframe.contentWindow?.print();
            setTimeout(()=>{
                document.body.removeChild(iframe);
            }, 1000);
        };
    }
}
const PDFUtils = {
    generateInvoiceHTML,
    downloadInvoicePDF,
    generateInvoicePDFBlob,
    printInvoice
};
}}),
"[project]/src/app/invoice/create/page.tsx [app-ssr] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const e = new Error(`Could not parse module '[project]/src/app/invoice/create/page.tsx'

Expected ident`);
e.code = 'MODULE_UNPARSEABLE';
throw e;}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__273957e2._.js.map