'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, FileText, Calendar, CheckCircle, AlertTriangle, Clock, Globe } from 'lucide-react'
import { toast } from 'sonner'

export default function Step1Page() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<string[]>([])

  const markSectionComplete = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section completed! ✅`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-green-600">Step 1: E-Invoicing Phases Overview</h1>
              <Badge className="bg-green-100 text-green-800">
                Foundation
              </Badge>
            </div>
            <Button
              onClick={() => {
                toast.success('Proceeding to Step 2...')
                router.push('/invoice/step2')
              }}
              className="bg-green-600 hover:bg-green-700"
            >
              Next Step
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview */}
        <Card className="mb-6 bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">📋 Understanding ZATCA's E-Invoicing Implementation</h3>
              <p className="text-blue-600 mb-4">Learn the two-phase approach and compliance requirements</p>
            </div>
          </CardContent>
        </Card>

        {/* Phase 1: Generation */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-blue-600" />
              <span>Phase 1: Generation Phase</span>
              <Badge className="bg-blue-100 text-blue-800">Since Dec 2021</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">Key Requirements:</h4>
                <ul className="space-y-2 text-blue-700">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Generate e-invoices in XML format (UBL 2.1)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Include QR codes with TLV (Tag-Length-Value) format</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Implement digital signatures (ECDSA with SHA-256)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Support Arabic language requirements</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">Implementation Status:</h4>
                <p className="text-yellow-700">✅ Mandatory since December 4, 2021 for all taxpayers</p>
              </div>

              <Button
                onClick={() => markSectionComplete('phase1')}
                variant={completedSections.includes('phase1') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('phase1') ? '✅ Phase 1 Understood' : 'Mark Phase 1 Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Phase 2: Integration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-6 w-6 text-purple-600" />
              <span>Phase 2: Integration Phase</span>
              <Badge className="bg-purple-100 text-purple-800">Since Jan 2023</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2">Key Requirements:</h4>
                <ul className="space-y-2 text-purple-700">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>API integration with ZATCA's FATOORA platform</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Real-time invoice clearance for B2B transactions</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Invoice reporting for B2C transactions (within 24 hours)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>EGS (Electronic Generation System) onboarding</span>
                  </li>
                </ul>
              </div>

              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">Critical Deadlines:</h4>
                <div className="space-y-2 text-red-700">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-red-600" />
                    <span><strong>Wave 1:</strong> January 1, 2023 - Large taxpayers</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-red-600" />
                    <span><strong>Wave 2:</strong> January 1, 2024 - Medium taxpayers</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-red-600" />
                    <span><strong>Wave 3:</strong> January 1, 2025 - Small taxpayers</span>
                  </div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('phase2')}
                variant={completedSections.includes('phase2') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('phase2') ? '✅ Phase 2 Understood' : 'Mark Phase 2 Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Key Differences */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-6 w-6 text-orange-600" />
              <span>Key Differences Between Phases</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-3">Phase 1 (Generation)</h4>
                <ul className="space-y-2 text-blue-700 text-sm">
                  <li>• Offline invoice generation</li>
                  <li>• Self-signed certificates</li>
                  <li>• QR code validation</li>
                  <li>• No real-time submission</li>
                  <li>• Local compliance only</li>
                </ul>
              </div>
              
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-3">Phase 2 (Integration)</h4>
                <ul className="space-y-2 text-purple-700 text-sm">
                  <li>• Online API integration</li>
                  <li>• ZATCA-issued certificates</li>
                  <li>• Real-time clearance/reporting</li>
                  <li>• Mandatory API submission</li>
                  <li>• Full ZATCA compliance</li>
                </ul>
              </div>
            </div>

            <Button
              onClick={() => markSectionComplete('differences')}
              variant={completedSections.includes('differences') ? 'secondary' : 'outline'}
              className="w-full mt-4"
            >
              {completedSections.includes('differences') ? '✅ Differences Understood' : 'Mark Differences Complete'}
            </Button>
          </CardContent>
        </Card>

        {/* Progress Summary */}
        <Card className="bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-green-800 mb-2">Step 1 Progress</h3>
              <p className="text-green-600 mb-4">
                {completedSections.length}/3 sections completed
              </p>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => router.push('/invoice')}
                  variant="outline"
                >
                  Back to Overview
                </Button>
                <Button
                  onClick={() => {
                    if (completedSections.length === 3) {
                      toast.success('Step 1 completed! Moving to Step 2...')
                      router.push('/invoice/step2')
                    } else {
                      toast.warning('Please complete all sections first')
                    }
                  }}
                  className="bg-green-600 hover:bg-green-700"
                  disabled={completedSections.length < 3}
                >
                  Continue to Step 2
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
