'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Users, Key, Shield, CheckCircle, AlertCircle, FileText, Clock } from 'lucide-react'
import { toast } from 'sonner'

export default function Step3Page() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<string[]>([])

  const markSectionComplete = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section completed! ✅`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-purple-600">Step 3: EGS Onboarding Process</h1>
              <Badge className="bg-purple-100 text-purple-800">
                Authentication
              </Badge>
            </div>
            <Button
              onClick={() => {
                toast.success('Proceeding to Step 4...')
                router.push('/invoice/step4')
              }}
              className="bg-purple-600 hover:bg-purple-700"
            >
              Next Step
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview */}
        <Card className="mb-6 bg-purple-50 border-purple-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-purple-800 mb-2">🔐 Electronic Generation System (EGS) Onboarding</h3>
              <p className="text-purple-600 mb-4">Complete the authentication and certificate issuance process</p>
            </div>
          </CardContent>
        </Card>

        {/* Onboarding Flow */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="h-6 w-6 text-blue-600" />
              <span>Onboarding Flow Overview</span>
              <Badge className="bg-blue-100 text-blue-800">Process</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-3">Step-by-Step Process:</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <span className="text-blue-700">Login to FATOORA Portal (SSO via ERAD credentials)</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <span className="text-blue-700">Generate OTP (One-Time Password)</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <span className="text-blue-700">Create and submit CSR (Certificate Signing Request)</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                    <span className="text-blue-700">Pass compliance checks</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                    <span className="text-green-700">Receive Production CSID (Certificate Serial ID)</span>
                  </div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('flow')}
                variant={completedSections.includes('flow') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('flow') ? '✅ Flow Understood' : 'Mark Flow Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* CSR Requirements */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-6 w-6 text-green-600" />
              <span>CSR (Certificate Signing Request) Fields</span>
              <Badge className="bg-green-100 text-green-800">Required</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-3">Mandatory CSR Fields:</h4>
                <div className="space-y-3">
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <div className="font-semibold text-green-800">Common Name (CN)</div>
                    <div className="text-green-700 text-sm">Device identifier (unique for each EGS)</div>
                    <div className="text-gray-600 text-xs font-mono">Example: EGS-DEVICE-001</div>
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <div className="font-semibold text-green-800">VAT Number</div>
                    <div className="text-green-700 text-sm">15-digit VAT registration number</div>
                    <div className="text-gray-600 text-xs font-mono">Example: 301234567890003</div>
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <div className="font-semibold text-green-800">Invoice Type</div>
                    <div className="text-green-700 text-sm">Supported invoice types (4-digit code)</div>
                    <div className="text-gray-600 text-xs font-mono">Example: "1100" (B2B + B2C)</div>
                  </div>
                  <div className="bg-white p-3 rounded border-l-4 border-green-500">
                    <div className="font-semibold text-green-800">Organization Unit (OU)</div>
                    <div className="text-green-700 text-sm">Business unit or department</div>
                    <div className="text-gray-600 text-xs font-mono">Example: "Finance Department"</div>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">Invoice Type Codes:</h4>
                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div><strong>0100:</strong> Tax Invoice (B2B)</div>
                  <div><strong>0200:</strong> Simplified Invoice (B2C)</div>
                  <div><strong>1000:</strong> Debit Note</div>
                  <div><strong>1100:</strong> B2B + B2C Combined</div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('csr')}
                variant={completedSections.includes('csr') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('csr') ? '✅ CSR Requirements Understood' : 'Mark CSR Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Checks */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-6 w-6 text-orange-600" />
              <span>Compliance Checks</span>
              <Badge className="bg-orange-100 text-orange-800">Validation</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-3">Required Compliance Tests:</h4>
                <ul className="space-y-2 text-orange-700">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>XML structure validation (UBL 2.1 compliance)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Digital signature verification (ECDSA)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>QR code format validation (TLV structure)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Arabic language support verification</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Invoice counter and UUID uniqueness</span>
                  </li>
                </ul>
              </div>

              <div className="bg-red-50 p-4 rounded-lg">
                <h4 className="font-semibold text-red-800 mb-2">Common Compliance Failures:</h4>
                <ul className="space-y-1 text-red-700 text-sm">
                  <li>• Incorrect XML namespace declarations</li>
                  <li>• Missing mandatory fields (VAT amounts, dates)</li>
                  <li>• Invalid digital signature format</li>
                  <li>• QR code encoding errors (HEX vs Base64)</li>
                  <li>• Missing Arabic translations</li>
                </ul>
              </div>

              <Button
                onClick={() => markSectionComplete('compliance')}
                variant={completedSections.includes('compliance') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('compliance') ? '✅ Compliance Checks Understood' : 'Mark Compliance Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* CSID Management */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Key className="h-6 w-6 text-indigo-600" />
              <span>CSID (Certificate Serial ID) Management</span>
              <Badge className="bg-indigo-100 text-indigo-800">Security</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-indigo-50 p-4 rounded-lg">
                <h4 className="font-semibold text-indigo-800 mb-3">CSID Lifecycle:</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-indigo-600" />
                    <span className="text-indigo-700"><strong>Issuance:</strong> After successful compliance checks</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Shield className="h-5 w-5 text-indigo-600" />
                    <span className="text-indigo-700"><strong>Validity:</strong> 365 days from issuance</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <AlertCircle className="h-5 w-5 text-orange-600" />
                    <span className="text-indigo-700"><strong>Renewal:</strong> Required before expiration</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Key className="h-5 w-5 text-indigo-600" />
                    <span className="text-indigo-700"><strong>Revocation:</strong> Automatic when joining VAT groups</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2">CSID Storage & Security:</h4>
                <ul className="space-y-1 text-gray-700 text-sm">
                  <li>• Store CSID securely (encrypted storage)</li>
                  <li>• Backup certificates and private keys</li>
                  <li>• Monitor expiration dates</li>
                  <li>• Implement automatic renewal processes</li>
                </ul>
              </div>

              <Button
                onClick={() => markSectionComplete('csid')}
                variant={completedSections.includes('csid') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('csid') ? '✅ CSID Management Understood' : 'Mark CSID Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Progress Summary */}
        <Card className="bg-purple-50 border-purple-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-purple-800 mb-2">Step 3 Progress</h3>
              <p className="text-purple-600 mb-4">
                {completedSections.length}/4 sections completed
              </p>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => router.push('/invoice/step2')}
                  variant="outline"
                >
                  Previous Step
                </Button>
                <Button
                  onClick={() => {
                    if (completedSections.length === 4) {
                      toast.success('Step 3 completed! Moving to Step 4...')
                      router.push('/invoice/step4')
                    } else {
                      toast.warning('Please complete all sections first')
                    }
                  }}
                  className="bg-purple-600 hover:bg-purple-700"
                  disabled={completedSections.length < 4}
                >
                  Continue to Step 4
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
