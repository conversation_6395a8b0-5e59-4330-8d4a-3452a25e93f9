(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Button": (()=>Button),
    "buttonVariants": (()=>buttonVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
;
const buttonVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cva"])("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive", {
    variants: {
        variant: {
            default: "bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",
            destructive: "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",
            outline: "border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",
            secondary: "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
            ghost: "hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",
            link: "text-primary underline-offset-4 hover:underline"
        },
        size: {
            default: "h-9 px-4 py-2 has-[>svg]:px-3",
            sm: "h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",
            lg: "h-10 rounded-md px-6 has-[>svg]:px-4",
            icon: "size-9"
        }
    },
    defaultVariants: {
        variant: "default",
        size: "default"
    }
});
function Button({ className, variant, size, asChild = false, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Slot"] : "button";
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "button",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(buttonVariants({
            variant,
            size,
            className
        })),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/button.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, this);
}
_c = Button;
;
var _c;
__turbopack_context__.k.register(_c, "Button");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/card.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardAction": (()=>CardAction),
    "CardContent": (()=>CardContent),
    "CardDescription": (()=>CardDescription),
    "CardFooter": (()=>CardFooter),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
function Card({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 7,
        columnNumber: 5
    }, this);
}
_c = Card;
function CardHeader({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-header",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 20,
        columnNumber: 5
    }, this);
}
_c1 = CardHeader;
function CardTitle({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-title",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("leading-none font-semibold", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 33,
        columnNumber: 5
    }, this);
}
_c2 = CardTitle;
function CardDescription({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-description",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("text-muted-foreground text-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
_c3 = CardDescription;
function CardAction({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-action",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("col-start-2 row-span-2 row-start-1 self-start justify-self-end", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 53,
        columnNumber: 5
    }, this);
}
_c4 = CardAction;
function CardContent({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-content",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("px-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 66,
        columnNumber: 5
    }, this);
}
_c5 = CardContent;
function CardFooter({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        "data-slot": "card-footer",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex items-center px-6 [.border-t]:pt-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
}
_c6 = CardFooter;
;
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "Card");
__turbopack_context__.k.register(_c1, "CardHeader");
__turbopack_context__.k.register(_c2, "CardTitle");
__turbopack_context__.k.register(_c3, "CardDescription");
__turbopack_context__.k.register(_c4, "CardAction");
__turbopack_context__.k.register(_c5, "CardContent");
__turbopack_context__.k.register(_c6, "CardFooter");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/input.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Input": (()=>Input)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Input = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, type, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
        type: type,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ref: ref,
        onChange: props.onChange,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/input.tsx",
        lineNumber: 11,
        columnNumber: 7
    }, this);
});
_c1 = Input;
Input.displayName = "Input";
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Input$React.forwardRef");
__turbopack_context__.k.register(_c1, "Input");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Textarea": (()=>Textarea)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
;
;
;
const Textarea = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ className, ...props }, ref)=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50", className),
        ref: ref,
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/textarea.tsx",
        lineNumber: 11,
        columnNumber: 7
    }, this);
});
_c1 = Textarea;
Textarea.displayName = "Textarea";
;
var _c, _c1;
__turbopack_context__.k.register(_c, "Textarea$React.forwardRef");
__turbopack_context__.k.register(_c1, "Textarea");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/zatcaQR.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// ZATCA QR Code Generation Utility
// Following ZATCA TLV (Tag-Length-Value) format specifications
__turbopack_context__.s({
    "ZATCAUtils": (()=>ZATCAUtils),
    "ZATCA_TAGS": (()=>ZATCA_TAGS),
    "formatCurrencyForZATCA": (()=>formatCurrencyForZATCA),
    "generateInvoiceHash": (()=>generateInvoiceHash),
    "generateQRCodeURL": (()=>generateQRCodeURL),
    "generateSampleZATCAQR": (()=>generateSampleZATCAQR),
    "generateTLV": (()=>generateTLV),
    "generateZATCAQRCode": (()=>generateZATCAQRCode),
    "generateZATCATimestamp": (()=>generateZATCATimestamp),
    "validateZATCAQRData": (()=>validateZATCAQRData)
});
const ZATCA_TAGS = {
    SELLER_NAME: 1,
    VAT_NUMBER: 2,
    TIMESTAMP: 3,
    INVOICE_TOTAL: 4,
    VAT_TOTAL: 5,
    INVOICE_HASH: 6
};
function generateTLV(tag, value) {
    const valueBytes = new TextEncoder().encode(value);
    const length = valueBytes.length;
    // Create TLV structure: [Tag][Length][Value]
    const tlv = new Uint8Array(2 + length);
    tlv[0] = tag;
    tlv[1] = length;
    tlv.set(valueBytes, 2);
    return tlv;
}
function generateZATCAQRCode(data) {
    try {
        // Generate TLV for each required field
        const sellerNameTLV = generateTLV(ZATCA_TAGS.SELLER_NAME, data.sellerName);
        const vatNumberTLV = generateTLV(ZATCA_TAGS.VAT_NUMBER, data.vatNumber);
        const timestampTLV = generateTLV(ZATCA_TAGS.TIMESTAMP, data.timestamp);
        const invoiceTotalTLV = generateTLV(ZATCA_TAGS.INVOICE_TOTAL, data.invoiceTotal);
        const vatTotalTLV = generateTLV(ZATCA_TAGS.VAT_TOTAL, data.vatTotal);
        // Calculate total length
        let totalLength = sellerNameTLV.length + vatNumberTLV.length + timestampTLV.length + invoiceTotalTLV.length + vatTotalTLV.length;
        // Add invoice hash if provided
        let invoiceHashTLV = null;
        if (data.invoiceHash) {
            invoiceHashTLV = generateTLV(ZATCA_TAGS.INVOICE_HASH, data.invoiceHash);
            totalLength += invoiceHashTLV.length;
        }
        // Combine all TLV data
        const qrData = new Uint8Array(totalLength);
        let offset = 0;
        qrData.set(sellerNameTLV, offset);
        offset += sellerNameTLV.length;
        qrData.set(vatNumberTLV, offset);
        offset += vatNumberTLV.length;
        qrData.set(timestampTLV, offset);
        offset += timestampTLV.length;
        qrData.set(invoiceTotalTLV, offset);
        offset += invoiceTotalTLV.length;
        qrData.set(vatTotalTLV, offset);
        offset += vatTotalTLV.length;
        if (invoiceHashTLV) {
            qrData.set(invoiceHashTLV, offset);
        }
        // Convert to Base64
        return btoa(String.fromCharCode(...qrData));
    } catch (error) {
        console.error('Error generating ZATCA QR code:', error);
        throw new Error('Failed to generate ZATCA QR code');
    }
}
async function generateInvoiceHash(invoiceData) {
    try {
        // Convert invoice data to string (in production, use XML canonicalization)
        const dataString = JSON.stringify(invoiceData);
        // Generate SHA-256 hash
        const encoder = new TextEncoder();
        const data = encoder.encode(dataString);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        // Convert to Base64
        const hashArray = new Uint8Array(hashBuffer);
        return btoa(String.fromCharCode(...hashArray));
    } catch (error) {
        console.error('Error generating invoice hash:', error);
        throw new Error('Failed to generate invoice hash');
    }
}
function validateZATCAQRData(data) {
    const errors = [];
    // Validate seller name
    if (!data.sellerName || data.sellerName.trim().length === 0) {
        errors.push('Seller name is required');
    }
    // Validate VAT number (should be 15 digits)
    if (!data.vatNumber || !/^\d{15}$/.test(data.vatNumber)) {
        errors.push('VAT number must be exactly 15 digits');
    }
    // Validate timestamp (ISO format)
    if (!data.timestamp || isNaN(Date.parse(data.timestamp))) {
        errors.push('Valid timestamp is required');
    }
    // Validate invoice total (should be numeric)
    if (!data.invoiceTotal || isNaN(parseFloat(data.invoiceTotal))) {
        errors.push('Valid invoice total is required');
    }
    // Validate VAT total (should be numeric)
    if (!data.vatTotal || isNaN(parseFloat(data.vatTotal))) {
        errors.push('Valid VAT total is required');
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
function generateQRCodeURL(qrData, size = 200) {
    // Using QR Server API (in production, consider using a local QR generator)
    const encodedData = encodeURIComponent(qrData);
    return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodedData}`;
}
function formatCurrencyForZATCA(amount, currency = 'SAR') {
    return amount.toFixed(2);
}
function generateZATCATimestamp(date = new Date()) {
    return date.toISOString();
}
function generateSampleZATCAQR() {
    const sampleData = {
        sellerName: 'KAAZMAAMAA Company',
        vatNumber: '30**********003',
        timestamp: generateZATCATimestamp(),
        invoiceTotal: '1150.00',
        vatTotal: '150.00'
    };
    return generateZATCAQRCode(sampleData);
}
const ZATCAUtils = {
    generateTLV,
    generateZATCAQRCode,
    generateInvoiceHash,
    validateZATCAQRData,
    generateQRCodeURL,
    formatCurrencyForZATCA,
    generateZATCATimestamp,
    generateSampleZATCAQR,
    ZATCA_TAGS
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/numberToWords.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Number to Words Converter for Invoice Amounts
__turbopack_context__.s({
    "NumberUtils": (()=>NumberUtils),
    "amountToWords": (()=>amountToWords),
    "formatAmountInWords": (()=>formatAmountInWords),
    "numberToWords": (()=>numberToWords)
});
const ones = [
    '',
    'One',
    'Two',
    'Three',
    'Four',
    'Five',
    'Six',
    'Seven',
    'Eight',
    'Nine',
    'Ten',
    'Eleven',
    'Twelve',
    'Thirteen',
    'Fourteen',
    'Fifteen',
    'Sixteen',
    'Seventeen',
    'Eighteen',
    'Nineteen'
];
const tens = [
    '',
    '',
    'Twenty',
    'Thirty',
    'Forty',
    'Fifty',
    'Sixty',
    'Seventy',
    'Eighty',
    'Ninety'
];
const scales = [
    '',
    'Thousand',
    'Million',
    'Billion',
    'Trillion'
];
/**
 * Convert a number (0-999) to words
 * @param num - Number to convert
 * @returns String representation
 */ function convertHundreds(num) {
    let result = '';
    if (num >= 100) {
        result += ones[Math.floor(num / 100)] + ' Hundred';
        num %= 100;
        if (num > 0) result += ' ';
    }
    if (num >= 20) {
        result += tens[Math.floor(num / 10)];
        num %= 10;
        if (num > 0) result += ' ';
    }
    if (num > 0) {
        result += ones[num];
    }
    return result;
}
function numberToWords(num) {
    if (num === 0) return 'Zero';
    let result = '';
    let scaleIndex = 0;
    while(num > 0){
        const chunk = num % 1000;
        if (chunk !== 0) {
            const chunkWords = convertHundreds(chunk);
            if (scaleIndex > 0) {
                result = chunkWords + ' ' + scales[scaleIndex] + (result ? ' ' + result : '');
            } else {
                result = chunkWords;
            }
        }
        num = Math.floor(num / 1000);
        scaleIndex++;
    }
    return result;
}
function amountToWords(amount, currency = 'SAR') {
    const wholePart = Math.floor(amount);
    const decimalPart = Math.round((amount - wholePart) * 100);
    let result = numberToWords(wholePart);
    // Add currency name
    const currencyNames = {
        'SAR': {
            singular: 'Riyal',
            plural: 'Riyals',
            subunit: 'Halala'
        },
        'USD': {
            singular: 'Dollar',
            plural: 'Dollars',
            subunit: 'Cent'
        },
        'EUR': {
            singular: 'Euro',
            plural: 'Euros',
            subunit: 'Cent'
        },
        'AED': {
            singular: 'Dirham',
            plural: 'Dirhams',
            subunit: 'Fils'
        }
    };
    const currencyInfo = currencyNames[currency] || {
        singular: currency,
        plural: currency + 's',
        subunit: 'Cent'
    };
    if (wholePart === 1) {
        result += ' ' + currencyInfo.singular;
    } else {
        result += ' ' + currencyInfo.plural;
    }
    // Add decimal part if exists
    if (decimalPart > 0) {
        result += ' and ' + numberToWords(decimalPart);
        if (decimalPart === 1) {
            result += ' ' + currencyInfo.subunit;
        } else {
            result += ' ' + currencyInfo.subunit + 's';
        }
    }
    return result + ' Only';
}
function formatAmountInWords(amount, currency = 'SAR') {
    return amountToWords(amount, currency);
}
const NumberUtils = {
    numberToWords,
    amountToWords,
    formatAmountInWords
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/pdfGenerator.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// PDF Generation Utility for ZATCA Invoices - HD Quality Version
// Enhanced with professional styling and textbox system
__turbopack_context__.s({
    "PDFUtils": (()=>PDFUtils),
    "downloadInvoicePDF": (()=>downloadInvoicePDF),
    "generateInvoiceHTML": (()=>generateInvoiceHTML),
    "generateInvoicePDFBlob": (()=>generateInvoicePDFBlob),
    "printInvoice": (()=>printInvoice)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$numberToWords$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/numberToWords.ts [app-client] (ecmascript)");
;
/**
 * Safely format date to avoid timezone issues
 * @param dateString - Date string in ISO format (YYYY-MM-DD)
 * @returns Formatted date string
 */ function formatDateSafely(dateString) {
    try {
        // If it's already in YYYY-MM-DD format, parse it safely
        if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateString.split('-').map(Number);
            const date = new Date(year, month - 1, day) // month is 0-indexed
            ;
            return date.toLocaleDateString();
        }
        // Otherwise, use regular Date parsing
        return new Date(dateString).toLocaleDateString();
    } catch (error) {
        console.error('Date formatting error:', error);
        return dateString // Return original string if parsing fails
        ;
    }
}
function generateInvoiceHTML(data) {
    const discountAmount = data.subtotal * data.discount / 100;
    const subtotalAfterDiscount = data.subtotal - discountAmount;
    // NEW: Use the passed subtotalAfterDeduction which includes deduction amount logic
    return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${data.invoiceNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Segoe UI', 'Arial', sans-serif;
          font-size: 11px;
          line-height: 1.5;
          color: #1f2937;
          background: white;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          print-color-adjust: exact;
        }

        .invoice-container {
          max-width: 850px;
          margin: 0 auto;
          padding: 30px;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          border: 1px solid #e5e7eb;
          width: 100%;
          box-sizing: border-box;
          overflow-x: hidden;
        }

        .header {
          display: grid;
          grid-template-columns: 1fr auto 1fr;
          align-items: center;
          margin-bottom: 35px;
          padding: 25px;
          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
          border-radius: 12px;
          gap: 25px;
          box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);
        }

        .qr-header {
          text-align: center;
          justify-self: center;
        }

        .qr-code-top {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .logo-section h1 {
          color: white;
          font-size: 26px;
          font-weight: 700;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .logo-section p {
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          font-weight: 500;
        }

        .invoice-title {
          text-align: right;
        }

        .invoice-title h2 {
          color: white;
          font-size: 32px;
          font-weight: 800;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          letter-spacing: 1px;
        }

        .invoice-number {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          font-weight: 600;
        }

        .details-section {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 30px;
          margin-bottom: 30px;
        }

        .detail-card {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #A78BFA;
          border: 1px solid #e5e7eb;
          overflow: hidden;
          word-wrap: break-word;
        }

        .detail-card h3 {
          color: #A78BFA;
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 15px;
          text-align: center;
          background: #A78BFA;
          color: white;
          padding: 8px;
          margin: -15px -15px 15px -15px;
          border-radius: 7px 7px 0 0;
        }

        .textbox-container {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          margin-bottom: 10px;
        }

        .textbox-full {
          grid-column: 1 / -1;
        }

        .textbox {
          border: 1.5px solid #d1d5db;
          border-radius: 6px;
          padding: 10px 12px;
          background: #ffffff;
          font-size: 11px;
          min-height: 24px;
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
          font-weight: 500;
          color: #374151;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
        }

        .textbox-label {
          font-size: 9px;
          color: #6b7280;
          font-weight: 600;
          margin-bottom: 4px;
          display: block;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .textbox-large {
          min-height: 50px;
          line-height: 1.4;
          padding: 12px;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
          max-height: 80px;
        }

        .textbox-address {
          min-height: 45px;
          line-height: 1.3;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
          max-height: 75px;
          font-size: 10px;
          padding: 10px;
        }

        .textbox-amount {
          font-weight: 700;
          color: #059669;
          font-size: 12px;
          text-align: right;
        }

        .seller-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 15px;
        }

        .seller-info {
          flex: 1;
        }

        .seller-logo {
          width: 80px;
          text-align: center;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          background: white;
          border: 1px solid #e5e7eb;
        }

        .items-table th {
          background: #A78BFA;
          color: white;
          padding: 12px 8px;
          text-align: left;
          font-weight: bold;
          font-size: 11px;
        }

        .items-table td {
          padding: 10px 8px;
          border-bottom: 1px solid #e5e7eb;
          font-size: 11px;
        }

        .items-table tr:nth-child(even) {
          background: #f9fafb;
        }

        .summary-section {
          display: grid;
          grid-template-columns: 280px 1fr 1fr;
          gap: 20px;
          margin-bottom: 30px;
          max-width: 100%;
          overflow: hidden;
        }

        .notes-section, .bank-section {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }

        .summary-card {
          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
          color: white;
          padding: 15px;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
          max-width: 280px;
          width: 100%;
          box-sizing: border-box;
        }

        .summary-card h4 {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 12px;
        }

        .summary-total {
          border-top: 2px solid rgba(255,255,255,0.3);
          padding-top: 10px;
          margin-top: 10px;
          font-size: 16px;
          font-weight: bold;
        }



        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 10px;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }

        @media print {
          body { margin: 0; }
          .invoice-container { padding: 0; }
          .summary-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .summary-card {
            max-width: 100%;
          }
        }

        @media (max-width: 768px) {
          .summary-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .summary-card {
            max-width: 100%;
          }
          .invoice-container {
            padding: 15px;
          }
          .details-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .textbox-address {
            font-size: 9px;
            max-height: 60px;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header with QR Code -->
        <div class="header">
          <div class="logo-section">
            <h1>🧾 ZATCA Invoice Generator</h1>
            <p>Create and manage your invoices with ease</p>
          </div>

          <!-- QR Code in Center Top -->
          <div class="qr-header">
            <div class="qr-code-top">
              <img src="https://api.qrserver.com/v1/create-qr-code/?size=130x130&data=${encodeURIComponent(data.qrCode)}"
                   alt="ZATCA QR Code" style="width: 130px; height: 130px; border: 3px solid white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);" />
            </div>
            <p style="font-size: 11px; color: rgba(255, 255, 255, 0.9); margin-top: 8px; text-align: center; font-weight: 600;">ZATCA QR Code</p>
          </div>

          <div class="invoice-title">
            <h2>INVOICE</h2>
            <p class="invoice-number">${data.invoiceNumber}</p>
          </div>
        </div>

        <!-- Details Section -->
        <div class="details-section">
          <div class="detail-card">
            <h3>📋 Invoice Details</h3>
            <div class="textbox-container">
              <div>
                <span class="textbox-label">Invoice Number</span>
                <div class="textbox">${data.invoiceNumber}</div>
              </div>
              <div>
                <span class="textbox-label">Currency</span>
                <div class="textbox">${data.currency}</div>
              </div>
              <div>
                <span class="textbox-label">Invoice Date</span>
                <div class="textbox">${formatDateSafely(data.invoiceDate)}</div>
              </div>
              <div>
                <span class="textbox-label">Due Date</span>
                <div class="textbox">${data.dueDate ? formatDateSafely(data.dueDate) : 'N/A'}</div>
              </div>
              ${data.discount > 0 ? `
                <div class="textbox-full">
                  <span class="textbox-label">Discount Applied</span>
                  <div class="textbox">${data.discount}% discount applied to subtotal</div>
                </div>
              ` : ''}
              ${Number(data.deductionAmount) > 0 ? `
                <div class="textbox-full">
                  <span class="textbox-label">⚠️ Deduction Amount</span>
                  <div class="textbox" style="color: #ea580c; font-weight: 600; background: #fef3f2; border: 1px solid #fecaca;">${data.currency} ${Number(data.deductionAmount).toFixed(2)} deducted before VAT calculation</div>
                </div>
                ${data.deductionReason ? `
                  <div class="textbox-full">
                    <span class="textbox-label">📝 Deduction Reason</span>
                    <div class="textbox" style="color: #374151; font-weight: 500; background: #fffbeb; border: 1px solid #fed7aa; padding: 12px; line-height: 1.4;">${data.deductionReason}</div>
                  </div>
                ` : ''}
              ` : ''}
            </div>
          </div>

          <div class="detail-card">
            <h3>🏢 Billed From (Seller)</h3>
            <div class="seller-container">
              <div class="seller-info">
                <div class="textbox-container">
                  <div class="textbox-full">
                    <span class="textbox-label">Company Name</span>
                    <div class="textbox">${data.sellerName}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">Company Address</span>
                    <div class="textbox textbox-address">${data.sellerAddress.replace(/\n/g, '<br>')}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">VAT Registration Number</span>
                    <div class="textbox">${data.sellerVAT}</div>
                  </div>
                </div>
              </div>
              ${data.sellerLogo ? `
                <div class="seller-logo">
                  <span class="textbox-label">Company Logo</span>
                  <div class="textbox" style="text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;">
                    <img src="${data.sellerLogo}" alt="Company Logo"
                         style="max-width: 70px; max-height: 60px; object-fit: contain;" />
                  </div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="detail-card">
            <h3>👤 Billed To (Client)</h3>
            <div class="seller-container">
              <div class="seller-info">
                <div class="textbox-container">
                  <div class="textbox-full">
                    <span class="textbox-label">Client Name</span>
                    <div class="textbox">${data.clientName}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">Client Address</span>
                    <div class="textbox textbox-address">${data.clientAddress.replace(/\n/g, '<br>')}</div>
                  </div>
                  ${data.clientVAT ? `
                    <div class="textbox-full">
                      <span class="textbox-label">Client VAT Registration Number</span>
                      <div class="textbox">${data.clientVAT}</div>
                    </div>
                  ` : `
                    <div class="textbox-full">
                      <span class="textbox-label">Client VAT Registration Number</span>
                      <div class="textbox" style="color: #9ca3af; font-style: italic;">Not provided</div>
                    </div>
                  `}
                </div>
              </div>
              ${data.clientLogo ? `
                <div class="seller-logo">
                  <span class="textbox-label">Client Logo</span>
                  <div class="textbox" style="text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;">
                    <img src="${data.clientLogo}" alt="Client Logo"
                         style="max-width: 70px; max-height: 60px; object-fit: contain;" />
                  </div>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40%">Description</th>
              <th style="width: 10%">Qty</th>
              <th style="width: 15%">Rate</th>
              <th style="width: 10%">VAT %</th>
              <th style="width: 15%">Amount (${data.currency})</th>
            </tr>
          </thead>
          <tbody>
            ${data.items.map((item)=>`
              <tr>
                <td>${item.description}</td>
                <td>${item.quantity}</td>
                <td>${item.rate.toFixed(2)}</td>
                <td>${item.vatPercent}%</td>
                <td>${item.amount.toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Summary Section -->
        <div class="summary-section">
          <div class="summary-card">
            <h4 style="color: white; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: rgba(255,255,255,0.25); padding: 8px; margin: -15px -15px 15px -15px; border-radius: 8px 8px 0 0;">💰 Invoice Summary</h4>
            <div class="textbox-container" style="gap: 8px; grid-template-columns: 1fr;">
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Subtotal</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;">${data.currency} ${data.subtotal.toFixed(2)}</div>
              </div>
              ${data.discount > 0 ? `
                <div>
                  <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Discount (${data.discount}%)</span>
                  <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #dc2626; padding: 8px; font-size: 10px;">-${data.currency} ${discountAmount.toFixed(2)}</div>
                </div>
              ` : ''}
              ${Number(data.deductionAmount) > 0 ? `
                <div>
                  <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Deduction Amount</span>
                  <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #ea580c; padding: 8px; font-size: 10px;">-${data.currency} ${Number(data.deductionAmount).toFixed(2)}</div>
                </div>
                ${data.deductionReason ? `
                  <div>
                    <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Deduction Reason</span>
                    <div class="textbox" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #6b7280; font-style: italic; padding: 8px; font-size: 9px; line-height: 1.3;">${data.deductionReason}</div>
                  </div>
                ` : ''}
              ` : ''}
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Amount Before VAT</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px; font-weight: 600;">${data.currency} ${data.subtotalAfterDeduction.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Total VAT (15%)</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;">${data.currency} ${data.totalVAT.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Total Amount</span>
                <div class="textbox" style="background: #10b981; border: 2px solid #059669; color: white; font-weight: 700; font-size: 14px; text-align: center; padding: 10px;">${data.currency} ${data.totalAmount.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Amount in Words</span>
                <div class="textbox" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); font-style: italic; color: #374151; text-align: center; font-weight: 600; padding: 8px; font-size: 9px; line-height: 1.3;">${(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$numberToWords$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatAmountInWords"])(data.totalAmount, data.currency)}</div>
              </div>
            </div>
          </div>

          <div class="bank-section">
            <h4 style="color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;">🏦 Bank Details</h4>
            <div class="textbox-container">
              ${data.accountName ? `
                <div>
                  <span class="textbox-label">Account Name</span>
                  <div class="textbox">${data.accountName}</div>
                </div>
              ` : ''}
              ${data.bankName ? `
                <div>
                  <span class="textbox-label">Bank Name</span>
                  <div class="textbox">${data.bankName}</div>
                </div>
              ` : ''}
              ${data.accountNumber ? `
                <div>
                  <span class="textbox-label">Account Number</span>
                  <div class="textbox">${data.accountNumber}</div>
                </div>
              ` : ''}
              ${data.iban ? `
                <div>
                  <span class="textbox-label">IBAN</span>
                  <div class="textbox">${data.iban}</div>
                </div>
              ` : ''}
              ${!data.accountName && !data.bankName && !data.accountNumber && !data.iban ? `
                <div class="textbox-full">
                  <span class="textbox-label">Payment Information</span>
                  <div class="textbox" style="color: #9ca3af; font-style: italic;">No bank details provided</div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="notes-section">
            <h4 style="color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;">📝 Notes & Terms</h4>
            <div class="textbox-container" style="grid-template-columns: 1fr;">
              ${Number(data.deductionAmount) > 0 && data.deductionReason ? `
                <div class="textbox-full">
                  <span class="textbox-label">⚠️ Important: Deduction Applied</span>
                  <div class="textbox" style="background: #fef3f2; border: 2px solid #fecaca; color: #dc2626; font-weight: 600; padding: 12px; line-height: 1.4;">
                    A deduction of ${data.currency} ${Number(data.deductionAmount).toFixed(2)} has been applied to this invoice.<br>
                    <strong>Reason:</strong> ${data.deductionReason}
                  </div>
                </div>
              ` : ''}
              ${data.notes ? `
                <div class="textbox-full">
                  <span class="textbox-label">Notes</span>
                  <div class="textbox textbox-large">${data.notes}</div>
                </div>
              ` : `
                <div class="textbox-full">
                  <span class="textbox-label">Notes</span>
                  <div class="textbox" style="color: #6b7280; font-style: italic; min-height: 30px; padding: 12px;">Thank you for your business!</div>
                </div>
              `}
              ${data.terms ? `
                <div class="textbox-full">
                  <span class="textbox-label">Terms & Conditions</span>
                  <div class="textbox textbox-large">${data.terms}</div>
                </div>
              ` : `
                <div class="textbox-full">
                  <span class="textbox-label">Terms & Conditions</span>
                  <div class="textbox textbox-large" style="color: #6b7280; font-style: normal; line-height: 1.6; padding: 15px;">
                    • Payment due within 30 days of invoice date<br>
                    • Late payments may incur additional charges<br>
                    • All prices are inclusive of applicable taxes<br>
                    • This invoice is computer generated and valid without signature
                  </div>
                </div>
              `}
            </div>
          </div>
        </div>



        <!-- Footer -->
        <div class="footer">
          <p>This invoice is generated by KAAZMAAMAA ZATCA Invoice System</p>
          <p>Compliant with Saudi Arabia's ZATCA e-invoicing regulations</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
function downloadInvoicePDF(data, filename) {
    const html = generateInvoiceHTML(data);
    const pdfFilename = filename || `invoice-${data.invoiceNumber}.pdf`;
    // Create a new window for PDF generation
    const printWindow = window.open('', '_blank');
    if (printWindow) {
        printWindow.document.write(html);
        printWindow.document.close();
        // Wait for content to load, then print
        printWindow.onload = ()=>{
            setTimeout(()=>{
                printWindow.print();
            // Don't close the window automatically - let user close it
            // printWindow.close()
            }, 500);
        };
    }
}
async function generateInvoicePDFBlob(data) {
    // This is a simplified version
    // In production, use libraries like jsPDF or Puppeteer for proper PDF generation
    const html = generateInvoiceHTML(data);
    const blob = new Blob([
        html
    ], {
        type: 'text/html'
    });
    return blob;
}
function printInvoice(data) {
    const html = generateInvoiceHTML(data);
    // Create a hidden iframe for printing
    const iframe = document.createElement('iframe');
    iframe.style.display = 'none';
    document.body.appendChild(iframe);
    const doc = iframe.contentDocument || iframe.contentWindow?.document;
    if (doc) {
        doc.write(html);
        doc.close();
        iframe.onload = ()=>{
            iframe.contentWindow?.print();
            setTimeout(()=>{
                document.body.removeChild(iframe);
            }, 1000);
        };
    }
}
const PDFUtils = {
    generateInvoiceHTML,
    downloadInvoicePDF,
    generateInvoicePDFBlob,
    printInvoice
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/invoice/create/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CreateInvoicePage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/textarea.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/file-text.js [app-client] (ecmascript) <export default as FileText>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/download.js [app-client] (ecmascript) <export default as Download>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/printer.js [app-client] (ecmascript) <export default as Printer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$qr$2d$code$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__QrCode$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/qr-code.js [app-client] (ecmascript) <export default as QrCode>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/plus.js [app-client] (ecmascript) <export default as Plus>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/upload.js [app-client] (ecmascript) <export default as Upload>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/trash-2.js [app-client] (ecmascript) <export default as Trash2>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$history$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__History$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/history.js [app-client] (ecmascript) <export default as History>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/square-pen.js [app-client] (ecmascript) <export default as Edit>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/copy.js [app-client] (ecmascript) <export default as Copy>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-client] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/calendar.js [app-client] (ecmascript) <export default as Calendar>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js [app-client] (ecmascript) <export default as DollarSign>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/eye.js [app-client] (ecmascript) <export default as Eye>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js [app-client] (ecmascript) <export default as MoreVertical>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/lock.js [app-client] (ecmascript) <export default as Lock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/lock-open.js [app-client] (ecmascript) <export default as Unlock>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zatcaQR$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/zatcaQR.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$pdfGenerator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/pdfGenerator.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
// SUPER SIMPLE LOCK PERSISTENCE - NO COMPLEX SYSTEMS
const SIMPLE_LOCK_KEY = 'SIMPLE_INVOICE_LOCKS';
// Simple save function
const saveLocks = (lockState)=>{
    try {
        const data = JSON.stringify(lockState);
        localStorage.setItem(SIMPLE_LOCK_KEY, data);
        localStorage.setItem(SIMPLE_LOCK_KEY + '_backup', data);
        console.log('💾 SIMPLE SAVE:', lockState);
        return true;
    } catch (e) {
        console.error('❌ Save failed:', e);
        return false;
    }
};
// Simple load function
const loadLocks = ()=>{
    try {
        let data = localStorage.getItem(SIMPLE_LOCK_KEY);
        if (!data) {
            data = localStorage.getItem(SIMPLE_LOCK_KEY + '_backup');
        }
        if (data) {
            const parsed = JSON.parse(data);
            console.log('📋 SIMPLE LOAD:', parsed);
            return parsed;
        }
    } catch (e) {
        console.error('❌ Load failed:', e);
    }
    return {
        invoiceDetails: false,
        sellerDetails: false,
        clientDetails: false,
        bankDetails: false,
        items: false,
        notes: false
    };
};
;
;
;
;
;
;
;
;
;
function CreateInvoicePage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    // Basic form state
    const [invoiceNumber, setInvoiceNumber] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('INV-2025-001');
    const [invoiceDate, setInvoiceDate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(new Date().toISOString().split('T')[0]);
    const [currency, setCurrency] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('SAR');
    const [discount, setDiscount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('0');
    const [deductionAmount, setDeductionAmount] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('0');
    const [deductionReason, setDeductionReason] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // Seller Details
    const [sellerName, setSellerName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [sellerAddress, setSellerAddress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [sellerVAT, setSellerVAT] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [sellerLogo, setSellerLogo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [sellerLogoFile, setSellerLogoFile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Client Details
    const [clientName, setClientName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [clientAddress, setClientAddress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [clientVAT, setClientVAT] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [clientLogo, setClientLogo] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [clientLogoFile, setClientLogoFile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Items
    const [items, setItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        {
            id: '1',
            description: '',
            quantity: '1',
            rate: '0',
            vatPercent: 15,
            amount: 0
        }
    ]);
    // Additional Details
    const [notes, setNotes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [terms, setTerms] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [bankName, setBankName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [accountName, setAccountName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [accountNumber, setAccountNumber] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [iban, setIban] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [dueDate, setDueDate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    // History navbar state
    const [showHistoryNavbar, setShowHistoryNavbar] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [historyInvoices, setHistoryInvoices] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [historySearchTerm, setHistorySearchTerm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [historyFilterStatus, setHistoryFilterStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('all');
    const [currentEditingInvoice, setCurrentEditingInvoice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Default lock state
    const defaultLockState = {
        invoiceDetails: false,
        sellerDetails: false,
        clientDetails: false,
        bankDetails: false,
        items: false,
        notes: false
    };
    // TEMPORARILY DISABLED AUTO-SUGGESTIONS TO FIX INPUT ISSUE
    // LOCK STATE - USES GLOBAL WINDOW SYSTEM
    const [lockedSections, setLockedSections] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultLockState);
    const [isHydrated, setIsHydrated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // LOAD LOCKS AND SUGGESTIONS ON MOUNT
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CreateInvoicePage.useEffect": ()=>{
            console.log('🔄 LOADING LOCKS AND SUGGESTIONS...');
            const loadSavedLocks = {
                "CreateInvoicePage.useEffect.loadSavedLocks": ()=>{
                    try {
                        const savedLocks = loadLocks();
                        console.log('📋 LOADED LOCKS:', savedLocks);
                        setLockedSections(savedLocks);
                        setIsHydrated(true);
                        // Apply to DOM
                        setTimeout({
                            "CreateInvoicePage.useEffect.loadSavedLocks": ()=>{
                                Object.entries(savedLocks).forEach({
                                    "CreateInvoicePage.useEffect.loadSavedLocks": ([section, isLocked])=>{
                                        const sectionContainer = document.querySelector(`[data-section="${section}"]`);
                                        if (sectionContainer) {
                                            const formElements = sectionContainer.querySelectorAll('input, textarea, button:not([data-lock-button="true"])');
                                            formElements.forEach({
                                                "CreateInvoicePage.useEffect.loadSavedLocks": (element)=>{
                                                    const el = element;
                                                    if (isLocked) {
                                                        el.disabled = true;
                                                        el.classList.add('opacity-50', 'cursor-not-allowed');
                                                    } else {
                                                        el.disabled = false;
                                                        el.classList.remove('opacity-50', 'cursor-not-allowed');
                                                    }
                                                }
                                            }["CreateInvoicePage.useEffect.loadSavedLocks"]);
                                        }
                                    }
                                }["CreateInvoicePage.useEffect.loadSavedLocks"]);
                            }
                        }["CreateInvoicePage.useEffect.loadSavedLocks"], 500);
                        const lockedCount = Object.values(savedLocks).filter(Boolean).length;
                        if (lockedCount > 0) {
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`🔒 ${lockedCount} sections restored!`, {
                                duration: 2000
                            });
                        }
                    } catch (error) {
                        console.error('❌ Load error:', error);
                        setLockedSections(defaultLockState);
                        setIsHydrated(true);
                    }
                }
            }["CreateInvoicePage.useEffect.loadSavedLocks"];
            // Suggestions temporarily disabled
            setTimeout(loadSavedLocks, 300);
        }
    }["CreateInvoicePage.useEffect"], []);
    // SUPER SIMPLE TOGGLE FUNCTION
    const toggleSectionLock = (section)=>{
        try {
            console.log(`🔄 SIMPLE TOGGLE: ${section}`);
            const newValue = !lockedSections[section];
            const newState = {
                ...lockedSections,
                [section]: newValue
            };
            // Save immediately
            saveLocks(newState);
            // Update React state
            setLockedSections(newState);
            // Apply to DOM
            setTimeout(()=>{
                const sectionContainer = document.querySelector(`[data-section="${section}"]`);
                if (sectionContainer) {
                    const formElements = sectionContainer.querySelectorAll('input, textarea, button:not([data-lock-button="true"])');
                    formElements.forEach((element)=>{
                        const el = element;
                        if (newValue) {
                            el.disabled = true;
                            el.classList.add('opacity-50', 'cursor-not-allowed');
                        } else {
                            el.disabled = false;
                            el.classList.remove('opacity-50', 'cursor-not-allowed');
                        }
                    });
                }
            }, 50);
            const sectionName = section.replace(/([A-Z])/g, ' $1').replace(/^./, (str)=>str.toUpperCase());
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`${newValue ? '🔒' : '🔓'} ${sectionName} ${newValue ? 'locked' : 'unlocked'}!`, {
                duration: 1500
            });
            console.log(`✅ SIMPLE TOGGLE COMPLETE: ${section} = ${newValue}`);
        } catch (error) {
            console.error('❌ SIMPLE TOGGLE ERROR:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to toggle lock');
        }
    };
    // REMOVED SUGGESTION INPUT - USING DIRECT INPUTS NOW
    // SIMPLE DEBUG FUNCTION
    const debugLockState = ()=>{
        try {
            console.log('=== 🔍 SIMPLE DEBUG ===');
            console.log('React State:', lockedSections);
            const stored = localStorage.getItem(SIMPLE_LOCK_KEY);
            console.log('localStorage:', stored ? JSON.parse(stored) : null);
            const backup = localStorage.getItem(SIMPLE_LOCK_KEY + '_backup');
            console.log('localStorage backup:', backup ? JSON.parse(backup) : null);
            console.log('Suggestions:', suggestions);
            console.log('=======================');
            const reactCount = Object.values(lockedSections).filter(Boolean).length;
            const suggestionCount = Object.keys(suggestions).length;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(`🔍 ${reactCount} sections locked | ${suggestionCount} suggestion fields`, {
                duration: 3000
            });
        } catch (error) {
            console.error('❌ Debug error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Debug failed');
        }
    };
    // SIMPLE DIRECT LOCK CONTROL
    const directLockControl = {
        getCurrentState: ()=>lockedSections
    };
    // Load invoice history on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CreateInvoicePage.useEffect": ()=>{
            loadInvoiceHistory();
        }
    }["CreateInvoicePage.useEffect"], []);
    // Load invoice history for navbar
    const loadInvoiceHistory = ()=>{
        try {
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const invoices = [];
            for(let i = 0; i < localStorage.length; i++){
                const key = localStorage.key(i);
                if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {
                    const data = localStorage.getItem(key);
                    if (data) {
                        try {
                            const invoice = JSON.parse(data);
                            if (invoice.invoiceNumber && invoice.timestamp) {
                                invoices.push({
                                    key,
                                    id: key,
                                    ...invoice,
                                    status: invoice.status || 'draft'
                                });
                            }
                        } catch (e) {
                        // Skip invalid data
                        }
                    }
                }
            }
            // Sort by timestamp (newest first)
            const sortedInvoices = invoices.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
            setHistoryInvoices(sortedInvoices);
        } catch (error) {
            console.error('Error loading invoice history:', error);
        }
    };
    // Logo upload handlers with dynamic auto-sizing
    const processAndResizeLogo = (file, maxWidth = 200, maxHeight = 150)=>{
        return new Promise((resolve, reject)=>{
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.onload = ()=>{
                // Calculate new dimensions while maintaining aspect ratio
                let { width, height } = img;
                if (width > maxWidth || height > maxHeight) {
                    const aspectRatio = width / height;
                    if (width > height) {
                        width = maxWidth;
                        height = width / aspectRatio;
                    } else {
                        height = maxHeight;
                        width = height * aspectRatio;
                    }
                }
                // Set canvas dimensions
                canvas.width = width;
                canvas.height = height;
                // Draw and resize image
                ctx?.drawImage(img, 0, 0, width, height);
                // Convert to base64 with high quality
                const resizedDataUrl = canvas.toDataURL('image/jpeg', 0.9);
                resolve(resizedDataUrl);
            };
            img.onerror = ()=>reject(new Error('Failed to load image'));
            img.src = URL.createObjectURL(file);
        });
    };
    const handleSellerLogoUpload = async (event)=>{
        const file = event.target.files?.[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Logo file size must be less than 5MB');
                return;
            }
            if (!file.type.startsWith('image/')) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please select a valid image file');
                return;
            }
            try {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].loading('Processing seller logo...', {
                    id: 'seller-logo'
                });
                const resizedLogo = await processAndResizeLogo(file, 200, 150);
                setSellerLogo(resizedLogo);
                setSellerLogoFile(file);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Seller logo uploaded and optimized! 🖼️✨', {
                    id: 'seller-logo'
                });
            } catch (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to process logo. Please try again.', {
                    id: 'seller-logo'
                });
            }
        }
    };
    const handleClientLogoUpload = async (event)=>{
        const file = event.target.files?.[0];
        if (file) {
            if (file.size > 5 * 1024 * 1024) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Logo file size must be less than 5MB');
                return;
            }
            if (!file.type.startsWith('image/')) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please select a valid image file');
                return;
            }
            try {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].loading('Processing client logo...', {
                    id: 'client-logo'
                });
                const resizedLogo = await processAndResizeLogo(file, 200, 150);
                setClientLogo(resizedLogo);
                setClientLogoFile(file);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Client logo uploaded and optimized! 🖼️✨', {
                    id: 'client-logo'
                });
            } catch (error) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to process logo. Please try again.', {
                    id: 'client-logo'
                });
            }
        }
    };
    const removeSellerLogo = ()=>{
        setSellerLogo(null);
        setSellerLogoFile(null);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Seller logo removed');
    };
    const removeClientLogo = ()=>{
        setClientLogo(null);
        setClientLogoFile(null);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Client logo removed');
    };
    // Invoice history management functions
    const editInvoice = (invoice)=>{
        try {
            // Load all invoice data into the form
            setInvoiceNumber(invoice.invoiceNumber || '');
            setInvoiceDate(invoice.invoiceDate || new Date().toISOString().split('T')[0]);
            setDueDate(invoice.dueDate || '');
            setCurrency(invoice.currency || 'SAR');
            setDiscount(invoice.discount || '0');
            setDeductionAmount(invoice.deductionAmount || '0');
            setDeductionReason(invoice.deductionReason || '');
            // Seller details
            setSellerName(invoice.sellerName || '');
            setSellerAddress(invoice.sellerAddress || '');
            setSellerVAT(invoice.sellerVAT || '');
            setSellerLogo(invoice.sellerLogo || null);
            // Client details
            setClientName(invoice.clientName || '');
            setClientAddress(invoice.clientAddress || '');
            setClientVAT(invoice.clientVAT || '');
            setClientLogo(invoice.clientLogo || null);
            // Additional details
            setNotes(invoice.notes || '');
            setTerms(invoice.terms || '');
            setBankName(invoice.bankName || '');
            setAccountName(invoice.accountName || '');
            setAccountNumber(invoice.accountNumber || '');
            setIban(invoice.iban || '');
            // Items
            if (invoice.items && invoice.items.length > 0) {
                setItems(invoice.items);
            }
            setCurrentEditingInvoice(invoice.key);
            setShowHistoryNavbar(false);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Editing invoice: ${invoice.invoiceNumber} 📝`);
        } catch (error) {
            console.error('Error editing invoice:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load invoice for editing');
        }
    };
    const duplicateInvoice = (invoice)=>{
        try {
            // Load invoice data but generate new invoice number
            const newInvoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;
            editInvoice(invoice);
            setInvoiceNumber(newInvoiceNumber);
            setCurrentEditingInvoice(null);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Invoice duplicated as: ${newInvoiceNumber} 📋`);
        } catch (error) {
            console.error('Error duplicating invoice:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to duplicate invoice');
        }
    };
    const deleteInvoice = (invoiceKey, invoiceNumber)=>{
        try {
            localStorage.removeItem(invoiceKey);
            loadInvoiceHistory();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Invoice ${invoiceNumber} deleted successfully! 🗑️`);
        } catch (error) {
            console.error('Error deleting invoice:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to delete invoice');
        }
    };
    const viewInvoiceDetails = (invoice)=>{
        // Create a preview of the invoice with current date
        const currentDate = new Date().toISOString().split('T')[0];
        console.log('📋 View Invoice Details - Using current date:', currentDate, 'Original date was:', invoice.invoiceDate);
        const invoiceData = {
            invoiceNumber: invoice.invoiceNumber,
            invoiceDate: currentDate,
            dueDate: invoice.dueDate,
            currency: invoice.currency,
            discount: invoice.discount,
            deductionAmount: invoice.deductionAmount,
            deductionReason: invoice.deductionReason,
            sellerName: invoice.sellerName,
            sellerAddress: invoice.sellerAddress,
            sellerVAT: invoice.sellerVAT,
            sellerLogo: invoice.sellerLogo,
            clientName: invoice.clientName,
            clientAddress: invoice.clientAddress,
            clientVAT: invoice.clientVAT,
            clientLogo: invoice.clientLogo,
            items: invoice.items || [],
            notes: invoice.notes,
            terms: invoice.terms,
            bankName: invoice.bankName,
            accountName: invoice.accountName,
            accountNumber: invoice.accountNumber,
            iban: invoice.iban,
            subtotal: invoice.subtotal,
            subtotalAfterDeduction: invoice.subtotalAfterDeduction,
            totalVAT: invoice.totalVAT,
            totalAmount: invoice.totalAmount,
            qrCode: generateQRCode(),
            logoPreview: undefined
        };
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$pdfGenerator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["downloadInvoicePDF"])(invoiceData);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Viewing invoice: ${invoice.invoiceNumber} with current date 👁️`);
        } catch (error) {
            console.error('Error viewing invoice:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to view invoice');
        }
    };
    // Simple calculations
    const subtotal = items.reduce((sum, item)=>sum + item.amount, 0);
    const discountAmount = subtotal * Number(discount) / 100;
    const subtotalAfterDiscount = subtotal - discountAmount;
    const subtotalAfterDeduction = Math.max(0, subtotalAfterDiscount - Number(deductionAmount));
    const totalVAT = subtotalAfterDeduction * 0.15;
    const totalAmount = subtotalAfterDeduction + totalVAT;
    // Simple functions
    const addItem = ()=>{
        const newItem = {
            id: Date.now().toString(),
            description: '',
            quantity: '1',
            rate: '0',
            vatPercent: 15,
            amount: 0
        };
        setItems([
            ...items,
            newItem
        ]);
    };
    const removeItem = (itemId)=>{
        if (items.length <= 1) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('At least one item is required');
            return;
        }
        setItems(items.filter((item)=>item.id !== itemId));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Item removed successfully! 🗑️');
    };
    // --- ENFORCE STRING STATE FOR ALL ITEM INPUTS ---
    // Defensive wrapper for updateItem to ensure value is always a string
    const safeUpdateItem = (id, field, value)=>{
        // Always pass value as string
        updateItem(id, field, String(value));
    };
    const updateItem = (id, field, value)=>{
        setItems(items.map((item)=>{
            if (item.id === id) {
                let updatedItem = {
                    ...item,
                    [field]: value
                };
                // Calculate amount only if both quantity and rate are valid numbers
                const quantityNum = Number(updatedItem.quantity);
                const rateNum = Number(updatedItem.rate);
                if (!isNaN(quantityNum) && !isNaN(rateNum)) {
                    updatedItem.amount = quantityNum * rateNum;
                } else {
                    updatedItem.amount = 0;
                }
                return updatedItem;
            }
            return item;
        }));
    };
    // QR Code generation function - ALWAYS uses current date
    const generateQRCode = ()=>{
        // Always use current date for QR code timestamp
        const currentDate = new Date();
        const qrData = {
            sellerName: sellerName || 'KAAZMAAMAA Company',
            vatNumber: sellerVAT || '30**********003',
            timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zatcaQR$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateZATCATimestamp"])(currentDate),
            invoiceTotal: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zatcaQR$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrencyForZATCA"])(totalAmount),
            vatTotal: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zatcaQR$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrencyForZATCA"])(totalVAT)
        };
        try {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$zatcaQR$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateZATCAQRCode"])(qrData);
        } catch (error) {
            console.error('QR Code generation error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to generate QR code');
            return '';
        }
    };
    // PDF Generation function
    const generatePDF = ()=>{
        if (!sellerName || !clientName || items.length === 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please fill in all required fields before generating PDF');
            return;
        }
        // Validate deduction reason if deduction amount is specified
        if (Number(deductionAmount) > 0 && !deductionReason.trim()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please provide a reason for the deduction amount');
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info('Generating PDF with ZATCA QR code...');
        // Always use current date for PDF generation
        const currentDate = new Date().toISOString().split('T')[0];
        console.log('🗓️ PDF Generation - Using current date:', currentDate);
        const invoiceData = {
            invoiceNumber,
            invoiceDate: currentDate,
            dueDate,
            currency,
            discount,
            deductionAmount,
            deductionReason,
            sellerName,
            sellerAddress,
            sellerVAT,
            sellerLogo,
            clientName,
            clientAddress,
            clientVAT,
            clientLogo,
            items,
            notes,
            terms,
            bankName,
            accountName,
            accountNumber,
            iban,
            subtotal,
            subtotalAfterDeduction,
            totalVAT,
            totalAmount,
            qrCode: generateQRCode(),
            logoPreview: undefined
        };
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$pdfGenerator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["downloadInvoicePDF"])(invoiceData);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`PDF generated with current date: ${new Date().toLocaleDateString()} 📄`);
        } catch (error) {
            console.error('PDF generation error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to generate PDF');
        }
    };
    // Print Invoice function
    const handlePrintInvoice = ()=>{
        if (!sellerName || !clientName || items.length === 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please fill in all required fields before printing');
            return;
        }
        // Validate deduction reason if deduction amount is specified
        if (Number(deductionAmount) > 0 && !deductionReason.trim()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please provide a reason for the deduction amount');
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info('Opening print dialog...');
        // Always use current date for print as well
        const currentDate = new Date().toISOString().split('T')[0];
        console.log('🖨️ Print Invoice - Using current date:', currentDate);
        const invoiceData = {
            invoiceNumber,
            invoiceDate: currentDate,
            dueDate,
            currency,
            discount,
            deductionAmount,
            deductionReason,
            sellerName,
            sellerAddress,
            sellerVAT,
            sellerLogo,
            clientName,
            clientAddress,
            clientVAT,
            clientLogo,
            items,
            notes,
            terms,
            bankName,
            accountName,
            accountNumber,
            iban,
            subtotal,
            subtotalAfterDeduction,
            totalVAT,
            totalAmount,
            qrCode: generateQRCode(),
            logoPreview: undefined
        };
        try {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$pdfGenerator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["printInvoice"])(invoiceData);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Print dialog opened successfully! 🖨️');
        } catch (error) {
            console.error('Print error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to open print dialog');
        }
    };
    const saveDraft = ()=>{
        try {
            const formData = {
                invoiceNumber,
                invoiceDate,
                dueDate,
                currency,
                discount,
                deductionAmount,
                deductionReason,
                sellerName,
                sellerAddress,
                sellerVAT,
                sellerLogo,
                clientName,
                clientAddress,
                clientVAT,
                clientLogo,
                notes,
                terms,
                bankName,
                accountName,
                accountNumber,
                iban,
                items,
                subtotal,
                subtotalAfterDeduction,
                totalVAT,
                totalAmount,
                status: 'draft',
                timestamp: new Date().toISOString(),
                lastModified: new Date().toISOString()
            };
            // Save as current draft
            localStorage.setItem('invoice-draft', JSON.stringify(formData));
            // Save with unique key for history
            if (sellerName || clientName || invoiceNumber) {
                let historyKey;
                if (currentEditingInvoice) {
                    // Update existing invoice
                    historyKey = currentEditingInvoice;
                    formData.lastModified = new Date().toISOString();
                } else {
                    // Create new invoice
                    historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`;
                }
                localStorage.setItem(historyKey, JSON.stringify(formData));
                // Update history
                loadInvoiceHistory();
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(currentEditingInvoice ? 'Invoice updated! 📝' : 'Draft saved! 💾');
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Save failed');
        }
    };
    const saveAsCompleted = ()=>{
        try {
            if (!sellerName || !clientName || items.length === 0) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please fill in all required fields before marking as completed');
                return;
            }
            const formData = {
                invoiceNumber,
                invoiceDate,
                dueDate,
                currency,
                discount,
                deductionAmount,
                deductionReason,
                sellerName,
                sellerAddress,
                sellerVAT,
                sellerLogo,
                clientName,
                clientAddress,
                clientVAT,
                clientLogo,
                notes,
                terms,
                bankName,
                accountName,
                accountNumber,
                iban,
                items,
                subtotal,
                subtotalAfterDeduction,
                totalVAT,
                totalAmount,
                status: 'completed',
                timestamp: new Date().toISOString(),
                lastModified: new Date().toISOString(),
                qrCode: generateQRCode()
            };
            let historyKey;
            if (currentEditingInvoice) {
                historyKey = currentEditingInvoice;
            } else {
                historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`;
            }
            localStorage.setItem(historyKey, JSON.stringify(formData));
            loadInvoiceHistory();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Invoice ${invoiceNumber} marked as completed! ✅`);
        } catch (error) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to save completed invoice');
        }
    };
    const createNewInvoice = ()=>{
        // Clear all form fields
        setInvoiceNumber(`INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`);
        setInvoiceDate(new Date().toISOString().split('T')[0]);
        setDueDate('');
        setCurrency('SAR');
        setDiscount('0');
        setDeductionAmount('0');
        setDeductionReason('');
        // Clear seller details (but keep for convenience)
        // setSellerName('')
        // setSellerAddress('')
        // setSellerVAT('')
        setSellerLogo(null);
        // Clear client details
        setClientName('');
        setClientAddress('');
        setClientVAT('');
        setClientLogo(null);
        // Clear additional details
        setNotes('');
        setTerms('');
        setBankName('');
        setAccountName('');
        setAccountNumber('');
        setIban('');
        // Reset items
        setItems([
            {
                id: '1',
                description: '',
                quantity: '1',
                rate: '0',
                vatPercent: 15,
                amount: 0
            }
        ]);
        setCurrentEditingInvoice(null);
        setShowHistoryNavbar(false);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('New invoice form ready! 📄');
    };
    // Function to set current date
    const setCurrentDate = ()=>{
        const currentDate = new Date().toISOString().split('T')[0];
        setInvoiceDate(currentDate);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Invoice date updated to current date! 📅');
    };
    // GLOBAL UNLOCK ALL
    const unlockAllSections = ()=>{
        if ("object" !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
            window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.unlockAll();
            // Update React state
            const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load();
            setLockedSections(newState);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('🔓 ALL SECTIONS GLOBAL UNLOCKED!', {
                duration: 3000
            });
        }
    };
    // GLOBAL LOCK ALL
    const lockAllSections = ()=>{
        if ("object" !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
            window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.lockAll();
            // Update React state
            const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load();
            setLockedSections(newState);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('🔒 ALL SECTIONS GLOBAL LOCKED - PERMANENT!', {
                duration: 3000
            });
        }
    };
    // GLOBAL FORCE RELOAD
    const forceReloadLocks = ()=>{
        try {
            console.log('🔄 GLOBAL FORCE RELOAD TRIGGERED');
            if ("object" !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
                const reloadedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load();
                console.log('🔒 GLOBAL RELOADED STATE:', reloadedState);
                // Update React state
                setLockedSections(reloadedState);
                // Apply to DOM
                window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(reloadedState);
                const lockedCount = Object.values(reloadedState).filter(Boolean).length;
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`🔄 GLOBAL RELOADED: ${lockedCount} sections locked!`, {
                    duration: 2000
                });
            }
        } catch (error) {
            console.error('❌ Global reload error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Global reload failed!');
        }
    };
    // GLOBAL FORCE SAVE
    const forceSave = ()=>{
        try {
            if ("object" !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
                const currentState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load();
                console.log('🚀 GLOBAL FORCE SAVE TRIGGERED:', currentState);
                const saved = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.save(currentState);
                if (saved) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('🚀 GLOBAL FORCE SAVE COMPLETE!', {
                        duration: 2000
                    });
                    console.log('✅ GLOBAL FORCE SAVE SUCCESSFUL');
                    // Show verification
                    setTimeout(()=>{
                        const verify = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load();
                        console.log('✅ GLOBAL SAVE VERIFIED:', verify);
                    }, 100);
                } else {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('❌ GLOBAL FORCE SAVE FAILED!');
                    console.error('❌ GLOBAL FORCE SAVE FAILED');
                }
            }
        } catch (error) {
            console.error('❌ Global force save error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Global force save failed!');
        }
    };
    // GLOBAL CLEAR (TESTING)
    const clearLockState = ()=>{
        try {
            console.log('🗑️ GLOBAL CLEAR TRIGGERED');
            if ("object" !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
                // Use global clear method
                window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.clear();
                // Update React state
                const clearedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load();
                setLockedSections(clearedState);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('🗑️ GLOBAL CLEARED FROM ALL 50+ LOCATIONS!', {
                    duration: 3000
                });
                console.log('✅ GLOBAL CLEAR COMPLETE');
            }
        } catch (error) {
            console.error('❌ Global clear error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Global clear failed!');
        }
    };
    // Preview function to show current modifications
    const previewInvoice = ()=>{
        if (!sellerName || !clientName || items.length === 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please fill in all required fields before preview');
            return;
        }
        if (Number(deductionAmount) > 0 && !deductionReason.trim()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Please provide a reason for the deduction amount');
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info('Generating preview with current date...');
        // Always use current date for preview
        const currentDate = new Date().toISOString().split('T')[0];
        const currentDateFormatted = new Date().toLocaleDateString();
        console.log('👁️ Preview Invoice - Using current date:', currentDate, 'Formatted:', currentDateFormatted);
        const invoiceData = {
            invoiceNumber,
            invoiceDate: currentDate,
            dueDate,
            currency,
            discount,
            deductionAmount,
            deductionReason,
            sellerName,
            sellerAddress,
            sellerVAT,
            sellerLogo,
            clientName,
            clientAddress,
            clientVAT,
            clientLogo,
            items,
            notes,
            terms,
            bankName,
            accountName,
            accountNumber,
            iban,
            subtotal,
            subtotalAfterDeduction,
            totalVAT,
            totalAmount,
            qrCode: generateQRCode(),
            logoPreview: undefined
        };
        try {
            // Create preview window
            const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes');
            if (previewWindow) {
                const html = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Invoice Preview - Current Date: ${currentDateFormatted}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background: #f5f5f5;
              }
              .preview-header {
                background: #4f46e5;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                text-align: center;
              }
              .preview-info {
                background: #e0f2fe;
                border: 2px solid #0288d1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
              }
              .lock-status {
                background: #fff3e0;
                border: 2px solid #ff9800;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
              }
              .locked { color: #d32f2f; font-weight: bold; }
              .unlocked { color: #388e3c; font-weight: bold; }
              .invoice-preview {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              .field { margin: 8px 0; }
              .label { font-weight: bold; color: #333; }
              .value { color: #666; margin-left: 10px; }
              .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
              .current-date { color: #4f46e5; font-weight: bold; font-size: 18px; }
            </style>
          </head>
          <body>
            <div class="preview-header">
              <h1>📄 Invoice Preview</h1>
              <p>Preview with Current Date Modifications Applied</p>
            </div>

            <div class="preview-info">
              <h3>🔍 Preview Information</h3>
              <div class="field">
                <span class="label">Current Date Applied:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
              <div class="field">
                <span class="label">QR Code Date:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
              <div class="field">
                <span class="label">PDF Date:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
            </div>

            <div class="lock-status">
              <h3>🔒 Lock Status</h3>
              <div class="field">
                <span class="label">Invoice Details:</span>
                <span class="value ${lockedSections.invoiceDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.invoiceDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Seller Details:</span>
                <span class="value ${lockedSections.sellerDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.sellerDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Client Details:</span>
                <span class="value ${lockedSections.clientDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.clientDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Bank Details:</span>
                <span class="value ${lockedSections.bankDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.bankDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Invoice Items:</span>
                <span class="value ${lockedSections.items ? 'locked' : 'unlocked'}">
                  ${lockedSections.items ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Additional Information:</span>
                <span class="value ${lockedSections.notes ? 'locked' : 'unlocked'}">
                  ${lockedSections.notes ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
            </div>

            <div class="invoice-preview">
              <h3>📋 Invoice Data Preview</h3>

              <div class="section">
                <h4>Invoice Details</h4>
                <div class="field">
                  <span class="label">Invoice Number:</span>
                  <span class="value">${invoiceNumber}</span>
                </div>
                <div class="field">
                  <span class="label">Invoice Date:</span>
                  <span class="value current-date">${currentDateFormatted} (CURRENT DATE APPLIED)</span>
                </div>
                <div class="field">
                  <span class="label">Due Date:</span>
                  <span class="value">${dueDate || 'Not specified'}</span>
                </div>
                <div class="field">
                  <span class="label">Currency:</span>
                  <span class="value">${currency}</span>
                </div>
              </div>

              <div class="section">
                <h4>Seller Details</h4>
                <div class="field">
                  <span class="label">Company Name:</span>
                  <span class="value">${sellerName}</span>
                </div>
                <div class="field">
                  <span class="label">VAT Number:</span>
                  <span class="value">${sellerVAT}</span>
                </div>
                <div class="field">
                  <span class="label">Address:</span>
                  <span class="value">${sellerAddress}</span>
                </div>
              </div>

              <div class="section">
                <h4>Client Details</h4>
                <div class="field">
                  <span class="label">Client Name:</span>
                  <span class="value">${clientName}</span>
                </div>
                <div class="field">
                  <span class="label">Client VAT:</span>
                  <span class="value">${clientVAT || 'Not provided'}</span>
                </div>
                <div class="field">
                  <span class="label">Client Address:</span>
                  <span class="value">${clientAddress}</span>
                </div>
              </div>

              <div class="section">
                <h4>Financial Summary</h4>
                <div class="field">
                  <span class="label">Subtotal:</span>
                  <span class="value">${currency} ${subtotal.toFixed(2)}</span>
                </div>
                ${Number(deductionAmount) > 0 ? `
                  <div class="field">
                    <span class="label">Deduction:</span>
                    <span class="value">-${currency} ${Number(deductionAmount).toFixed(2)}</span>
                  </div>
                ` : ''}
                <div class="field">
                  <span class="label">Total VAT (15%):</span>
                  <span class="value">${currency} ${totalVAT.toFixed(2)}</span>
                </div>
                <div class="field">
                  <span class="label">Total Amount:</span>
                  <span class="value current-date">${currency} ${totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </body>
          </html>
        `;
                previewWindow.document.write(html);
                previewWindow.document.close();
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Preview opened with current date: ${currentDateFormatted} 👁️`);
            }
        } catch (error) {
            console.error('Preview error:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to open preview');
        }
    };
    // Load previous user data from localStorage
    const loadPreviousData = ()=>{
        try {
            const savedData = localStorage.getItem('invoice-draft');
            if (savedData) {
                const data = JSON.parse(savedData);
                // Load all the saved data
                if (data.invoiceNumber) setInvoiceNumber(data.invoiceNumber);
                if (data.invoiceDate) setInvoiceDate(data.invoiceDate);
                if (data.dueDate) setDueDate(data.dueDate);
                if (data.currency) setCurrency(data.currency);
                if (data.discount !== undefined) setDiscount(data.discount);
                if (data.deductionAmount !== undefined) setDeductionAmount(data.deductionAmount);
                if (data.deductionReason) setDeductionReason(data.deductionReason);
                // Seller details
                if (data.sellerName) setSellerName(data.sellerName);
                if (data.sellerAddress) setSellerAddress(data.sellerAddress);
                if (data.sellerVAT) setSellerVAT(data.sellerVAT);
                if (data.sellerLogo) setSellerLogo(data.sellerLogo);
                // Client details
                if (data.clientName) setClientName(data.clientName);
                if (data.clientAddress) setClientAddress(data.clientAddress);
                if (data.clientVAT) setClientVAT(data.clientVAT);
                if (data.clientLogo) setClientLogo(data.clientLogo);
                // Additional details
                if (data.notes) setNotes(data.notes);
                if (data.terms) setTerms(data.terms);
                if (data.bankName) setBankName(data.bankName);
                if (data.accountName) setAccountName(data.accountName);
                if (data.accountNumber) setAccountNumber(data.accountNumber);
                if (data.iban) setIban(data.iban);
                // Items
                if (data.items && data.items.length > 0) {
                    setItems(data.items);
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Previous data loaded successfully! 📋');
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info('No previous data found. Start entering your information.');
            }
        } catch (error) {
            console.error('Error loading previous data:', error);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Failed to load previous data');
        }
    };
    // Get recent invoices for quick loading
    const getRecentInvoices = ()=>{
        try {
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            } // Server-side rendering check
            const recentInvoices = [];
            for(let i = 0; i < localStorage.length; i++){
                const key = localStorage.key(i);
                if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {
                    const data = localStorage.getItem(key);
                    if (data) {
                        try {
                            const invoice = JSON.parse(data);
                            if (invoice.invoiceNumber && invoice.timestamp) {
                                recentInvoices.push({
                                    key,
                                    ...invoice
                                });
                            }
                        } catch (e) {
                        // Skip invalid data
                        }
                    }
                }
            }
            // Sort by timestamp (newest first) and take top 5
            return recentInvoices.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 5);
        } catch (error) {
            console.error('Error getting recent invoices:', error);
            return [];
        }
    };
    const loadInvoiceData = (invoice)=>{
        // Load all the invoice data
        setInvoiceNumber(invoice.invoiceNumber || '');
        setInvoiceDate(invoice.invoiceDate || '');
        setDueDate(invoice.dueDate || '');
        setCurrency(invoice.currency || 'SAR');
        setDiscount(invoice.discount || '0');
        setDeductionAmount(invoice.deductionAmount || '0');
        setDeductionReason(invoice.deductionReason || '');
        // Seller details
        setSellerName(invoice.sellerName || '');
        setSellerAddress(invoice.sellerAddress || '');
        setSellerVAT(invoice.sellerVAT || '');
        setSellerLogo(invoice.sellerLogo || null);
        // Client details
        setClientName(invoice.clientName || '');
        setClientAddress(invoice.clientAddress || '');
        setClientVAT(invoice.clientVAT || '');
        setClientLogo(invoice.clientLogo || null);
        // Additional details
        setNotes(invoice.notes || '');
        setTerms(invoice.terms || '');
        setBankName(invoice.bankName || '');
        setAccountName(invoice.accountName || '');
        setAccountNumber(invoice.accountNumber || '');
        setIban(invoice.iban || '');
        // Items
        if (invoice.items && invoice.items.length > 0) {
            setItems(invoice.items);
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(`Invoice ${invoice.invoiceNumber} loaded! 📋`);
    };
    // Defensive value for quantity and rate
    function getSafeString(val, field) {
        if (typeof val !== 'string') {
            console.warn(`Invoice item field '${field}' received non-string value:`, val);
            return val === undefined || val === null ? '' : String(val);
        }
        return val;
    }
    // Add debug log in parent render
    console.log('[CreateInvoicePage] render, deductionReason=', deductionReason, 'sellerName=', sellerName, 'clientName=', clientName, 'bankName=', bankName, 'notes=', notes, 'terms=', terms);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen bg-gray-50",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "bg-white shadow-sm border-b",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 py-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "ghost",
                                        size: "sm",
                                        onClick: ()=>router.push('/invoice'),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                                className: "h-4 w-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1363,
                                                columnNumber: 17
                                            }, this),
                                            "Back to Invoice"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1358,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                                className: "text-2xl font-bold text-purple-600",
                                                children: currentEditingInvoice ? 'Edit Invoice' : 'Create Invoice'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1367,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm text-gray-600",
                                                children: currentEditingInvoice ? 'Modify and update your existing invoice' : 'Generate professional ZATCA-compliant invoices'
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1370,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1366,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                lineNumber: 1357,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: ()=>setShowHistoryNavbar(!showHistoryNavbar),
                                        className: "bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$history$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__History$3e$__["History"], {
                                                className: "h-4 w-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1385,
                                                columnNumber: 17
                                            }, this),
                                            "History (",
                                            historyInvoices.length,
                                            ")"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1379,
                                        columnNumber: 15
                                    }, this),
                                    currentEditingInvoice && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: createNewInvoice,
                                        className: "bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                className: "h-4 w-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1395,
                                                columnNumber: 19
                                            }, this),
                                            "New Invoice"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1389,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: loadPreviousData,
                                        className: "bg-blue-600 hover:bg-blue-700 text-white",
                                        children: "📋 Load Previous Data"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1399,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: saveDraft,
                                        children: "💾 Save Draft"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1408,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: saveAsCompleted,
                                        className: "bg-green-50 text-green-600 hover:bg-green-100 border-green-200",
                                        children: "✅ Mark Complete"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1415,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: unlockAllSections,
                                        className: "bg-yellow-50 text-yellow-600 hover:bg-yellow-100 border-yellow-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                className: "h-4 w-4 mr-1"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1429,
                                                columnNumber: 17
                                            }, this),
                                            "Unlock All"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1423,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: lockAllSections,
                                        className: "bg-red-50 text-red-600 hover:bg-red-100 border-red-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                className: "h-4 w-4 mr-1"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1438,
                                                columnNumber: 17
                                            }, this),
                                            "Lock All"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1432,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: previewInvoice,
                                        className: "bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                className: "h-4 w-4 mr-1"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1447,
                                                columnNumber: 17
                                            }, this),
                                            "Preview"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1441,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: debugLockState,
                                        className: "bg-gray-50 text-gray-600 hover:bg-gray-100 border-gray-200",
                                        title: "Debug lock state",
                                        children: "🔍 Debug"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1450,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: forceReloadLocks,
                                        className: "bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200",
                                        title: "Force reload lock state",
                                        children: "🔄 Reload"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1459,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: forceSave,
                                        className: "bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200",
                                        title: "Force save lock state",
                                        children: "🚀 Save"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1468,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: clearLockState,
                                        className: "bg-orange-50 text-orange-600 hover:bg-orange-100 border-orange-200",
                                        title: "Clear all lock state (for testing)",
                                        children: "🗑️ Clear"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1477,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: generatePDF,
                                        className: "bg-green-600 hover:bg-green-700 text-white",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                className: "h-4 w-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1492,
                                                columnNumber: 17
                                            }, this),
                                            "Generate PDF"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1486,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        variant: "outline",
                                        size: "sm",
                                        onClick: handlePrintInvoice,
                                        className: "bg-purple-600 hover:bg-purple-700 text-white",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"], {
                                                className: "h-4 w-4 mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1501,
                                                columnNumber: 17
                                            }, this),
                                            "Print Invoice"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1495,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                lineNumber: 1378,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/invoice/create/page.tsx",
                        lineNumber: 1356,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/invoice/create/page.tsx",
                    lineNumber: 1355,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/invoice/create/page.tsx",
                lineNumber: 1354,
                columnNumber: 7
            }, this),
            showHistoryNavbar && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white border-b shadow-sm",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "max-w-7xl mx-auto px-4 py-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between mb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-lg font-semibold text-gray-900",
                                            children: "Invoice History"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1515,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm text-gray-500",
                                            children: [
                                                "(",
                                                historyInvoices.length,
                                                " invoices)"
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1516,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 1514,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-3",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                                    className: "h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 1520,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                    placeholder: "Search invoices...",
                                                    value: historySearchTerm,
                                                    onChange: (e)=>setHistorySearchTerm(e.target.value),
                                                    className: "pl-10 w-64"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 1521,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1519,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                            value: historyFilterStatus,
                                            onChange: (e)=>setHistoryFilterStatus(e.target.value),
                                            className: "px-3 py-2 border border-gray-300 rounded-md text-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "all",
                                                    children: "All Status"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 1533,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "draft",
                                                    children: "Draft"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 1534,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                    value: "completed",
                                                    children: "Completed"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 1535,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1528,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                            variant: "outline",
                                            size: "sm",
                                            onClick: ()=>setShowHistoryNavbar(false),
                                            children: "Close"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1537,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 1518,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/invoice/create/page.tsx",
                            lineNumber: 1513,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto",
                            children: [
                                historyInvoices.filter((invoice)=>{
                                    const matchesSearch = !historySearchTerm || invoice.invoiceNumber?.toLowerCase().includes(historySearchTerm.toLowerCase()) || invoice.clientName?.toLowerCase().includes(historySearchTerm.toLowerCase()) || invoice.sellerName?.toLowerCase().includes(historySearchTerm.toLowerCase());
                                    const matchesStatus = historyFilterStatus === 'all' || invoice.status === historyFilterStatus;
                                    return matchesSearch && matchesStatus;
                                }).map((invoice)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "border rounded-lg p-4 hover:shadow-md transition-shadow bg-gray-50",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-start justify-between mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                                className: "font-medium text-gray-900 truncate",
                                                                children: invoice.invoiceNumber
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1566,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                className: "text-sm text-gray-600 truncate",
                                                                children: invoice.clientName || 'No client'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1567,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center space-x-2 mt-1",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: `px-2 py-1 text-xs rounded-full ${invoice.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`,
                                                                        children: invoice.status || 'draft'
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 1569,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-xs text-gray-500",
                                                                        children: new Date(invoice.timestamp).toLocaleDateString()
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 1576,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1568,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1565,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "relative",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            variant: "ghost",
                                                            size: "sm",
                                                            className: "h-8 w-8 p-0",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2d$vertical$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreVertical$3e$__["MoreVertical"], {
                                                                className: "h-4 w-4"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1587,
                                                                columnNumber: 27
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 1582,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1581,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1564,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$dollar$2d$sign$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__DollarSign$3e$__["DollarSign"], {
                                                                className: "h-4 w-4 text-green-600"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1594,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-semibold text-green-600",
                                                                children: [
                                                                    invoice.currency,
                                                                    " ",
                                                                    (invoice.totalAmount || 0).toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1595,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1593,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                                className: "h-4 w-4 text-gray-400"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1600,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-xs text-gray-500",
                                                                children: invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : 'No due date'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1601,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1599,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1592,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>editInvoice(invoice),
                                                        className: "flex-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$square$2d$pen$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Edit$3e$__["Edit"], {
                                                                className: "h-3 w-3 mr-1"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1614,
                                                                columnNumber: 25
                                                            }, this),
                                                            "Edit"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1608,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>duplicateInvoice(invoice),
                                                        className: "flex-1 text-green-600 hover:text-green-700 hover:bg-green-50",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$copy$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Copy$3e$__["Copy"], {
                                                                className: "h-3 w-3 mr-1"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1623,
                                                                columnNumber: 25
                                                            }, this),
                                                            "Copy"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1617,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>viewInvoiceDetails(invoice),
                                                        className: "flex-1 text-purple-600 hover:text-purple-700 hover:bg-purple-50",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                className: "h-3 w-3 mr-1"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1632,
                                                                columnNumber: 25
                                                            }, this),
                                                            "View"
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1626,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1607,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, invoice.key, true, {
                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                        lineNumber: 1560,
                                        columnNumber: 19
                                    }, this)),
                                historyInvoices.length === 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "col-span-full text-center py-8 text-gray-500",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$history$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__History$3e$__["History"], {
                                            className: "h-12 w-12 mx-auto mb-4 text-gray-300"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1641,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-lg font-medium",
                                            children: "No invoices found"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1642,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm",
                                            children: "Create and save invoices to see them here"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1643,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 1640,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/invoice/create/page.tsx",
                            lineNumber: 1547,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/invoice/create/page.tsx",
                    lineNumber: 1512,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/invoice/create/page.tsx",
                lineNumber: 1511,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "max-w-7xl mx-auto px-4 py-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-3 gap-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "lg:col-span-2 space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2 text-purple-600",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$file$2d$text$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FileText$3e$__["FileText"], {
                                                                className: "h-5 w-5"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1660,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Invoice Details"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1661,
                                                                columnNumber: 21
                                                            }, this),
                                                            isHydrated && lockedSections.invoiceDetails && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                className: "h-4 w-4 text-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1663,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1659,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>toggleSectionLock('invoiceDetails'),
                                                        "data-lock-button": "true",
                                                        className: `${isHydrated && lockedSections.invoiceDetails ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200' : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'}`,
                                                        children: !isHydrated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1680,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true) : directLockControl.getCurrentState().invoiceDetails ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1685,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Unlock"
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1690,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1666,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1658,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1657,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            "data-section": "invoiceDetails",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Invoice Number"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1700,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: invoiceNumber,
                                                                onChange: (e)=>setInvoiceNumber(e.target.value),
                                                                placeholder: "INV-2025-001",
                                                                disabled: isHydrated && lockedSections.invoiceDetails
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1701,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1699,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Invoice Date"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1709,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex space-x-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                        type: "date",
                                                                        value: invoiceDate,
                                                                        onChange: (e)=>setInvoiceDate(e.target.value),
                                                                        className: "flex-1",
                                                                        disabled: isHydrated && lockedSections.invoiceDetails
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 1711,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                        type: "button",
                                                                        variant: "outline",
                                                                        size: "sm",
                                                                        onClick: setCurrentDate,
                                                                        className: "bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200 whitespace-nowrap",
                                                                        title: "Set to current date",
                                                                        disabled: isHydrated && lockedSections.invoiceDetails,
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$calendar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Calendar$3e$__["Calendar"], {
                                                                                className: "h-4 w-4 mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 1727,
                                                                                columnNumber: 25
                                                                            }, this),
                                                                            "Today"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 1718,
                                                                        columnNumber: 23
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1710,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1708,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Due Date"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1733,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                type: "date",
                                                                value: dueDate,
                                                                onChange: (e)=>setDueDate(e.target.value),
                                                                disabled: isHydrated && lockedSections.invoiceDetails
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1734,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1732,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Currency"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1742,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: currency,
                                                                onChange: (e)=>setCurrency(e.target.value),
                                                                placeholder: "SAR",
                                                                disabled: isHydrated && lockedSections.invoiceDetails
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1743,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1741,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Discount (%)"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1751,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                type: "number",
                                                                value: discount,
                                                                onChange: (e)=>setDiscount(e.target.value),
                                                                placeholder: "0",
                                                                min: "0",
                                                                max: "100",
                                                                disabled: isHydrated && lockedSections.invoiceDetails
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1752,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1750,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: [
                                                                    "Deduction Amount (",
                                                                    currency,
                                                                    ")"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1763,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                type: "number",
                                                                value: deductionAmount,
                                                                onChange: (e)=>setDeductionAmount(e.target.value),
                                                                placeholder: "0.00",
                                                                min: "0",
                                                                step: "0.01",
                                                                disabled: isHydrated && lockedSections.invoiceDetails
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1766,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1762,
                                                        columnNumber: 19
                                                    }, this),
                                                    Number(deductionAmount) > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:col-span-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: [
                                                                    "Deduction Reason ",
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "text-red-500",
                                                                        children: "*"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 1779,
                                                                        columnNumber: 42
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1778,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: deductionReason,
                                                                onChange: (e)=>setDeductionReason(e.target.value),
                                                                placeholder: "e.g., Early payment discount, Advance payment received, Material cost adjustment, etc.",
                                                                disabled: isHydrated && lockedSections.invoiceDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1781,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1777,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1698,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1697,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 1656,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Seller Details"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1799,
                                                                columnNumber: 21
                                                            }, this),
                                                            isHydrated && lockedSections.sellerDetails && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                className: "h-4 w-4 text-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1801,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1798,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>toggleSectionLock('sellerDetails'),
                                                        "data-lock-button": "true",
                                                        className: `${isHydrated && lockedSections.sellerDetails ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200' : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'}`,
                                                        children: !isHydrated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1818,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true) : lockedSections.sellerDetails ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1823,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Unlock"
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1828,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1804,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1797,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1796,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            "data-section": "sellerDetails",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Company Name"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1838,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: sellerName,
                                                                onChange: (e)=>setSellerName(e.target.value),
                                                                placeholder: "Your Company Name",
                                                                disabled: isHydrated && lockedSections.sellerDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1839,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1837,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "VAT Number"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1848,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: sellerVAT,
                                                                onChange: (e)=>setSellerVAT(e.target.value),
                                                                placeholder: "30**********003",
                                                                disabled: isHydrated && lockedSections.sellerDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1849,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1847,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:col-span-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Address"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1858,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                                                value: sellerAddress,
                                                                onChange: (e)=>setSellerAddress(e.target.value),
                                                                placeholder: "Company Address",
                                                                rows: 3,
                                                                disabled: isHydrated && lockedSections.sellerDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1859,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1857,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:col-span-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-2",
                                                                children: "Company Logo"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1871,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center space-x-4",
                                                                children: sellerLogo ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                                src: sellerLogo,
                                                                                alt: "Seller Logo",
                                                                                className: "w-full h-full object-contain"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 1876,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 1875,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex flex-col space-y-2",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "text-sm text-green-600 font-medium",
                                                                                    children: "✓ Logo uploaded & optimized"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 1883,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "text-xs text-gray-500",
                                                                                    children: "Auto-resized for best quality"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 1884,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                                    type: "button",
                                                                                    variant: "outline",
                                                                                    size: "sm",
                                                                                    onClick: removeSellerLogo,
                                                                                    className: "text-red-600 hover:text-red-700 hover:bg-red-50",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                                            className: "h-3 w-3 mr-1"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                            lineNumber: 1892,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        "Remove Logo"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 1885,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 1882,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1874,
                                                                    columnNumber: 25
                                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                                className: "h-6 w-6 text-gray-400"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 1900,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 1899,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex flex-col",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                    id: "seller-logo-input",
                                                                                    type: "file",
                                                                                    accept: "image/*",
                                                                                    onChange: handleSellerLogoUpload,
                                                                                    className: "hidden"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 1903,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                                    type: "button",
                                                                                    variant: "outline",
                                                                                    size: "sm",
                                                                                    className: "bg-blue-50 text-blue-600 hover:bg-blue-100",
                                                                                    onClick: ()=>document.getElementById('seller-logo-input')?.click(),
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                                            className: "h-4 w-4 mr-2"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                            lineNumber: 1917,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        "Upload Logo"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 1910,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "text-xs text-gray-500 mt-1",
                                                                                    children: "PNG, JPG up to 5MB"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 1920,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 1902,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1898,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1872,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1870,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1836,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1835,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 1795,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Client Details"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1935,
                                                                columnNumber: 21
                                                            }, this),
                                                            isHydrated && lockedSections.clientDetails && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                className: "h-4 w-4 text-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1937,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1934,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>toggleSectionLock('clientDetails'),
                                                        "data-lock-button": "true",
                                                        className: `${isHydrated && lockedSections.clientDetails ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200' : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'}`,
                                                        children: !isHydrated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1954,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true) : lockedSections.clientDetails ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1959,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Unlock"
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 1964,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1940,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1933,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1932,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            "data-section": "clientDetails",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Client Name"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1974,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: clientName,
                                                                onChange: (e)=>setClientName(e.target.value),
                                                                placeholder: "Client Company Name",
                                                                disabled: isHydrated && lockedSections.clientDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1975,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1973,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Client VAT"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1984,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: clientVAT,
                                                                onChange: (e)=>setClientVAT(e.target.value),
                                                                placeholder: "301987654321009",
                                                                disabled: isHydrated && lockedSections.clientDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1985,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1983,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:col-span-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Client Address"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1994,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                                                value: clientAddress,
                                                                onChange: (e)=>setClientAddress(e.target.value),
                                                                placeholder: "Client Address",
                                                                rows: 3,
                                                                disabled: isHydrated && lockedSections.clientDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 1995,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 1993,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:col-span-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-2",
                                                                children: "Client Logo"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2007,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center space-x-4",
                                                                children: clientLogo ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                                src: clientLogo,
                                                                                alt: "Client Logo",
                                                                                className: "w-full h-full object-contain"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 2012,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2011,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex flex-col space-y-2",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "text-sm text-green-600 font-medium",
                                                                                    children: "✓ Logo uploaded & optimized"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 2019,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "text-xs text-gray-500",
                                                                                    children: "Auto-resized for best quality"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 2020,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                                    type: "button",
                                                                                    variant: "outline",
                                                                                    size: "sm",
                                                                                    onClick: removeClientLogo,
                                                                                    className: "text-red-600 hover:text-red-700 hover:bg-red-50",
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                                            className: "h-3 w-3 mr-1"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                            lineNumber: 2028,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        "Remove Logo"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 2021,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2018,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2010,
                                                                    columnNumber: 25
                                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center space-x-3",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                                className: "h-6 w-6 text-gray-400"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 2036,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2035,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "flex flex-col",
                                                                            children: [
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                                    id: "client-logo-input",
                                                                                    type: "file",
                                                                                    accept: "image/*",
                                                                                    onChange: handleClientLogoUpload,
                                                                                    className: "hidden"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 2039,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                                    type: "button",
                                                                                    variant: "outline",
                                                                                    size: "sm",
                                                                                    className: "bg-green-50 text-green-600 hover:bg-green-100",
                                                                                    onClick: ()=>document.getElementById('client-logo-input')?.click(),
                                                                                    children: [
                                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$upload$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Upload$3e$__["Upload"], {
                                                                                            className: "h-4 w-4 mr-2"
                                                                                        }, void 0, false, {
                                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                            lineNumber: 2053,
                                                                                            columnNumber: 31
                                                                                        }, this),
                                                                                        "Upload Logo"
                                                                                    ]
                                                                                }, void 0, true, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 2046,
                                                                                    columnNumber: 29
                                                                                }, this),
                                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                                    className: "text-xs text-gray-500 mt-1",
                                                                                    children: "PNG, JPG up to 5MB"
                                                                                }, void 0, false, {
                                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                    lineNumber: 2056,
                                                                                    columnNumber: 29
                                                                                }, this)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2038,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2034,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2008,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2006,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 1972,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 1971,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 1931,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Bank Details"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2071,
                                                                columnNumber: 21
                                                            }, this),
                                                            isHydrated && lockedSections.bankDetails && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                className: "h-4 w-4 text-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2073,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2070,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>toggleSectionLock('bankDetails'),
                                                        "data-lock-button": "true",
                                                        className: `${isHydrated && lockedSections.bankDetails ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200' : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'}`,
                                                        children: !isHydrated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2090,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true) : lockedSections.bankDetails ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2095,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Unlock"
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2100,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2076,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2069,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2068,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            "data-section": "bankDetails",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Bank Name"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2110,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: bankName,
                                                                onChange: (e)=>setBankName(e.target.value),
                                                                placeholder: "Saudi National Bank",
                                                                disabled: isHydrated && lockedSections.bankDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2111,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2109,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Account Name"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2120,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: accountName,
                                                                onChange: (e)=>setAccountName(e.target.value),
                                                                placeholder: "Company Account Name",
                                                                disabled: isHydrated && lockedSections.bankDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2121,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2119,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Account Number"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2130,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: accountNumber,
                                                                onChange: (e)=>setAccountNumber(e.target.value),
                                                                placeholder: "**********",
                                                                disabled: isHydrated && lockedSections.bankDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2131,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2129,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "IBAN"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2140,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                value: iban,
                                                                onChange: (e)=>setIban(e.target.value),
                                                                placeholder: "************************",
                                                                disabled: isHydrated && lockedSections.bankDetails,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2141,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2139,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2108,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2107,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2067,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Invoice Items"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2158,
                                                                columnNumber: 21
                                                            }, this),
                                                            isHydrated && lockedSections.items && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                className: "h-4 w-4 text-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2160,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2157,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                type: "button",
                                                                variant: "outline",
                                                                size: "sm",
                                                                onClick: ()=>toggleSectionLock('items'),
                                                                "data-lock-button": "true",
                                                                className: `${isHydrated && lockedSections.items ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200' : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'}`,
                                                                children: !isHydrated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                            className: "h-4 w-4 mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2178,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        "Lock"
                                                                    ]
                                                                }, void 0, true) : lockedSections.items ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                                            className: "h-4 w-4 mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2183,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        "Unlock"
                                                                    ]
                                                                }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                            className: "h-4 w-4 mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2188,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        "Lock"
                                                                    ]
                                                                }, void 0, true)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2164,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                size: "sm",
                                                                onClick: addItem,
                                                                className: "bg-blue-600 hover:bg-blue-700",
                                                                disabled: isHydrated && lockedSections.items,
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$plus$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Plus$3e$__["Plus"], {
                                                                        className: "h-4 w-4 mr-2"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2199,
                                                                        columnNumber: 23
                                                                    }, this),
                                                                    "Add Item"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2193,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2163,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2156,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2155,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            "data-section": "items",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-4",
                                                children: items.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border rounded-lg relative",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "md:col-span-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                                                        children: "Description"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2210,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                        value: item.description,
                                                                        onChange: (e)=>safeUpdateItem(item.id, 'description', e.target.value),
                                                                        placeholder: "Item description",
                                                                        disabled: isHydrated && lockedSections.items
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2211,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2209,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                                                        children: "Quantity"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2219,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                        type: "text",
                                                                        value: getSafeString(item.quantity, 'quantity'),
                                                                        onChange: (e)=>safeUpdateItem(item.id, 'quantity', e.target.value),
                                                                        min: "1",
                                                                        disabled: isHydrated && lockedSections.items
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2220,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2218,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                                                        children: [
                                                                            "Rate (",
                                                                            currency,
                                                                            ")"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2229,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                        type: "text",
                                                                        value: getSafeString(item.rate, 'rate'),
                                                                        onChange: (e)=>safeUpdateItem(item.id, 'rate', e.target.value),
                                                                        min: "0",
                                                                        step: "0.01",
                                                                        disabled: isHydrated && lockedSections.items
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2230,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2228,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                        className: "block text-sm font-medium text-gray-700 mb-1",
                                                                        children: [
                                                                            "Amount (",
                                                                            currency,
                                                                            ")"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2240,
                                                                        columnNumber: 25
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Input"], {
                                                                        value: item.amount.toFixed(2),
                                                                        readOnly: true,
                                                                        className: "bg-gray-50"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                        lineNumber: 2241,
                                                                        columnNumber: 25
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2239,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-end",
                                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                                    type: "button",
                                                                    variant: "outline",
                                                                    size: "sm",
                                                                    onClick: ()=>removeItem(item.id),
                                                                    disabled: items.length <= 1 || lockedSections.items,
                                                                    className: `w-full ${items.length <= 1 || lockedSections.items ? 'opacity-50 cursor-not-allowed' : 'text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200'}`,
                                                                    title: lockedSections.items ? 'Items section is locked' : items.length <= 1 ? 'At least one item is required' : 'Remove this item',
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$trash$2d$2$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Trash2$3e$__["Trash2"], {
                                                                            className: "h-4 w-4 mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2267,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        "Remove"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2248,
                                                                    columnNumber: 25
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2247,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, item.id, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2208,
                                                        columnNumber: 21
                                                    }, this))
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2206,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2205,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2154,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center justify-between",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center space-x-2",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Additional Information"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2282,
                                                                columnNumber: 21
                                                            }, this),
                                                            isHydrated && lockedSections.notes && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                className: "h-4 w-4 text-red-500"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2284,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2281,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                        type: "button",
                                                        variant: "outline",
                                                        size: "sm",
                                                        onClick: ()=>toggleSectionLock('notes'),
                                                        "data-lock-button": "true",
                                                        className: `${isHydrated && lockedSections.notes ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200' : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'}`,
                                                        children: !isHydrated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2301,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true) : lockedSections.notes ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2d$open$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Unlock$3e$__["Unlock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2306,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Unlock"
                                                            ]
                                                        }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$lock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Lock$3e$__["Lock"], {
                                                                    className: "h-4 w-4 mr-1"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2311,
                                                                    columnNumber: 25
                                                                }, this),
                                                                "Lock"
                                                            ]
                                                        }, void 0, true)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2287,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2280,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2279,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            "data-section": "notes",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Notes"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2321,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                                                value: notes,
                                                                onChange: (e)=>setNotes(e.target.value),
                                                                placeholder: "Thank you for your business!",
                                                                rows: 4,
                                                                disabled: isHydrated && lockedSections.notes,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2322,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2320,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                                className: "block text-sm font-medium text-gray-700 mb-1",
                                                                children: "Terms & Conditions"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2332,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$textarea$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Textarea"], {
                                                                value: terms,
                                                                onChange: (e)=>setTerms(e.target.value),
                                                                placeholder: "Payment due within 30 days...",
                                                                rows: 4,
                                                                disabled: isHydrated && lockedSections.notes,
                                                                className: "w-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2333,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2331,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2319,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2318,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2278,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/invoice/create/page.tsx",
                            lineNumber: 1654,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                children: "Invoice Summary"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2351,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2350,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Subtotal:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2356,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-semibold",
                                                                children: [
                                                                    currency,
                                                                    " ",
                                                                    subtotal.toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2357,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2355,
                                                        columnNumber: 19
                                                    }, this),
                                                    discountAmount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-red-600",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: [
                                                                    "Discount (",
                                                                    discount,
                                                                    "%):"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2361,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: [
                                                                    "-",
                                                                    currency,
                                                                    " ",
                                                                    discountAmount.toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2362,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2360,
                                                        columnNumber: 21
                                                    }, this),
                                                    Number(deductionAmount) > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-orange-600",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Deduction Amount:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2367,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: [
                                                                    "-",
                                                                    currency,
                                                                    " ",
                                                                    Number(deductionAmount).toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2368,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2366,
                                                        columnNumber: 21
                                                    }, this),
                                                    Number(deductionAmount) > 0 && deductionReason && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm text-gray-600 bg-orange-50 p-2 rounded",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("strong", {
                                                                children: "Deduction Reason:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2373,
                                                                columnNumber: 23
                                                            }, this),
                                                            " ",
                                                            deductionReason
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2372,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Amount Before VAT:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2377,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-semibold",
                                                                children: [
                                                                    currency,
                                                                    " ",
                                                                    subtotalAfterDeduction.toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2378,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2376,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Total VAT (15%):"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2381,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-semibold",
                                                                children: [
                                                                    currency,
                                                                    " ",
                                                                    totalVAT.toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2382,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2380,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex justify-between text-lg font-bold border-t pt-3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                children: "Total Amount:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2385,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-purple-600",
                                                                children: [
                                                                    currency,
                                                                    " ",
                                                                    totalAmount.toFixed(2)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2386,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2384,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2354,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2353,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2349,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center space-x-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$qr$2d$code$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__QrCode$3e$__["QrCode"], {
                                                        className: "h-5 w-5"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2396,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: "Actions"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2397,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2395,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2394,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "space-y-3",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            onClick: previewInvoice,
                                                            className: "w-full bg-purple-600 hover:bg-purple-700",
                                                            size: "lg",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$eye$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Eye$3e$__["Eye"], {
                                                                    className: "h-4 w-4 mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2407,
                                                                    columnNumber: 21
                                                                }, this),
                                                                "Preview Invoice"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2402,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            onClick: generatePDF,
                                                            className: "w-full bg-green-600 hover:bg-green-700",
                                                            size: "lg",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$download$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Download$3e$__["Download"], {
                                                                    className: "h-4 w-4 mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2415,
                                                                    columnNumber: 21
                                                                }, this),
                                                                "Generate & Download PDF"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2410,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            onClick: handlePrintInvoice,
                                                            variant: "outline",
                                                            className: "w-full border-purple-300 text-purple-600 hover:bg-purple-50",
                                                            size: "lg",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$printer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Printer$3e$__["Printer"], {
                                                                    className: "h-4 w-4 mr-2"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2424,
                                                                    columnNumber: 21
                                                                }, this),
                                                                "Print Invoice"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2418,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            onClick: saveDraft,
                                                            variant: "outline",
                                                            className: "w-full",
                                                            size: "lg",
                                                            children: "💾 Save Draft"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2427,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                                            onClick: saveAsCompleted,
                                                            variant: "outline",
                                                            className: "w-full bg-green-50 text-green-600 hover:bg-green-100 border-green-200",
                                                            size: "lg",
                                                            children: "✅ Mark as Completed"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2435,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 2401,
                                                    columnNumber: 17
                                                }, this),
                                                (sellerLogo || clientLogo) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-gray-200",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-700 mb-3 text-center font-medium",
                                                            children: "📸 Logo Preview"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2448,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-center space-x-6",
                                                            children: [
                                                                sellerLogo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-blue-600 mb-2 font-medium",
                                                                            children: "Seller Logo"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2452,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-20 h-16 bg-white border-2 border-blue-200 rounded-lg overflow-hidden shadow-sm",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                                src: sellerLogo,
                                                                                alt: "Seller Logo Preview",
                                                                                className: "w-full h-full object-contain"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 2454,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2453,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-gray-500 mt-1",
                                                                            children: "Auto-optimized"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2460,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2451,
                                                                    columnNumber: 25
                                                                }, this),
                                                                clientLogo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-center",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-green-600 mb-2 font-medium",
                                                                            children: "Client Logo"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2465,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                            className: "w-20 h-16 bg-white border-2 border-green-200 rounded-lg overflow-hidden shadow-sm",
                                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                                src: clientLogo,
                                                                                alt: "Client Logo Preview",
                                                                                className: "w-full h-full object-contain"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                                lineNumber: 2467,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2466,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-gray-500 mt-1",
                                                                            children: "Auto-optimized"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2473,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2464,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2449,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-center text-gray-600 mt-3",
                                                            children: "✨ Logos are automatically resized and optimized for PDF generation"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2477,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 2447,
                                                    columnNumber: 19
                                                }, this),
                                                sellerName && clientName && totalAmount > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-4 p-3 bg-gray-50 rounded-lg text-center",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-gray-600 mb-2",
                                                            children: "ZATCA QR Code will be generated"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2486,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-16 h-16 bg-white border-2 border-gray-300 rounded mx-auto flex items-center justify-center",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$qr$2d$code$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__QrCode$3e$__["QrCode"], {
                                                                className: "h-8 w-8 text-gray-400"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2488,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2487,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-gray-500 mt-2",
                                                            children: "QR code for ZATCA compliance"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2490,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs text-blue-600 mt-1",
                                                            children: [
                                                                "📅 Always uses current date: ",
                                                                new Date().toLocaleDateString()
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2491,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 2485,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2400,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2393,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                className: "flex items-center space-x-2",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    children: "📋 Recent Invoices"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 2503,
                                                    columnNumber: 19
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2502,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2501,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2",
                                                children: getRecentInvoices().length > 0 ? getRecentInvoices().map((invoice, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors",
                                                        onClick: ()=>loadInvoiceData(invoice),
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex justify-between items-start",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex-1",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "font-medium text-sm text-blue-600",
                                                                            children: invoice.invoiceNumber
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2517,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-gray-600 truncate",
                                                                            children: invoice.clientName || 'No client'
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2518,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-gray-500",
                                                                            children: new Date(invoice.timestamp).toLocaleDateString()
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2519,
                                                                            columnNumber: 29
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2516,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-right",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-sm font-semibold text-green-600",
                                                                            children: [
                                                                                invoice.currency,
                                                                                " ",
                                                                                (invoice.totalAmount || 0).toFixed(2)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2524,
                                                                            columnNumber: 29
                                                                        }, this),
                                                                        Number(invoice.deductionAmount) > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                                            className: "text-xs text-orange-600",
                                                                            children: [
                                                                                "-",
                                                                                Number(invoice.deductionAmount).toFixed(2)
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                            lineNumber: 2528,
                                                                            columnNumber: 31
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                    lineNumber: 2523,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2515,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, invoice.key, false, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2510,
                                                        columnNumber: 23
                                                    }, this)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-center py-4 text-gray-500",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm",
                                                            children: "No recent invoices found"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2536,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-xs",
                                                            children: "Create and save invoices to see them here"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                                            lineNumber: 2537,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                                    lineNumber: 2535,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2507,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2506,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2500,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                                                children: "Validation Status"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2547,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2546,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-2",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm",
                                                                children: "Seller Details:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2552,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `text-sm ${sellerName && sellerVAT ? 'text-green-600' : 'text-red-600'}`,
                                                                children: sellerName && sellerVAT ? '✓ Complete' : '✗ Missing'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2553,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2551,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm",
                                                                children: "Client Details:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2558,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `text-sm ${clientName && clientVAT ? 'text-green-600' : 'text-red-600'}`,
                                                                children: clientName && clientVAT ? '✓ Complete' : '✗ Missing'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2559,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2557,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm",
                                                                children: "Invoice Items:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2564,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `text-sm ${items.length > 0 && items[0].description ? 'text-green-600' : 'text-red-600'}`,
                                                                children: items.length > 0 && items[0].description ? '✓ Complete' : '✗ Missing'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2565,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2563,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-sm",
                                                                children: "Deduction Reason:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2570,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: `text-sm ${Number(deductionAmount) > 0 ? deductionReason.trim() ? 'text-green-600' : 'text-red-600' : 'text-gray-400'}`,
                                                                children: Number(deductionAmount) > 0 ? deductionReason.trim() ? '✓ Complete' : '✗ Required' : 'N/A'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                                lineNumber: 2571,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/invoice/create/page.tsx",
                                                        lineNumber: 2569,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/invoice/create/page.tsx",
                                                lineNumber: 2550,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/invoice/create/page.tsx",
                                            lineNumber: 2549,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/invoice/create/page.tsx",
                                    lineNumber: 2545,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/invoice/create/page.tsx",
                            lineNumber: 2348,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/invoice/create/page.tsx",
                    lineNumber: 1652,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/invoice/create/page.tsx",
                lineNumber: 1651,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/invoice/create/page.tsx",
        lineNumber: 1352,
        columnNumber: 5
    }, this);
}
_s(CreateInvoicePage, "dHN0vO/UtZhsi079LQVQg0fR+nM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = CreateInvoicePage;
var _c;
__turbopack_context__.k.register(_c, "CreateInvoicePage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_677d3d3a._.js.map