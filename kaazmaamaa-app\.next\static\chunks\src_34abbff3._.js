(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function AuthProvider({ children }) {
    _s();
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [userRole, setUserRole] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true) // Start with loading true
    ;
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load authentication state from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const loadAuthState = {
                "AuthProvider.useEffect.loadAuthState": ()=>{
                    try {
                        const savedUser = localStorage.getItem('kaazmaamaa-user');
                        const savedRole = localStorage.getItem('kaazmaamaa-user-role');
                        if (savedUser && savedRole) {
                            const parsedUser = JSON.parse(savedUser);
                            // Re-detect the user's role to ensure it's correct (in case profiles were created after login)
                            const reDetectedRole = detectUserProfileType(parsedUser.email);
                            console.log('🔄 Re-detecting role for:', parsedUser.email, 'Saved:', savedRole, 'Re-detected:', reDetectedRole);
                            // Use re-detected role if it's different from saved role
                            const finalRole = reDetectedRole !== 'worker' ? reDetectedRole : savedRole;
                            setUser(parsedUser);
                            setUserRole(finalRole);
                            // Update localStorage if role changed
                            if (finalRole !== savedRole) {
                                console.log('🔄 Updating role in localStorage from', savedRole, 'to', finalRole);
                                localStorage.setItem('kaazmaamaa-user-role', finalRole);
                            }
                            console.log('✅ Loaded user from localStorage:', parsedUser.email, 'Final role:', finalRole);
                        } else {
                            console.log('No saved authentication found');
                        }
                    } catch (error) {
                        console.error('Error loading auth state from localStorage:', error);
                    } finally{
                        setLoading(false);
                        setIsInitialized(true);
                    }
                }
            }["AuthProvider.useEffect.loadAuthState"];
            loadAuthState();
        }
    }["AuthProvider.useEffect"], []);
    const signUp = async (email, password, role)=>{
        // Mock implementation for now
        console.log('Sign up:', {
            email,
            password,
            role
        });
        const mockUser = {
            id: Date.now().toString(),
            email
        };
        // Save to state
        setUser(mockUser);
        setUserRole(role);
        // Save to localStorage
        try {
            localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser));
            localStorage.setItem('kaazmaamaa-user-role', role);
            console.log('User authentication saved to localStorage');
        } catch (error) {
            console.error('Error saving auth to localStorage:', error);
        }
        return {
            data: {
                user: mockUser
            },
            error: null
        };
    };
    // Function to detect user's profile type by checking all profile contexts
    const detectUserProfileType = (userEmail)=>{
        console.log('🔍 Detecting profile type for email:', userEmail);
        console.log('🔍 All localStorage keys:', Object.keys(localStorage));
        try {
            // Check Workers profiles
            const workersData = localStorage.getItem('kaazmaamaa-workers');
            console.log('📋 Workers data:', workersData ? 'Found' : 'Not found');
            if (workersData) {
                const workers = JSON.parse(workersData);
                console.log('👷 Checking', workers.length, 'worker profiles');
                const workerProfile = workers.find((worker)=>{
                    const emailMatch = worker.personalInfo?.email === userEmail;
                    const userIdMatch = worker.userId === userEmail;
                    console.log('👷 Worker check:', worker.personalInfo?.workerName, 'Email match:', emailMatch, 'UserId match:', userIdMatch);
                    return emailMatch || userIdMatch;
                });
                if (workerProfile) {
                    console.log('✅ Found worker profile for:', userEmail, workerProfile.personalInfo?.workerName);
                    return 'worker';
                }
            }
            // Check Suppliers profiles
            const suppliersData = localStorage.getItem('kaazmaamaa-suppliers');
            console.log('📋 Suppliers data:', suppliersData ? 'Found' : 'Not found');
            if (suppliersData) {
                const suppliers = JSON.parse(suppliersData);
                console.log('🏭 Checking', suppliers.length, 'supplier profiles');
                const supplierProfile = suppliers.find((supplier)=>{
                    const emailMatch = supplier.personalInfo?.email === userEmail;
                    const userIdMatch = supplier.userId === userEmail;
                    console.log('🏭 Supplier check:', supplier.personalInfo?.supplierName, 'Email match:', emailMatch, 'UserId match:', userIdMatch);
                    console.log('🏭 Supplier data structure:', {
                        personalInfo: supplier.personalInfo,
                        userId: supplier.userId
                    });
                    return emailMatch || userIdMatch;
                });
                if (supplierProfile) {
                    console.log('✅ Found supplier profile for:', userEmail, supplierProfile.personalInfo?.supplierName);
                    return 'supplier';
                }
            }
            // Check Companies profiles
            const companiesData = localStorage.getItem('kaazmaamaa-companies');
            console.log('📋 Companies data:', companiesData ? 'Found' : 'Not found');
            if (companiesData) {
                const companies = JSON.parse(companiesData);
                console.log('🏢 Checking', companies.length, 'company profiles');
                const companyProfile = companies.find((company)=>{
                    const emailMatch = company.contactInfo?.hrEmail === userEmail;
                    const userIdMatch = company.userId === userEmail;
                    console.log('🏢 Company check:', company.companyInfo?.companyName, 'Email match:', emailMatch, 'UserId match:', userIdMatch);
                    return emailMatch || userIdMatch;
                });
                if (companyProfile) {
                    console.log('✅ Found company profile for:', userEmail, companyProfile.companyInfo?.companyName);
                    return 'company';
                }
            }
            console.log('❌ No profile found for:', userEmail, 'defaulting to worker');
            return 'worker' // Default fallback
            ;
        } catch (error) {
            console.error('💥 Error detecting profile type:', error);
            return 'worker' // Default fallback
            ;
        }
    };
    const signIn = async (email, password)=>{
        // Mock implementation for now
        console.log('Sign in:', {
            email,
            password
        });
        // Use email as the consistent user ID
        const mockUser = {
            id: email,
            email
        };
        // Detect the user's actual profile type
        const detectedRole = detectUserProfileType(email);
        console.log('Detected user role:', detectedRole, 'for email:', email);
        // Save to state
        setUser(mockUser);
        setUserRole(detectedRole);
        // Save to localStorage
        try {
            localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser));
            localStorage.setItem('kaazmaamaa-user-role', detectedRole);
            console.log('User authentication saved to localStorage with role:', detectedRole);
        } catch (error) {
            console.error('Error saving auth to localStorage:', error);
        }
        return {
            data: {
                user: mockUser
            },
            error: null
        };
    };
    const signOut = async ()=>{
        // Clear state
        setUser(null);
        setUserRole(null);
        // Clear localStorage
        try {
            localStorage.removeItem('kaazmaamaa-user');
            localStorage.removeItem('kaazmaamaa-user-role');
            console.log('User authentication cleared from localStorage');
        } catch (error) {
            console.error('Error clearing auth from localStorage:', error);
        }
    };
    const getUserProfile = ()=>{
        if (!user || !userRole) return null;
        try {
            if (userRole === 'worker') {
                const workersData = localStorage.getItem('kaazmaamaa-workers');
                if (workersData) {
                    const workers = JSON.parse(workersData);
                    return workers.find((worker)=>worker.personalInfo?.email === user.email || worker.userId === user.email);
                }
            } else if (userRole === 'supplier') {
                const suppliersData = localStorage.getItem('kaazmaamaa-suppliers');
                if (suppliersData) {
                    const suppliers = JSON.parse(suppliersData);
                    return suppliers.find((supplier)=>supplier.personalInfo?.email === user.email || supplier.userId === user.email);
                }
            } else if (userRole === 'company') {
                const companiesData = localStorage.getItem('kaazmaamaa-companies');
                if (companiesData) {
                    const companies = JSON.parse(companiesData);
                    return companies.find((company)=>company.contactInfo?.hrEmail === user.email || company.userId === user.email);
                }
            }
        } catch (error) {
            console.error('Error getting user profile:', error);
        }
        return null;
    };
    const debugProfiles = ()=>{
        console.log('🐛 DEBUG: All localStorage data:');
        console.log('Workers:', localStorage.getItem('kaazmaamaa-workers'));
        console.log('Suppliers:', localStorage.getItem('kaazmaamaa-suppliers'));
        console.log('Companies:', localStorage.getItem('kaazmaamaa-companies'));
        try {
            const workers = JSON.parse(localStorage.getItem('kaazmaamaa-workers') || '[]');
            const suppliers = JSON.parse(localStorage.getItem('kaazmaamaa-suppliers') || '[]');
            const companies = JSON.parse(localStorage.getItem('kaazmaamaa-companies') || '[]');
            console.log('🐛 Parsed Workers:', workers);
            console.log('🐛 Parsed Suppliers:', suppliers);
            console.log('🐛 Parsed Companies:', companies);
        } catch (error) {
            console.error('🐛 Error parsing localStorage data:', error);
        }
    };
    const refreshUserRole = ()=>{
        if (user?.email) {
            console.log('🔄 Manually refreshing user role for:', user.email);
            const newRole = detectUserProfileType(user.email);
            console.log('🔄 New role detected:', newRole);
            if (newRole !== userRole) {
                setUserRole(newRole);
                localStorage.setItem('kaazmaamaa-user-role', newRole);
                console.log('✅ Role updated from', userRole, 'to', newRole);
            } else {
                console.log('✅ Role unchanged:', userRole);
            }
        }
    };
    const value = {
        user,
        userRole,
        loading,
        signUp,
        signIn,
        signOut,
        getUserProfile,
        debugProfiles,
        refreshUserRole
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 288,
        columnNumber: 10
    }, this);
}
_s(AuthProvider, "nutbXUp/DxipcvR98n7Ll6y3wI0=");
_c = AuthProvider;
function useAuth() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/PostsContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PostsProvider": (()=>PostsProvider),
    "formatExpirationInfo": (()=>formatExpirationInfo),
    "formatTimeAgo": (()=>formatTimeAgo),
    "usePosts": (()=>usePosts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const PostsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Helper function to calculate expiration date (1 month from creation)
const getExpirationDate = (createdAt)=>{
    const created = new Date(createdAt);
    const expiration = new Date(created);
    expiration.setMonth(expiration.getMonth() + 1) // Add 1 month
    ;
    return expiration.toISOString();
};
// Initial mock posts
const initialPosts = [
    {
        id: '1',
        user_id: 'mock-user-1',
        user_name: 'Ahmed Al-Rashid',
        user_role: 'worker',
        content: 'Looking for construction work in Riyadh. I have 5 years of experience in electrical work. Available immediately! #ElectricalWork #Riyadh #Available',
        post_type: 'text',
        visibility: 'public',
        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        expires_at: getExpirationDate(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),
        likes: 12,
        comments: 3,
        shared_count: 2,
        post_comments: [
            {
                id: 'comment-1',
                post_id: '1',
                user_id: 'commenter-1',
                user_name: 'Sarah Construction',
                user_role: 'company',
                content: 'We have openings for electrical work. Please send your CV.',
                created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
            },
            {
                id: 'comment-2',
                post_id: '1',
                user_id: 'commenter-2',
                user_name: 'Ali Hassan',
                user_role: 'worker',
                content: 'Good luck brother! I also work in electrical.',
                created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()
            }
        ]
    },
    {
        id: '2',
        user_id: 'mock-user-2',
        user_name: 'Saudi Construction Co.',
        user_role: 'company',
        content: 'We are hiring 20 skilled workers for our new project in Jeddah. Competitive salary and accommodation provided. Contact us for details. #Hiring #Jeddah #Construction',
        post_type: 'text',
        visibility: 'public',
        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        expires_at: getExpirationDate(new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()),
        likes: 28,
        comments: 15
    },
    {
        id: '3',
        user_id: 'mock-user-3',
        user_name: 'Gulf Manpower Solutions',
        user_role: 'supplier',
        content: 'New batch of certified welders available for immediate deployment. All workers have valid IQAMA and safety certifications. #Welders #Certified #Available',
        post_type: 'text',
        visibility: 'public',
        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        expires_at: getExpirationDate(new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()),
        likes: 18,
        comments: 8
    },
    {
        id: '4',
        user_id: 'mock-user-4',
        user_name: 'Mohammed Hassan',
        user_role: 'worker',
        content: 'Just completed my safety certification course! Ready for new opportunities in the oil and gas sector. #SafetyCertified #OilAndGas #Ready',
        post_type: 'text',
        visibility: 'public',
        created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
        expires_at: getExpirationDate(new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()),
        likes: 15,
        comments: 5
    }
];
function PostsProvider({ children }) {
    _s();
    const [posts, setPosts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isLoaded, setIsLoaded] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load posts from localStorage on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PostsProvider.useEffect": ()=>{
            const loadPostsFromStorage = {
                "PostsProvider.useEffect.loadPostsFromStorage": ()=>{
                    try {
                        const savedPosts = localStorage.getItem('kaazmaamaa-posts');
                        if (savedPosts) {
                            const parsedPosts = JSON.parse(savedPosts);
                            console.log('Loaded posts from localStorage:', parsedPosts.length);
                            // Filter out expired posts when loading
                            const now = new Date();
                            const activePosts = parsedPosts.filter({
                                "PostsProvider.useEffect.loadPostsFromStorage.activePosts": (post)=>{
                                    // Handle posts that might not have expires_at field (legacy posts)
                                    if (!post.expires_at) {
                                        // Add expires_at to legacy posts (1 month from creation)
                                        post.expires_at = getExpirationDate(post.created_at);
                                    }
                                    const expirationDate = new Date(post.expires_at);
                                    return expirationDate > now;
                                }
                            }["PostsProvider.useEffect.loadPostsFromStorage.activePosts"]);
                            const expiredCount = parsedPosts.length - activePosts.length;
                            if (expiredCount > 0) {
                                console.log(`Filtered out ${expiredCount} expired posts on load`);
                                // Save cleaned posts back to localStorage
                                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts));
                            }
                            setPosts(activePosts);
                        } else {
                            // If no saved posts, use initial posts
                            console.log('No saved posts found, using initial posts');
                            setPosts(initialPosts);
                            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts));
                        }
                    } catch (error) {
                        console.error('Error loading posts from localStorage:', error);
                        setPosts(initialPosts);
                    } finally{
                        setIsLoaded(true);
                    }
                }
            }["PostsProvider.useEffect.loadPostsFromStorage"];
            loadPostsFromStorage();
        }
    }["PostsProvider.useEffect"], []);
    // Save posts to localStorage whenever posts change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PostsProvider.useEffect": ()=>{
            if (isLoaded && posts.length > 0) {
                try {
                    localStorage.setItem('kaazmaamaa-posts', JSON.stringify(posts));
                    console.log('Saved posts to localStorage:', posts.length);
                } catch (error) {
                    console.error('Error saving posts to localStorage:', error);
                }
            }
        }
    }["PostsProvider.useEffect"], [
        posts,
        isLoaded
    ]);
    console.log('PostsProvider initialized with posts:', posts.length);
    const addPost = (newPost)=>{
        const createdAt = new Date().toISOString();
        const post = {
            ...newPost,
            id: Date.now().toString(),
            created_at: createdAt,
            expires_at: getExpirationDate(createdAt),
            likes: 0,
            comments: 0
        };
        console.log('Adding post to context:', post);
        setPosts((prevPosts)=>{
            const updatedPosts = [
                post,
                ...prevPosts
            ];
            console.log('Updated posts array:', updatedPosts);
            // Immediately save to localStorage
            try {
                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));
                console.log('Post immediately saved to localStorage');
            } catch (error) {
                console.error('Error saving post to localStorage:', error);
            }
            return updatedPosts;
        });
    };
    const likePost = (postId)=>{
        setPosts((prevPosts)=>{
            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {
                    ...post,
                    likes: post.liked_by_user ? post.likes - 1 : post.likes + 1,
                    liked_by_user: !post.liked_by_user
                } : post);
            // Save likes to localStorage
            try {
                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));
                console.log('Like status saved to localStorage');
            } catch (error) {
                console.error('Error saving like to localStorage:', error);
            }
            return updatedPosts;
        });
    };
    const sharePost = (postId)=>{
        setPosts((prevPosts)=>{
            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {
                    ...post,
                    shared_count: (post.shared_count || 0) + 1
                } : post);
            // Save share count to localStorage
            try {
                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));
                console.log('Share count saved to localStorage');
            } catch (error) {
                console.error('Error saving share to localStorage:', error);
            }
            return updatedPosts;
        });
    };
    const addComment = (postId, content, user)=>{
        const newComment = {
            id: Date.now().toString(),
            post_id: postId,
            user_id: user.id,
            user_name: user.name,
            user_role: user.role,
            content: content.trim(),
            created_at: new Date().toISOString()
        };
        setPosts((prevPosts)=>{
            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {
                    ...post,
                    comments: post.comments + 1,
                    post_comments: [
                        ...post.post_comments || [],
                        newComment
                    ]
                } : post);
            // Save comments to localStorage
            try {
                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));
                console.log('Comment saved to localStorage');
            } catch (error) {
                console.error('Error saving comment to localStorage:', error);
            }
            return updatedPosts;
        });
    };
    const deletePost = (postId)=>{
        setPosts((prevPosts)=>{
            const updatedPosts = prevPosts.filter((post)=>post.id !== postId);
            // Save deletion to localStorage
            try {
                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));
                console.log('Post deletion saved to localStorage');
            } catch (error) {
                console.error('Error saving deletion to localStorage:', error);
            }
            return updatedPosts;
        });
    };
    const clearAllPosts = ()=>{
        setPosts([]);
        try {
            localStorage.removeItem('kaazmaamaa-posts');
            console.log('All posts cleared from localStorage');
        } catch (error) {
            console.error('Error clearing posts from localStorage:', error);
        }
    };
    const resetToInitialPosts = ()=>{
        setPosts(initialPosts);
        try {
            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts));
            console.log('Reset to initial posts and saved to localStorage');
        } catch (error) {
            console.error('Error resetting posts in localStorage:', error);
        }
    };
    // Function to clean up expired posts (older than 1 month)
    const cleanupExpiredPosts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PostsProvider.useCallback[cleanupExpiredPosts]": ()=>{
            const now = new Date();
            setPosts({
                "PostsProvider.useCallback[cleanupExpiredPosts]": (prevPosts)=>{
                    const activePosts = prevPosts.filter({
                        "PostsProvider.useCallback[cleanupExpiredPosts].activePosts": (post)=>{
                            const expirationDate = new Date(post.expires_at);
                            return expirationDate > now;
                        }
                    }["PostsProvider.useCallback[cleanupExpiredPosts].activePosts"]);
                    const expiredCount = prevPosts.length - activePosts.length;
                    if (expiredCount > 0) {
                        console.log(`Cleaned up ${expiredCount} expired posts`);
                        // Save cleaned posts to localStorage
                        try {
                            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts));
                            console.log('Cleaned posts saved to localStorage');
                        } catch (error) {
                            console.error('Error saving cleaned posts to localStorage:', error);
                        }
                    }
                    return activePosts;
                }
            }["PostsProvider.useCallback[cleanupExpiredPosts]"]);
        }
    }["PostsProvider.useCallback[cleanupExpiredPosts]"], []);
    const migratePostsToFullNames = ()=>{
        console.log('Migrating posts to use full names...');
        // Clear localStorage and reset to initial posts with proper names
        try {
            localStorage.removeItem('kaazmaamaa-posts');
            console.log('Cleared old posts from localStorage');
        } catch (error) {
            console.error('Error clearing localStorage:', error);
        }
        resetToInitialPosts();
    };
    const value = {
        posts,
        addPost,
        likePost,
        sharePost,
        deletePost,
        addComment,
        clearAllPosts,
        resetToInitialPosts,
        cleanupExpiredPosts,
        migratePostsToFullNames
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PostsContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/PostsContext.tsx",
        lineNumber: 393,
        columnNumber: 5
    }, this);
}
_s(PostsProvider, "grrvA51HETDAPO/ii/ehM7XMTSc=");
_c = PostsProvider;
function usePosts() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(PostsContext);
    if (context === undefined) {
        throw new Error('usePosts must be used within a PostsProvider');
    }
    return context;
}
_s1(usePosts, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function formatTimeAgo(dateString) {
    const now = new Date();
    const postDate = new Date(dateString);
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return postDate.toLocaleDateString();
}
function formatExpirationInfo(expiresAt) {
    const now = new Date();
    const expirationDate = new Date(expiresAt);
    const diffInMs = expirationDate.getTime() - now.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    if (diffInMs <= 0) {
        return {
            text: 'Expired',
            isExpiringSoon: true
        };
    }
    if (diffInDays <= 3) {
        return {
            text: `Expires in ${diffInDays} day${diffInDays !== 1 ? 's' : ''}`,
            isExpiringSoon: true
        };
    }
    if (diffInDays <= 7) {
        return {
            text: `Expires in ${diffInDays} days`,
            isExpiringSoon: false
        };
    }
    const diffInWeeks = Math.floor(diffInDays / 7);
    if (diffInWeeks < 4) {
        return {
            text: `Expires in ${diffInWeeks} week${diffInWeeks !== 1 ? 's' : ''}`,
            isExpiringSoon: false
        };
    }
    return {
        text: 'Expires in 1 month',
        isExpiringSoon: false
    };
}
var _c;
__turbopack_context__.k.register(_c, "PostsProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/OnlineUsersContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "OnlineUsersProvider": (()=>OnlineUsersProvider),
    "formatLastSeen": (()=>formatLastSeen),
    "useOnlineUsers": (()=>useOnlineUsers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const OnlineUsersContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Mock online users data
const mockOnlineUsers = [
    {
        id: 'user-1',
        name: 'Ahmed Al-Rashid',
        email: '<EMAIL>',
        role: 'worker',
        lastSeen: new Date().toISOString(),
        isOnline: true
    },
    {
        id: 'user-2',
        name: 'Sarah Construction',
        email: '<EMAIL>',
        role: 'company',
        lastSeen: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        isOnline: true
    },
    {
        id: 'user-3',
        name: 'Gulf Manpower',
        email: '<EMAIL>',
        role: 'supplier',
        lastSeen: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
        isOnline: true
    },
    {
        id: 'user-4',
        name: 'Mohammed Hassan',
        email: '<EMAIL>',
        role: 'worker',
        lastSeen: new Date(Date.now() - 1 * 60 * 1000).toISOString(),
        isOnline: true
    },
    {
        id: 'user-5',
        name: 'Riyadh Builders',
        email: '<EMAIL>',
        role: 'company',
        lastSeen: new Date(Date.now() - 3 * 60 * 1000).toISOString(),
        isOnline: true
    },
    {
        id: 'user-6',
        name: 'Ali Construction',
        email: '<EMAIL>',
        role: 'worker',
        lastSeen: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        isOnline: false
    }
];
function OnlineUsersProvider({ children }) {
    _s();
    const [onlineUsers, setOnlineUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "OnlineUsersProvider.useEffect": ()=>{
            // Load initial online users
            setOnlineUsers(mockOnlineUsers);
            // Simulate real-time updates
            const interval = setInterval({
                "OnlineUsersProvider.useEffect.interval": ()=>{
                    setOnlineUsers({
                        "OnlineUsersProvider.useEffect.interval": (prevUsers)=>{
                            return prevUsers.map({
                                "OnlineUsersProvider.useEffect.interval": (user)=>{
                                    // Randomly update online status for demo
                                    const shouldToggle = Math.random() < 0.1 // 10% chance to toggle
                                    ;
                                    if (shouldToggle) {
                                        return {
                                            ...user,
                                            isOnline: !user.isOnline,
                                            lastSeen: new Date().toISOString()
                                        };
                                    }
                                    return user;
                                }
                            }["OnlineUsersProvider.useEffect.interval"]);
                        }
                    }["OnlineUsersProvider.useEffect.interval"]);
                }
            }["OnlineUsersProvider.useEffect.interval"], 30000) // Update every 30 seconds
            ;
            return ({
                "OnlineUsersProvider.useEffect": ()=>clearInterval(interval)
            })["OnlineUsersProvider.useEffect"];
        }
    }["OnlineUsersProvider.useEffect"], []);
    const setUserOnline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "OnlineUsersProvider.useCallback[setUserOnline]": (user)=>{
            setOnlineUsers({
                "OnlineUsersProvider.useCallback[setUserOnline]": (prevUsers)=>{
                    const existingUserIndex = prevUsers.findIndex({
                        "OnlineUsersProvider.useCallback[setUserOnline].existingUserIndex": (u)=>u.id === user.id
                    }["OnlineUsersProvider.useCallback[setUserOnline].existingUserIndex"]);
                    const onlineUser = {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        role: user.role,
                        lastSeen: new Date().toISOString(),
                        isOnline: true
                    };
                    if (existingUserIndex >= 0) {
                        // Update existing user only if status changed
                        const existingUser = prevUsers[existingUserIndex];
                        if (existingUser.isOnline) {
                            // User is already online, no need to update
                            return prevUsers;
                        }
                        const updatedUsers = [
                            ...prevUsers
                        ];
                        updatedUsers[existingUserIndex] = onlineUser;
                        return updatedUsers;
                    } else {
                        // Add new user
                        return [
                            ...prevUsers,
                            onlineUser
                        ];
                    }
                }
            }["OnlineUsersProvider.useCallback[setUserOnline]"]);
        }
    }["OnlineUsersProvider.useCallback[setUserOnline]"], []);
    const setUserOffline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "OnlineUsersProvider.useCallback[setUserOffline]": (userId)=>{
            setOnlineUsers({
                "OnlineUsersProvider.useCallback[setUserOffline]": (prevUsers)=>prevUsers.map({
                        "OnlineUsersProvider.useCallback[setUserOffline]": (user)=>user.id === userId ? {
                                ...user,
                                isOnline: false,
                                lastSeen: new Date().toISOString()
                            } : user
                    }["OnlineUsersProvider.useCallback[setUserOffline]"])
            }["OnlineUsersProvider.useCallback[setUserOffline]"]);
        }
    }["OnlineUsersProvider.useCallback[setUserOffline]"], []);
    const isUserOnline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "OnlineUsersProvider.useCallback[isUserOnline]": (userId)=>{
            const user = onlineUsers.find({
                "OnlineUsersProvider.useCallback[isUserOnline].user": (u)=>u.id === userId
            }["OnlineUsersProvider.useCallback[isUserOnline].user"]);
            return user?.isOnline || false;
        }
    }["OnlineUsersProvider.useCallback[isUserOnline]"], [
        onlineUsers
    ]);
    const totalOnlineCount = onlineUsers.filter((user)=>user.isOnline).length;
    const value = {
        onlineUsers,
        totalOnlineCount,
        setUserOnline,
        setUserOffline,
        isUserOnline
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(OnlineUsersContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/OnlineUsersContext.tsx",
        lineNumber: 160,
        columnNumber: 5
    }, this);
}
_s(OnlineUsersProvider, "ElvQvg97wPCuiVPI7GOphkZf3sg=");
_c = OnlineUsersProvider;
function useOnlineUsers() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(OnlineUsersContext);
    if (context === undefined) {
        throw new Error('useOnlineUsers must be used within an OnlineUsersProvider');
    }
    return context;
}
_s1(useOnlineUsers, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function formatLastSeen(lastSeenString) {
    const now = new Date();
    const lastSeen = new Date(lastSeenString);
    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return lastSeen.toLocaleDateString();
}
var _c;
__turbopack_context__.k.register(_c, "OnlineUsersProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/WorkersContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "WorkersProvider": (()=>WorkersProvider),
    "useWorkers": (()=>useWorkers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const WorkersContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Mock data for demonstration
const mockWorkers = [
    {
        id: 'worker-1',
        userId: 'user-1',
        personalInfo: {
            workerName: 'Ahmed Al-Rashid',
            nationality: 'Saudi Arabia',
            kofilName: 'Ahmed Mohammed Al-Rashid',
            kofilPhone: '+966501234567',
            email: '<EMAIL>',
            phone: '+966501234567',
            whatsapp: '+966501234567',
            address: 'King Fahd Road, Riyadh 12345, Saudi Arabia'
        },
        identificationInfo: {
            iqamaNumber: '2345678901',
            passportNumber: 'A12345678',
            crNumber: 'CR-1010987654'
        },
        professionalInfo: {
            workerCategory: 'Electrical Technician',
            title: 'Senior Electrical Technician',
            specialty: 'Electrical Work',
            experience: 5,
            skills: [
                'Electrical Installation',
                'Maintenance',
                'Troubleshooting',
                'Safety Protocols'
            ],
            languages: [
                'Arabic',
                'English'
            ],
            expectedSalaryPerHour: 85,
            currency: 'SAR',
            salaryPayingDuration: 'weekly',
            availability: 'immediate'
        },
        documents: [
            {
                id: 'doc-1',
                name: 'Iqama Document',
                type: 'iqama',
                url: '/documents/iqama.pdf',
                uploadedAt: new Date().toISOString(),
                isValidated: true
            },
            {
                id: 'doc-2',
                name: 'Visa Document',
                type: 'visa',
                url: '/documents/visa.pdf',
                uploadedAt: new Date().toISOString()
            },
            {
                id: 'doc-3',
                name: 'Passport Copy',
                type: 'passport',
                url: '/documents/passport.pdf',
                uploadedAt: new Date().toISOString()
            },
            {
                id: 'doc-4',
                name: 'Medical Certificate',
                type: 'medical',
                url: '/documents/medical.pdf',
                uploadedAt: new Date().toISOString()
            },
            {
                id: 'doc-5',
                name: 'Fitness Certificate',
                type: 'fitness',
                url: '/documents/fitness.pdf',
                uploadedAt: new Date().toISOString()
            }
        ],
        videos: [
            {
                id: 'video-1',
                title: 'Professional Introduction',
                type: 'introduction',
                url: '/videos/intro.mp4',
                duration: 120,
                uploadedAt: new Date().toISOString()
            }
        ],
        workExperience: [
            {
                id: 'exp-1',
                company: 'Saudi Electric Company',
                position: 'Electrical Technician',
                startDate: '2020-01-01',
                endDate: '2023-12-31',
                description: 'Responsible for electrical installations and maintenance',
                location: 'Riyadh',
                isCurrentJob: false
            }
        ],
        lifecycle: {
            status: 'seeking',
            notes: 'Looking for new opportunities in electrical field'
        },
        preferences: {
            workType: 'full_time',
            sideLocationInterest: [
                'Riyadh',
                'Jeddah',
                'Dammam'
            ],
            companyNameInterest: [
                'Saudi Electric Company',
                'ARAMCO',
                'SABIC'
            ],
            willingToRelocate: true,
            remoteWork: false,
            neededDocuments: [
                'Work Permit',
                'Safety Certificate',
                'Medical Clearance'
            ],
            otherRequirements: 'Prefer companies with good safety standards and career growth opportunities'
        },
        isLiveStreaming: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isVerified: true,
        rating: 4.8,
        totalReviews: 15
    },
    {
        id: 'worker-2',
        userId: 'user-2',
        personalInfo: {
            workerName: 'Mohammed Al-Zahrani',
            nationality: 'Saudi Arabia',
            kofilName: 'Mohammed Abdullah Al-Zahrani',
            kofilPhone: '+966502345678',
            email: '<EMAIL>',
            phone: '+966502345678',
            whatsapp: '+966502345678',
            address: 'Prince Sultan Road, Jeddah 21455, Saudi Arabia'
        },
        identificationInfo: {
            iqamaNumber: '**********',
            passportNumber: 'B23456789',
            crNumber: 'CR-**********'
        },
        professionalInfo: {
            workerCategory: 'Plumber',
            title: 'Senior Plumber',
            specialty: 'Plumbing & Water Systems',
            experience: 8,
            skills: [
                'Pipe Installation',
                'Water System Repair',
                'Drainage',
                'Emergency Repairs'
            ],
            languages: [
                'Arabic',
                'English',
                'Urdu'
            ],
            expectedSalaryPerHour: 75,
            currency: 'SAR',
            salaryPayingDuration: 'daily',
            availability: 'within_week'
        },
        documents: [
            {
                id: 'doc-6',
                name: 'Iqama Document',
                type: 'iqama',
                url: '/documents/iqama2.pdf',
                uploadedAt: new Date().toISOString(),
                isValidated: true
            },
            {
                id: 'doc-7',
                name: 'Plumbing Certificate',
                type: 'experience',
                url: '/documents/plumbing-cert.pdf',
                uploadedAt: new Date().toISOString()
            }
        ],
        videos: [
            {
                id: 'video-2',
                title: 'Plumbing Skills Demo',
                type: 'skills',
                url: '/videos/plumbing-demo.mp4',
                duration: 180,
                uploadedAt: new Date().toISOString()
            }
        ],
        workExperience: [
            {
                id: 'exp-2',
                company: 'Saudi Water Company',
                position: 'Senior Plumber',
                startDate: '2018-03-01',
                endDate: '2024-01-01',
                description: 'Specialized in water system installations and repairs',
                location: 'Jeddah',
                isCurrentJob: false
            }
        ],
        lifecycle: {
            status: 'available',
            notes: 'Available for immediate plumbing work'
        },
        preferences: {
            workType: 'contract',
            sideLocationInterest: [
                'Jeddah',
                'Mecca',
                'Taif'
            ],
            companyNameInterest: [
                'Saudi Water Company',
                'National Water Company'
            ],
            willingToRelocate: false,
            remoteWork: false,
            neededDocuments: [
                'Work Permit',
                'Plumbing License'
            ],
            otherRequirements: 'Prefer emergency repair contracts'
        },
        isLiveStreaming: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isVerified: true,
        rating: 4.6,
        totalReviews: 23
    },
    {
        id: 'worker-3',
        userId: 'user-3',
        personalInfo: {
            workerName: 'Khalid Al-Mutairi',
            nationality: 'Saudi Arabia',
            kofilName: 'Khalid Saad Al-Mutairi',
            kofilPhone: '+966503456789',
            email: '<EMAIL>',
            phone: '+966503456789',
            whatsapp: '+966503456789',
            address: 'King Abdul Aziz Road, Dammam 31411, Saudi Arabia'
        },
        identificationInfo: {
            iqamaNumber: '4*********',
            passportNumber: '*********',
            crNumber: 'CR-**********'
        },
        professionalInfo: {
            workerCategory: 'Carpenter',
            title: 'Master Carpenter',
            specialty: 'Woodworking & Furniture',
            experience: 12,
            skills: [
                'Custom Furniture',
                'Cabinet Making',
                'Wood Finishing',
                'Restoration'
            ],
            languages: [
                'Arabic',
                'English'
            ],
            expectedSalaryPerHour: 95,
            currency: 'SAR',
            salaryPayingDuration: 'weekly',
            availability: 'immediate'
        },
        documents: [
            {
                id: 'doc-8',
                name: 'Iqama Document',
                type: 'iqama',
                url: '/documents/iqama3.pdf',
                uploadedAt: new Date().toISOString(),
                isValidated: true
            },
            {
                id: 'doc-9',
                name: 'Carpentry Master Certificate',
                type: 'experience',
                url: '/documents/carpentry-master.pdf',
                uploadedAt: new Date().toISOString()
            }
        ],
        videos: [
            {
                id: 'video-3',
                title: 'Custom Furniture Portfolio',
                type: 'portfolio',
                url: '/videos/furniture-portfolio.mp4',
                duration: 300,
                uploadedAt: new Date().toISOString()
            }
        ],
        workExperience: [
            {
                id: 'exp-3',
                company: 'Royal Furniture Workshop',
                position: 'Master Carpenter',
                startDate: '2015-01-01',
                description: 'Creating custom furniture for luxury homes and offices',
                location: 'Dammam',
                isCurrentJob: true
            }
        ],
        lifecycle: {
            status: 'seeking',
            notes: 'Looking for high-end furniture projects'
        },
        preferences: {
            workType: 'freelance',
            sideLocationInterest: [
                'Dammam',
                'Khobar',
                'Dhahran'
            ],
            companyNameInterest: [
                'Royal Furniture',
                'Elite Woodworks'
            ],
            willingToRelocate: true,
            remoteWork: false,
            neededDocuments: [
                'Work Permit',
                'Carpentry License',
                'Portfolio'
            ],
            otherRequirements: 'Prefer luxury furniture projects'
        },
        isLiveStreaming: true,
        liveStreamUrl: 'https://stream.example.com/khalid-live',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isVerified: true,
        rating: 4.9,
        totalReviews: 31
    }
];
function WorkersProvider({ children }) {
    _s();
    const [workers, setWorkers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentWorkerProfile, setCurrentWorkerProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "WorkersProvider.useEffect": ()=>{
            try {
                // Load workers from localStorage or use mock data
                const savedWorkers = localStorage.getItem('kaazmaamaa-workers');
                if (savedWorkers) {
                    const parsedWorkers = JSON.parse(savedWorkers);
                    // Validate the data structure
                    if (Array.isArray(parsedWorkers) && parsedWorkers.length > 0) {
                        setWorkers(parsedWorkers);
                    } else {
                        // Use mock data if saved data is invalid
                        setWorkers(mockWorkers);
                        localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers));
                    }
                } else {
                    // Use mock data for first time
                    setWorkers(mockWorkers);
                    localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers));
                }
            } catch (error) {
                console.error('Error loading workers:', error);
                // Fallback to mock data if there's an error
                setWorkers(mockWorkers);
                localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers));
            }
        }
    }["WorkersProvider.useEffect"], []);
    const saveToStorage = (updatedWorkers)=>{
        localStorage.setItem('kaazmaamaa-workers', JSON.stringify(updatedWorkers));
    };
    const createWorkerProfile = (profile)=>{
        const newProfile = {
            ...profile,
            id: Date.now().toString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const updatedWorkers = [
            ...workers,
            newProfile
        ];
        setWorkers(updatedWorkers);
        saveToStorage(updatedWorkers);
        setCurrentWorkerProfile(newProfile);
    };
    const updateWorkerProfile = (id, updates)=>{
        const updatedWorkers = workers.map((worker)=>worker.id === id ? {
                ...worker,
                ...updates,
                updatedAt: new Date().toISOString()
            } : worker);
        setWorkers(updatedWorkers);
        saveToStorage(updatedWorkers);
    };
    const updateWorker = (profile)=>{
        const existingWorkerIndex = workers.findIndex((w)=>w.userId === profile.userId);
        if (existingWorkerIndex >= 0) {
            // Update existing worker
            const updatedWorkers = [
                ...workers
            ];
            updatedWorkers[existingWorkerIndex] = {
                ...profile,
                updatedAt: new Date().toISOString()
            };
            setWorkers(updatedWorkers);
            saveToStorage(updatedWorkers);
        } else {
            // Create new worker if doesn't exist
            const newProfile = {
                ...profile,
                id: profile.id || Date.now().toString(),
                createdAt: profile.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            const updatedWorkers = [
                ...workers,
                newProfile
            ];
            setWorkers(updatedWorkers);
            saveToStorage(updatedWorkers);
        }
    };
    const addDocument = (workerId, document)=>{
        const newDocument = {
            ...document,
            id: Date.now().toString(),
            uploadedAt: new Date().toISOString()
        };
        updateWorkerProfile(workerId, {
            documents: [
                ...workers.find((w)=>w.id === workerId)?.documents || [],
                newDocument
            ]
        });
    };
    const addVideo = (workerId, video)=>{
        const newVideo = {
            ...video,
            id: Date.now().toString(),
            uploadedAt: new Date().toISOString()
        };
        updateWorkerProfile(workerId, {
            videos: [
                ...workers.find((w)=>w.id === workerId)?.videos || [],
                newVideo
            ]
        });
    };
    const updateLifecycle = (workerId, lifecycle)=>{
        const worker = workers.find((w)=>w.id === workerId);
        if (worker) {
            updateWorkerProfile(workerId, {
                lifecycle: {
                    ...worker.lifecycle,
                    ...lifecycle
                }
            });
        }
    };
    const startLiveStream = (workerId, streamUrl)=>{
        updateWorkerProfile(workerId, {
            isLiveStreaming: true,
            liveStreamUrl: streamUrl
        });
    };
    const stopLiveStream = (workerId)=>{
        updateWorkerProfile(workerId, {
            isLiveStreaming: false,
            liveStreamUrl: undefined
        });
    };
    const getWorkersBySpecialty = (specialty)=>{
        return workers.filter((worker)=>worker.professionalInfo.specialty.toLowerCase().includes(specialty.toLowerCase()));
    };
    const getAvailableWorkers = ()=>{
        return workers.filter((worker)=>worker.lifecycle.status === 'available' || worker.lifecycle.status === 'seeking');
    };
    const getWorkersByCategory = (category)=>{
        return workers.filter((worker)=>worker.professionalInfo.workerCategory.toLowerCase().includes(category.toLowerCase()));
    };
    const validateWorkerIdUniqueness = (workerId, excludeId)=>{
        return !workers.some((worker)=>worker.id === workerId && worker.id !== excludeId);
    };
    const validateIqamaDocument = async (iqamaNumber)=>{
        // Mock validation - in real app, this would call government API
        return new Promise((resolve)=>{
            setTimeout(()=>{
                // Simple validation: check if it's 10 digits
                const isValid = /^\d{10}$/.test(iqamaNumber);
                resolve(isValid);
            }, 1000);
        });
    };
    const value = {
        workers,
        currentWorkerProfile,
        createWorkerProfile,
        updateWorkerProfile,
        updateWorker,
        addDocument,
        addVideo,
        updateLifecycle,
        startLiveStream,
        stopLiveStream,
        getWorkersBySpecialty,
        getWorkersByCategory,
        getAvailableWorkers,
        validateWorkerIdUniqueness,
        validateIqamaDocument
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(WorkersContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/WorkersContext.tsx",
        lineNumber: 600,
        columnNumber: 5
    }, this);
}
_s(WorkersProvider, "KKXOZhvkmZeES00uw8NkdvjCakg=");
_c = WorkersProvider;
function useWorkers() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(WorkersContext);
    if (context === undefined) {
        throw new Error('useWorkers must be used within a WorkersProvider');
    }
    return context;
}
_s1(useWorkers, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "WorkersProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/SuppliersContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SuppliersProvider": (()=>SuppliersProvider),
    "useSuppliers": (()=>useSuppliers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const SuppliersContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Mock data for demonstration
const mockSuppliers = [
    {
        id: 'supplier-1',
        userId: 'user-supplier-1',
        personalInfo: {
            supplierName: 'Ahmed Construction Supplier',
            nationality: 'Saudi Arabia',
            kofilName: 'Ahmed Al-Rashid',
            kofilPhone: '+966501234567',
            address: 'King Fahd Road, Riyadh 12345',
            phone: '+966501234567',
            whatsapp: '+966501234567',
            email: '<EMAIL>'
        },
        businessInfo: {
            companyName: 'Al-Rashid Construction Supply Co.',
            crNumber: 'CR-1010123456',
            licenceNumber: 'LIC-2024-001',
            businessType: 'Construction Supply',
            establishedYear: 2015,
            employeeCount: '50-100'
        },
        documents: [
            {
                id: 'doc-1',
                name: 'Business Licence',
                type: 'licence',
                url: '/documents/business-licence.pdf',
                uploadedAt: new Date().toISOString()
            },
            {
                id: 'doc-2',
                name: 'Commercial Registration',
                type: 'cr',
                url: '/documents/cr-certificate.pdf',
                uploadedAt: new Date().toISOString()
            }
        ],
        jobDemands: [
            {
                id: 'job-1',
                category: 'Electrical Work',
                position: 'Senior Electrician',
                description: 'Looking for experienced electricians for large construction project',
                requirements: [
                    '5+ years experience',
                    'Electrical license',
                    'Safety certification'
                ],
                salaryPerHour: 85,
                currency: 'SAR',
                paymentDuration: 'weekly',
                location: 'Riyadh',
                urgency: 'immediate',
                documentsRequired: [
                    'Iqama',
                    'Electrical License',
                    'Medical Certificate'
                ],
                createdAt: new Date().toISOString()
            }
        ],
        videos: [
            {
                id: 'video-1',
                title: 'Company Introduction',
                type: 'company_intro',
                url: '/videos/company-intro.mp4',
                duration: 180,
                uploadedAt: new Date().toISOString()
            }
        ],
        isLiveStreaming: false,
        operatingLocations: [
            'Riyadh',
            'Jeddah',
            'Dammam'
        ],
        preferredWorkerCategories: [
            'Electrical Work',
            'Plumbing',
            'Construction'
        ],
        rating: 4.7,
        totalReviews: 23,
        isVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
function SuppliersProvider({ children }) {
    _s();
    const [suppliers, setSuppliers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentSupplierProfile, setCurrentSupplierProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SuppliersProvider.useEffect": ()=>{
            // Load suppliers from localStorage or use mock data
            const savedSuppliers = localStorage.getItem('kaazmaamaa-suppliers');
            if (savedSuppliers) {
                setSuppliers(JSON.parse(savedSuppliers));
            } else {
                setSuppliers(mockSuppliers);
                localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(mockSuppliers));
            }
        }
    }["SuppliersProvider.useEffect"], []);
    const saveToStorage = (updatedSuppliers)=>{
        localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(updatedSuppliers));
    };
    const createSupplierProfile = (profile)=>{
        const newProfile = {
            ...profile,
            id: `supplier-${Date.now()}`,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        const updatedSuppliers = [
            ...suppliers,
            newProfile
        ];
        setSuppliers(updatedSuppliers);
        saveToStorage(updatedSuppliers);
        setCurrentSupplierProfile(newProfile);
    };
    const updateSupplierProfile = (id, updates)=>{
        const updatedSuppliers = suppliers.map((supplier)=>supplier.id === id ? {
                ...supplier,
                ...updates,
                updatedAt: new Date().toISOString()
            } : supplier);
        setSuppliers(updatedSuppliers);
        saveToStorage(updatedSuppliers);
    };
    const addDocument = (supplierId, document)=>{
        const newDocument = {
            ...document,
            id: Date.now().toString(),
            uploadedAt: new Date().toISOString()
        };
        updateSupplierProfile(supplierId, {
            documents: [
                ...suppliers.find((s)=>s.id === supplierId)?.documents || [],
                newDocument
            ]
        });
    };
    const addJobDemand = (supplierId, jobDemand)=>{
        const newJobDemand = {
            ...jobDemand,
            id: Date.now().toString(),
            createdAt: new Date().toISOString()
        };
        updateSupplierProfile(supplierId, {
            jobDemands: [
                ...suppliers.find((s)=>s.id === supplierId)?.jobDemands || [],
                newJobDemand
            ]
        });
    };
    const updateJobDemand = (supplierId, jobDemandId, updates)=>{
        const supplier = suppliers.find((s)=>s.id === supplierId);
        if (supplier) {
            const updatedJobDemands = supplier.jobDemands.map((job)=>job.id === jobDemandId ? {
                    ...job,
                    ...updates
                } : job);
            updateSupplierProfile(supplierId, {
                jobDemands: updatedJobDemands
            });
        }
    };
    const deleteJobDemand = (supplierId, jobDemandId)=>{
        const supplier = suppliers.find((s)=>s.id === supplierId);
        if (supplier) {
            const updatedJobDemands = supplier.jobDemands.filter((job)=>job.id !== jobDemandId);
            updateSupplierProfile(supplierId, {
                jobDemands: updatedJobDemands
            });
        }
    };
    const startLiveStream = (supplierId, streamUrl)=>{
        updateSupplierProfile(supplierId, {
            isLiveStreaming: true,
            liveStreamUrl: streamUrl
        });
    };
    const stopLiveStream = (supplierId)=>{
        updateSupplierProfile(supplierId, {
            isLiveStreaming: false,
            liveStreamUrl: undefined
        });
    };
    const getSuppliersByLocation = (location)=>{
        return suppliers.filter((supplier)=>supplier.operatingLocations.some((loc)=>loc.toLowerCase().includes(location.toLowerCase())));
    };
    const getJobDemandsByCategory = (category)=>{
        const results = [];
        suppliers.forEach((supplier)=>{
            supplier.jobDemands.forEach((jobDemand)=>{
                if (jobDemand.category.toLowerCase().includes(category.toLowerCase())) {
                    results.push({
                        supplier,
                        jobDemand
                    });
                }
            });
        });
        return results;
    };
    const validateLicenceUniqueness = (licenceNumber, excludeId)=>{
        return !suppliers.some((supplier)=>supplier.businessInfo.licenceNumber === licenceNumber && supplier.id !== excludeId);
    };
    const value = {
        suppliers,
        currentSupplierProfile,
        createSupplierProfile,
        updateSupplierProfile,
        addDocument,
        addJobDemand,
        updateJobDemand,
        deleteJobDemand,
        startLiveStream,
        stopLiveStream,
        getSuppliersByLocation,
        getJobDemandsByCategory,
        validateLicenceUniqueness
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SuppliersContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SuppliersContext.tsx",
        lineNumber: 308,
        columnNumber: 5
    }, this);
}
_s(SuppliersProvider, "3gmfkyutDXn6EsJvq8U5xzhRuD8=");
_c = SuppliersProvider;
function useSuppliers() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SuppliersContext);
    if (context === undefined) {
        throw new Error('useSuppliers must be used within a SuppliersProvider');
    }
    return context;
}
_s1(useSuppliers, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "SuppliersProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/CompaniesContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CompaniesProvider": (()=>CompaniesProvider),
    "useCompanies": (()=>useCompanies)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const CompaniesContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Mock data for demonstration
const mockCompanies = [
    {
        id: 'company-1',
        userId: 'user-company-1',
        companyInfo: {
            companyName: 'Saudi Tech Solutions',
            tradeName: 'STS',
            industry: 'Information Technology',
            companySize: '201-500',
            foundedYear: 2010,
            headquarters: 'Riyadh, Saudi Arabia',
            website: 'https://sauditechsolutions.com',
            description: 'Leading technology solutions provider in the Kingdom of Saudi Arabia',
            mission: 'To provide innovative technology solutions that drive digital transformation',
            vision: 'To be the leading technology partner in the Middle East'
        },
        contactInfo: {
            hrManagerName: 'Sarah Al-Mahmoud',
            hrEmail: '<EMAIL>',
            hrPhone: '+966112345678',
            hrWhatsapp: '+966501234567',
            companyPhone: '+966112345678',
            companyEmail: '<EMAIL>',
            address: 'King Fahd Road, Olaya District, Riyadh 12345',
            poBox: 'P.O. Box 12345, Riyadh 11564'
        },
        legalInfo: {
            crNumber: 'CR-**********',
            taxNumber: 'TAX-300123456789003',
            licenceNumber: 'LIC-TECH-2024-001',
            establishmentNumber: 'EST-*********',
            chamberMembership: 'CHAMBER-RUH-2024-456'
        },
        documents: [
            {
                id: 'doc-1',
                name: 'Commercial Registration',
                type: 'cr',
                url: '/documents/company-cr.pdf',
                uploadedAt: new Date().toISOString()
            },
            {
                id: 'doc-2',
                name: 'Business License',
                type: 'licence',
                url: '/documents/company-license.pdf',
                uploadedAt: new Date().toISOString()
            }
        ],
        jobOpenings: [
            {
                id: 'job-1',
                title: 'Senior Software Engineer',
                department: 'Engineering',
                category: 'Software Development',
                description: 'We are looking for a senior software engineer to join our growing team',
                requirements: [
                    '5+ years experience',
                    'React/Node.js',
                    'AWS knowledge',
                    'Team leadership'
                ],
                salaryRange: {
                    min: 15000,
                    max: 25000,
                    currency: 'SAR'
                },
                employmentType: 'full_time',
                experienceLevel: 'senior',
                location: 'Riyadh',
                isRemote: false,
                benefits: [
                    'Health Insurance',
                    'Annual Bonus',
                    'Training Budget',
                    'Flexible Hours'
                ],
                urgency: 'immediate',
                documentsRequired: [
                    'CV',
                    'Portfolio',
                    'Certificates'
                ],
                createdAt: new Date().toISOString(),
                applicationsCount: 23
            }
        ],
        videos: [
            {
                id: 'video-1',
                title: 'Company Culture Overview',
                type: 'company_intro',
                url: '/videos/company-culture.mp4',
                duration: 240,
                uploadedAt: new Date().toISOString()
            }
        ],
        isLiveStreaming: false,
        operatingLocations: [
            'Riyadh',
            'Jeddah',
            'Dammam'
        ],
        preferredWorkerCategories: [
            'Software Development',
            'Data Analysis',
            'Project Management'
        ],
        companyBenefits: [
            'Health Insurance',
            'Annual Bonus',
            'Training Programs',
            'Career Development'
        ],
        workEnvironment: {
            isRemoteFriendly: true,
            hasFlexibleHours: true,
            providesTraining: true,
            hasCareerGrowth: true,
            workLifeBalance: 4
        },
        rating: 4.6,
        totalReviews: 87,
        totalEmployees: 350,
        isVerified: true,
        isHiring: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    }
];
function CompaniesProvider({ children }) {
    _s();
    const [companies, setCompanies] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentCompanyProfile, setCurrentCompanyProfile] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CompaniesProvider.useEffect": ()=>{
            // Load companies from localStorage or use mock data
            const savedCompanies = localStorage.getItem('kaazmaamaa-companies');
            if (savedCompanies) {
                setCompanies(JSON.parse(savedCompanies));
            } else {
                setCompanies(mockCompanies);
                localStorage.setItem('kaazmaamaa-companies', JSON.stringify(mockCompanies));
            }
        }
    }["CompaniesProvider.useEffect"], []);
    const saveToStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[saveToStorage]": (updatedCompanies)=>{
            localStorage.setItem('kaazmaamaa-companies', JSON.stringify(updatedCompanies));
        }
    }["CompaniesProvider.useCallback[saveToStorage]"], []);
    const createCompanyProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[createCompanyProfile]": (profile)=>{
            const newProfile = {
                ...profile,
                id: `company-${Date.now()}`,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            const updatedCompanies = [
                ...companies,
                newProfile
            ];
            setCompanies(updatedCompanies);
            saveToStorage(updatedCompanies);
            setCurrentCompanyProfile(newProfile);
        }
    }["CompaniesProvider.useCallback[createCompanyProfile]"], [
        companies,
        saveToStorage
    ]);
    const updateCompanyProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[updateCompanyProfile]": (id, updates)=>{
            const updatedCompanies = companies.map({
                "CompaniesProvider.useCallback[updateCompanyProfile].updatedCompanies": (company)=>company.id === id ? {
                        ...company,
                        ...updates,
                        updatedAt: new Date().toISOString()
                    } : company
            }["CompaniesProvider.useCallback[updateCompanyProfile].updatedCompanies"]);
            setCompanies(updatedCompanies);
            saveToStorage(updatedCompanies);
        }
    }["CompaniesProvider.useCallback[updateCompanyProfile]"], [
        companies,
        saveToStorage
    ]);
    const addDocument = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[addDocument]": (companyId, document)=>{
            const newDocument = {
                ...document,
                id: Date.now().toString(),
                uploadedAt: new Date().toISOString()
            };
            updateCompanyProfile(companyId, {
                documents: [
                    ...companies.find({
                        "CompaniesProvider.useCallback[addDocument]": (c)=>c.id === companyId
                    }["CompaniesProvider.useCallback[addDocument]"])?.documents || [],
                    newDocument
                ]
            });
        }
    }["CompaniesProvider.useCallback[addDocument]"], [
        companies,
        updateCompanyProfile
    ]);
    const addJobOpening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[addJobOpening]": (companyId, jobOpening)=>{
            const newJobOpening = {
                ...jobOpening,
                id: Date.now().toString(),
                createdAt: new Date().toISOString(),
                applicationsCount: 0
            };
            updateCompanyProfile(companyId, {
                jobOpenings: [
                    ...companies.find({
                        "CompaniesProvider.useCallback[addJobOpening]": (c)=>c.id === companyId
                    }["CompaniesProvider.useCallback[addJobOpening]"])?.jobOpenings || [],
                    newJobOpening
                ]
            });
        }
    }["CompaniesProvider.useCallback[addJobOpening]"], [
        companies,
        updateCompanyProfile
    ]);
    const updateJobOpening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[updateJobOpening]": (companyId, jobOpeningId, updates)=>{
            const company = companies.find({
                "CompaniesProvider.useCallback[updateJobOpening].company": (c)=>c.id === companyId
            }["CompaniesProvider.useCallback[updateJobOpening].company"]);
            if (company) {
                const updatedJobOpenings = company.jobOpenings.map({
                    "CompaniesProvider.useCallback[updateJobOpening].updatedJobOpenings": (job)=>job.id === jobOpeningId ? {
                            ...job,
                            ...updates
                        } : job
                }["CompaniesProvider.useCallback[updateJobOpening].updatedJobOpenings"]);
                updateCompanyProfile(companyId, {
                    jobOpenings: updatedJobOpenings
                });
            }
        }
    }["CompaniesProvider.useCallback[updateJobOpening]"], [
        companies,
        updateCompanyProfile
    ]);
    const deleteJobOpening = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[deleteJobOpening]": (companyId, jobOpeningId)=>{
            const company = companies.find({
                "CompaniesProvider.useCallback[deleteJobOpening].company": (c)=>c.id === companyId
            }["CompaniesProvider.useCallback[deleteJobOpening].company"]);
            if (company) {
                const updatedJobOpenings = company.jobOpenings.filter({
                    "CompaniesProvider.useCallback[deleteJobOpening].updatedJobOpenings": (job)=>job.id !== jobOpeningId
                }["CompaniesProvider.useCallback[deleteJobOpening].updatedJobOpenings"]);
                updateCompanyProfile(companyId, {
                    jobOpenings: updatedJobOpenings
                });
            }
        }
    }["CompaniesProvider.useCallback[deleteJobOpening]"], [
        companies,
        updateCompanyProfile
    ]);
    const startLiveStream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[startLiveStream]": (companyId, streamUrl)=>{
            updateCompanyProfile(companyId, {
                isLiveStreaming: true,
                liveStreamUrl: streamUrl
            });
        }
    }["CompaniesProvider.useCallback[startLiveStream]"], [
        updateCompanyProfile
    ]);
    const stopLiveStream = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[stopLiveStream]": (companyId)=>{
            updateCompanyProfile(companyId, {
                isLiveStreaming: false,
                liveStreamUrl: undefined
            });
        }
    }["CompaniesProvider.useCallback[stopLiveStream]"], [
        updateCompanyProfile
    ]);
    const getCompaniesByLocation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[getCompaniesByLocation]": (location)=>{
            return companies.filter({
                "CompaniesProvider.useCallback[getCompaniesByLocation]": (company)=>company.operatingLocations.some({
                        "CompaniesProvider.useCallback[getCompaniesByLocation]": (loc)=>loc.toLowerCase().includes(location.toLowerCase())
                    }["CompaniesProvider.useCallback[getCompaniesByLocation]"])
            }["CompaniesProvider.useCallback[getCompaniesByLocation]"]);
        }
    }["CompaniesProvider.useCallback[getCompaniesByLocation]"], [
        companies
    ]);
    const getCompaniesByIndustry = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[getCompaniesByIndustry]": (industry)=>{
            return companies.filter({
                "CompaniesProvider.useCallback[getCompaniesByIndustry]": (company)=>company.companyInfo.industry.toLowerCase().includes(industry.toLowerCase())
            }["CompaniesProvider.useCallback[getCompaniesByIndustry]"]);
        }
    }["CompaniesProvider.useCallback[getCompaniesByIndustry]"], [
        companies
    ]);
    const getJobOpeningsByCategory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[getJobOpeningsByCategory]": (category)=>{
            const results = [];
            companies.forEach({
                "CompaniesProvider.useCallback[getJobOpeningsByCategory]": (company)=>{
                    company.jobOpenings.forEach({
                        "CompaniesProvider.useCallback[getJobOpeningsByCategory]": (jobOpening)=>{
                            if (jobOpening.category.toLowerCase().includes(category.toLowerCase())) {
                                results.push({
                                    company,
                                    jobOpening
                                });
                            }
                        }
                    }["CompaniesProvider.useCallback[getJobOpeningsByCategory]"]);
                }
            }["CompaniesProvider.useCallback[getJobOpeningsByCategory]"]);
            return results;
        }
    }["CompaniesProvider.useCallback[getJobOpeningsByCategory]"], [
        companies
    ]);
    const getHiringCompanies = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[getHiringCompanies]": ()=>{
            return companies.filter({
                "CompaniesProvider.useCallback[getHiringCompanies]": (company)=>company.isHiring && company.jobOpenings.length > 0
            }["CompaniesProvider.useCallback[getHiringCompanies]"]);
        }
    }["CompaniesProvider.useCallback[getHiringCompanies]"], [
        companies
    ]);
    const validateLicenceUniqueness = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CompaniesProvider.useCallback[validateLicenceUniqueness]": (licenceNumber, excludeId)=>{
            return !companies.some({
                "CompaniesProvider.useCallback[validateLicenceUniqueness]": (company)=>company.legalInfo.licenceNumber === licenceNumber && company.id !== excludeId
            }["CompaniesProvider.useCallback[validateLicenceUniqueness]"]);
        }
    }["CompaniesProvider.useCallback[validateLicenceUniqueness]"], [
        companies
    ]);
    const value = {
        companies,
        currentCompanyProfile,
        createCompanyProfile,
        updateCompanyProfile,
        addDocument,
        addJobOpening,
        updateJobOpening,
        deleteJobOpening,
        startLiveStream,
        stopLiveStream,
        getCompaniesByLocation,
        getCompaniesByIndustry,
        getJobOpeningsByCategory,
        getHiringCompanies,
        validateLicenceUniqueness
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CompaniesContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/CompaniesContext.tsx",
        lineNumber: 381,
        columnNumber: 5
    }, this);
}
_s(CompaniesProvider, "FKeZTbHh/FN9PCZHvIOZENvC0ZI=");
_c = CompaniesProvider;
function useCompanies() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(CompaniesContext);
    if (context === undefined) {
        throw new Error('useCompanies must be used within a CompaniesProvider');
    }
    return context;
}
_s1(useCompanies, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "CompaniesProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/SocialContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SocialProvider": (()=>SocialProvider),
    "useSocial": (()=>useSocial)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const SocialContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function SocialProvider({ children }) {
    _s();
    const [connections, setConnections] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [profileInteractions, setProfileInteractions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "SocialProvider.useEffect": ()=>{
            // Load social data from localStorage
            const savedConnections = localStorage.getItem('kaazmaamaa-connections');
            const savedInteractions = localStorage.getItem('kaazmaamaa-profile-interactions');
            if (savedConnections) {
                setConnections(JSON.parse(savedConnections));
            }
            if (savedInteractions) {
                setProfileInteractions(JSON.parse(savedInteractions));
            }
        }
    }["SocialProvider.useEffect"], []);
    const saveConnections = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[saveConnections]": (updatedConnections)=>{
            localStorage.setItem('kaazmaamaa-connections', JSON.stringify(updatedConnections));
        }
    }["SocialProvider.useCallback[saveConnections]"], []);
    const saveInteractions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[saveInteractions]": (updatedInteractions)=>{
            localStorage.setItem('kaazmaamaa-profile-interactions', JSON.stringify(updatedInteractions));
        }
    }["SocialProvider.useCallback[saveInteractions]"], []);
    const followUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[followUser]": (userId, userName, userRole)=>{
            const newConnection = {
                userId,
                userName,
                userRole,
                connectedAt: new Date().toISOString(),
                isFollowing: true,
                isFollowedBy: false
            };
            const updatedConnections = [
                ...connections.filter({
                    "SocialProvider.useCallback[followUser]": (c)=>c.userId !== userId
                }["SocialProvider.useCallback[followUser]"]),
                newConnection
            ];
            setConnections(updatedConnections);
            saveConnections(updatedConnections);
        }
    }["SocialProvider.useCallback[followUser]"], [
        connections,
        saveConnections
    ]);
    const unfollowUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[unfollowUser]": (userId)=>{
            const updatedConnections = connections.filter({
                "SocialProvider.useCallback[unfollowUser].updatedConnections": (c)=>c.userId !== userId
            }["SocialProvider.useCallback[unfollowUser].updatedConnections"]);
            setConnections(updatedConnections);
            saveConnections(updatedConnections);
        }
    }["SocialProvider.useCallback[unfollowUser]"], [
        connections,
        saveConnections
    ]);
    const isFollowing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[isFollowing]": (userId)=>{
            return connections.some({
                "SocialProvider.useCallback[isFollowing]": (c)=>c.userId === userId && c.isFollowing
            }["SocialProvider.useCallback[isFollowing]"]);
        }
    }["SocialProvider.useCallback[isFollowing]"], [
        connections
    ]);
    const getFollowers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[getFollowers]": (userId)=>{
            return connections.filter({
                "SocialProvider.useCallback[getFollowers]": (c)=>c.userId === userId && c.isFollowedBy
            }["SocialProvider.useCallback[getFollowers]"]);
        }
    }["SocialProvider.useCallback[getFollowers]"], [
        connections
    ]);
    const getFollowing = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[getFollowing]": (userId)=>{
            return connections.filter({
                "SocialProvider.useCallback[getFollowing]": (c)=>c.isFollowing
            }["SocialProvider.useCallback[getFollowing]"]);
        }
    }["SocialProvider.useCallback[getFollowing]"], [
        connections
    ]);
    const recordProfileView = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[recordProfileView]": (userId)=>{
            setProfileInteractions({
                "SocialProvider.useCallback[recordProfileView]": (prev)=>{
                    const current = prev[userId] || {
                        userId,
                        profileViews: 0,
                        lastViewedAt: new Date().toISOString(),
                        totalLikes: 0,
                        totalComments: 0,
                        totalShares: 0
                    };
                    const updated = {
                        ...prev,
                        [userId]: {
                            ...current,
                            profileViews: current.profileViews + 1,
                            lastViewedAt: new Date().toISOString()
                        }
                    };
                    saveInteractions(updated);
                    return updated;
                }
            }["SocialProvider.useCallback[recordProfileView]"]);
        }
    }["SocialProvider.useCallback[recordProfileView]"], [
        saveInteractions
    ]);
    const getProfileStats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[getProfileStats]": (userId)=>{
            return profileInteractions[userId] || {
                userId,
                profileViews: 0,
                lastViewedAt: new Date().toISOString(),
                totalLikes: 0,
                totalComments: 0,
                totalShares: 0
            };
        }
    }["SocialProvider.useCallback[getProfileStats]"], [
        profileInteractions
    ]);
    const updateProfileInteraction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "SocialProvider.useCallback[updateProfileInteraction]": (userId, type)=>{
            setProfileInteractions({
                "SocialProvider.useCallback[updateProfileInteraction]": (prev)=>{
                    const current = prev[userId] || {
                        userId,
                        profileViews: 0,
                        lastViewedAt: new Date().toISOString(),
                        totalLikes: 0,
                        totalComments: 0,
                        totalShares: 0
                    };
                    const updated = {
                        ...prev,
                        [userId]: {
                            ...current,
                            [`total${type.charAt(0).toUpperCase() + type.slice(1)}s`]: current[`total${type.charAt(0).toUpperCase() + type.slice(1)}s`] + 1
                        }
                    };
                    saveInteractions(updated);
                    return updated;
                }
            }["SocialProvider.useCallback[updateProfileInteraction]"]);
        }
    }["SocialProvider.useCallback[updateProfileInteraction]"], [
        saveInteractions
    ]);
    const value = {
        connections,
        profileInteractions,
        followUser,
        unfollowUser,
        isFollowing,
        getFollowers,
        getFollowing,
        recordProfileView,
        getProfileStats,
        updateProfileInteraction
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(SocialContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/SocialContext.tsx",
        lineNumber: 172,
        columnNumber: 5
    }, this);
}
_s(SocialProvider, "jtSm31hvPMKjJHPw0658O08ozLM=");
_c = SocialProvider;
function useSocial() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SocialContext);
    if (context === undefined) {
        throw new Error('useSocial must be used within a SocialProvider');
    }
    return context;
}
_s1(useSocial, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "SocialProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/PaymentContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "PaymentProvider": (()=>PaymentProvider),
    "SAUDI_PAYMENT_METHODS": (()=>SAUDI_PAYMENT_METHODS),
    "usePayment": (()=>usePayment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const PaymentContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Saudi Arabian Payment Gateway Configuration
const PAYMENT_CONFIG = {
    contact_access: {
        amount: 25,
        currency: 'SAR',
        validityDays: 30
    },
    document_download: {
        amount: 50,
        currency: 'SAR',
        validityDays: 90
    },
    premium_access: {
        amount: 75,
        currency: 'SAR',
        validityDays: 90
    }
};
function PaymentProvider({ children }) {
    _s();
    const [transactions, setTransactions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [permissions, setPermissions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [currentUserId, setCurrentUserId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PaymentProvider.useEffect": ()=>{
            // Load payment data from localStorage (client-side only)
            if ("TURBOPACK compile-time truthy", 1) {
                const savedTransactions = localStorage.getItem('kaazmaamaa-transactions');
                const savedPermissions = localStorage.getItem('kaazmaamaa-permissions');
                const userId = localStorage.getItem('kaazmaamaa-current-user') || 'current-user';
                setCurrentUserId(userId);
                if (savedTransactions) {
                    setTransactions(JSON.parse(savedTransactions));
                }
                if (savedPermissions) {
                    setPermissions(JSON.parse(savedPermissions));
                }
            }
        }
    }["PaymentProvider.useEffect"], []);
    const saveToStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[saveToStorage]": ()=>{
            if ("TURBOPACK compile-time truthy", 1) {
                localStorage.setItem('kaazmaamaa-transactions', JSON.stringify(transactions));
                localStorage.setItem('kaazmaamaa-permissions', JSON.stringify(permissions));
            }
        }
    }["PaymentProvider.useCallback[saveToStorage]"], [
        transactions,
        permissions
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PaymentProvider.useEffect": ()=>{
            saveToStorage();
        }
    }["PaymentProvider.useEffect"], [
        transactions,
        permissions,
        saveToStorage
    ]);
    const initiatePayment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[initiatePayment]": async (profileId, profileType, purpose)=>{
            try {
                if (!profileId || !profileType || !purpose) {
                    throw new Error('Missing required payment parameters');
                }
                const config = PAYMENT_CONFIG[purpose];
                if (!config) {
                    throw new Error(`Invalid payment purpose: ${purpose}`);
                }
                const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const transaction = {
                    id: transactionId,
                    userId: currentUserId,
                    profileId,
                    profileType,
                    amount: config.amount,
                    currency: config.currency,
                    purpose,
                    status: 'pending',
                    paymentMethod: 'mada',
                    createdAt: new Date().toISOString(),
                    expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes expiry
                };
                setTransactions({
                    "PaymentProvider.useCallback[initiatePayment]": (prev)=>[
                            ...prev,
                            transaction
                        ]
                }["PaymentProvider.useCallback[initiatePayment]"]);
                return transactionId;
            } catch (error) {
                console.error('Error initiating payment:', error);
                throw error;
            }
        }
    }["PaymentProvider.useCallback[initiatePayment]"], [
        currentUserId
    ]);
    const processPayment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[processPayment]": async (transactionId, paymentMethod, paymentDetails)=>{
            // Simulate Saudi payment gateway processing
            return new Promise({
                "PaymentProvider.useCallback[processPayment]": (resolve)=>{
                    setTimeout({
                        "PaymentProvider.useCallback[processPayment]": ()=>{
                            const transaction = transactions.find({
                                "PaymentProvider.useCallback[processPayment].transaction": (t)=>t.id === transactionId
                            }["PaymentProvider.useCallback[processPayment].transaction"]);
                            if (!transaction) {
                                resolve(false);
                                return;
                            }
                            // Simulate payment success (90% success rate)
                            const isSuccess = Math.random() > 0.1;
                            if (isSuccess) {
                                // Update transaction status
                                setTransactions({
                                    "PaymentProvider.useCallback[processPayment]": (prev)=>prev.map({
                                            "PaymentProvider.useCallback[processPayment]": (t)=>t.id === transactionId ? {
                                                    ...t,
                                                    status: 'completed',
                                                    paymentMethod: paymentMethod,
                                                    completedAt: new Date().toISOString(),
                                                    transactionId: `saudi_${Date.now()}`
                                                } : t
                                        }["PaymentProvider.useCallback[processPayment]"])
                                }["PaymentProvider.useCallback[processPayment]"]);
                                // Grant access permissions
                                const config = PAYMENT_CONFIG[transaction.purpose];
                                if (!config) {
                                    console.error('Invalid payment purpose in processPayment:', transaction.purpose);
                                    resolve(false);
                                    return;
                                }
                                const expiryDate = new Date();
                                expiryDate.setDate(expiryDate.getDate() + config.validityDays);
                                const permission = {
                                    userId: currentUserId,
                                    profileId: transaction.profileId,
                                    profileType: transaction.profileType,
                                    hasContactAccess: transaction.purpose === 'contact_access' || transaction.purpose === 'premium_access',
                                    hasDocumentAccess: transaction.purpose === 'document_download' || transaction.purpose === 'premium_access',
                                    purchasedAt: new Date().toISOString(),
                                    expiresAt: expiryDate.toISOString(),
                                    transactionId
                                };
                                setPermissions({
                                    "PaymentProvider.useCallback[processPayment]": (prev)=>{
                                        // Remove any existing permission for the same profile
                                        const filtered = prev.filter({
                                            "PaymentProvider.useCallback[processPayment].filtered": (p)=>!(p.profileId === transaction.profileId && p.userId === currentUserId)
                                        }["PaymentProvider.useCallback[processPayment].filtered"]);
                                        return [
                                            ...filtered,
                                            permission
                                        ];
                                    }
                                }["PaymentProvider.useCallback[processPayment]"]);
                                resolve(true);
                            } else {
                                // Update transaction as failed
                                setTransactions({
                                    "PaymentProvider.useCallback[processPayment]": (prev)=>prev.map({
                                            "PaymentProvider.useCallback[processPayment]": (t)=>t.id === transactionId ? {
                                                    ...t,
                                                    status: 'failed'
                                                } : t
                                        }["PaymentProvider.useCallback[processPayment]"])
                                }["PaymentProvider.useCallback[processPayment]"]);
                                resolve(false);
                            }
                        }
                    }["PaymentProvider.useCallback[processPayment]"], 2000) // Simulate 2 second processing time
                    ;
                }
            }["PaymentProvider.useCallback[processPayment]"]);
        }
    }["PaymentProvider.useCallback[processPayment]"], [
        transactions,
        currentUserId
    ]);
    const hasAccess = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[hasAccess]": (profileId, accessType)=>{
            const permission = permissions.find({
                "PaymentProvider.useCallback[hasAccess].permission": (p)=>p.profileId === profileId && p.userId === currentUserId && new Date(p.expiresAt) > new Date()
            }["PaymentProvider.useCallback[hasAccess].permission"]);
            if (!permission) return false;
            return accessType === 'contact' ? permission.hasContactAccess : permission.hasDocumentAccess;
        }
    }["PaymentProvider.useCallback[hasAccess]"], [
        permissions,
        currentUserId
    ]);
    const getTransactionStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[getTransactionStatus]": (transactionId)=>{
            return transactions.find({
                "PaymentProvider.useCallback[getTransactionStatus]": (t)=>t.id === transactionId
            }["PaymentProvider.useCallback[getTransactionStatus]"]) || null;
        }
    }["PaymentProvider.useCallback[getTransactionStatus]"], [
        transactions
    ]);
    const getUserTransactions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[getUserTransactions]": ()=>{
            return transactions.filter({
                "PaymentProvider.useCallback[getUserTransactions]": (t)=>t.userId === currentUserId
            }["PaymentProvider.useCallback[getUserTransactions]"]);
        }
    }["PaymentProvider.useCallback[getUserTransactions]"], [
        transactions,
        currentUserId
    ]);
    const getUserPermissions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PaymentProvider.useCallback[getUserPermissions]": ()=>{
            return permissions.filter({
                "PaymentProvider.useCallback[getUserPermissions]": (p)=>p.userId === currentUserId && new Date(p.expiresAt) > new Date()
            }["PaymentProvider.useCallback[getUserPermissions]"]);
        }
    }["PaymentProvider.useCallback[getUserPermissions]"], [
        permissions,
        currentUserId
    ]);
    const value = {
        transactions,
        permissions,
        initiatePayment,
        processPayment,
        hasAccess,
        getTransactionStatus,
        getUserTransactions,
        getUserPermissions
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PaymentContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/PaymentContext.tsx",
        lineNumber: 251,
        columnNumber: 5
    }, this);
}
_s(PaymentProvider, "HZl1gqnnYIscmD7Ic+JyIuPb/Q0=");
_c = PaymentProvider;
function usePayment() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(PaymentContext);
    if (context === undefined) {
        throw new Error('usePayment must be used within a PaymentProvider');
    }
    return context;
}
_s1(usePayment, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
const SAUDI_PAYMENT_METHODS = [
    {
        id: 'mada',
        name: 'MADA',
        icon: '💳',
        description: 'Saudi national payment system'
    },
    {
        id: 'visa',
        name: 'Visa',
        icon: '💳',
        description: 'Visa credit/debit cards'
    },
    {
        id: 'mastercard',
        name: 'Mastercard',
        icon: '💳',
        description: 'Mastercard credit/debit cards'
    },
    {
        id: 'stc_pay',
        name: 'STC Pay',
        icon: '📱',
        description: 'STC digital wallet'
    },
    {
        id: 'apple_pay',
        name: 'Apple Pay',
        icon: '🍎',
        description: 'Apple Pay digital wallet'
    }
];
var _c;
__turbopack_context__.k.register(_c, "PaymentProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/BartaContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BartaProvider": (()=>BartaProvider),
    "useBarta": (()=>useBarta)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WorkersContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/WorkersContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SuppliersContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/SuppliersContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CompaniesContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/CompaniesContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const BartaContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function BartaProvider({ children }) {
    _s();
    const { user, userRole } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { workers } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WorkersContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWorkers"])();
    const { suppliers } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SuppliersContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSuppliers"])();
    const { companies } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CompaniesContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCompanies"])();
    // State
    const [conversations, setConversations] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [activeConversation, setActiveConversation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isTyping, setIsTyping] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [onlineUsers, setOnlineUsers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Load data from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BartaProvider.useEffect": ()=>{
            try {
                const savedConversations = localStorage.getItem('kaazmaamaa-barta-conversations');
                const savedMessages = localStorage.getItem('kaazmaamaa-barta-messages');
                if (savedConversations) {
                    setConversations(JSON.parse(savedConversations));
                }
                if (savedMessages) {
                    setMessages(JSON.parse(savedMessages));
                }
                console.log('📱 Barta: Loaded conversations and messages from localStorage');
            } catch (error) {
                console.error('📱 Barta: Error loading data from localStorage:', error);
            }
        }
    }["BartaProvider.useEffect"], []);
    // Save conversations to localStorage whenever they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BartaProvider.useEffect": ()=>{
            if (conversations.length > 0) {
                localStorage.setItem('kaazmaamaa-barta-conversations', JSON.stringify(conversations));
            }
        }
    }["BartaProvider.useEffect"], [
        conversations
    ]);
    // Save messages to localStorage whenever they change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BartaProvider.useEffect": ()=>{
            if (Object.keys(messages).length > 0) {
                localStorage.setItem('kaazmaamaa-barta-messages', JSON.stringify(messages));
            }
        }
    }["BartaProvider.useEffect"], [
        messages
    ]);
    // Build online users list from all profile types
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BartaProvider.useEffect": ()=>{
            const allUsers = [];
            // Add workers
            workers.forEach({
                "BartaProvider.useEffect": (worker)=>{
                    if (worker.personalInfo?.workerName && worker.personalInfo?.email) {
                        allUsers.push({
                            id: worker.userId,
                            name: worker.personalInfo.workerName,
                            email: worker.personalInfo.email,
                            role: 'worker',
                            isOnline: Math.random() > 0.5,
                            lastSeen: new Date().toISOString()
                        });
                    }
                }
            }["BartaProvider.useEffect"]);
            // Add suppliers
            suppliers.forEach({
                "BartaProvider.useEffect": (supplier)=>{
                    if (supplier.personalInfo?.supplierName && supplier.personalInfo?.email) {
                        allUsers.push({
                            id: supplier.userId,
                            name: supplier.personalInfo.supplierName,
                            email: supplier.personalInfo.email,
                            role: 'supplier',
                            isOnline: Math.random() > 0.5,
                            lastSeen: new Date().toISOString()
                        });
                    }
                }
            }["BartaProvider.useEffect"]);
            // Add companies
            companies.forEach({
                "BartaProvider.useEffect": (company)=>{
                    if (company.companyInfo?.companyName && company.contactInfo?.hrEmail) {
                        allUsers.push({
                            id: company.userId,
                            name: company.companyInfo.companyName,
                            email: company.contactInfo.hrEmail,
                            role: 'company',
                            isOnline: Math.random() > 0.5,
                            lastSeen: new Date().toISOString()
                        });
                    }
                }
            }["BartaProvider.useEffect"]);
            setOnlineUsers(allUsers);
            console.log('📱 Barta: Updated online users list:', allUsers.length, 'users');
        }
    }["BartaProvider.useEffect"], [
        workers,
        suppliers,
        companies
    ]);
    // Generate unique ID for messages and conversations
    const generateId = ()=>{
        return Date.now().toString() + Math.random().toString(36).substr(2, 9);
    };
    // Get user by ID from all profile types
    const getUserById = (userId)=>{
        return onlineUsers.find((user)=>user.id === userId) || null;
    };
    // Search users across all profile types
    const searchUsers = (query)=>{
        if (!query.trim()) return onlineUsers;
        const lowercaseQuery = query.toLowerCase();
        return onlineUsers.filter((user)=>user.name.toLowerCase().includes(lowercaseQuery) || user.email.toLowerCase().includes(lowercaseQuery) || user.role.toLowerCase().includes(lowercaseQuery));
    };
    // Get existing conversation with a user
    const getConversationWithUser = (userId)=>{
        return conversations.find((conv)=>!conv.isGroup && conv.participants.includes(userId) && conv.participants.includes(user?.id || '')) || null;
    };
    // Create new conversation
    const createConversation = (participantId)=>{
        if (!user?.id) return '';
        // Check if conversation already exists
        const existingConv = getConversationWithUser(participantId);
        if (existingConv) {
            return existingConv.id;
        }
        const participant = getUserById(participantId);
        if (!participant) return '';
        const conversationId = generateId();
        const currentUserDetails = getUserById(user.id);
        const newConversation = {
            id: conversationId,
            participants: [
                user.id,
                participantId
            ],
            participantDetails: [
                currentUserDetails,
                participant
            ].filter(Boolean),
            lastActivity: new Date().toISOString(),
            unreadCount: 0,
            isGroup: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        setConversations((prev)=>[
                newConversation,
                ...prev
            ]);
        console.log('📱 Barta: Created new conversation:', conversationId);
        return conversationId;
    };
    // Send message
    const sendMessage = (conversationId, content, type = 'text', replyTo)=>{
        if (!user?.id || !content.trim()) return;
        const messageId = generateId();
        const currentUserDetails = getUserById(user.id);
        // Get reply message if replying
        let replyToMessage;
        if (replyTo) {
            const conversationMessages = messages[conversationId] || [];
            replyToMessage = conversationMessages.find((msg)=>msg.id === replyTo);
        }
        const newMessage = {
            id: messageId,
            conversationId,
            senderId: user.id,
            senderName: currentUserDetails?.name || user.email || 'Unknown User',
            senderRole: userRole || 'worker',
            content: content.trim(),
            type,
            timestamp: new Date().toISOString(),
            isRead: false,
            isDelivered: true,
            replyTo,
            replyToMessage,
            reactions: {}
        };
        // Add message to messages
        setMessages((prev)=>({
                ...prev,
                [conversationId]: [
                    ...prev[conversationId] || [],
                    newMessage
                ]
            }));
        // Update conversation's last message and activity
        setConversations((prev)=>prev.map((conv)=>{
                if (conv.id === conversationId) {
                    return {
                        ...conv,
                        lastMessage: newMessage,
                        lastActivity: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                }
                return conv;
            }));
        console.log('📱 Barta: Message sent:', messageId);
    };
    // Mark conversation as read
    const markAsRead = (conversationId)=>{
        setMessages((prev)=>({
                ...prev,
                [conversationId]: (prev[conversationId] || []).map((msg)=>({
                        ...msg,
                        isRead: true
                    }))
            }));
        setConversations((prev)=>prev.map((conv)=>conv.id === conversationId ? {
                    ...conv,
                    unreadCount: 0
                } : conv));
    };
    // Set typing status
    const setTypingStatus = (conversationId, isTypingNow)=>{
        if (!user?.id) return;
        setIsTyping((prev)=>{
            const currentTypers = prev[conversationId] || [];
            if (isTypingNow) {
                if (!currentTypers.includes(user.id)) {
                    return {
                        ...prev,
                        [conversationId]: [
                            ...currentTypers,
                            user.id
                        ]
                    };
                }
            } else {
                return {
                    ...prev,
                    [conversationId]: currentTypers.filter((id)=>id !== user.id)
                };
            }
            return prev;
        });
    };
    // Delete message
    const deleteMessage = (messageId, conversationId)=>{
        setMessages((prev)=>({
                ...prev,
                [conversationId]: (prev[conversationId] || []).filter((msg)=>msg.id !== messageId)
            }));
    };
    // Edit message
    const editMessage = (messageId, newContent)=>{
        setMessages((prev)=>{
            const updatedMessages = {
                ...prev
            };
            Object.keys(updatedMessages).forEach((convId)=>{
                updatedMessages[convId] = updatedMessages[convId].map((msg)=>msg.id === messageId ? {
                        ...msg,
                        content: newContent,
                        isEdited: true,
                        editedAt: new Date().toISOString()
                    } : msg);
            });
            return updatedMessages;
        });
    };
    // Add reaction to message
    const addReaction = (messageId, conversationId, emoji)=>{
        if (!user?.id) return;
        setMessages((prev)=>({
                ...prev,
                [conversationId]: (prev[conversationId] || []).map((msg)=>{
                    if (msg.id === messageId) {
                        const reactions = {
                            ...msg.reactions
                        };
                        if (reactions[emoji]) {
                            // Add user to existing reaction
                            if (!reactions[emoji].users.includes(user.id)) {
                                reactions[emoji].users.push(user.id);
                                reactions[emoji].count++;
                            }
                        } else {
                            // Create new reaction
                            reactions[emoji] = {
                                users: [
                                    user.id
                                ],
                                count: 1
                            };
                        }
                        return {
                            ...msg,
                            reactions
                        };
                    }
                    return msg;
                })
            }));
    };
    // Remove reaction from message
    const removeReaction = (messageId, conversationId, emoji)=>{
        if (!user?.id) return;
        setMessages((prev)=>({
                ...prev,
                [conversationId]: (prev[conversationId] || []).map((msg)=>{
                    if (msg.id === messageId) {
                        const reactions = {
                            ...msg.reactions
                        };
                        if (reactions[emoji]) {
                            reactions[emoji].users = reactions[emoji].users.filter((id)=>id !== user.id);
                            reactions[emoji].count = reactions[emoji].users.length;
                            // Remove reaction if no users left
                            if (reactions[emoji].count === 0) {
                                delete reactions[emoji];
                            }
                        }
                        return {
                            ...msg,
                            reactions
                        };
                    }
                    return msg;
                })
            }));
    };
    // Get total unread count
    const getUnreadCount = ()=>{
        return conversations.reduce((total, conv)=>total + conv.unreadCount, 0);
    };
    // Format last seen time
    const formatLastSeen = (timestamp)=>{
        const now = new Date();
        const lastSeen = new Date(timestamp);
        const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60));
        if (diffInMinutes < 1) return 'Just now';
        if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
        if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
        return `${Math.floor(diffInMinutes / 1440)}d ago`;
    };
    const value = {
        // State
        conversations,
        messages,
        activeConversation,
        isTyping,
        onlineUsers,
        // Actions
        sendMessage,
        createConversation,
        setActiveConversation,
        markAsRead,
        setTyping: setTypingStatus,
        searchUsers,
        getConversationWithUser,
        deleteMessage,
        editMessage,
        addReaction,
        removeReaction,
        // Utilities
        getUserById,
        getUnreadCount,
        formatLastSeen
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(BartaContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/BartaContext.tsx",
        lineNumber: 476,
        columnNumber: 5
    }, this);
}
_s(BartaProvider, "O4BoY10wuU+ce09GQokcK/QfgzE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$WorkersContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useWorkers"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$SuppliersContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSuppliers"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$CompaniesContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCompanies"]
    ];
});
_c = BartaProvider;
function useBarta() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(BartaContext);
    if (context === undefined) {
        throw new Error('useBarta must be used within a BartaProvider');
    }
    return context;
}
_s1(useBarta, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "BartaProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
const Toaster = ({ ...props })=>{
    _s();
    const { theme = "system" } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            "--normal-bg": "var(--popover)",
            "--normal-text": "var(--popover-foreground)",
            "--normal-border": "var(--border)"
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_s(Toaster, "EriOrahfenYKDCErPq+L6926Dw4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_34abbff3._.js.map