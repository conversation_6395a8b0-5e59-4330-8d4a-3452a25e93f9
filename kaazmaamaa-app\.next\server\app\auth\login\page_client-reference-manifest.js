globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/auth/login/page"] = {"moduleLoading":{"prefix":"","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/node_modules_next_dist_1a6ee436._.js","/_next/static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"async":false},"[project]/src/contexts/AuthContext.tsx <module evaluation>":{"id":"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/AuthContext.tsx":{"id":"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/PostsContext.tsx <module evaluation>":{"id":"[project]/src/contexts/PostsContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/PostsContext.tsx":{"id":"[project]/src/contexts/PostsContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/OnlineUsersContext.tsx <module evaluation>":{"id":"[project]/src/contexts/OnlineUsersContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/OnlineUsersContext.tsx":{"id":"[project]/src/contexts/OnlineUsersContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/WorkersContext.tsx <module evaluation>":{"id":"[project]/src/contexts/WorkersContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/WorkersContext.tsx":{"id":"[project]/src/contexts/WorkersContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/SuppliersContext.tsx <module evaluation>":{"id":"[project]/src/contexts/SuppliersContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/SuppliersContext.tsx":{"id":"[project]/src/contexts/SuppliersContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/CompaniesContext.tsx <module evaluation>":{"id":"[project]/src/contexts/CompaniesContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/CompaniesContext.tsx":{"id":"[project]/src/contexts/CompaniesContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/SocialContext.tsx <module evaluation>":{"id":"[project]/src/contexts/SocialContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/SocialContext.tsx":{"id":"[project]/src/contexts/SocialContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/PaymentContext.tsx <module evaluation>":{"id":"[project]/src/contexts/PaymentContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/PaymentContext.tsx":{"id":"[project]/src/contexts/PaymentContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/BartaContext.tsx <module evaluation>":{"id":"[project]/src/contexts/BartaContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/contexts/BartaContext.tsx":{"id":"[project]/src/contexts/BartaContext.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/ui/sonner.tsx <module evaluation>":{"id":"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/components/ui/sonner.tsx":{"id":"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js"],"async":false},"[project]/src/app/auth/login/page.tsx <module evaluation>":{"id":"[project]/src/app/auth/login/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/node_modules_b3c7a983._.js","/_next/static/chunks/src_e110ac48._.js","/_next/static/chunks/src_app_auth_login_page_tsx_cdf1c39a._.js"],"async":false},"[project]/src/app/auth/login/page.tsx":{"id":"[project]/src/app/auth/login/page.tsx [app-client] (ecmascript)","name":"*","chunks":["/_next/static/chunks/src_34abbff3._.js","/_next/static/chunks/node_modules_7edd3172._.js","/_next/static/chunks/src_app_layout_tsx_c0237562._.js","/_next/static/chunks/node_modules_b3c7a983._.js","/_next/static/chunks/src_e110ac48._.js","/_next/static/chunks/src_app_auth_login_page_tsx_cdf1c39a._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_84bfc1e5._.js","server/chunks/ssr/[root-of-the-server]__0a46983d._.js"],"async":false}},"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/PostsContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/PostsContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/OnlineUsersContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/OnlineUsersContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/WorkersContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/WorkersContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/SuppliersContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/SuppliersContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/CompaniesContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/CompaniesContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/SocialContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/SocialContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/PaymentContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/PaymentContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/contexts/BartaContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/BartaContext.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/sonner.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js"],"async":false}},"[project]/src/app/auth/login/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/auth/login/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root-of-the-server]__d6122445._.js","server/chunks/ssr/node_modules_93f71f50._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_ce32aaff._.js","server/chunks/ssr/node_modules_next_9218fe4e._.js","server/chunks/ssr/node_modules_tailwind-merge_dist_bundle-mjs_mjs_6c53872a._.js","server/chunks/ssr/node_modules_c2942141._.js","server/chunks/ssr/[root-of-the-server]__893b6214._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/AuthContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/PostsContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/PostsContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/OnlineUsersContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/OnlineUsersContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/WorkersContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/WorkersContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/SuppliersContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/SuppliersContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/CompaniesContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/CompaniesContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/SocialContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/SocialContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/PaymentContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/PaymentContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/contexts/BartaContext.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/contexts/BartaContext.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/ui/sonner.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}},"[project]/src/app/auth/login/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/auth/login/page.tsx (client reference/proxy)","name":"*","chunks":["server/app/auth/login/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}],"[project]/src/app/auth/login/page":[{"path":"static/chunks/[root-of-the-server]__8ebb6d4b._.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_8a7a8fdc._.js"],"[project]/src/app/layout":["static/chunks/src_34abbff3._.js","static/chunks/node_modules_7edd3172._.js","static/chunks/src_app_layout_tsx_c0237562._.js"],"[project]/src/app/auth/login/page":["static/chunks/src_34abbff3._.js","static/chunks/node_modules_7edd3172._.js","static/chunks/src_app_layout_tsx_c0237562._.js","static/chunks/node_modules_b3c7a983._.js","static/chunks/src_e110ac48._.js","static/chunks/src_app_auth_login_page_tsx_cdf1c39a._.js"]}}
