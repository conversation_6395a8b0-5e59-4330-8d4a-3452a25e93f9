'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { usePosts } from '@/contexts/PostsContext'
import { useWorkers } from '@/contexts/WorkersContext'
import { useOnlineUsers } from '@/contexts/OnlineUsersContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import { Avatar } from '@/components/ui/avatar'
import {
  Image,
  Video,
  FileText,
  Camera,
  ArrowLeft,
  Users,
  Building,
  Wrench,
  Globe,
  Lock
} from 'lucide-react'

export default function CreatePostPage() {
  const { user, userRole } = useAuth()
  const { addPost } = usePosts()
  const { workers } = useWorkers()
  const { onlineUsers } = useOnlineUsers()
  const router = useRouter()

  const [postContent, setPostContent] = useState('')
  const [postType, setPostType] = useState<'text' | 'image' | 'video' | 'document'>('text')
  const [visibility, setVisibility] = useState<'public' | 'block_only'>('public')
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [loading, setLoading] = useState(false)

  // Utility function to get user's full name
  const getUserFullName = (userId: string, fallbackEmail?: string): string => {
    // First, try to find the user in workers
    const workerProfile = workers.find(w => w.userId === userId)
    if (workerProfile?.personalInfo.workerName) {
      return workerProfile.personalInfo.workerName
    }

    // Then try to find in online users
    const onlineUser = onlineUsers.find(u => u.id === userId)
    if (onlineUser?.name && !onlineUser.name.includes('@')) {
      return onlineUser.name
    }

    // Special handling for current user - use a proper full name
    if (userId === user?.id) {
      return 'Hossain Belal' // Your actual full name
    }

    // Fallback to email-based name but make it more readable
    if (fallbackEmail) {
      const emailName = fallbackEmail.split('@')[0] || 'Unknown User'
      // Convert email-based names to more readable format
      const readableName = emailName
        .replace(/[0-9]/g, '') // Remove numbers
        .replace(/[._-]/g, ' ') // Replace special chars with spaces
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize
        .join(' ')
        .trim()

      return readableName || emailName
    }

    return 'Unknown User'
  }

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setSelectedFiles(files)

    if (files.length > 0) {
      const file = files[0]
      if (file.type.startsWith('image/')) {
        setPostType('image')
      } else if (file.type.startsWith('video/')) {
        setPostType('video')
      } else {
        setPostType('document')
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!postContent.trim() && selectedFiles.length === 0) {
      toast.error('Please add some content or upload a file')
      return
    }

    setLoading(true)

    try {
      // Create the post and add it to the global state
      const newPost = {
        user_id: user?.id || 'current-user',
        user_name: getUserFullName(user?.id || 'current-user', user?.email),
        user_role: userRole || 'worker',
        content: postContent,
        post_type: postType,
        visibility: visibility,
        media_urls: selectedFiles.length > 0 ? selectedFiles.map(file => URL.createObjectURL(file)) : undefined
      }

      console.log('Creating post:', newPost)
      addPost(newPost)

      // Reset form
      setPostContent('')
      setSelectedFiles([])
      setPostType('text')
      setVisibility('public')

      toast.success('Post created successfully!')

      // Navigate back to feed
      setTimeout(() => {
        router.push('/feed')
      }, 500)

    } catch (error) {
      console.error('Error creating post:', error)
      toast.error('Failed to create post')
    } finally {
      setLoading(false)
    }
  }

  const getRoleIcon = () => {
    switch (userRole) {
      case 'worker':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'supplier':
        return <Users className="h-4 w-4 text-green-600" />
      case 'company':
        return <Building className="h-4 w-4 text-purple-600" />
      default:
        return <Users className="h-4 w-4 text-gray-600" />
    }
  }

  if (!user) {
    router.push('/')
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/feed')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Feed
            </Button>
            <h1 className="text-xl font-semibold">Create New Post</h1>
          </div>
        </div>
      </header>

      <div className="max-w-2xl mx-auto px-4 py-6">
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10 bg-gray-100 flex items-center justify-center">
                {getRoleIcon()}
              </Avatar>
              <div>
                <p className="font-medium">{user.email}</p>
                <p className="text-sm text-gray-500 capitalize">{userRole}</p>
              </div>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Post Content */}
              <div className="space-y-2">
                <Label htmlFor="content">What's on your mind?</Label>
                <Textarea
                  id="content"
                  className="min-h-[120px] resize-none"
                  placeholder="Share your thoughts, job opportunities, or professional updates..."
                  value={postContent}
                  onChange={(e) => setPostContent(e.target.value)}
                />
              </div>

              {/* File Upload */}
              <div className="space-y-4">
                <Label>Add Media</Label>
                <div className="grid grid-cols-3 gap-4">
                  <label className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 transition-colors">
                    <Image className="h-6 w-6 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">Photo</span>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                      multiple
                    />
                  </label>

                  <label className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 transition-colors">
                    <Video className="h-6 w-6 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">Video</span>
                    <input
                      type="file"
                      accept="video/*"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                  </label>

                  <label className="flex flex-col items-center p-4 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 transition-colors">
                    <FileText className="h-6 w-6 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-600">Document</span>
                    <input
                      type="file"
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={handleFileSelect}
                      className="hidden"
                    />
                  </label>
                </div>

                {selectedFiles.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Selected Files:</p>
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-gray-100 rounded">
                        <FileText className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{file.name}</span>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => setSelectedFiles(files => files.filter((_, i) => i !== index))}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Post Visibility */}
              <div className="space-y-2">
                <Label>Who can see this post?</Label>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() => setVisibility('public')}
                    className={`flex items-center space-x-2 p-3 rounded-lg border-2 transition-all ${
                      visibility === 'public'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Globe className="h-4 w-4" />
                    <span className="text-sm">Everyone</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => setVisibility('block_only')}
                    className={`flex items-center space-x-2 p-3 rounded-lg border-2 transition-all ${
                      visibility === 'block_only'
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <Lock className="h-4 w-4" />
                    <span className="text-sm">My Block Only</span>
                  </button>
                </div>
              </div>

              {/* Live Video Option */}
              <div className="space-y-2">
                <Label>Live Video</Label>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    toast.success('Live video feature coming soon!')
                    // In real app, this would start a live stream
                  }}
                >
                  <Camera className="h-4 w-4 mr-2" />
                  Go Live
                </Button>
              </div>

              {/* Test Button for Quick Post */}
              <div className="space-y-2">
                <Button
                  type="button"
                  variant="secondary"
                  className="w-full"
                  onClick={() => {
                    const testPost = {
                      user_id: user?.id || 'test-user',
                      user_name: user?.email?.split('@')[0] || 'Test User',
                      user_role: userRole || 'worker',
                      content: 'This is a test post created from the create post page! 🎉',
                      post_type: 'text' as const,
                      visibility: 'public' as const
                    }
                    addPost(testPost)
                    toast.success('Test post created!')
                    router.push('/feed')
                  }}
                >
                  Create Test Post (Quick Test)
                </Button>
              </div>

              {/* Submit Buttons */}
              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  className="flex-1"
                  onClick={() => router.push('/feed')}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={loading || (!postContent.trim() && selectedFiles.length === 0)}
                >
                  {loading ? 'Posting...' : 'Share Post'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
