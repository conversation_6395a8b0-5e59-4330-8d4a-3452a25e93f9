'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import PaymentGateway from '@/components/PaymentGateway'
import { usePayment } from '@/contexts/PaymentContext'
import { 
  Download, 
  FileText, 
  Lock, 
  CreditCard, 
  CheckCircle,
  AlertCircle,
  User,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Award,
  Briefcase
} from 'lucide-react'
import { toast } from 'sonner'
import type { WorkerProfile } from '@/contexts/WorkersContext'

interface WorkerDocumentDownloadProps {
  worker: WorkerProfile
  currentUserRole: 'worker' | 'supplier' | 'company' | 'admin'
  currentUserId: string
}

export default function WorkerDocumentDownload({ 
  worker, 
  currentUserRole, 
  currentUserId 
}: WorkerDocumentDownloadProps) {
  const [showPaymentGateway, setShowPaymentGateway] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const { hasAccess } = usePayment()

  // Check if current user has already paid for access
  const hasDocumentAccess = hasAccess(worker.id, 'documents')

  // Generate auto CV content
  const generateCV = () => {
    const cvContent = `
CURRICULUM VITAE

PERSONAL INFORMATION
Name: ${worker.personalInfo.workerName}
Nationality: ${worker.personalInfo.nationality}
Age: ${worker.personalInfo.age || 'Not specified'}
Phone: ${worker.personalInfo.phone}
Email: ${worker.personalInfo.email}
WhatsApp: ${worker.personalInfo.whatsapp}
Address: ${worker.personalInfo.address}

IDENTIFICATION
IQAMA Number: ${worker.identificationInfo.iqamaNumber}
Passport Number: ${worker.identificationInfo.passportNumber}
${worker.identificationInfo.crNumber ? `C.R. Number: ${worker.identificationInfo.crNumber}` : ''}

PROFESSIONAL INFORMATION
Category: ${worker.professionalInfo.workerCategory}
Title: ${worker.professionalInfo.title}
Specialty: ${worker.professionalInfo.specialty}
Experience: ${worker.professionalInfo.experience} years
Expected Salary: ${worker.professionalInfo.expectedSalaryPerHour} ${worker.professionalInfo.currency} per hour
Availability: ${worker.professionalInfo.availability}

WORK PREFERENCES
Work Type: ${worker.preferences.workType}
Willing to Relocate: ${worker.preferences.willingToRelocate ? 'Yes' : 'No'}
Remote Work: ${worker.preferences.remoteWork ? 'Yes' : 'No'}
${worker.preferences.otherRequirements ? `Other Requirements: ${worker.preferences.otherRequirements}` : ''}

DOCUMENTS AVAILABLE
${worker.documents.map(doc => `• ${doc.name} (${doc.type})`).join('\n')}

Generated on: ${new Date().toLocaleDateString()}
Generated by: KAAZMAAMAA Platform
    `
    return cvContent
  }

  const downloadCV = () => {
    const cvContent = generateCV()
    const blob = new Blob([cvContent], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${worker.personalInfo.workerName.replace(/\s+/g, '_')}_CV.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('CV downloaded successfully! 📄')
  }

  const downloadDocument = (doc: any) => {
    if (!hasDocumentAccess) {
      toast.error('Payment required to download documents')
      return
    }

    // In a real app, this would download from cloud storage
    const link = document.createElement('a')
    link.href = doc.url
    link.download = doc.fileName || doc.name
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    toast.success(`${doc.name} downloaded successfully! 📄`)
  }

  const downloadAllDocuments = async () => {
    if (!hasDocumentAccess) {
      toast.error('Payment required to download documents')
      return
    }

    setIsDownloading(true)
    
    try {
      // Download all documents
      for (const doc of worker.documents) {
        await new Promise(resolve => setTimeout(resolve, 500)) // Simulate download delay
        downloadDocument(doc)
      }
      
      // Download auto-generated CV
      downloadCV()
      
      toast.success('All documents and CV downloaded successfully! 🎉')
    } catch (error) {
      toast.error('Failed to download some documents')
    } finally {
      setIsDownloading(false)
    }
  }

  const handlePaymentSuccess = () => {
    setShowPaymentGateway(false)
    toast.success('Payment successful! You can now download all documents. 🎉')
    // Access status will be automatically updated by the payment context
  }

  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'absher_iqama':
      case 'passport':
      case 'visa':
        return CreditCard
      case 'medical':
      case 'fitness':
        return Award
      case 'insurance':
      case 'gosi':
        return CheckCircle
      default:
        return FileText
    }
  }

  const getDocumentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'absher_iqama': 'Absher IQAMA Copy',
      'muqeem': 'Muqeem Copy',
      'medical': 'Medical Certificate',
      'insurance': 'Insurance Document',
      'chamber': 'Chamber Certificate',
      'ajeer': 'Ajeer Work Permit',
      'experience': 'Experience Certificates',
      'fitness': 'Fitness Certificate',
      'passport': 'Passport Copy',
      'visa': 'Visa Copy',
      'gosi': 'GOSI Document'
    }
    return labels[type] || type
  }

  if (showPaymentGateway) {
    return (
      <PaymentGateway
        profileId={worker.id}
        profileType="worker"
        profileName={worker.personalInfo.workerName}
        accessType="documents"
        onSuccess={handlePaymentSuccess}
        onCancel={() => setShowPaymentGateway(false)}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Worker Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5 text-blue-600" />
            <span>{worker.personalInfo.workerName}</span>
            {worker.isVerified && (
              <Badge className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Briefcase className="h-4 w-4 text-gray-500" />
                <span>{worker.professionalInfo.title}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Award className="h-4 w-4 text-gray-500" />
                <span>{worker.professionalInfo.experience} years experience</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span>{worker.personalInfo.nationality}</span>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Phone className="h-4 w-4 text-gray-500" />
                <span>{hasDocumentAccess ? worker.personalInfo.phone : '***-***-****'}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Mail className="h-4 w-4 text-gray-500" />
                <span>{hasDocumentAccess ? worker.personalInfo.email : '****@****.com'}</span>
              </div>
              <div className="flex items-center space-x-2 text-sm">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span>Available: {worker.professionalInfo.availability}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documents Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-600" />
              <span>Documents & CV</span>
              <Badge variant="outline">
                {worker.documents.length} documents
              </Badge>
            </div>
            
            {hasDocumentAccess ? (
              <Button
                onClick={downloadAllDocuments}
                disabled={isDownloading}
                className="bg-green-600 hover:bg-green-700"
              >
                <Download className="h-4 w-4 mr-2" />
                {isDownloading ? 'Downloading...' : 'Download All + CV'}
              </Button>
            ) : (
              <Button
                onClick={() => setShowPaymentGateway(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Pay to Access Documents
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!hasDocumentAccess && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center space-x-2">
                <Lock className="h-5 w-5 text-yellow-600" />
                <div>
                  <h3 className="font-semibold text-yellow-800">Payment Required</h3>
                  <p className="text-sm text-yellow-700">
                    Complete payment to access all documents and auto-generated CV for this worker.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {worker.documents.map((doc, index) => {
              const IconComponent = getDocumentIcon(doc.type)
              return (
                <div
                  key={doc.id}
                  className={`border rounded-lg p-4 ${hasDocumentAccess ? 'hover:bg-gray-50 cursor-pointer' : 'opacity-60'}`}
                  onClick={() => hasDocumentAccess && downloadDocument(doc)}
                >
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-8 w-8 text-blue-600" />
                    <div className="flex-1">
                      <h3 className="font-medium text-sm">{getDocumentLabel(doc.type)}</h3>
                      <p className="text-xs text-gray-500">
                        {hasDocumentAccess ? doc.fileName || doc.name : 'Locked'}
                      </p>
                      <p className="text-xs text-gray-400">
                        Uploaded: {new Date(doc.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                    {hasDocumentAccess ? (
                      <Download className="h-4 w-4 text-green-600" />
                    ) : (
                      <Lock className="h-4 w-4 text-gray-400" />
                    )}
                  </div>
                </div>
              )
            })}

            {/* Auto-generated CV */}
            <div
              className={`border-2 border-dashed border-blue-300 rounded-lg p-4 ${hasDocumentAccess ? 'hover:bg-blue-50 cursor-pointer' : 'opacity-60'}`}
              onClick={() => hasDocumentAccess && downloadCV()}
            >
              <div className="flex items-center space-x-3">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="flex-1">
                  <h3 className="font-medium text-sm">Auto-Generated CV</h3>
                  <p className="text-xs text-gray-500">
                    {hasDocumentAccess ? 'Professional CV' : 'Locked'}
                  </p>
                  <p className="text-xs text-blue-600">
                    Generated automatically
                  </p>
                </div>
                {hasDocumentAccess ? (
                  <Download className="h-4 w-4 text-green-600" />
                ) : (
                  <Lock className="h-4 w-4 text-gray-400" />
                )}
              </div>
            </div>
          </div>

          {hasDocumentAccess && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <h3 className="font-semibold text-green-800">Access Granted</h3>
                  <p className="text-sm text-green-700">
                    You have full access to all documents and can download them anytime.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
