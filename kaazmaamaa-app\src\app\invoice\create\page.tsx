'use client'

import { useState, useEffect, useCallback } from 'react'

// SUPER SIMPLE LOCK PERSISTENCE - NO COMPLEX SYSTEMS
const SIMPLE_LOCK_KEY = 'SIMPLE_INVOICE_LOCKS'

// Simple save function
const saveLocks = (lockState: any) => {
  try {
    const data = JSON.stringify(lockState)
    localStorage.setItem(SIMPLE_LOCK_KEY, data)
    localStorage.setItem(SIMPLE_LOCK_KEY + '_backup', data)
    console.log('💾 SIMPLE SAVE:', lockState)
    return true
  } catch (e) {
    console.error('❌ Save failed:', e)
    return false
  }
}

// Simple load function
const loadLocks = () => {
  try {
    let data = localStorage.getItem(SIMPLE_LOCK_KEY)
    if (!data) {
      data = localStorage.getItem(SIMPLE_LOCK_KEY + '_backup')
    }
    if (data) {
      const parsed = JSON.parse(data)
      console.log('📋 SIMPLE LOAD:', parsed)
      return parsed
    }
  } catch (e) {
    console.error('❌ Load failed:', e)
  }
  return {
    invoiceDetails: false,
    sellerDetails: false,
    clientDetails: false,
    bankDetails: false,
    items: false,
    notes: false
  }
}


import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, FileText, Download, Printer, QrCode, Plus, Upload, Trash2, History, Edit, Copy, Search, Calendar, DollarSign, Eye, MoreVertical, Lock, Unlock } from 'lucide-react'
import { toast } from 'sonner'
import { generateZATCAQRCode, generateZATCATimestamp, formatCurrencyForZATCA, type ZATCAQRData } from '@/utils/zatcaQR'
import { downloadInvoicePDF, printInvoice, type InvoiceData } from '@/utils/pdfGenerator'

interface InvoiceItem {
  id: string
  description: string
  quantity: string
  rate: string
  vatPercent: number
  amount: number
}

export default function CreateInvoicePage() {
  const router = useRouter()

  // Basic form state
  const [invoiceNumber, setInvoiceNumber] = useState('INV-2025-001')
  const [invoiceDate, setInvoiceDate] = useState(new Date().toISOString().split('T')[0])
  const [currency, setCurrency] = useState('SAR')
  const [discount, setDiscount] = useState('0')
  const [deductionAmount, setDeductionAmount] = useState('0')
  const [deductionReason, setDeductionReason] = useState('')

  // Seller Details
  const [sellerName, setSellerName] = useState('')
  const [sellerAddress, setSellerAddress] = useState('')
  const [sellerVAT, setSellerVAT] = useState('')
  const [sellerLogo, setSellerLogo] = useState<string | null>(null)
  const [sellerLogoFile, setSellerLogoFile] = useState<File | null>(null)

  // Client Details
  const [clientName, setClientName] = useState('')
  const [clientAddress, setClientAddress] = useState('')
  const [clientVAT, setClientVAT] = useState('')
  const [clientLogo, setClientLogo] = useState<string | null>(null)
  const [clientLogoFile, setClientLogoFile] = useState<File | null>(null)

  // Items
  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: '1',
      description: '',
      quantity: '1',
      rate: '0',
      vatPercent: 15,
      amount: 0
    }
  ])

  // Additional Details
  const [notes, setNotes] = useState('')
  const [terms, setTerms] = useState('')
  const [bankName, setBankName] = useState('')
  const [accountName, setAccountName] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [iban, setIban] = useState('')
  const [dueDate, setDueDate] = useState('')



  // History navbar state
  const [showHistoryNavbar, setShowHistoryNavbar] = useState(false)
  const [historyInvoices, setHistoryInvoices] = useState<any[]>([])
  const [historySearchTerm, setHistorySearchTerm] = useState('')
  const [historyFilterStatus, setHistoryFilterStatus] = useState('all')
  const [currentEditingInvoice, setCurrentEditingInvoice] = useState<string | null>(null)











  // Default lock state
  const defaultLockState = {
    invoiceDetails: false,
    sellerDetails: false,
    clientDetails: false,
    bankDetails: false,
    items: false,
    notes: false
  }

  // AUTO-SUGGESTION STATE
  const [suggestions, setSuggestions] = useState<{[key: string]: string[]}>({})
  const [showSuggestions, setShowSuggestions] = useState<{[key: string]: boolean}>({})
  const [activeSuggestionIndex, setActiveSuggestionIndex] = useState<{[key: string]: number}>({})

  // Load suggestions from localStorage
  const loadSuggestions = () => {
    try {
      const savedSuggestions = localStorage.getItem('invoice-suggestions')
      if (savedSuggestions) {
        const parsed = JSON.parse(savedSuggestions)
        setSuggestions(parsed)
        console.log('📋 Loaded suggestions:', parsed)
      }
    } catch (error) {
      console.error('Error loading suggestions:', error)
    }
  }

  // Save suggestions to localStorage
  const saveSuggestions = (newSuggestions: {[key: string]: string[]}) => {
    try {
      localStorage.setItem('invoice-suggestions', JSON.stringify(newSuggestions))
      console.log('💾 Saved suggestions:', newSuggestions)
    } catch (error) {
      console.error('Error saving suggestions:', error)
    }
  }

  // Add value to suggestions
  const addToSuggestions = (field: string, value: string) => {
    if (!value || value.trim().length < 2) return

    const trimmedValue = value.trim()
    const currentSuggestions = suggestions[field] || []

    // Don't add if already exists
    if (currentSuggestions.includes(trimmedValue)) return

    // Add to beginning and limit to 10 suggestions
    const newFieldSuggestions = [trimmedValue, ...currentSuggestions].slice(0, 10)
    const newSuggestions = { ...suggestions, [field]: newFieldSuggestions }

    setSuggestions(newSuggestions)
    saveSuggestions(newSuggestions)
  }

  // Filter suggestions based on input
  const getFilteredSuggestions = (field: string, value: string): string[] => {
    if (!value || value.length < 1) return []

    const fieldSuggestions = suggestions[field] || []
    return fieldSuggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(value.toLowerCase()) &&
      suggestion.toLowerCase() !== value.toLowerCase()
    ).slice(0, 5)
  }

  // Handle suggestion selection
  const selectSuggestion = (field: string, suggestion: string) => {
    // Set the field value based on field name
    switch (field) {
      case 'sellerName': setSellerName(suggestion); break
      case 'sellerAddress': setSellerAddress(suggestion); break
      case 'sellerVAT': setSellerVAT(suggestion); break
      case 'clientName': setClientName(suggestion); break
      case 'clientAddress': setClientAddress(suggestion); break
      case 'clientVAT': setClientVAT(suggestion); break
      case 'bankName': setBankName(suggestion); break
      case 'accountName': setAccountName(suggestion); break
      case 'accountNumber': setAccountNumber(suggestion); break
      case 'iban': setIban(suggestion); break
      case 'notes': setNotes(suggestion); break
      case 'terms': setTerms(suggestion); break
      case 'deductionReason': setDeductionReason(suggestion); break
    }

    // Hide suggestions
    setShowSuggestions({ ...showSuggestions, [field]: false })
    setActiveSuggestionIndex({ ...activeSuggestionIndex, [field]: -1 })
  }





  // LOCK STATE - USES GLOBAL WINDOW SYSTEM
  const [lockedSections, setLockedSections] = useState(defaultLockState)
  const [isHydrated, setIsHydrated] = useState(false)

  // LOAD LOCKS AND SUGGESTIONS ON MOUNT
  useEffect(() => {
    console.log('🔄 LOADING LOCKS AND SUGGESTIONS...')

    const loadSavedLocks = () => {
      try {
        const savedLocks = loadLocks()
        console.log('📋 LOADED LOCKS:', savedLocks)

        setLockedSections(savedLocks)
        setIsHydrated(true)

        // Apply to DOM
        setTimeout(() => {
          Object.entries(savedLocks).forEach(([section, isLocked]) => {
            const sectionContainer = document.querySelector(`[data-section="${section}"]`)
            if (sectionContainer) {
              const formElements = sectionContainer.querySelectorAll('input, textarea, button:not([data-lock-button="true"])')
              formElements.forEach(element => {
                const el = element as HTMLInputElement
                if (isLocked) {
                  el.disabled = true
                  el.classList.add('opacity-50', 'cursor-not-allowed')
                } else {
                  el.disabled = false
                  el.classList.remove('opacity-50', 'cursor-not-allowed')
                }
              })
            }
          })
        }, 500)

        const lockedCount = Object.values(savedLocks).filter(Boolean).length
        if (lockedCount > 0) {
          toast.success(`🔒 ${lockedCount} sections restored!`, { duration: 2000 })
        }

      } catch (error) {
        console.error('❌ Load error:', error)
        setLockedSections(defaultLockState)
        setIsHydrated(true)
      }
    }

    // Load suggestions
    loadSuggestions()

    setTimeout(loadSavedLocks, 300)
  }, [])

  // SUPER SIMPLE TOGGLE FUNCTION
  const toggleSectionLock = (section: keyof typeof defaultLockState) => {
    try {
      console.log(`🔄 SIMPLE TOGGLE: ${section}`)

      const newValue = !lockedSections[section]
      const newState = { ...lockedSections, [section]: newValue }

      // Save immediately
      saveLocks(newState)

      // Update React state
      setLockedSections(newState)

      // Apply to DOM
      setTimeout(() => {
        const sectionContainer = document.querySelector(`[data-section="${section}"]`)
        if (sectionContainer) {
          const formElements = sectionContainer.querySelectorAll('input, textarea, button:not([data-lock-button="true"])')
          formElements.forEach(element => {
            const el = element as HTMLInputElement
            if (newValue) {
              el.disabled = true
              el.classList.add('opacity-50', 'cursor-not-allowed')
            } else {
              el.disabled = false
              el.classList.remove('opacity-50', 'cursor-not-allowed')
            }
          })
        }
      }, 50)

      const sectionName = section.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
      toast.success(`${newValue ? '🔒' : '🔓'} ${sectionName} ${newValue ? 'locked' : 'unlocked'}!`, { duration: 1500 })

      console.log(`✅ SIMPLE TOGGLE COMPLETE: ${section} = ${newValue}`)

    } catch (error) {
      console.error('❌ SIMPLE TOGGLE ERROR:', error)
      toast.error('Failed to toggle lock')
    }
  }

  // WORKING INPUT COMPONENT WITH SMART SUGGESTIONS
  const SuggestionInput = ({
    value,
    onChange,
    field,
    placeholder,
    label,
    disabled = false,
    type = "text",
    rows = undefined
  }: {
    value: string
    onChange: (value: string) => void
    field: string
    placeholder: string
    label: string
    disabled?: boolean
    type?: string
    rows?: number
  }) => {
    const [localShowSuggestions, setLocalShowSuggestions] = useState(false)
    const [localActiveSuggestion, setLocalActiveSuggestion] = useState(-1)
    const isTextarea = rows !== undefined

    // Get filtered suggestions
    const filtered = getFilteredSuggestions(field, value)

    // Handle input change
    const handleInputChange = (newValue: string) => {
      onChange(newValue) // Update parent state immediately

      // Show suggestions if we have matches
      const hasMatches = getFilteredSuggestions(field, newValue).length > 0
      setLocalShowSuggestions(hasMatches && newValue.length > 0)
      setLocalActiveSuggestion(-1)
    }

    // Handle suggestion selection
    const handleSelectSuggestion = (suggestion: string) => {
      onChange(suggestion)
      setLocalShowSuggestions(false)
      setLocalActiveSuggestion(-1)
      addToSuggestions(field, suggestion)
    }

    // Handle blur
    const handleBlur = () => {
      setTimeout(() => {
        addToSuggestions(field, value)
        setLocalShowSuggestions(false)
      }, 200)
    }

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (!localShowSuggestions || filtered.length === 0) return

      if (e.key === 'ArrowDown') {
        e.preventDefault()
        setLocalActiveSuggestion(prev =>
          prev < filtered.length - 1 ? prev + 1 : 0
        )
      } else if (e.key === 'ArrowUp') {
        e.preventDefault()
        setLocalActiveSuggestion(prev =>
          prev > 0 ? prev - 1 : filtered.length - 1
        )
      } else if (e.key === 'Enter' && localActiveSuggestion >= 0) {
        e.preventDefault()
        handleSelectSuggestion(filtered[localActiveSuggestion])
      } else if (e.key === 'Escape') {
        setLocalShowSuggestions(false)
        setLocalActiveSuggestion(-1)
      }
    }

    return (
      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
        {isTextarea ? (
          <Textarea
            value={value}
            onChange={(e) => handleInputChange(e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            rows={rows}
            disabled={disabled}
            className="w-full"
          />
        ) : (
          <Input
            type={type}
            value={value}
            onChange={(e) => handleInputChange(e.target.value)}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full"
          />
        )}

        {/* Suggestions Dropdown */}
        {localShowSuggestions && filtered.length > 0 && !disabled && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
            {filtered.map((suggestion, index) => (
              <div
                key={index}
                className={`px-3 py-2 cursor-pointer text-sm ${
                  index === localActiveSuggestion
                    ? 'bg-blue-100 text-blue-900'
                    : 'hover:bg-gray-100'
                }`}
                onMouseDown={() => handleSelectSuggestion(suggestion)}
                onMouseEnter={() => setLocalActiveSuggestion(index)}
              >
                <div className="flex items-center">
                  <span className="text-blue-500 mr-2">💡</span>
                  {suggestion}
                </div>
              </div>
            ))}
            <div className="px-3 py-1 text-xs text-gray-500 border-t bg-gray-50">
              Use ↑↓ to navigate, Enter to select, Esc to close
            </div>
          </div>
        )}
      </div>
    )
  }

  // SIMPLE DEBUG FUNCTION
  const debugLockState = () => {
    try {
      console.log('=== 🔍 SIMPLE DEBUG ===')
      console.log('React State:', lockedSections)

      const stored = localStorage.getItem(SIMPLE_LOCK_KEY)
      console.log('localStorage:', stored ? JSON.parse(stored) : null)

      const backup = localStorage.getItem(SIMPLE_LOCK_KEY + '_backup')
      console.log('localStorage backup:', backup ? JSON.parse(backup) : null)

      console.log('Suggestions:', suggestions)
      console.log('=======================')

      const reactCount = Object.values(lockedSections).filter(Boolean).length
      const suggestionCount = Object.keys(suggestions).length
      toast.info(`🔍 ${reactCount} sections locked | ${suggestionCount} suggestion fields`, { duration: 3000 })

    } catch (error) {
      console.error('❌ Debug error:', error)
      toast.error('Debug failed')
    }
  }

  // SIMPLE DIRECT LOCK CONTROL
  const directLockControl = {
    getCurrentState: () => lockedSections
  }

  // Load invoice history on component mount
  useEffect(() => {
    loadInvoiceHistory()
  }, [])

  // Load invoice history for navbar
  const loadInvoiceHistory = () => {
    try {
      if (typeof window === 'undefined') return
      const invoices = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              const invoice = JSON.parse(data)
              if (invoice.invoiceNumber && invoice.timestamp) {
                invoices.push({
                  key,
                  id: key,
                  ...invoice,
                  status: invoice.status || 'draft'
                })
              }
            } catch (e) {
              // Skip invalid data
            }
          }
        }
      }

      // Sort by timestamp (newest first)
      const sortedInvoices = invoices.sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )

      setHistoryInvoices(sortedInvoices)
    } catch (error) {
      console.error('Error loading invoice history:', error)
    }
  }

  // Logo upload handlers with dynamic auto-sizing
  const processAndResizeLogo = (file: File, maxWidth: number = 200, maxHeight: number = 150): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img

        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height

          if (width > height) {
            width = maxWidth
            height = width / aspectRatio
          } else {
            height = maxHeight
            width = height * aspectRatio
          }
        }

        // Set canvas dimensions
        canvas.width = width
        canvas.height = height

        // Draw and resize image
        ctx?.drawImage(img, 0, 0, width, height)

        // Convert to base64 with high quality
        const resizedDataUrl = canvas.toDataURL('image/jpeg', 0.9)
        resolve(resizedDataUrl)
      }

      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = URL.createObjectURL(file)
    })
  }

  const handleSellerLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Logo file size must be less than 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file')
        return
      }

      try {
        toast.loading('Processing seller logo...', { id: 'seller-logo' })
        const resizedLogo = await processAndResizeLogo(file, 200, 150)
        setSellerLogo(resizedLogo)
        setSellerLogoFile(file)
        toast.success('Seller logo uploaded and optimized! 🖼️✨', { id: 'seller-logo' })
      } catch (error) {
        toast.error('Failed to process logo. Please try again.', { id: 'seller-logo' })
      }
    }
  }

  const handleClientLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Logo file size must be less than 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file')
        return
      }

      try {
        toast.loading('Processing client logo...', { id: 'client-logo' })
        const resizedLogo = await processAndResizeLogo(file, 200, 150)
        setClientLogo(resizedLogo)
        setClientLogoFile(file)
        toast.success('Client logo uploaded and optimized! 🖼️✨', { id: 'client-logo' })
      } catch (error) {
        toast.error('Failed to process logo. Please try again.', { id: 'client-logo' })
      }
    }
  }

  const removeSellerLogo = () => {
    setSellerLogo(null)
    setSellerLogoFile(null)
    toast.success('Seller logo removed')
  }

  const removeClientLogo = () => {
    setClientLogo(null)
    setClientLogoFile(null)
    toast.success('Client logo removed')
  }

  // Invoice history management functions
  const editInvoice = (invoice: any) => {
    try {
      // Load all invoice data into the form
      setInvoiceNumber(invoice.invoiceNumber || '')
      setInvoiceDate(invoice.invoiceDate || new Date().toISOString().split('T')[0])
      setDueDate(invoice.dueDate || '')
      setCurrency(invoice.currency || 'SAR')
      setDiscount(invoice.discount || '0')
      setDeductionAmount(invoice.deductionAmount || '0')
      setDeductionReason(invoice.deductionReason || '')

      // Seller details
      setSellerName(invoice.sellerName || '')
      setSellerAddress(invoice.sellerAddress || '')
      setSellerVAT(invoice.sellerVAT || '')
      setSellerLogo(invoice.sellerLogo || null)

      // Client details
      setClientName(invoice.clientName || '')
      setClientAddress(invoice.clientAddress || '')
      setClientVAT(invoice.clientVAT || '')
      setClientLogo(invoice.clientLogo || null)

      // Additional details
      setNotes(invoice.notes || '')
      setTerms(invoice.terms || '')
      setBankName(invoice.bankName || '')
      setAccountName(invoice.accountName || '')
      setAccountNumber(invoice.accountNumber || '')
      setIban(invoice.iban || '')

      // Items
      if (invoice.items && invoice.items.length > 0) {
        setItems(invoice.items)
      }

      setCurrentEditingInvoice(invoice.key)
      setShowHistoryNavbar(false)
      toast.success(`Editing invoice: ${invoice.invoiceNumber} 📝`)
    } catch (error) {
      console.error('Error editing invoice:', error)
      toast.error('Failed to load invoice for editing')
    }
  }

  const duplicateInvoice = (invoice: any) => {
    try {
      // Load invoice data but generate new invoice number
      const newInvoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`

      editInvoice(invoice)
      setInvoiceNumber(newInvoiceNumber)
      setCurrentEditingInvoice(null)
      toast.success(`Invoice duplicated as: ${newInvoiceNumber} 📋`)
    } catch (error) {
      console.error('Error duplicating invoice:', error)
      toast.error('Failed to duplicate invoice')
    }
  }

  const deleteInvoice = (invoiceKey: string, invoiceNumber: string) => {
    try {
      localStorage.removeItem(invoiceKey)
      loadInvoiceHistory()
      toast.success(`Invoice ${invoiceNumber} deleted successfully! 🗑️`)
    } catch (error) {
      console.error('Error deleting invoice:', error)
      toast.error('Failed to delete invoice')
    }
  }

  const viewInvoiceDetails = (invoice: any) => {
    // Create a preview of the invoice with current date
    const currentDate = new Date().toISOString().split('T')[0]
    console.log('📋 View Invoice Details - Using current date:', currentDate, 'Original date was:', invoice.invoiceDate)

    const invoiceData: InvoiceData = {
      invoiceNumber: invoice.invoiceNumber,
      invoiceDate: currentDate, // Always use current date for viewing
      dueDate: invoice.dueDate,
      currency: invoice.currency,
      discount: invoice.discount,
      deductionAmount: invoice.deductionAmount,
      deductionReason: invoice.deductionReason,
      sellerName: invoice.sellerName,
      sellerAddress: invoice.sellerAddress,
      sellerVAT: invoice.sellerVAT,
      sellerLogo: invoice.sellerLogo,
      clientName: invoice.clientName,
      clientAddress: invoice.clientAddress,
      clientVAT: invoice.clientVAT,
      clientLogo: invoice.clientLogo,
      items: invoice.items || [],
      notes: invoice.notes,
      terms: invoice.terms,
      bankName: invoice.bankName,
      accountName: invoice.accountName,
      accountNumber: invoice.accountNumber,
      iban: invoice.iban,
      subtotal: invoice.subtotal,
      subtotalAfterDeduction: invoice.subtotalAfterDeduction,
      totalVAT: invoice.totalVAT,
      totalAmount: invoice.totalAmount,
      qrCode: generateQRCode(), // Generate fresh QR code with current date
      logoPreview: undefined
    }

    try {
      downloadInvoicePDF(invoiceData)
      toast.success(`Viewing invoice: ${invoice.invoiceNumber} with current date 👁️`)
    } catch (error) {
      console.error('Error viewing invoice:', error)
      toast.error('Failed to view invoice')
    }
  }

  // Simple calculations
  const subtotal = items.reduce((sum, item) => sum + item.amount, 0)
  const discountAmount = (subtotal * Number(discount)) / 100
  const subtotalAfterDiscount = subtotal - discountAmount
  const subtotalAfterDeduction = Math.max(0, subtotalAfterDiscount - Number(deductionAmount))
  const totalVAT = subtotalAfterDeduction * 0.15
  const totalAmount = subtotalAfterDeduction + totalVAT

  // Simple functions
  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: '',
      quantity: '1',
      rate: '0',
      vatPercent: 15,
      amount: 0
    }
    setItems([...items, newItem])
  }

  const removeItem = (itemId: string) => {
    if (items.length <= 1) {
      toast.error('At least one item is required')
      return
    }
    setItems(items.filter(item => item.id !== itemId))
    toast.success('Item removed successfully! 🗑️')
  }

  // --- ENFORCE STRING STATE FOR ALL ITEM INPUTS ---
  // Defensive wrapper for updateItem to ensure value is always a string
  const safeUpdateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    // Always pass value as string
    updateItem(id, field, String(value))
  }

  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        let updatedItem = { ...item, [field]: value }
        // Calculate amount only if both quantity and rate are valid numbers
        const quantityNum = Number(updatedItem.quantity)
        const rateNum = Number(updatedItem.rate)
        if (!isNaN(quantityNum) && !isNaN(rateNum)) {
          updatedItem.amount = quantityNum * rateNum
        } else {
          updatedItem.amount = 0
        }
        return updatedItem
      }
      return item
    }))
  }

  // QR Code generation function - ALWAYS uses current date
  const generateQRCode = () => {
    // Always use current date for QR code timestamp
    const currentDate = new Date()

    const qrData: ZATCAQRData = {
      sellerName: sellerName || 'KAAZMAAMAA Company',
      vatNumber: sellerVAT || '30**********003',
      timestamp: generateZATCATimestamp(currentDate),
      invoiceTotal: formatCurrencyForZATCA(totalAmount),
      vatTotal: formatCurrencyForZATCA(totalVAT)
    }

    try {
      return generateZATCAQRCode(qrData)
    } catch (error) {
      console.error('QR Code generation error:', error)
      toast.error('Failed to generate QR code')
      return ''
    }
  }

  // PDF Generation function
  const generatePDF = () => {
    if (!sellerName || !clientName || items.length === 0) {
      toast.error('Please fill in all required fields before generating PDF')
      return
    }

    // Validate deduction reason if deduction amount is specified
    if (deductionAmount > 0 && !deductionReason.trim()) {
      toast.error('Please provide a reason for the deduction amount')
      return
    }

    toast.info('Generating PDF with ZATCA QR code...')

    // Always use current date for PDF generation
    const currentDate = new Date().toISOString().split('T')[0]
    console.log('🗓️ PDF Generation - Using current date:', currentDate)

    const invoiceData: InvoiceData = {
      invoiceNumber,
      invoiceDate: currentDate, // Always use current date
      dueDate,
      currency,
      discount,
      deductionAmount,
      deductionReason,
      sellerName,
      sellerAddress,
      sellerVAT,
      sellerLogo,
      clientName,
      clientAddress,
      clientVAT,
      clientLogo,
      items,
      notes,
      terms,
      bankName,
      accountName,
      accountNumber,
      iban,
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(), // This also uses current date
      logoPreview: undefined
    }

    try {
      downloadInvoicePDF(invoiceData)
      toast.success(`PDF generated with current date: ${new Date().toLocaleDateString()} 📄`)
    } catch (error) {
      console.error('PDF generation error:', error)
      toast.error('Failed to generate PDF')
    }
  }

  // Print Invoice function
  const handlePrintInvoice = () => {
    if (!sellerName || !clientName || items.length === 0) {
      toast.error('Please fill in all required fields before printing')
      return
    }

    // Validate deduction reason if deduction amount is specified
    if (deductionAmount > 0 && !deductionReason.trim()) {
      toast.error('Please provide a reason for the deduction amount')
      return
    }

    toast.info('Opening print dialog...')

    // Always use current date for print as well
    const currentDate = new Date().toISOString().split('T')[0]
    console.log('🖨️ Print Invoice - Using current date:', currentDate)

    const invoiceData: InvoiceData = {
      invoiceNumber,
      invoiceDate: currentDate, // Always use current date
      dueDate,
      currency,
      discount,
      deductionAmount,
      deductionReason,
      sellerName,
      sellerAddress,
      sellerVAT,
      sellerLogo,
      clientName,
      clientAddress,
      clientVAT,
      clientLogo,
      items,
      notes,
      terms,
      bankName,
      accountName,
      accountNumber,
      iban,
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(), // This also uses current date
      logoPreview: undefined
    }

    try {
      printInvoice(invoiceData)
      toast.success('Print dialog opened successfully! 🖨️')
    } catch (error) {
      console.error('Print error:', error)
      toast.error('Failed to open print dialog')
    }
  }

  const saveDraft = () => {
    try {
      const formData = {
        invoiceNumber, invoiceDate, dueDate, currency, discount, deductionAmount, deductionReason,
        sellerName, sellerAddress, sellerVAT, sellerLogo, clientName, clientAddress, clientVAT, clientLogo,
        notes, terms, bankName, accountName, accountNumber, iban, items,
        subtotal, subtotalAfterDeduction, totalVAT, totalAmount,
        status: 'draft',
        timestamp: new Date().toISOString(),
        lastModified: new Date().toISOString()
      }

      // Save as current draft
      localStorage.setItem('invoice-draft', JSON.stringify(formData))

      // Save with unique key for history
      if (sellerName || clientName || invoiceNumber) {
        let historyKey
        if (currentEditingInvoice) {
          // Update existing invoice
          historyKey = currentEditingInvoice
          formData.lastModified = new Date().toISOString()
        } else {
          // Create new invoice
          historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`
        }

        localStorage.setItem(historyKey, JSON.stringify(formData))

        // Update history
        loadInvoiceHistory()
      }

      toast.success(currentEditingInvoice ? 'Invoice updated! 📝' : 'Draft saved! 💾')
    } catch (error) {
      toast.error('Save failed')
    }
  }

  const saveAsCompleted = () => {
    try {
      if (!sellerName || !clientName || items.length === 0) {
        toast.error('Please fill in all required fields before marking as completed')
        return
      }

      const formData = {
        invoiceNumber, invoiceDate, dueDate, currency, discount, deductionAmount, deductionReason,
        sellerName, sellerAddress, sellerVAT, sellerLogo, clientName, clientAddress, clientVAT, clientLogo,
        notes, terms, bankName, accountName, accountNumber, iban, items,
        subtotal, subtotalAfterDeduction, totalVAT, totalAmount,
        status: 'completed',
        timestamp: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        qrCode: generateQRCode()
      }

      let historyKey
      if (currentEditingInvoice) {
        historyKey = currentEditingInvoice
      } else {
        historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`
      }

      localStorage.setItem(historyKey, JSON.stringify(formData))
      loadInvoiceHistory()

      toast.success(`Invoice ${invoiceNumber} marked as completed! ✅`)
    } catch (error) {
      toast.error('Failed to save completed invoice')
    }
  }

  const createNewInvoice = () => {
    // Clear all form fields
    setInvoiceNumber(`INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`)
    setInvoiceDate(new Date().toISOString().split('T')[0])
    setDueDate('')
    setCurrency('SAR')
    setDiscount('0')
    setDeductionAmount('0')
    setDeductionReason('')

    // Clear seller details (but keep for convenience)
    // setSellerName('')
    // setSellerAddress('')
    // setSellerVAT('')
    setSellerLogo(null)

    // Clear client details
    setClientName('')
    setClientAddress('')
    setClientVAT('')
    setClientLogo(null)

    // Clear additional details
    setNotes('')
    setTerms('')
    setBankName('')
    setAccountName('')
    setAccountNumber('')
    setIban('')

    // Reset items
    setItems([{
      id: '1',
      description: '',
      quantity: '1',
      rate: '0',
      vatPercent: 15,
      amount: 0
    }])

    setCurrentEditingInvoice(null)
    setShowHistoryNavbar(false)
    toast.success('New invoice form ready! 📄')
  }

  // Function to set current date
  const setCurrentDate = () => {
    const currentDate = new Date().toISOString().split('T')[0]
    setInvoiceDate(currentDate)
    toast.success('Invoice date updated to current date! 📅')
  }



  // GLOBAL UNLOCK ALL
  const unlockAllSections = () => {
    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.unlockAll()

      // Update React state
      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      setLockedSections(newState)

      toast.success('🔓 ALL SECTIONS GLOBAL UNLOCKED!', { duration: 3000 })
    }
  }

  // GLOBAL LOCK ALL
  const lockAllSections = () => {
    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.lockAll()

      // Update React state
      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      setLockedSections(newState)

      toast.success('🔒 ALL SECTIONS GLOBAL LOCKED - PERMANENT!', { duration: 3000 })
    }
  }

  // GLOBAL FORCE RELOAD
  const forceReloadLocks = () => {
    try {
      console.log('🔄 GLOBAL FORCE RELOAD TRIGGERED')

      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        const reloadedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        console.log('🔒 GLOBAL RELOADED STATE:', reloadedState)

        // Update React state
        setLockedSections(reloadedState)

        // Apply to DOM
        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(reloadedState)

        const lockedCount = Object.values(reloadedState).filter(Boolean).length
        toast.success(`🔄 GLOBAL RELOADED: ${lockedCount} sections locked!`, { duration: 2000 })
      }
    } catch (error) {
      console.error('❌ Global reload error:', error)
      toast.error('Global reload failed!')
    }
  }

  // GLOBAL FORCE SAVE
  const forceSave = () => {
    try {
      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        const currentState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        console.log('🚀 GLOBAL FORCE SAVE TRIGGERED:', currentState)
        const saved = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.save(currentState)
        if (saved) {
          toast.success('🚀 GLOBAL FORCE SAVE COMPLETE!', { duration: 2000 })
          console.log('✅ GLOBAL FORCE SAVE SUCCESSFUL')

          // Show verification
          setTimeout(() => {
            const verify = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
            console.log('✅ GLOBAL SAVE VERIFIED:', verify)
          }, 100)
        } else {
          toast.error('❌ GLOBAL FORCE SAVE FAILED!')
          console.error('❌ GLOBAL FORCE SAVE FAILED')
        }
      }
    } catch (error) {
      console.error('❌ Global force save error:', error)
      toast.error('Global force save failed!')
    }
  }

  // GLOBAL CLEAR (TESTING)
  const clearLockState = () => {
    try {
      console.log('🗑️ GLOBAL CLEAR TRIGGERED')

      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        // Use global clear method
        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.clear()

        // Update React state
        const clearedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        setLockedSections(clearedState)

        toast.success('🗑️ GLOBAL CLEARED FROM ALL 50+ LOCATIONS!', { duration: 3000 })
        console.log('✅ GLOBAL CLEAR COMPLETE')
      }
    } catch (error) {
      console.error('❌ Global clear error:', error)
      toast.error('Global clear failed!')
    }
  }

  // Preview function to show current modifications
  const previewInvoice = () => {
    if (!sellerName || !clientName || items.length === 0) {
      toast.error('Please fill in all required fields before preview')
      return
    }

    if (deductionAmount > 0 && !deductionReason.trim()) {
      toast.error('Please provide a reason for the deduction amount')
      return
    }

    toast.info('Generating preview with current date...')

    // Always use current date for preview
    const currentDate = new Date().toISOString().split('T')[0]
    const currentDateFormatted = new Date().toLocaleDateString()
    console.log('👁️ Preview Invoice - Using current date:', currentDate, 'Formatted:', currentDateFormatted)

    const invoiceData: InvoiceData = {
      invoiceNumber,
      invoiceDate: currentDate, // Always use current date
      dueDate,
      currency,
      discount,
      deductionAmount,
      deductionReason,
      sellerName,
      sellerAddress,
      sellerVAT,
      sellerLogo,
      clientName,
      clientAddress,
      clientVAT,
      clientLogo,
      items,
      notes,
      terms,
      bankName,
      accountName,
      accountNumber,
      iban,
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(), // This also uses current date
      logoPreview: undefined
    }

    try {
      // Create preview window
      const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
      if (previewWindow) {
        const html = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Invoice Preview - Current Date: ${currentDateFormatted}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background: #f5f5f5;
              }
              .preview-header {
                background: #4f46e5;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                text-align: center;
              }
              .preview-info {
                background: #e0f2fe;
                border: 2px solid #0288d1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
              }
              .lock-status {
                background: #fff3e0;
                border: 2px solid #ff9800;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
              }
              .locked { color: #d32f2f; font-weight: bold; }
              .unlocked { color: #388e3c; font-weight: bold; }
              .invoice-preview {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              .field { margin: 8px 0; }
              .label { font-weight: bold; color: #333; }
              .value { color: #666; margin-left: 10px; }
              .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
              .current-date { color: #4f46e5; font-weight: bold; font-size: 18px; }
            </style>
          </head>
          <body>
            <div class="preview-header">
              <h1>📄 Invoice Preview</h1>
              <p>Preview with Current Date Modifications Applied</p>
            </div>

            <div class="preview-info">
              <h3>🔍 Preview Information</h3>
              <div class="field">
                <span class="label">Current Date Applied:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
              <div class="field">
                <span class="label">QR Code Date:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
              <div class="field">
                <span class="label">PDF Date:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
            </div>

            <div class="lock-status">
              <h3>🔒 Lock Status</h3>
              <div class="field">
                <span class="label">Invoice Details:</span>
                <span class="value ${lockedSections.invoiceDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.invoiceDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Seller Details:</span>
                <span class="value ${lockedSections.sellerDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.sellerDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Client Details:</span>
                <span class="value ${lockedSections.clientDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.clientDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Bank Details:</span>
                <span class="value ${lockedSections.bankDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.bankDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Invoice Items:</span>
                <span class="value ${lockedSections.items ? 'locked' : 'unlocked'}">
                  ${lockedSections.items ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Additional Information:</span>
                <span class="value ${lockedSections.notes ? 'locked' : 'unlocked'}">
                  ${lockedSections.notes ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
            </div>

            <div class="invoice-preview">
              <h3>📋 Invoice Data Preview</h3>

              <div class="section">
                <h4>Invoice Details</h4>
                <div class="field">
                  <span class="label">Invoice Number:</span>
                  <span class="value">${invoiceNumber}</span>
                </div>
                <div class="field">
                  <span class="label">Invoice Date:</span>
                  <span class="value current-date">${currentDateFormatted} (CURRENT DATE APPLIED)</span>
                </div>
                <div class="field">
                  <span class="label">Due Date:</span>
                  <span class="value">${dueDate || 'Not specified'}</span>
                </div>
                <div class="field">
                  <span class="label">Currency:</span>
                  <span class="value">${currency}</span>
                </div>
              </div>

              <div class="section">
                <h4>Seller Details</h4>
                <div class="field">
                  <span class="label">Company Name:</span>
                  <span class="value">${sellerName}</span>
                </div>
                <div class="field">
                  <span class="label">VAT Number:</span>
                  <span class="value">${sellerVAT}</span>
                </div>
                <div class="field">
                  <span class="label">Address:</span>
                  <span class="value">${sellerAddress}</span>
                </div>
              </div>

              <div class="section">
                <h4>Client Details</h4>
                <div class="field">
                  <span class="label">Client Name:</span>
                  <span class="value">${clientName}</span>
                </div>
                <div class="field">
                  <span class="label">Client VAT:</span>
                  <span class="value">${clientVAT || 'Not provided'}</span>
                </div>
                <div class="field">
                  <span class="label">Client Address:</span>
                  <span class="value">${clientAddress}</span>
                </div>
              </div>

              <div class="section">
                <h4>Financial Summary</h4>
                <div class="field">
                  <span class="label">Subtotal:</span>
                  <span class="value">${currency} ${subtotal.toFixed(2)}</span>
                </div>
                ${deductionAmount > 0 ? `
                  <div class="field">
                    <span class="label">Deduction:</span>
                    <span class="value">-${currency} ${deductionAmount.toFixed(2)}</span>
                  </div>
                ` : ''}
                <div class="field">
                  <span class="label">Total VAT (15%):</span>
                  <span class="value">${currency} ${totalVAT.toFixed(2)}</span>
                </div>
                <div class="field">
                  <span class="label">Total Amount:</span>
                  <span class="value current-date">${currency} ${totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </body>
          </html>
        `

        previewWindow.document.write(html)
        previewWindow.document.close()

        toast.success(`Preview opened with current date: ${currentDateFormatted} 👁️`)
      }
    } catch (error) {
      console.error('Preview error:', error)
      toast.error('Failed to open preview')
    }
  }

  // Load previous user data from localStorage
  const loadPreviousData = () => {
    try {
      const savedData = localStorage.getItem('invoice-draft')
      if (savedData) {
        const data = JSON.parse(savedData)

        // Load all the saved data
        if (data.invoiceNumber) setInvoiceNumber(data.invoiceNumber)
        if (data.invoiceDate) setInvoiceDate(data.invoiceDate)
        if (data.dueDate) setDueDate(data.dueDate)
        if (data.currency) setCurrency(data.currency)
        if (data.discount !== undefined) setDiscount(data.discount)
        if (data.deductionAmount !== undefined) setDeductionAmount(data.deductionAmount)
        if (data.deductionReason) setDeductionReason(data.deductionReason)

        // Seller details
        if (data.sellerName) setSellerName(data.sellerName)
        if (data.sellerAddress) setSellerAddress(data.sellerAddress)
        if (data.sellerVAT) setSellerVAT(data.sellerVAT)
        if (data.sellerLogo) setSellerLogo(data.sellerLogo)

        // Client details
        if (data.clientName) setClientName(data.clientName)
        if (data.clientAddress) setClientAddress(data.clientAddress)
        if (data.clientVAT) setClientVAT(data.clientVAT)
        if (data.clientLogo) setClientLogo(data.clientLogo)

        // Additional details
        if (data.notes) setNotes(data.notes)
        if (data.terms) setTerms(data.terms)
        if (data.bankName) setBankName(data.bankName)
        if (data.accountName) setAccountName(data.accountName)
        if (data.accountNumber) setAccountNumber(data.accountNumber)
        if (data.iban) setIban(data.iban)

        // Items
        if (data.items && data.items.length > 0) {
          setItems(data.items)
        }

        toast.success('Previous data loaded successfully! 📋')
      } else {
        toast.info('No previous data found. Start entering your information.')
      }
    } catch (error) {
      console.error('Error loading previous data:', error)
      toast.error('Failed to load previous data')
    }
  }

  // Get recent invoices for quick loading
  const getRecentInvoices = () => {
    try {
      if (typeof window === 'undefined') return [] // Server-side rendering check
      const recentInvoices = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              const invoice = JSON.parse(data)
              if (invoice.invoiceNumber && invoice.timestamp) {
                recentInvoices.push({
                  key,
                  ...invoice
                })
              }
            } catch (e) {
              // Skip invalid data
            }
          }
        }
      }

      // Sort by timestamp (newest first) and take top 5
      return recentInvoices
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 5)
    } catch (error) {
      console.error('Error getting recent invoices:', error)
      return []
    }
  }

  const loadInvoiceData = (invoice: any) => {
    // Load all the invoice data
    setInvoiceNumber(invoice.invoiceNumber || '')
    setInvoiceDate(invoice.invoiceDate || '')
    setDueDate(invoice.dueDate || '')
    setCurrency(invoice.currency || 'SAR')
    setDiscount(invoice.discount || '0')
    setDeductionAmount(invoice.deductionAmount || '0')
    setDeductionReason(invoice.deductionReason || '')

    // Seller details
    setSellerName(invoice.sellerName || '')
    setSellerAddress(invoice.sellerAddress || '')
    setSellerVAT(invoice.sellerVAT || '')
    setSellerLogo(invoice.sellerLogo || null)

    // Client details
    setClientName(invoice.clientName || '')
    setClientAddress(invoice.clientAddress || '')
    setClientVAT(invoice.clientVAT || '')
    setClientLogo(invoice.clientLogo || null)

    // Additional details
    setNotes(invoice.notes || '')
    setTerms(invoice.terms || '')
    setBankName(invoice.bankName || '')
    setAccountName(invoice.accountName || '')
    setAccountNumber(invoice.accountNumber || '')
    setIban(invoice.iban || '')

    // Items
    if (invoice.items && invoice.items.length > 0) {
      setItems(invoice.items)
    }

    toast.success(`Invoice ${invoice.invoiceNumber} loaded! 📋`)
  }

  // Defensive value for quantity and rate
  function getSafeString(val: any, field: string) {
    if (typeof val !== 'string') {
      console.warn(`Invoice item field '${field}' received non-string value:`, val)
      return val === undefined || val === null ? '' : String(val)
    }
    return val
  }

  // Add debug log in parent render
  console.log('[CreateInvoicePage] render, deductionReason=', deductionReason, 'sellerName=', sellerName, 'clientName=', clientName, 'bankName=', bankName, 'notes=', notes, 'terms=', terms)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-purple-600">
                  {currentEditingInvoice ? 'Edit Invoice' : 'Create Invoice'}
                </h1>
                <p className="text-sm text-gray-600">
                  {currentEditingInvoice
                    ? 'Modify and update your existing invoice'
                    : 'Generate professional ZATCA-compliant invoices'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistoryNavbar(!showHistoryNavbar)}
                className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
              >
                <History className="h-4 w-4 mr-2" />
                History ({historyInvoices.length})
              </Button>
              {currentEditingInvoice && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={createNewInvoice}
                  className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Invoice
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={loadPreviousData}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                📋 Load Previous Data
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={saveDraft}
              >
                💾 Save Draft
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={saveAsCompleted}
                className="bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
              >
                ✅ Mark Complete
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={unlockAllSections}
                className="bg-yellow-50 text-yellow-600 hover:bg-yellow-100 border-yellow-200"
              >
                <Unlock className="h-4 w-4 mr-1" />
                Unlock All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={lockAllSections}
                className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
              >
                <Lock className="h-4 w-4 mr-1" />
                Lock All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={previewInvoice}
                className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
              >
                <Eye className="h-4 w-4 mr-1" />
                Preview
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={debugLockState}
                className="bg-gray-50 text-gray-600 hover:bg-gray-100 border-gray-200"
                title="Debug lock state"
              >
                🔍 Debug
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={forceReloadLocks}
                className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
                title="Force reload lock state"
              >
                🔄 Reload
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={forceSave}
                className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
                title="Force save lock state"
              >
                🚀 Save
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearLockState}
                className="bg-orange-50 text-orange-600 hover:bg-orange-100 border-orange-200"
                title="Clear all lock state (for testing)"
              >
                🗑️ Clear
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={generatePDF}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Generate PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrintInvoice}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Invoice
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* History Navbar */}
      {showHistoryNavbar && (
        <div className="bg-white border-b shadow-sm">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <h2 className="text-lg font-semibold text-gray-900">Invoice History</h2>
                <span className="text-sm text-gray-500">({historyInvoices.length} invoices)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search invoices..."
                    value={historySearchTerm}
                    onChange={(e) => setHistorySearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <select
                  value={historyFilterStatus}
                  onChange={(e) => setHistoryFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="completed">Completed</option>
                </select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowHistoryNavbar(false)}
                >
                  Close
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
              {historyInvoices
                .filter(invoice => {
                  const matchesSearch = !historySearchTerm ||
                    invoice.invoiceNumber?.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
                    invoice.clientName?.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
                    invoice.sellerName?.toLowerCase().includes(historySearchTerm.toLowerCase())

                  const matchesStatus = historyFilterStatus === 'all' || invoice.status === historyFilterStatus

                  return matchesSearch && matchesStatus
                })
                .map((invoice) => (
                  <div
                    key={invoice.key}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-gray-50"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 truncate">{invoice.invoiceNumber}</h3>
                        <p className="text-sm text-gray-600 truncate">{invoice.clientName || 'No client'}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            invoice.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {invoice.status || 'draft'}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(invoice.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="font-semibold text-green-600">
                          {invoice.currency} {(invoice.totalAmount || 0).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : 'No due date'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editInvoice(invoice)}
                        className="flex-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => duplicateInvoice(invoice)}
                        className="flex-1 text-green-600 hover:text-green-700 hover:bg-green-50"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewInvoiceDetails(invoice)}
                        className="flex-1 text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}

              {historyInvoices.length === 0 && (
                <div className="col-span-full text-center py-8 text-gray-500">
                  <History className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">No invoices found</p>
                  <p className="text-sm">Create and save invoices to see them here</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Invoice Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Invoice Details Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-purple-600">
                    <FileText className="h-5 w-5" />
                    <span>Invoice Details</span>
                    {isHydrated && lockedSections.invoiceDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('invoiceDetails')}
                    data-lock-button="true"
                    className={`${
                      isHydrated && lockedSections.invoiceDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : directLockControl.getCurrentState().invoiceDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="invoiceDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Number</label>
                    <Input
                      value={invoiceNumber}
                      onChange={(e) => setInvoiceNumber(e.target.value)}
                      placeholder="INV-2025-001"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Date</label>
                    <div className="flex space-x-2">
                      <Input
                        type="date"
                        value={invoiceDate}
                        onChange={(e) => setInvoiceDate(e.target.value)}
                        className="flex-1"
                        disabled={isHydrated && lockedSections.invoiceDetails}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={setCurrentDate}
                        className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200 whitespace-nowrap"
                        title="Set to current date"
                        disabled={isHydrated && lockedSections.invoiceDetails}
                      >
                        <Calendar className="h-4 w-4 mr-1" />
                        Today
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                    <Input
                      type="date"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                    <Input
                      value={currency}
                      onChange={(e) => setCurrency(e.target.value)}
                      placeholder="SAR"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Discount (%)</label>
                    <Input
                      type="number"
                      value={discount}
                      onChange={(e) => setDiscount(e.target.value)}
                      placeholder="0"
                      min="0"
                      max="100"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Deduction Amount ({currency})
                    </label>
                    <Input
                      type="number"
                      value={deductionAmount}
                      onChange={(e) => setDeductionAmount(e.target.value)}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  {deductionAmount > 0 && (
                    <div className="md:col-span-2">
                      <SuggestionInput
                        value={deductionReason}
                        onChange={setDeductionReason}
                        field="deductionReason"
                        placeholder="e.g., Early payment discount, Advance payment received, Material cost adjustment, etc."
                        label="Deduction Reason *"
                        disabled={isHydrated && lockedSections.invoiceDetails}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Seller Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Seller Details</span>
                    {isHydrated && lockedSections.sellerDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('sellerDetails')}
                    data-lock-button="true"
                    className={`${
                      isHydrated && lockedSections.sellerDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.sellerDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="sellerDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SuggestionInput
                    value={sellerName}
                    onChange={setSellerName}
                    field="sellerName"
                    placeholder="Your Company Name"
                    label="Company Name"
                    disabled={isHydrated && lockedSections.sellerDetails}
                  />
                  <SuggestionInput
                    value={sellerVAT}
                    onChange={setSellerVAT}
                    field="sellerVAT"
                    placeholder="30**********003"
                    label="VAT Number"
                    disabled={isHydrated && lockedSections.sellerDetails}
                  />
                  <div className="md:col-span-2">
                    <SuggestionInput
                      value={sellerAddress}
                      onChange={setSellerAddress}
                      field="sellerAddress"
                      placeholder="Company Address"
                      label="Address"
                      rows={3}
                      disabled={isHydrated && lockedSections.sellerDetails}
                    />
                  </div>

                  {/* Seller Logo Upload */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Company Logo</label>
                    <div className="flex items-center space-x-4">
                      {sellerLogo ? (
                        <div className="flex items-center space-x-3">
                          <div className="w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                            <img
                              src={sellerLogo}
                              alt="Seller Logo"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <div className="flex flex-col space-y-2">
                            <span className="text-sm text-green-600 font-medium">✓ Logo uploaded & optimized</span>
                            <span className="text-xs text-gray-500">Auto-resized for best quality</span>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={removeSellerLogo}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Remove Logo
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3">
                          <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                            <Upload className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="flex flex-col">
                            <input
                              id="seller-logo-input"
                              type="file"
                              accept="image/*"
                              onChange={handleSellerLogoUpload}
                              className="hidden"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="bg-blue-50 text-blue-600 hover:bg-blue-100"
                              onClick={() => document.getElementById('seller-logo-input')?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Logo
                            </Button>
                            <span className="text-xs text-gray-500 mt-1">PNG, JPG up to 5MB</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Client Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Client Details</span>
                    {isHydrated && lockedSections.clientDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('clientDetails')}
                    data-lock-button="true"
                    className={`${
                      isHydrated && lockedSections.clientDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.clientDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="clientDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SuggestionInput
                    value={clientName}
                    onChange={setClientName}
                    field="clientName"
                    placeholder="Client Company Name"
                    label="Client Name"
                    disabled={isHydrated && lockedSections.clientDetails}
                  />
                  <SuggestionInput
                    value={clientVAT}
                    onChange={setClientVAT}
                    field="clientVAT"
                    placeholder="301987654321009"
                    label="Client VAT"
                    disabled={isHydrated && lockedSections.clientDetails}
                  />
                  <div className="md:col-span-2">
                    <SuggestionInput
                      value={clientAddress}
                      onChange={setClientAddress}
                      field="clientAddress"
                      placeholder="Client Address"
                      label="Client Address"
                      rows={3}
                      disabled={isHydrated && lockedSections.clientDetails}
                    />
                  </div>

                  {/* Client Logo Upload */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Client Logo</label>
                    <div className="flex items-center space-x-4">
                      {clientLogo ? (
                        <div className="flex items-center space-x-3">
                          <div className="w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                            <img
                              src={clientLogo}
                              alt="Client Logo"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <div className="flex flex-col space-y-2">
                            <span className="text-sm text-green-600 font-medium">✓ Logo uploaded & optimized</span>
                            <span className="text-xs text-gray-500">Auto-resized for best quality</span>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={removeClientLogo}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Remove Logo
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3">
                          <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                            <Upload className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="flex flex-col">
                            <input
                              id="client-logo-input"
                              type="file"
                              accept="image/*"
                              onChange={handleClientLogoUpload}
                              className="hidden"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="bg-green-50 text-green-600 hover:bg-green-100"
                              onClick={() => document.getElementById('client-logo-input')?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Logo
                            </Button>
                            <span className="text-xs text-gray-500 mt-1">PNG, JPG up to 5MB</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bank Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Bank Details</span>
                    {isHydrated && lockedSections.bankDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('bankDetails')}
                    data-lock-button="true"
                    className={`${
                      isHydrated && lockedSections.bankDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.bankDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="bankDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SuggestionInput
                    value={bankName}
                    onChange={setBankName}
                    field="bankName"
                    placeholder="Saudi National Bank"
                    label="Bank Name"
                    disabled={isHydrated && lockedSections.bankDetails}
                  />
                  <SuggestionInput
                    value={accountName}
                    onChange={setAccountName}
                    field="accountName"
                    placeholder="Company Account Name"
                    label="Account Name"
                    disabled={isHydrated && lockedSections.bankDetails}
                  />
                  <SuggestionInput
                    value={accountNumber}
                    onChange={setAccountNumber}
                    field="accountNumber"
                    placeholder="**********"
                    label="Account Number"
                    disabled={isHydrated && lockedSections.bankDetails}
                  />
                  <SuggestionInput
                    value={iban}
                    onChange={setIban}
                    field="iban"
                    placeholder="************************"
                    label="IBAN"
                    disabled={isHydrated && lockedSections.bankDetails}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Invoice Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Invoice Items</span>
                    {isHydrated && lockedSections.items && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => toggleSectionLock('items')}
                      data-lock-button="true"
                      className={`${
                        isHydrated && lockedSections.items
                          ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                          : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                      }`}
                    >
                      {!isHydrated ? (
                        <>
                          <Lock className="h-4 w-4 mr-1" />
                          Lock
                        </>
                      ) : lockedSections.items ? (
                        <>
                          <Unlock className="h-4 w-4 mr-1" />
                          Unlock
                        </>
                      ) : (
                        <>
                          <Lock className="h-4 w-4 mr-1" />
                          Lock
                        </>
                      )}
                    </Button>
                    <Button
                      size="sm"
                      onClick={addItem}
                      className="bg-blue-600 hover:bg-blue-700"
                      disabled={isHydrated && lockedSections.items}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="items">
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <div key={item.id} className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border rounded-lg relative">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <Input
                          value={item.description}
                          onChange={(e) => safeUpdateItem(item.id, 'description', e.target.value)}
                          placeholder="Item description"
                          disabled={isHydrated && lockedSections.items}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                        <Input
                          type="text"
                          value={getSafeString(item.quantity, 'quantity')}
                          onChange={(e) => safeUpdateItem(item.id, 'quantity', e.target.value)}
                          min="1"
                          disabled={isHydrated && lockedSections.items}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Rate ({currency})</label>
                        <Input
                          type="text"
                          value={getSafeString(item.rate, 'rate')}
                          onChange={(e) => safeUpdateItem(item.id, 'rate', e.target.value)}
                          min="0"
                          step="0.01"
                          disabled={isHydrated && lockedSections.items}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Amount ({currency})</label>
                        <Input
                          value={item.amount.toFixed(2)}
                          readOnly
                          className="bg-gray-50"
                        />
                      </div>
                      <div className="flex items-end">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                          disabled={items.length <= 1 || lockedSections.items}
                          className={`w-full ${
                            items.length <= 1 || lockedSections.items
                              ? 'opacity-50 cursor-not-allowed'
                              : 'text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200'
                          }`}
                          title={
                            lockedSections.items
                              ? 'Items section is locked'
                              : items.length <= 1
                                ? 'At least one item is required'
                                : 'Remove this item'
                          }
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Notes and Terms */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Additional Information</span>
                    {isHydrated && lockedSections.notes && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('notes')}
                    data-lock-button="true"
                    className={`${
                      isHydrated && lockedSections.notes
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.notes ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="notes">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SuggestionInput
                    value={notes}
                    onChange={setNotes}
                    field="notes"
                    placeholder="Thank you for your business!"
                    label="Notes"
                    rows={4}
                    disabled={isHydrated && lockedSections.notes}
                  />
                  <SuggestionInput
                    value={terms}
                    onChange={setTerms}
                    field="terms"
                    placeholder="Payment due within 30 days..."
                    label="Terms & Conditions"
                    rows={4}
                    disabled={isHydrated && lockedSections.notes}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Summary */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Invoice Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span className="font-semibold">{currency} {subtotal.toFixed(2)}</span>
                  </div>
                  {discountAmount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>Discount ({discount}%):</span>
                      <span>-{currency} {discountAmount.toFixed(2)}</span>
                    </div>
                  )}
                  {Number(deductionAmount) > 0 && (
                    <div className="flex justify-between text-orange-600">
                      <span>Deduction Amount:</span>
                      <span>-{currency} {Number(deductionAmount).toFixed(2)}</span>
                    </div>
                  )}
                  {Number(deductionAmount) > 0 && deductionReason && (
                    <div className="text-sm text-gray-600 bg-orange-50 p-2 rounded">
                      <strong>Deduction Reason:</strong> {deductionReason}
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Amount Before VAT:</span>
                    <span className="font-semibold">{currency} {subtotalAfterDeduction.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total VAT (15%):</span>
                    <span className="font-semibold">{currency} {totalVAT.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t pt-3">
                    <span>Total Amount:</span>
                    <span className="text-purple-600">{currency} {totalAmount.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <QrCode className="h-5 w-5" />
                  <span>Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button
                    onClick={previewInvoice}
                    className="w-full bg-purple-600 hover:bg-purple-700"
                    size="lg"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Invoice
                  </Button>
                  <Button
                    onClick={generatePDF}
                    className="w-full bg-green-600 hover:bg-green-700"
                    size="lg"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Generate & Download PDF
                  </Button>
                  <Button
                    onClick={handlePrintInvoice}
                    variant="outline"
                    className="w-full border-purple-300 text-purple-600 hover:bg-purple-50"
                    size="lg"
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    Print Invoice
                  </Button>
                  <Button
                    onClick={saveDraft}
                    variant="outline"
                    className="w-full"
                    size="lg"
                  >
                    💾 Save Draft
                  </Button>
                  <Button
                    onClick={saveAsCompleted}
                    variant="outline"
                    className="w-full bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
                    size="lg"
                  >
                    ✅ Mark as Completed
                  </Button>
                </div>

                {/* Logo Preview */}
                {(sellerLogo || clientLogo) && (
                  <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-gray-200">
                    <p className="text-sm text-gray-700 mb-3 text-center font-medium">📸 Logo Preview</p>
                    <div className="flex justify-center space-x-6">
                      {sellerLogo && (
                        <div className="text-center">
                          <p className="text-xs text-blue-600 mb-2 font-medium">Seller Logo</p>
                          <div className="w-20 h-16 bg-white border-2 border-blue-200 rounded-lg overflow-hidden shadow-sm">
                            <img
                              src={sellerLogo}
                              alt="Seller Logo Preview"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">Auto-optimized</p>
                        </div>
                      )}
                      {clientLogo && (
                        <div className="text-center">
                          <p className="text-xs text-green-600 mb-2 font-medium">Client Logo</p>
                          <div className="w-20 h-16 bg-white border-2 border-green-200 rounded-lg overflow-hidden shadow-sm">
                            <img
                              src={clientLogo}
                              alt="Client Logo Preview"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">Auto-optimized</p>
                        </div>
                      )}
                    </div>
                    <p className="text-xs text-center text-gray-600 mt-3">
                      ✨ Logos are automatically resized and optimized for PDF generation
                    </p>
                  </div>
                )}

                {/* QR Code Preview */}
                {sellerName && clientName && totalAmount > 0 && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-lg text-center">
                    <p className="text-sm text-gray-600 mb-2">ZATCA QR Code will be generated</p>
                    <div className="w-16 h-16 bg-white border-2 border-gray-300 rounded mx-auto flex items-center justify-center">
                      <QrCode className="h-8 w-8 text-gray-400" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">QR code for ZATCA compliance</p>
                    <p className="text-xs text-blue-600 mt-1">
                      📅 Always uses current date: {new Date().toLocaleDateString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Invoices */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📋 Recent Invoices</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {getRecentInvoices().length > 0 ? (
                    getRecentInvoices().map((invoice, index) => (
                      <div
                        key={invoice.key}
                        className="p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors"
                        onClick={() => loadInvoiceData(invoice)}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="font-medium text-sm text-blue-600">{invoice.invoiceNumber}</p>
                            <p className="text-xs text-gray-600 truncate">{invoice.clientName || 'No client'}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(invoice.timestamp).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-green-600">
                              {invoice.currency} {(invoice.totalAmount || 0).toFixed(2)}
                            </p>
                            {invoice.deductionAmount > 0 && (
                              <p className="text-xs text-orange-600">-{invoice.deductionAmount}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p className="text-sm">No recent invoices found</p>
                      <p className="text-xs">Create and save invoices to see them here</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Validation Status */}
            <Card>
              <CardHeader>
                <CardTitle>Validation Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Seller Details:</span>
                    <span className={`text-sm ${sellerName && sellerVAT ? 'text-green-600' : 'text-red-600'}`}>
                      {sellerName && sellerVAT ? '✓ Complete' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Client Details:</span>
                    <span className={`text-sm ${clientName && clientVAT ? 'text-green-600' : 'text-red-600'}`}>
                      {clientName && clientVAT ? '✓ Complete' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Invoice Items:</span>
                    <span className={`text-sm ${items.length > 0 && items[0].description ? 'text-green-600' : 'text-red-600'}`}>
                      {items.length > 0 && items[0].description ? '✓ Complete' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Deduction Reason:</span>
                    <span className={`text-sm ${deductionAmount > 0 ? (deductionReason.trim() ? 'text-green-600' : 'text-red-600') : 'text-gray-400'}`}>
                      {deductionAmount > 0 ? (deductionReason.trim() ? '✓ Complete' : '✗ Required') : 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
