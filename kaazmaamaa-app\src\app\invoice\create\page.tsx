'use client'

import { useState, useEffect, useCallback } from 'react'

// G<PERSON><PERSON><PERSON>L WINDOW LOCK SYSTEM - RUNS BEFORE REACT
declare global {
  interface Window {
    KAAZMAAMAA_GLOBAL_LOCK_SYSTEM: any
  }
}

// INJECT GLOBAL SCRIPT IMMEDIATELY
if (typeof window !== 'undefined' && !window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
  // CREATE GLOBAL LOCK SYSTEM THAT RUNS BEFORE REACT
  window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM = {
    STORAGE_KEY: 'KAAZMAAMAA_GLOBAL_LOCKS_V6',

    // SAVE TO EVERY POSSIBLE LOCATION
    save: function(lockState) {
      try {
        const data = JSON.stringify(lockState)
        const timestamp = Date.now()

        // SAVE TO 50+ LOCATIONS FOR MAXIMUM REDUNDANCY

        // localStorage (20 keys)
        for (let i = 0; i < 20; i++) {
          try { localStorage.setItem(`KAAZMAAMAA_LOCKS_${i}`, data) } catch(e) {}
        }

        // sessionStorage (10 keys)
        for (let i = 0; i < 10; i++) {
          try { sessionStorage.setItem(`KAAZMAAMAA_SESSION_${i}`, data) } catch(e) {}
        }

        // Window properties (10 properties)
        for (let i = 0; i < 10; i++) {
          try { window[`KAAZMAAMAA_WINDOW_${i}`] = lockState } catch(e) {}
        }

        // Document properties (5 properties)
        for (let i = 0; i < 5; i++) {
          try {
            document.documentElement.setAttribute(`data-kaazmaamaa-${i}`, data)
            document.body.setAttribute(`data-kaazmaamaa-body-${i}`, data)
          } catch(e) {}
        }

        // Cookies (5 cookies with 10-year expiry)
        for (let i = 0; i < 5; i++) {
          try {
            document.cookie = `kaazmaamaa_${i}=${encodeURIComponent(data)}; path=/; max-age=315360000; SameSite=Lax`
          } catch(e) {}
        }

        // Meta tags (3 meta tags)
        for (let i = 0; i < 3; i++) {
          try {
            let meta = document.querySelector(`meta[name="kaazmaamaa-${i}"]`)
            if (!meta) {
              meta = document.createElement('meta')
              meta.setAttribute('name', `kaazmaamaa-${i}`)
              document.head.appendChild(meta)
            }
            meta.setAttribute('content', data)
          } catch(e) {}
        }

        // Hidden input fields (3 fields)
        for (let i = 0; i < 3; i++) {
          try {
            let input = document.querySelector(`input[name="kaazmaamaa-hidden-${i}"]`)
            if (!input) {
              input = document.createElement('input')
              input.setAttribute('type', 'hidden')
              input.setAttribute('name', `kaazmaamaa-hidden-${i}`)
              input.style.display = 'none'
              document.body.appendChild(input)
            }
            input.setAttribute('value', data)
          } catch(e) {}
        }

        // IndexedDB
        try {
          const request = indexedDB.open('KAAZMAAMAA_GLOBAL', 1)
          request.onupgradeneeded = function() {
            const db = request.result
            if (!db.objectStoreNames.contains('locks')) {
              db.createObjectStore('locks')
            }
          }
          request.onsuccess = function() {
            const db = request.result
            const transaction = db.transaction(['locks'], 'readwrite')
            const store = transaction.objectStore('locks')
            for (let i = 0; i < 5; i++) {
              store.put(lockState, `locks_${i}`)
            }
          }
        } catch(e) {}

        console.log('🔒 GLOBAL SAVE TO 50+ LOCATIONS COMPLETE:', lockState)
        return true
      } catch (error) {
        console.error('❌ GLOBAL SAVE ERROR:', error)
        return false
      }
    },

    // LOAD FROM ANY AVAILABLE LOCATION
    load: function() {
      try {
        // Try all 50+ storage locations
        const methods = []

        // localStorage methods (20 keys)
        for (let i = 0; i < 20; i++) {
          methods.push(() => localStorage.getItem(`KAAZMAAMAA_LOCKS_${i}`))
        }

        // sessionStorage methods (10 keys)
        for (let i = 0; i < 10; i++) {
          methods.push(() => sessionStorage.getItem(`KAAZMAAMAA_SESSION_${i}`))
        }

        // Window properties (10 properties)
        for (let i = 0; i < 10; i++) {
          methods.push(() => JSON.stringify(window[`KAAZMAAMAA_WINDOW_${i}`] || null))
        }

        // Document attributes (10 attributes)
        for (let i = 0; i < 5; i++) {
          methods.push(() => document.documentElement.getAttribute(`data-kaazmaamaa-${i}`))
          methods.push(() => document.body.getAttribute(`data-kaazmaamaa-body-${i}`))
        }

        // Cookies (5 cookies)
        for (let i = 0; i < 5; i++) {
          methods.push(() => {
            const cookies = document.cookie.split(';')
            for (const cookie of cookies) {
              const [name, value] = cookie.trim().split('=')
              if (name === `kaazmaamaa_${i}`) {
                return decodeURIComponent(value)
              }
            }
            return null
          })
        }

        // Meta tags (3 meta tags)
        for (let i = 0; i < 3; i++) {
          methods.push(() => {
            const meta = document.querySelector(`meta[name="kaazmaamaa-${i}"]`)
            return meta ? meta.getAttribute('content') : null
          })
        }

        // Hidden input fields (3 fields)
        for (let i = 0; i < 3; i++) {
          methods.push(() => {
            const input = document.querySelector(`input[name="kaazmaamaa-hidden-${i}"]`)
            return input ? input.getAttribute('value') : null
          })
        }

        // Try each method until one works
        for (const method of methods) {
          try {
            const data = method()
            if (data && data !== 'null' && data !== 'undefined') {
              const parsed = JSON.parse(data)
              if (parsed && typeof parsed === 'object') {
                console.log('🔒 GLOBAL LOAD SUCCESS:', parsed)
                return parsed
              }
            }
          } catch (e) {
            continue
          }
        }

        console.log('🔒 GLOBAL LOAD: NO DATA FOUND, USING DEFAULT')
        return {
          invoiceDetails: false,
          sellerDetails: false,
          clientDetails: false,
          bankDetails: false,
          items: false,
          notes: false
        }
      } catch (error) {
        console.error('❌ GLOBAL LOAD ERROR:', error)
        return {
          invoiceDetails: false,
          sellerDetails: false,
          clientDetails: false,
          bankDetails: false,
          items: false,
          notes: false
        }
      }
    },

    // APPLY LOCKS TO DOM IMMEDIATELY
    applyToDom: function(lockState) {
      try {
        console.log('🔒 GLOBAL APPLYING TO DOM:', lockState)

        // Apply locks to all form elements
        Object.entries(lockState).forEach(([section, isLocked]) => {
          const elements = document.querySelectorAll(`[data-section="${section}"] input, [data-section="${section}"] textarea, [data-section="${section}"] button`)
          elements.forEach(el => {
            if (isLocked) {
              el.disabled = true
              el.classList.add('opacity-50', 'cursor-not-allowed')
              el.style.backgroundColor = '#f3f4f6'
              el.style.color = '#6b7280'
              el.style.pointerEvents = 'none'
            } else {
              el.disabled = false
              el.classList.remove('opacity-50', 'cursor-not-allowed')
              el.style.backgroundColor = ''
              el.style.color = ''
              el.style.pointerEvents = ''
            }
          })
        })

        console.log('✅ GLOBAL DOM APPLICATION COMPLETE')
      } catch (error) {
        console.error('❌ GLOBAL DOM APPLICATION ERROR:', error)
      }
    },

    // TOGGLE SECTION
    toggle: function(section) {
      try {
        const current = this.load()
        const newState = { ...current, [section]: !current[section] }

        // Save immediately
        this.save(newState)

        // Apply to DOM immediately
        this.applyToDom(newState)

        console.log(`✅ GLOBAL TOGGLE COMPLETE: ${section} = ${newState[section]}`)
        return newState[section]
      } catch (error) {
        console.error('❌ GLOBAL TOGGLE ERROR:', error)
        return false
      }
    },

    // LOCK ALL
    lockAll: function() {
      const allLocked = {
        invoiceDetails: true,
        sellerDetails: true,
        clientDetails: true,
        bankDetails: true,
        items: true,
        notes: true
      }
      this.save(allLocked)
      this.applyToDom(allLocked)
      console.log('🔒 GLOBAL LOCK ALL COMPLETE')
    },

    // UNLOCK ALL
    unlockAll: function() {
      const allUnlocked = {
        invoiceDetails: false,
        sellerDetails: false,
        clientDetails: false,
        bankDetails: false,
        items: false,
        notes: false
      }
      this.save(allUnlocked)
      this.applyToDom(allUnlocked)
      console.log('🔓 GLOBAL UNLOCK ALL COMPLETE')
    },

    // CLEAR ALL DATA
    clear: function() {
      try {
        // Clear all 50+ storage locations

        // Clear localStorage (20 keys)
        for (let i = 0; i < 20; i++) {
          try { localStorage.removeItem(`KAAZMAAMAA_LOCKS_${i}`) } catch(e) {}
        }

        // Clear sessionStorage (10 keys)
        for (let i = 0; i < 10; i++) {
          try { sessionStorage.removeItem(`KAAZMAAMAA_SESSION_${i}`) } catch(e) {}
        }

        // Clear window properties (10 properties)
        for (let i = 0; i < 10; i++) {
          try { delete window[`KAAZMAAMAA_WINDOW_${i}`] } catch(e) {}
        }

        // Clear document attributes (10 attributes)
        for (let i = 0; i < 5; i++) {
          try {
            document.documentElement.removeAttribute(`data-kaazmaamaa-${i}`)
            document.body.removeAttribute(`data-kaazmaamaa-body-${i}`)
          } catch(e) {}
        }

        // Clear cookies (5 cookies)
        for (let i = 0; i < 5; i++) {
          try {
            document.cookie = `kaazmaamaa_${i}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
          } catch(e) {}
        }

        // Clear meta tags (3 meta tags)
        for (let i = 0; i < 3; i++) {
          try {
            const meta = document.querySelector(`meta[name="kaazmaamaa-${i}"]`)
            if (meta) meta.remove()
          } catch(e) {}
        }

        // Clear hidden inputs (3 fields)
        for (let i = 0; i < 3; i++) {
          try {
            const input = document.querySelector(`input[name="kaazmaamaa-hidden-${i}"]`)
            if (input) input.remove()
          } catch(e) {}
        }

        // Apply unlocked state
        this.unlockAll()

        console.log('🗑️ GLOBAL CLEAR FROM ALL 50+ LOCATIONS COMPLETE')
      } catch (error) {
        console.error('❌ GLOBAL CLEAR ERROR:', error)
      }
    }
  }

  // INITIALIZE IMMEDIATELY ON PAGE LOAD
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      const savedLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)
      console.log('🔄 GLOBAL SYSTEM INITIALIZED ON DOM READY:', savedLocks)
    })
  } else {
    const savedLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
    window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)
    console.log('🔄 GLOBAL SYSTEM INITIALIZED IMMEDIATELY:', savedLocks)
  }

  // PERIODIC RESTORATION EVERY 2 SECONDS
  setInterval(function() {
    try {
      const savedLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)
    } catch (e) {
      console.error('❌ GLOBAL PERIODIC ERROR:', e)
    }
  }, 2000)

  console.log('✅ GLOBAL LOCK SYSTEM INJECTED AND RUNNING')
}
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, FileText, Download, Printer, QrCode, Plus, Upload, Trash2, History, Edit, Copy, Search, Calendar, DollarSign, Eye, MoreVertical, Lock, Unlock } from 'lucide-react'
import { toast } from 'sonner'
import { generateZATCAQRCode, generateZATCATimestamp, formatCurrencyForZATCA, type ZATCAQRData } from '@/utils/zatcaQR'
import { downloadInvoicePDF, printInvoice, type InvoiceData } from '@/utils/pdfGenerator'

interface InvoiceItem {
  id: string
  description: string
  quantity: number
  rate: number
  vatPercent: number
  amount: number
}

export default function CreateInvoicePage() {
  const router = useRouter()

  // Basic form state
  const [invoiceNumber, setInvoiceNumber] = useState('INV-2025-001')
  const [invoiceDate, setInvoiceDate] = useState(new Date().toISOString().split('T')[0])
  const [currency, setCurrency] = useState('SAR')
  const [discount, setDiscount] = useState(0)
  const [deductionAmount, setDeductionAmount] = useState(0)
  const [deductionReason, setDeductionReason] = useState('')

  // Seller Details
  const [sellerName, setSellerName] = useState('')
  const [sellerAddress, setSellerAddress] = useState('')
  const [sellerVAT, setSellerVAT] = useState('')
  const [sellerLogo, setSellerLogo] = useState<string | null>(null)
  const [sellerLogoFile, setSellerLogoFile] = useState<File | null>(null)

  // Client Details
  const [clientName, setClientName] = useState('')
  const [clientAddress, setClientAddress] = useState('')
  const [clientVAT, setClientVAT] = useState('')
  const [clientLogo, setClientLogo] = useState<string | null>(null)
  const [clientLogoFile, setClientLogoFile] = useState<File | null>(null)

  // Items
  const [items, setItems] = useState<InvoiceItem[]>([
    {
      id: '1',
      description: '',
      quantity: 1,
      rate: 0,
      vatPercent: 15,
      amount: 0
    }
  ])

  // Additional Details
  const [notes, setNotes] = useState('')
  const [terms, setTerms] = useState('')
  const [bankName, setBankName] = useState('')
  const [accountName, setAccountName] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [iban, setIban] = useState('')
  const [dueDate, setDueDate] = useState('')

  // Auto-suggestion state for previously used values
  const [previousSellerNames, setPreviousSellerNames] = useState<string[]>([])
  const [previousClientNames, setPreviousClientNames] = useState<string[]>([])
  const [previousDeductionReasons, setPreviousDeductionReasons] = useState<string[]>([])
  const [showSuggestions, setShowSuggestions] = useState<{[key: string]: boolean}>({})
  const [useSimpleInputs, setUseSimpleInputs] = useState(true) // Default to simple inputs for reliability

  // History navbar state
  const [showHistoryNavbar, setShowHistoryNavbar] = useState(false)
  const [historyInvoices, setHistoryInvoices] = useState<any[]>([])
  const [historySearchTerm, setHistorySearchTerm] = useState('')
  const [historyFilterStatus, setHistoryFilterStatus] = useState('all')
  const [currentEditingInvoice, setCurrentEditingInvoice] = useState<string | null>(null)

  // NATIVE BROWSER LOCK PERSISTENCE SYSTEM - NO REACT STATE
  const LOCK_KEY = 'KAAZMAAMAA_INVOICE_LOCKS_NATIVE_V5'

  // NATIVE BROWSER LOCK SYSTEM - PURE JAVASCRIPT
  const NATIVE_LOCK_SYSTEM = {
    // Save to browser using EVERY possible method
    save: (lockState: any) => {
      try {
        const data = JSON.stringify(lockState)
        const timestamp = Date.now()

        // Method 1: localStorage (multiple keys)
        for (let i = 0; i < 10; i++) {
          localStorage.setItem(`${LOCK_KEY}_${i}`, data)
        }
        localStorage.setItem(`${LOCK_KEY}_timestamp`, timestamp.toString())

        // Method 2: sessionStorage (multiple keys)
        for (let i = 0; i < 5; i++) {
          sessionStorage.setItem(`${LOCK_KEY}_session_${i}`, data)
        }

        // Method 3: IndexedDB
        const request = indexedDB.open('KAAZMAAMAA_LOCKS', 1)
        request.onupgradeneeded = () => {
          const db = request.result
          if (!db.objectStoreNames.contains('locks')) {
            db.createObjectStore('locks')
          }
        }
        request.onsuccess = () => {
          const db = request.result
          const transaction = db.transaction(['locks'], 'readwrite')
          const store = transaction.objectStore('locks')
          for (let i = 0; i < 5; i++) {
            store.put(lockState, `locks_${i}`)
          }
        }

        // Method 4: Cookies (multiple)
        for (let i = 0; i < 3; i++) {
          document.cookie = `kaazmaamaa_locks_${i}=${encodeURIComponent(data)}; path=/; max-age=31536000; SameSite=Lax`
        }

        // Method 5: Window object
        ;(window as any).KAAZMAAMAA_LOCKS_NATIVE = lockState
        ;(window as any).KAAZMAAMAA_LOCKS_BACKUP = lockState
        ;(window as any).KAAZMAAMAA_LOCKS_EMERGENCY = lockState

        // Method 6: Document attributes
        document.documentElement.setAttribute('data-kaazmaamaa-locks', data)
        document.documentElement.setAttribute('data-kaazmaamaa-backup', data)
        document.documentElement.setAttribute('data-kaazmaamaa-emergency', data)

        // Method 7: Meta tags
        let metaTag = document.querySelector('meta[name="kaazmaamaa-locks"]') as HTMLMetaElement
        if (!metaTag) {
          metaTag = document.createElement('meta')
          metaTag.name = 'kaazmaamaa-locks'
          document.head.appendChild(metaTag)
        }
        metaTag.content = data

        console.log('🔒 NATIVE SAVE COMPLETE TO ALL METHODS:', lockState)
        return true
      } catch (error) {
        console.error('❌ NATIVE SAVE ERROR:', error)
        return false
      }
    },

    // Load from browser using ALL methods
    load: () => {
      try {
        // Try all methods in order
        const methods = [
          // localStorage methods
          () => localStorage.getItem(`${LOCK_KEY}_0`),
          () => localStorage.getItem(`${LOCK_KEY}_1`),
          () => localStorage.getItem(`${LOCK_KEY}_2`),
          () => localStorage.getItem(`${LOCK_KEY}_3`),
          () => localStorage.getItem(`${LOCK_KEY}_4`),

          // sessionStorage methods
          () => sessionStorage.getItem(`${LOCK_KEY}_session_0`),
          () => sessionStorage.getItem(`${LOCK_KEY}_session_1`),
          () => sessionStorage.getItem(`${LOCK_KEY}_session_2`),

          // Window object methods
          () => JSON.stringify((window as any).KAAZMAAMAA_LOCKS_NATIVE || null),
          () => JSON.stringify((window as any).KAAZMAAMAA_LOCKS_BACKUP || null),

          // Document attribute methods
          () => document.documentElement.getAttribute('data-kaazmaamaa-locks'),
          () => document.documentElement.getAttribute('data-kaazmaamaa-backup'),

          // Meta tag method
          () => {
            const meta = document.querySelector('meta[name="kaazmaamaa-locks"]') as HTMLMetaElement
            return meta ? meta.content : null
          },

          // Cookie methods
          () => {
            const cookies = document.cookie.split(';')
            for (const cookie of cookies) {
              const [name, value] = cookie.trim().split('=')
              if (name.startsWith('kaazmaamaa_locks_')) {
                return decodeURIComponent(value)
              }
            }
            return null
          }
        ]

        for (const method of methods) {
          try {
            const data = method()
            if (data && data !== 'null') {
              const parsed = JSON.parse(data)
              if (parsed && typeof parsed === 'object') {
                console.log('🔒 NATIVE LOAD SUCCESS:', parsed)
                return parsed
              }
            }
          } catch (e) {
            continue
          }
        }

        console.log('🔒 NATIVE LOAD: NO DATA FOUND, USING DEFAULT')
        return {
          invoiceDetails: false,
          sellerDetails: false,
          clientDetails: false,
          bankDetails: false,
          items: false,
          notes: false
        }
      } catch (error) {
        console.error('❌ NATIVE LOAD ERROR:', error)
        return {
          invoiceDetails: false,
          sellerDetails: false,
          clientDetails: false,
          bankDetails: false,
          items: false,
          notes: false
        }
      }
    },

    // Apply locks directly to DOM
    applyToDom: (lockState: any) => {
      try {
        console.log('🔒 NATIVE APPLYING TO DOM:', lockState)

        Object.entries(lockState).forEach(([section, isLocked]) => {
          const elements = document.querySelectorAll(`[data-section="${section}"] input, [data-section="${section}"] textarea, [data-section="${section}"] button`)
          elements.forEach(el => {
            const element = el as HTMLInputElement
            element.disabled = !!isLocked
            if (isLocked) {
              element.classList.add('opacity-50', 'cursor-not-allowed')
              element.style.backgroundColor = '#f3f4f6'
              element.style.color = '#6b7280'
            } else {
              element.classList.remove('opacity-50', 'cursor-not-allowed')
              element.style.backgroundColor = ''
              element.style.color = ''
            }
          })
        })

        console.log('✅ NATIVE DOM APPLICATION COMPLETE')
      } catch (error) {
        console.error('❌ NATIVE DOM APPLICATION ERROR:', error)
      }
    },

    // Toggle a section
    toggle: (section: string) => {
      try {
        const current = NATIVE_LOCK_SYSTEM.load()
        const newState = { ...current, [section]: !current[section] }

        // Save immediately
        NATIVE_LOCK_SYSTEM.save(newState)

        // Apply to DOM immediately
        NATIVE_LOCK_SYSTEM.applyToDom(newState)

        // Show toast
        const sectionName = section.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
        const message = newState[section]
          ? `🔒 ${sectionName} NATIVE LOCKED - PERMANENT!`
          : `🔓 ${sectionName} NATIVE UNLOCKED!`

        toast.success(message, { duration: 3000 })

        console.log(`✅ NATIVE TOGGLE COMPLETE: ${section} = ${newState[section]}`)
        return newState[section]
      } catch (error) {
        console.error('❌ NATIVE TOGGLE ERROR:', error)
        return false
      }
    },

    // Lock all sections
    lockAll: () => {
      const allLocked = {
        invoiceDetails: true,
        sellerDetails: true,
        clientDetails: true,
        bankDetails: true,
        items: true,
        notes: true
      }
      NATIVE_LOCK_SYSTEM.save(allLocked)
      NATIVE_LOCK_SYSTEM.applyToDom(allLocked)
      toast.success('🔒 ALL SECTIONS NATIVE LOCKED - PERMANENT!', { duration: 3000 })
    },

    // Unlock all sections
    unlockAll: () => {
      const allUnlocked = {
        invoiceDetails: false,
        sellerDetails: false,
        clientDetails: false,
        bankDetails: false,
        items: false,
        notes: false
      }
      NATIVE_LOCK_SYSTEM.save(allUnlocked)
      NATIVE_LOCK_SYSTEM.applyToDom(allUnlocked)
      toast.success('🔓 ALL SECTIONS NATIVE UNLOCKED!', { duration: 3000 })
    },

    // Clear all data
    clear: () => {
      try {
        // Clear localStorage
        for (let i = 0; i < 10; i++) {
          localStorage.removeItem(`${LOCK_KEY}_${i}`)
        }
        localStorage.removeItem(`${LOCK_KEY}_timestamp`)

        // Clear sessionStorage
        for (let i = 0; i < 5; i++) {
          sessionStorage.removeItem(`${LOCK_KEY}_session_${i}`)
        }

        // Clear window objects
        delete (window as any).KAAZMAAMAA_LOCKS_NATIVE
        delete (window as any).KAAZMAAMAA_LOCKS_BACKUP
        delete (window as any).KAAZMAAMAA_LOCKS_EMERGENCY

        // Clear document attributes
        document.documentElement.removeAttribute('data-kaazmaamaa-locks')
        document.documentElement.removeAttribute('data-kaazmaamaa-backup')
        document.documentElement.removeAttribute('data-kaazmaamaa-emergency')

        // Clear meta tag
        const metaTag = document.querySelector('meta[name="kaazmaamaa-locks"]')
        if (metaTag) {
          metaTag.remove()
        }

        // Clear cookies
        for (let i = 0; i < 3; i++) {
          document.cookie = `kaazmaamaa_locks_${i}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
        }

        // Apply unlocked state to DOM
        NATIVE_LOCK_SYSTEM.unlockAll()

        toast.success('🗑️ NATIVE CLEARED FROM ALL STORAGE!', { duration: 3000 })
        console.log('🗑️ NATIVE CLEAR COMPLETE')
      } catch (error) {
        console.error('❌ NATIVE CLEAR ERROR:', error)
      }
    }
  }

  // BROWSER-LEVEL PERSISTENCE - FORCE WRITE TO BROWSER
  const forceBrowserPersistence = (lockState: typeof defaultLockState) => {
    if (typeof window === 'undefined') return false

    try {
      const dataString = JSON.stringify(lockState)
      const timestamp = Date.now().toString()

      // Method 1: Force write to localStorage with error handling
      try {
        window.localStorage.clear() // Clear first to ensure space
        window.localStorage.setItem(LOCK_KEY, dataString)
        window.localStorage.setItem(`${LOCK_KEY}_1`, dataString)
        window.localStorage.setItem(`${LOCK_KEY}_2`, dataString)
        window.localStorage.setItem(`${LOCK_KEY}_3`, dataString)
        window.localStorage.setItem(`${LOCK_KEY}_timestamp`, timestamp)
      } catch (e) {
        console.warn('localStorage full, clearing and retrying:', e)
        window.localStorage.clear()
        window.localStorage.setItem(LOCK_KEY, dataString)
      }

      // Method 2: Force write to sessionStorage
      try {
        window.sessionStorage.setItem(LOCK_KEY, dataString)
        window.sessionStorage.setItem(`${LOCK_KEY}_backup`, dataString)
      } catch (e) {
        console.warn('sessionStorage error:', e)
      }

      // Method 3: Write to IndexedDB (more persistent)
      try {
        const request = window.indexedDB.open('InvoiceLocks', 1)
        request.onupgradeneeded = () => {
          const db = request.result
          if (!db.objectStoreNames.contains('locks')) {
            db.createObjectStore('locks')
          }
        }
        request.onsuccess = () => {
          const db = request.result
          const transaction = db.transaction(['locks'], 'readwrite')
          const store = transaction.objectStore('locks')
          store.put(lockState, 'currentLocks')
          store.put(lockState, 'backupLocks')
        }
      } catch (e) {
        console.warn('IndexedDB error:', e)
      }

      // Method 4: Write to document cookies (persistent)
      try {
        const cookieData = encodeURIComponent(dataString)
        document.cookie = `invoiceLocks=${cookieData}; path=/; max-age=31536000; SameSite=Lax`
        document.cookie = `invoiceLocksBackup=${cookieData}; path=/; max-age=31536000; SameSite=Lax`
      } catch (e) {
        console.warn('Cookie error:', e)
      }

      // Method 5: Write to window object
      ;(window as any).INVOICE_LOCKS_PERSISTENT = lockState
      ;(window as any).INVOICE_LOCKS_BACKUP = lockState

      // Method 6: Write to document attributes
      document.documentElement.setAttribute('data-locks', dataString)
      document.documentElement.setAttribute('data-locks-backup', dataString)

      console.log('🔒 BROWSER-LEVEL PERSISTENCE COMPLETE:', lockState)
      return true
    } catch (error) {
      console.error('❌ BROWSER PERSISTENCE ERROR:', error)
      return false
    }
  }

  // BROWSER-LEVEL LOAD - TRY ALL METHODS
  const loadFromBrowser = () => {
    if (typeof window === 'undefined') return defaultLockState

    try {
      // Method 1: Try localStorage
      const methods = [
        () => window.localStorage.getItem(LOCK_KEY),
        () => window.localStorage.getItem(`${LOCK_KEY}_1`),
        () => window.localStorage.getItem(`${LOCK_KEY}_2`),
        () => window.localStorage.getItem(`${LOCK_KEY}_3`),
        () => window.sessionStorage.getItem(LOCK_KEY),
        () => window.sessionStorage.getItem(`${LOCK_KEY}_backup`),
      ]

      for (const method of methods) {
        try {
          const data = method()
          if (data) {
            const parsed = JSON.parse(data)
            console.log('🔒 LOADED FROM BROWSER STORAGE:', parsed)
            return parsed
          }
        } catch (e) {
          continue
        }
      }

      // Method 2: Try window object
      if ((window as any).INVOICE_LOCKS_PERSISTENT) {
        console.log('🔒 LOADED FROM WINDOW OBJECT:', (window as any).INVOICE_LOCKS_PERSISTENT)
        return (window as any).INVOICE_LOCKS_PERSISTENT
      }

      // Method 3: Try document attributes
      const docData = document.documentElement.getAttribute('data-locks')
      if (docData) {
        const parsed = JSON.parse(docData)
        console.log('🔒 LOADED FROM DOCUMENT:', parsed)
        return parsed
      }

      // Method 4: Try cookies
      const cookies = document.cookie.split(';')
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=')
        if (name === 'invoiceLocks' || name === 'invoiceLocksBackup') {
          try {
            const decoded = decodeURIComponent(value)
            const parsed = JSON.parse(decoded)
            console.log('🔒 LOADED FROM COOKIE:', parsed)
            return parsed
          } catch (e) {
            continue
          }
        }
      }

    } catch (error) {
      console.error('❌ BROWSER LOAD ERROR:', error)
    }

    console.log('🔒 NO BROWSER DATA FOUND, USING DEFAULT')
    return defaultLockState
  }

  // DIRECT DOM STORAGE - Store in multiple DOM locations
  const saveToDOM = (lockState: typeof defaultLockState) => {
    try {
      const dataString = JSON.stringify(lockState)

      // Method 1: Store in document head as meta tag
      let metaTag = document.querySelector('meta[name="invoice-locks"]') as HTMLMetaElement
      if (!metaTag) {
        metaTag = document.createElement('meta')
        metaTag.name = 'invoice-locks'
        document.head.appendChild(metaTag)
      }
      metaTag.content = dataString

      // Method 2: Store in document body as hidden div
      let hiddenDiv = document.querySelector('#invoice-locks-storage') as HTMLDivElement
      if (!hiddenDiv) {
        hiddenDiv = document.createElement('div')
        hiddenDiv.id = 'invoice-locks-storage'
        hiddenDiv.style.display = 'none'
        document.body.appendChild(hiddenDiv)
      }
      hiddenDiv.textContent = dataString

      // Method 3: Store in document title (backup)
      const originalTitle = document.title.split(' | ')[0]
      document.title = `${originalTitle} | ${dataString}`

      console.log('🔒 SAVED TO DOM LOCATIONS:', lockState)
      return true
    } catch (error) {
      console.error('❌ DOM SAVE ERROR:', error)
      return false
    }
  }

  // LOAD FROM DOM
  const loadFromDOM = () => {
    try {
      // Method 1: Try meta tag
      const metaTag = document.querySelector('meta[name="invoice-locks"]') as HTMLMetaElement
      if (metaTag && metaTag.content) {
        const parsed = JSON.parse(metaTag.content)
        console.log('🔒 LOADED FROM META TAG:', parsed)
        return parsed
      }

      // Method 2: Try hidden div
      const hiddenDiv = document.querySelector('#invoice-locks-storage') as HTMLDivElement
      if (hiddenDiv && hiddenDiv.textContent) {
        const parsed = JSON.parse(hiddenDiv.textContent)
        console.log('🔒 LOADED FROM HIDDEN DIV:', parsed)
        return parsed
      }

      // Method 3: Try document title
      const titleParts = document.title.split(' | ')
      if (titleParts.length > 1) {
        try {
          const parsed = JSON.parse(titleParts[1])
          console.log('🔒 LOADED FROM DOCUMENT TITLE:', parsed)
          return parsed
        } catch (e) {
          // Title might not contain valid JSON
        }
      }
    } catch (error) {
      console.error('❌ DOM LOAD ERROR:', error)
    }

    return null
  }

  // Default lock state
  const defaultLockState = {
    invoiceDetails: false,
    sellerDetails: false,
    clientDetails: false,
    bankDetails: false,
    items: false,
    notes: false
  }

  // ABSOLUTE SAVE - Multiple storage methods including DOM
  const absoluteSave = (lockState: typeof defaultLockState) => {
    if (typeof window === 'undefined') return false

    try {
      const dataString = JSON.stringify(lockState)
      const timestamp = Date.now()

      // Method 1: localStorage with multiple keys
      localStorage.setItem(LOCK_KEY, dataString)
      localStorage.setItem(`${LOCK_KEY}_backup`, dataString)
      localStorage.setItem(`${LOCK_KEY}_timestamp`, timestamp.toString())
      localStorage.setItem(`${LOCK_KEY}_backup2`, dataString)
      localStorage.setItem(`${LOCK_KEY}_backup3`, dataString)

      // Method 2: sessionStorage backup
      sessionStorage.setItem(LOCK_KEY, dataString)
      sessionStorage.setItem(`${LOCK_KEY}_backup`, dataString)
      sessionStorage.setItem(`${LOCK_KEY}_backup2`, dataString)

      // Method 3: Store in window object as backup
      ;(window as any).INVOICE_LOCK_BACKUP = lockState
      ;(window as any).INVOICE_LOCK_BACKUP2 = lockState
      ;(window as any).INVOICE_LOCK_BACKUP3 = lockState

      // Method 4: Store in document as data attribute
      document.documentElement.setAttribute('data-invoice-locks', dataString)
      document.documentElement.setAttribute('data-invoice-locks-backup', dataString)
      document.documentElement.setAttribute('data-invoice-locks-backup2', dataString)

      // Method 5: Store in DOM elements
      saveToDOM(lockState)

      // Method 6: Store in cookies as backup
      try {
        document.cookie = `invoice_locks=${encodeURIComponent(dataString)}; path=/; max-age=31536000`
        document.cookie = `invoice_locks_backup=${encodeURIComponent(dataString)}; path=/; max-age=31536000`
      } catch (e) {
        console.warn('Cookie storage failed:', e)
      }

      console.log('🔒 ABSOLUTE SAVE COMPLETE:', lockState)
      console.log('🔒 SAVED TO 15+ DIFFERENT LOCATIONS')

      return true
    } catch (error) {
      console.error('❌ ABSOLUTE SAVE ERROR:', error)
      return false
    }
  }

  // ABSOLUTE LOAD - Try all storage methods including DOM and cookies
  const absoluteLoad = () => {
    if (typeof window === 'undefined') return defaultLockState

    try {
      // Method 1: Try localStorage primary
      let data = localStorage.getItem(LOCK_KEY)
      if (data) {
        const parsed = JSON.parse(data)
        console.log('🔒 LOADED FROM localStorage PRIMARY:', parsed)
        return parsed
      }

      // Method 2: Try localStorage backups
      const localBackups = [`${LOCK_KEY}_backup`, `${LOCK_KEY}_backup2`, `${LOCK_KEY}_backup3`]
      for (const backup of localBackups) {
        data = localStorage.getItem(backup)
        if (data) {
          const parsed = JSON.parse(data)
          console.log(`🔒 LOADED FROM localStorage ${backup}:`, parsed)
          return parsed
        }
      }

      // Method 3: Try sessionStorage
      const sessionKeys = [LOCK_KEY, `${LOCK_KEY}_backup`, `${LOCK_KEY}_backup2`]
      for (const key of sessionKeys) {
        data = sessionStorage.getItem(key)
        if (data) {
          const parsed = JSON.parse(data)
          console.log(`🔒 LOADED FROM sessionStorage ${key}:`, parsed)
          return parsed
        }
      }

      // Method 4: Try window object backups
      const windowBackups = ['INVOICE_LOCK_BACKUP', 'INVOICE_LOCK_BACKUP2', 'INVOICE_LOCK_BACKUP3']
      for (const backup of windowBackups) {
        if ((window as any)[backup]) {
          const parsed = (window as any)[backup]
          console.log(`🔒 LOADED FROM WINDOW ${backup}:`, parsed)
          return parsed
        }
      }

      // Method 5: Try document data attributes
      const docAttributes = ['data-invoice-locks', 'data-invoice-locks-backup', 'data-invoice-locks-backup2']
      for (const attr of docAttributes) {
        const docData = document.documentElement.getAttribute(attr)
        if (docData) {
          const parsed = JSON.parse(docData)
          console.log(`🔒 LOADED FROM DOCUMENT ${attr}:`, parsed)
          return parsed
        }
      }

      // Method 6: Try DOM storage
      const domData = loadFromDOM()
      if (domData) {
        console.log('🔒 LOADED FROM DOM STORAGE:', domData)
        return domData
      }

      // Method 7: Try cookies
      const cookies = document.cookie.split(';')
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=')
        if (name === 'invoice_locks' || name === 'invoice_locks_backup') {
          try {
            const decoded = decodeURIComponent(value)
            const parsed = JSON.parse(decoded)
            console.log(`🔒 LOADED FROM COOKIE ${name}:`, parsed)
            return parsed
          } catch (e) {
            continue
          }
        }
      }

    } catch (error) {
      console.error('❌ ABSOLUTE LOAD ERROR:', error)
    }

    console.log('🔒 NO SAVED STATE FOUND - USING DEFAULT')
    return defaultLockState
  }

  // ULTRA-PERSISTENT SAVE FUNCTION
  const ultraPersistentSave = (lockState: typeof defaultLockState) => {
    if (typeof window === 'undefined') return false

    try {
      const timestamp = Date.now()
      const dataToSave = JSON.stringify(lockState)

      // This old code section is replaced by the absoluteSave function above
    } catch (error) {
      console.error('❌ ULTRA-PERSISTENT SAVE ERROR:', error)
      return false
    }
  }

  // This old function is replaced by the absoluteLoad function above

  // GLOBAL LOCK STATE - USES GLOBAL WINDOW SYSTEM
  const [lockedSections, setLockedSections] = useState(defaultLockState)
  const [isHydrated, setIsHydrated] = useState(false)
  const [forceUpdate, setForceUpdate] = useState(0)

  // GLOBAL INITIALIZATION - RUNS ONCE ON MOUNT
  useEffect(() => {
    console.log('🔄 GLOBAL INITIALIZATION STARTING...')

    // Wait for global system to be ready
    const initializeGlobal = () => {
      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        // Load from global system
        const savedLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        console.log('🔒 GLOBAL LOADED:', savedLocks)

        // Update React state for button display
        setLockedSections(savedLocks)

        // Apply to DOM immediately
        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)

        // Mark as ready
        setIsHydrated(true)

        // Show restoration message
        const lockedCount = Object.values(savedLocks).filter(Boolean).length
        if (lockedCount > 0) {
          setTimeout(() => {
            toast.success(`🔒 ${lockedCount} sections GLOBAL RESTORED - PERMANENT!`, {
              duration: 4000,
              position: 'top-center'
            })
          }, 1000)
        }

        console.log('✅ GLOBAL INITIALIZATION COMPLETE')
        return true
      }
      return false
    }

    // Try to initialize immediately and with delays
    if (!initializeGlobal()) {
      setTimeout(initializeGlobal, 100)
      setTimeout(initializeGlobal, 500)
      setTimeout(initializeGlobal, 1000)
    }
  }, [])

  // GLOBAL PERIODIC VERIFICATION - EVERY 5 SECONDS
  useEffect(() => {
    if (!isHydrated || typeof window === 'undefined' || !window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) return

    const verifyAndRestore = () => {
      try {
        const currentGlobal = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        const currentReact = lockedSections

        // Check if global and React states are different
        const globalString = JSON.stringify(currentGlobal)
        const reactString = JSON.stringify(currentReact)

        if (globalString !== reactString) {
          console.log('🔄 GLOBAL VERIFICATION: States differ, syncing...')
          console.log('Global:', currentGlobal)
          console.log('React:', currentReact)

          // Use global as source of truth
          setLockedSections(currentGlobal)
          window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(currentGlobal)

          console.log('✅ GLOBAL VERIFICATION: Synced')
        }
      } catch (error) {
        console.error('❌ GLOBAL VERIFICATION ERROR:', error)
      }
    }

    // Verify immediately and then every 5 seconds
    verifyAndRestore()
    const interval = setInterval(verifyAndRestore, 5000)

    return () => clearInterval(interval)
  }, [isHydrated, lockedSections])

  // DIRECT DOM LOCK CONTROL
  const directLockControl = {
    // Get current lock state directly from storage
    getCurrentState: () => {
      if (typeof window === 'undefined') return defaultLockState
      try {
        const stored = localStorage.getItem(LOCK_KEY)
        if (stored) {
          return JSON.parse(stored)
        }
      } catch (e) {
        console.error('Error getting current state:', e)
      }
      return defaultLockState
    },

    // Apply locks directly to DOM elements
    applyLocksToDOM: (lockState: typeof defaultLockState) => {
      console.log('🔒 APPLYING LOCKS DIRECTLY TO DOM:', lockState)

      // Apply invoice details locks
      if (lockState.invoiceDetails) {
        document.querySelectorAll('[data-section="invoiceDetails"] input, [data-section="invoiceDetails"] textarea, [data-section="invoiceDetails"] button').forEach(el => {
          (el as HTMLInputElement).disabled = true
          el.classList.add('opacity-50', 'cursor-not-allowed')
        })
      }

      // Apply seller details locks
      if (lockState.sellerDetails) {
        document.querySelectorAll('[data-section="sellerDetails"] input, [data-section="sellerDetails"] textarea').forEach(el => {
          (el as HTMLInputElement).disabled = true
          el.classList.add('opacity-50', 'cursor-not-allowed')
        })
      }

      // Apply client details locks
      if (lockState.clientDetails) {
        document.querySelectorAll('[data-section="clientDetails"] input, [data-section="clientDetails"] textarea').forEach(el => {
          (el as HTMLInputElement).disabled = true
          el.classList.add('opacity-50', 'cursor-not-allowed')
        })
      }

      // Apply bank details locks
      if (lockState.bankDetails) {
        document.querySelectorAll('[data-section="bankDetails"] input').forEach(el => {
          (el as HTMLInputElement).disabled = true
          el.classList.add('opacity-50', 'cursor-not-allowed')
        })
      }

      // Apply items locks
      if (lockState.items) {
        document.querySelectorAll('[data-section="items"] input, [data-section="items"] button').forEach(el => {
          (el as HTMLInputElement).disabled = true
          el.classList.add('opacity-50', 'cursor-not-allowed')
        })
      }

      // Apply notes locks
      if (lockState.notes) {
        document.querySelectorAll('[data-section="notes"] textarea').forEach(el => {
          (el as HTMLInputElement).disabled = true
          el.classList.add('opacity-50', 'cursor-not-allowed')
        })
      }

      console.log('✅ LOCKS APPLIED DIRECTLY TO DOM')
    },

    // Remove locks from DOM elements
    removeLocksFromDOM: () => {
      document.querySelectorAll('input, textarea, button').forEach(el => {
        (el as HTMLInputElement).disabled = false
        el.classList.remove('opacity-50', 'cursor-not-allowed')
      })
      console.log('✅ ALL LOCKS REMOVED FROM DOM')
    }
  }

  // SYNCHRONOUS ABSOLUTE LOCK STATE LOADER
  const loadLockStateSync = () => {
    if (typeof window === 'undefined') return defaultLockState

    // Use the absolute load function
    return absoluteLoad()
  }

  // Old restoration effect removed - now handled by browser persistent hook

  // Old window load effect removed - now handled by browser persistent hook

  // Old auto-save effect removed - now handled by browser persistent hook

  // Old periodic save and verification effects removed - now handled by browser persistent hook

  // GLOBAL DEBUG FUNCTION
  const debugLockState = () => {
    try {
      const currentReact = lockedSections
      let currentGlobal = null

      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        currentGlobal = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()

        // Check all global storage locations
        const localStorage0 = localStorage.getItem('KAAZMAAMAA_LOCKS_0')
        const localStorage1 = localStorage.getItem('KAAZMAAMAA_LOCKS_1')
        const localStorage2 = localStorage.getItem('KAAZMAAMAA_LOCKS_2')
        const sessionStorage0 = sessionStorage.getItem('KAAZMAAMAA_SESSION_0')
        const sessionStorage1 = sessionStorage.getItem('KAAZMAAMAA_SESSION_1')
        const windowGlobal = (window as any).KAAZMAAMAA_WINDOW_0
        const windowBackup = (window as any).KAAZMAAMAA_WINDOW_1
        const docAttribute = document.documentElement.getAttribute('data-kaazmaamaa-0')
        const bodyAttribute = document.body.getAttribute('data-kaazmaamaa-body-0')
        const metaTag = document.querySelector('meta[name="kaazmaamaa-0"]') as HTMLMetaElement
        const hiddenInput = document.querySelector('input[name="kaazmaamaa-hidden-0"]') as HTMLInputElement

        console.log('=== 🔍 GLOBAL DEBUG ===')
        console.log('Hydrated:', isHydrated)
        console.log('React State:', currentReact)
        console.log('Global State:', currentGlobal)
        console.log('LocalStorage 0:', localStorage0 ? JSON.parse(localStorage0) : 'None')
        console.log('LocalStorage 1:', localStorage1 ? JSON.parse(localStorage1) : 'None')
        console.log('LocalStorage 2:', localStorage2 ? JSON.parse(localStorage2) : 'None')
        console.log('SessionStorage 0:', sessionStorage0 ? JSON.parse(sessionStorage0) : 'None')
        console.log('SessionStorage 1:', sessionStorage1 ? JSON.parse(sessionStorage1) : 'None')
        console.log('Window Global:', windowGlobal || 'None')
        console.log('Window Backup:', windowBackup || 'None')
        console.log('Document Attribute:', docAttribute ? JSON.parse(docAttribute) : 'None')
        console.log('Body Attribute:', bodyAttribute ? JSON.parse(bodyAttribute) : 'None')
        console.log('Meta Tag:', metaTag ? JSON.parse(metaTag.content) : 'None')
        console.log('Hidden Input:', hiddenInput ? JSON.parse(hiddenInput.value) : 'None')
        console.log('Global System Available:', !!window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM)
        console.log('==========================')

        const reactCount = Object.values(currentReact).filter(Boolean).length
        const globalCount = currentGlobal ? Object.values(currentGlobal).filter(Boolean).length : 0
        const localStorage0Count = localStorage0 ? Object.values(JSON.parse(localStorage0)).filter(Boolean).length : 0

        toast.info(`🔍 React: ${reactCount} | Global: ${globalCount} | Storage: ${localStorage0Count} | 50+ Locations Checked`, {
          duration: 5000
        })
      } else {
        console.log('❌ GLOBAL SYSTEM NOT AVAILABLE')
        toast.error('Global system not available!')
      }
    } catch (error) {
      console.error('❌ Global debug error:', error)
      toast.error('Global debug failed')
    }
  }

  // Load previous values for auto-suggestions
  const loadPreviousValues = () => {
    try {
      if (typeof window === 'undefined') return // Server-side rendering check
      // Load all previous invoices from localStorage
      const allInvoices = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('invoice-')) {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              allInvoices.push(JSON.parse(data))
            } catch (e) {
              // Skip invalid data
            }
          }
        }
      }

      // Extract unique values
      const sellerNames = [...new Set(allInvoices.map(inv => inv.sellerName).filter(Boolean))]
      const clientNames = [...new Set(allInvoices.map(inv => inv.clientName).filter(Boolean))]
      const deductionReasons = [...new Set(allInvoices.map(inv => inv.deductionReason).filter(Boolean))]

      setPreviousSellerNames(sellerNames)
      setPreviousClientNames(clientNames)
      setPreviousDeductionReasons(deductionReasons)
    } catch (error) {
      console.error('Error loading previous values:', error)
    }
  }

  // Load previous values on component mount
  useEffect(() => {
    loadPreviousValues()
    loadInvoiceHistory()
  }, [])

  // Load invoice history for navbar
  const loadInvoiceHistory = () => {
    try {
      if (typeof window === 'undefined') return
      const invoices = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              const invoice = JSON.parse(data)
              if (invoice.invoiceNumber && invoice.timestamp) {
                invoices.push({
                  key,
                  id: key,
                  ...invoice,
                  status: invoice.status || 'draft'
                })
              }
            } catch (e) {
              // Skip invalid data
            }
          }
        }
      }

      // Sort by timestamp (newest first)
      const sortedInvoices = invoices.sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      )

      setHistoryInvoices(sortedInvoices)
    } catch (error) {
      console.error('Error loading invoice history:', error)
    }
  }

  // Logo upload handlers with dynamic auto-sizing
  const processAndResizeLogo = (file: File, maxWidth: number = 200, maxHeight: number = 150): Promise<string> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img

        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height

          if (width > height) {
            width = maxWidth
            height = width / aspectRatio
          } else {
            height = maxHeight
            width = height * aspectRatio
          }
        }

        // Set canvas dimensions
        canvas.width = width
        canvas.height = height

        // Draw and resize image
        ctx?.drawImage(img, 0, 0, width, height)

        // Convert to base64 with high quality
        const resizedDataUrl = canvas.toDataURL('image/jpeg', 0.9)
        resolve(resizedDataUrl)
      }

      img.onerror = () => reject(new Error('Failed to load image'))
      img.src = URL.createObjectURL(file)
    })
  }

  const handleSellerLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Logo file size must be less than 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file')
        return
      }

      try {
        toast.loading('Processing seller logo...', { id: 'seller-logo' })
        const resizedLogo = await processAndResizeLogo(file, 200, 150)
        setSellerLogo(resizedLogo)
        setSellerLogoFile(file)
        toast.success('Seller logo uploaded and optimized! 🖼️✨', { id: 'seller-logo' })
      } catch (error) {
        toast.error('Failed to process logo. Please try again.', { id: 'seller-logo' })
      }
    }
  }

  const handleClientLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Logo file size must be less than 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file')
        return
      }

      try {
        toast.loading('Processing client logo...', { id: 'client-logo' })
        const resizedLogo = await processAndResizeLogo(file, 200, 150)
        setClientLogo(resizedLogo)
        setClientLogoFile(file)
        toast.success('Client logo uploaded and optimized! 🖼️✨', { id: 'client-logo' })
      } catch (error) {
        toast.error('Failed to process logo. Please try again.', { id: 'client-logo' })
      }
    }
  }

  const removeSellerLogo = () => {
    setSellerLogo(null)
    setSellerLogoFile(null)
    toast.success('Seller logo removed')
  }

  const removeClientLogo = () => {
    setClientLogo(null)
    setClientLogoFile(null)
    toast.success('Client logo removed')
  }

  // Invoice history management functions
  const editInvoice = (invoice: any) => {
    try {
      // Load all invoice data into the form
      setInvoiceNumber(invoice.invoiceNumber || '')
      setInvoiceDate(invoice.invoiceDate || new Date().toISOString().split('T')[0])
      setDueDate(invoice.dueDate || '')
      setCurrency(invoice.currency || 'SAR')
      setDiscount(invoice.discount || 0)
      setDeductionAmount(invoice.deductionAmount || 0)
      setDeductionReason(invoice.deductionReason || '')

      // Seller details
      setSellerName(invoice.sellerName || '')
      setSellerAddress(invoice.sellerAddress || '')
      setSellerVAT(invoice.sellerVAT || '')
      setSellerLogo(invoice.sellerLogo || null)

      // Client details
      setClientName(invoice.clientName || '')
      setClientAddress(invoice.clientAddress || '')
      setClientVAT(invoice.clientVAT || '')
      setClientLogo(invoice.clientLogo || null)

      // Additional details
      setNotes(invoice.notes || '')
      setTerms(invoice.terms || '')
      setBankName(invoice.bankName || '')
      setAccountName(invoice.accountName || '')
      setAccountNumber(invoice.accountNumber || '')
      setIban(invoice.iban || '')

      // Items
      if (invoice.items && invoice.items.length > 0) {
        setItems(invoice.items)
      }

      setCurrentEditingInvoice(invoice.key)
      setShowHistoryNavbar(false)
      toast.success(`Editing invoice: ${invoice.invoiceNumber} 📝`)
    } catch (error) {
      console.error('Error editing invoice:', error)
      toast.error('Failed to load invoice for editing')
    }
  }

  const duplicateInvoice = (invoice: any) => {
    try {
      // Load invoice data but generate new invoice number
      const newInvoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`

      editInvoice(invoice)
      setInvoiceNumber(newInvoiceNumber)
      setCurrentEditingInvoice(null)
      toast.success(`Invoice duplicated as: ${newInvoiceNumber} 📋`)
    } catch (error) {
      console.error('Error duplicating invoice:', error)
      toast.error('Failed to duplicate invoice')
    }
  }

  const deleteInvoice = (invoiceKey: string, invoiceNumber: string) => {
    try {
      localStorage.removeItem(invoiceKey)
      loadInvoiceHistory()
      toast.success(`Invoice ${invoiceNumber} deleted successfully! 🗑️`)
    } catch (error) {
      console.error('Error deleting invoice:', error)
      toast.error('Failed to delete invoice')
    }
  }

  const viewInvoiceDetails = (invoice: any) => {
    // Create a preview of the invoice with current date
    const currentDate = new Date().toISOString().split('T')[0]

    const invoiceData: InvoiceData = {
      invoiceNumber: invoice.invoiceNumber,
      invoiceDate: currentDate, // Always use current date for viewing
      dueDate: invoice.dueDate,
      currency: invoice.currency,
      discount: invoice.discount,
      deductionAmount: invoice.deductionAmount,
      deductionReason: invoice.deductionReason,
      sellerName: invoice.sellerName,
      sellerAddress: invoice.sellerAddress,
      sellerVAT: invoice.sellerVAT,
      sellerLogo: invoice.sellerLogo,
      clientName: invoice.clientName,
      clientAddress: invoice.clientAddress,
      clientVAT: invoice.clientVAT,
      clientLogo: invoice.clientLogo,
      items: invoice.items || [],
      notes: invoice.notes,
      terms: invoice.terms,
      bankName: invoice.bankName,
      accountName: invoice.accountName,
      accountNumber: invoice.accountNumber,
      iban: invoice.iban,
      subtotal: invoice.subtotal,
      subtotalAfterDeduction: invoice.subtotalAfterDeduction,
      totalVAT: invoice.totalVAT,
      totalAmount: invoice.totalAmount,
      qrCode: generateQRCode(), // Generate fresh QR code with current date
      logoPreview: undefined
    }

    try {
      downloadInvoicePDF(invoiceData)
      toast.success(`Viewing invoice: ${invoice.invoiceNumber} with current date 👁️`)
    } catch (error) {
      console.error('Error viewing invoice:', error)
      toast.error('Failed to view invoice')
    }
  }

  // Simple calculations
  const subtotal = items.reduce((sum, item) => sum + item.amount, 0)
  const discountAmount = (subtotal * discount) / 100
  const subtotalAfterDiscount = subtotal - discountAmount
  const subtotalAfterDeduction = Math.max(0, subtotalAfterDiscount - deductionAmount)
  const totalVAT = subtotalAfterDeduction * 0.15
  const totalAmount = subtotalAfterDeduction + totalVAT

  // Simple functions
  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      rate: 0,
      vatPercent: 15,
      amount: 0
    }
    setItems([...items, newItem])
  }

  const removeItem = (itemId: string) => {
    if (items.length <= 1) {
      toast.error('At least one item is required')
      return
    }
    setItems(items.filter(item => item.id !== itemId))
    toast.success('Item removed successfully! 🗑️')
  }

  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {
    setItems(items.map(item => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value }
        if (field === 'quantity' || field === 'rate') {
          updatedItem.amount = updatedItem.quantity * updatedItem.rate
        }
        return updatedItem
      }
      return item
    }))
  }

  // QR Code generation function - ALWAYS uses current date
  const generateQRCode = () => {
    // Always use current date for QR code timestamp
    const currentDate = new Date()

    const qrData: ZATCAQRData = {
      sellerName: sellerName || 'KAAZMAAMAA Company',
      vatNumber: sellerVAT || '30**********003',
      timestamp: generateZATCATimestamp(currentDate),
      invoiceTotal: formatCurrencyForZATCA(totalAmount),
      vatTotal: formatCurrencyForZATCA(totalVAT)
    }

    try {
      return generateZATCAQRCode(qrData)
    } catch (error) {
      console.error('QR Code generation error:', error)
      toast.error('Failed to generate QR code')
      return ''
    }
  }

  // PDF Generation function
  const generatePDF = () => {
    if (!sellerName || !clientName || items.length === 0) {
      toast.error('Please fill in all required fields before generating PDF')
      return
    }

    // Validate deduction reason if deduction amount is specified
    if (deductionAmount > 0 && !deductionReason.trim()) {
      toast.error('Please provide a reason for the deduction amount')
      return
    }

    toast.info('Generating PDF with ZATCA QR code...')

    // Always use current date for PDF generation
    const currentDate = new Date().toISOString().split('T')[0]

    const invoiceData: InvoiceData = {
      invoiceNumber,
      invoiceDate: currentDate, // Always use current date
      dueDate,
      currency,
      discount,
      deductionAmount,
      deductionReason,
      sellerName,
      sellerAddress,
      sellerVAT,
      sellerLogo,
      clientName,
      clientAddress,
      clientVAT,
      clientLogo,
      items,
      notes,
      terms,
      bankName,
      accountName,
      accountNumber,
      iban,
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(), // This also uses current date
      logoPreview: undefined
    }

    try {
      downloadInvoicePDF(invoiceData)
      toast.success(`PDF generated with current date: ${new Date().toLocaleDateString()} 📄`)
    } catch (error) {
      console.error('PDF generation error:', error)
      toast.error('Failed to generate PDF')
    }
  }

  // Print Invoice function
  const handlePrintInvoice = () => {
    if (!sellerName || !clientName || items.length === 0) {
      toast.error('Please fill in all required fields before printing')
      return
    }

    // Validate deduction reason if deduction amount is specified
    if (deductionAmount > 0 && !deductionReason.trim()) {
      toast.error('Please provide a reason for the deduction amount')
      return
    }

    toast.info('Opening print dialog...')

    // Always use current date for print as well
    const currentDate = new Date().toISOString().split('T')[0]

    const invoiceData: InvoiceData = {
      invoiceNumber,
      invoiceDate: currentDate, // Always use current date
      dueDate,
      currency,
      discount,
      deductionAmount,
      deductionReason,
      sellerName,
      sellerAddress,
      sellerVAT,
      sellerLogo,
      clientName,
      clientAddress,
      clientVAT,
      clientLogo,
      items,
      notes,
      terms,
      bankName,
      accountName,
      accountNumber,
      iban,
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(), // This also uses current date
      logoPreview: undefined
    }

    try {
      printInvoice(invoiceData)
      toast.success('Print dialog opened successfully! 🖨️')
    } catch (error) {
      console.error('Print error:', error)
      toast.error('Failed to open print dialog')
    }
  }

  const saveDraft = () => {
    try {
      const formData = {
        invoiceNumber, invoiceDate, dueDate, currency, discount, deductionAmount, deductionReason,
        sellerName, sellerAddress, sellerVAT, sellerLogo, clientName, clientAddress, clientVAT, clientLogo,
        notes, terms, bankName, accountName, accountNumber, iban, items,
        subtotal, subtotalAfterDeduction, totalVAT, totalAmount,
        status: 'draft',
        timestamp: new Date().toISOString(),
        lastModified: new Date().toISOString()
      }

      // Save as current draft
      localStorage.setItem('invoice-draft', JSON.stringify(formData))

      // Save with unique key for history
      if (sellerName || clientName || invoiceNumber) {
        let historyKey
        if (currentEditingInvoice) {
          // Update existing invoice
          historyKey = currentEditingInvoice
          formData.lastModified = new Date().toISOString()
        } else {
          // Create new invoice
          historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`
        }

        localStorage.setItem(historyKey, JSON.stringify(formData))

        // Update previous values and history
        loadPreviousValues()
        loadInvoiceHistory()
      }

      toast.success(currentEditingInvoice ? 'Invoice updated! 📝' : 'Draft saved! 💾')
    } catch (error) {
      toast.error('Save failed')
    }
  }

  const saveAsCompleted = () => {
    try {
      if (!sellerName || !clientName || items.length === 0) {
        toast.error('Please fill in all required fields before marking as completed')
        return
      }

      const formData = {
        invoiceNumber, invoiceDate, dueDate, currency, discount, deductionAmount, deductionReason,
        sellerName, sellerAddress, sellerVAT, sellerLogo, clientName, clientAddress, clientVAT, clientLogo,
        notes, terms, bankName, accountName, accountNumber, iban, items,
        subtotal, subtotalAfterDeduction, totalVAT, totalAmount,
        status: 'completed',
        timestamp: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        qrCode: generateQRCode()
      }

      let historyKey
      if (currentEditingInvoice) {
        historyKey = currentEditingInvoice
      } else {
        historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`
      }

      localStorage.setItem(historyKey, JSON.stringify(formData))
      loadInvoiceHistory()

      toast.success(`Invoice ${invoiceNumber} marked as completed! ✅`)
    } catch (error) {
      toast.error('Failed to save completed invoice')
    }
  }

  const createNewInvoice = () => {
    // Clear all form fields
    setInvoiceNumber(`INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`)
    setInvoiceDate(new Date().toISOString().split('T')[0])
    setDueDate('')
    setCurrency('SAR')
    setDiscount(0)
    setDeductionAmount(0)
    setDeductionReason('')

    // Clear seller details (but keep for convenience)
    // setSellerName('')
    // setSellerAddress('')
    // setSellerVAT('')
    setSellerLogo(null)

    // Clear client details
    setClientName('')
    setClientAddress('')
    setClientVAT('')
    setClientLogo(null)

    // Clear additional details
    setNotes('')
    setTerms('')
    setBankName('')
    setAccountName('')
    setAccountNumber('')
    setIban('')

    // Reset items
    setItems([{
      id: '1',
      description: '',
      quantity: 1,
      rate: 0,
      vatPercent: 15,
      amount: 0
    }])

    setCurrentEditingInvoice(null)
    setShowHistoryNavbar(false)
    toast.success('New invoice form ready! 📄')
  }

  // Function to set current date
  const setCurrentDate = () => {
    const currentDate = new Date().toISOString().split('T')[0]
    setInvoiceDate(currentDate)
    toast.success('Invoice date updated to current date! 📅')
  }

  // GLOBAL LOCK TOGGLE
  const toggleSectionLock = (section: keyof typeof defaultLockState) => {
    console.log(`🔄 GLOBAL TOGGLING SECTION: ${section}`)

    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
      // Use the global system
      const isLocked = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.toggle(section)

      // Update React state for button display
      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      setLockedSections(newState)

      // Show toast message
      const sectionName = section.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())
      const message = isLocked
        ? `🔒 ${sectionName} GLOBAL LOCKED - PERMANENT!`
        : `🔓 ${sectionName} GLOBAL UNLOCKED!`

      toast.success(message, { duration: 3000 })

      // Force component update
      setForceUpdate(prev => prev + 1)

      console.log(`✅ GLOBAL TOGGLE COMPLETE: ${section} = ${isLocked}`)
    } else {
      toast.error('❌ Global lock system not available!')
    }
  }

  // GLOBAL UNLOCK ALL
  const unlockAllSections = () => {
    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.unlockAll()

      // Update React state
      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      setLockedSections(newState)
      setForceUpdate(prev => prev + 1)

      toast.success('🔓 ALL SECTIONS GLOBAL UNLOCKED!', { duration: 3000 })
    }
  }

  // GLOBAL LOCK ALL
  const lockAllSections = () => {
    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.lockAll()

      // Update React state
      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
      setLockedSections(newState)
      setForceUpdate(prev => prev + 1)

      toast.success('🔒 ALL SECTIONS GLOBAL LOCKED - PERMANENT!', { duration: 3000 })
    }
  }

  // GLOBAL FORCE RELOAD
  const forceReloadLocks = () => {
    try {
      console.log('🔄 GLOBAL FORCE RELOAD TRIGGERED')

      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        const reloadedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        console.log('🔒 GLOBAL RELOADED STATE:', reloadedState)

        // Update React state
        setLockedSections(reloadedState)

        // Apply to DOM
        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(reloadedState)

        const lockedCount = Object.values(reloadedState).filter(Boolean).length
        toast.success(`🔄 GLOBAL RELOADED: ${lockedCount} sections locked!`, { duration: 2000 })

        setForceUpdate(prev => prev + 1)
      }
    } catch (error) {
      console.error('❌ Global reload error:', error)
      toast.error('Global reload failed!')
    }
  }

  // GLOBAL FORCE SAVE
  const forceSave = () => {
    try {
      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        const currentState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        console.log('🚀 GLOBAL FORCE SAVE TRIGGERED:', currentState)
        const saved = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.save(currentState)
        if (saved) {
          toast.success('🚀 GLOBAL FORCE SAVE COMPLETE!', { duration: 2000 })
          console.log('✅ GLOBAL FORCE SAVE SUCCESSFUL')

          // Show verification
          setTimeout(() => {
            const verify = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
            console.log('✅ GLOBAL SAVE VERIFIED:', verify)
          }, 100)
        } else {
          toast.error('❌ GLOBAL FORCE SAVE FAILED!')
          console.error('❌ GLOBAL FORCE SAVE FAILED')
        }
      }
    } catch (error) {
      console.error('❌ Global force save error:', error)
      toast.error('Global force save failed!')
    }
  }

  // GLOBAL CLEAR (TESTING)
  const clearLockState = () => {
    try {
      console.log('🗑️ GLOBAL CLEAR TRIGGERED')

      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {
        // Use global clear method
        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.clear()

        // Update React state
        const clearedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()
        setLockedSections(clearedState)
        setForceUpdate(prev => prev + 1)

        toast.success('🗑️ GLOBAL CLEARED FROM ALL 50+ LOCATIONS!', { duration: 3000 })
        console.log('✅ GLOBAL CLEAR COMPLETE')
      }
    } catch (error) {
      console.error('❌ Global clear error:', error)
      toast.error('Global clear failed!')
    }
  }

  // Preview function to show current modifications
  const previewInvoice = () => {
    if (!sellerName || !clientName || items.length === 0) {
      toast.error('Please fill in all required fields before preview')
      return
    }

    if (deductionAmount > 0 && !deductionReason.trim()) {
      toast.error('Please provide a reason for the deduction amount')
      return
    }

    toast.info('Generating preview with current date...')

    // Always use current date for preview
    const currentDate = new Date().toISOString().split('T')[0]
    const currentDateFormatted = new Date().toLocaleDateString()

    const invoiceData: InvoiceData = {
      invoiceNumber,
      invoiceDate: currentDate, // Always use current date
      dueDate,
      currency,
      discount,
      deductionAmount,
      deductionReason,
      sellerName,
      sellerAddress,
      sellerVAT,
      sellerLogo,
      clientName,
      clientAddress,
      clientVAT,
      clientLogo,
      items,
      notes,
      terms,
      bankName,
      accountName,
      accountNumber,
      iban,
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(), // This also uses current date
      logoPreview: undefined
    }

    try {
      // Create preview window
      const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')
      if (previewWindow) {
        const html = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Invoice Preview - Current Date: ${currentDateFormatted}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                background: #f5f5f5;
              }
              .preview-header {
                background: #4f46e5;
                color: white;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                text-align: center;
              }
              .preview-info {
                background: #e0f2fe;
                border: 2px solid #0288d1;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
              }
              .lock-status {
                background: #fff3e0;
                border: 2px solid #ff9800;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
              }
              .locked { color: #d32f2f; font-weight: bold; }
              .unlocked { color: #388e3c; font-weight: bold; }
              .invoice-preview {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              }
              .field { margin: 8px 0; }
              .label { font-weight: bold; color: #333; }
              .value { color: #666; margin-left: 10px; }
              .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
              .current-date { color: #4f46e5; font-weight: bold; font-size: 18px; }
            </style>
          </head>
          <body>
            <div class="preview-header">
              <h1>📄 Invoice Preview</h1>
              <p>Preview with Current Date Modifications Applied</p>
            </div>

            <div class="preview-info">
              <h3>🔍 Preview Information</h3>
              <div class="field">
                <span class="label">Current Date Applied:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
              <div class="field">
                <span class="label">QR Code Date:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
              <div class="field">
                <span class="label">PDF Date:</span>
                <span class="value current-date">${currentDateFormatted}</span>
              </div>
            </div>

            <div class="lock-status">
              <h3>🔒 Lock Status</h3>
              <div class="field">
                <span class="label">Invoice Details:</span>
                <span class="value ${lockedSections.invoiceDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.invoiceDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Seller Details:</span>
                <span class="value ${lockedSections.sellerDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.sellerDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Client Details:</span>
                <span class="value ${lockedSections.clientDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.clientDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Bank Details:</span>
                <span class="value ${lockedSections.bankDetails ? 'locked' : 'unlocked'}">
                  ${lockedSections.bankDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Invoice Items:</span>
                <span class="value ${lockedSections.items ? 'locked' : 'unlocked'}">
                  ${lockedSections.items ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
              <div class="field">
                <span class="label">Additional Information:</span>
                <span class="value ${lockedSections.notes ? 'locked' : 'unlocked'}">
                  ${lockedSections.notes ? '🔒 LOCKED' : '🔓 UNLOCKED'}
                </span>
              </div>
            </div>

            <div class="invoice-preview">
              <h3>📋 Invoice Data Preview</h3>

              <div class="section">
                <h4>Invoice Details</h4>
                <div class="field">
                  <span class="label">Invoice Number:</span>
                  <span class="value">${invoiceNumber}</span>
                </div>
                <div class="field">
                  <span class="label">Invoice Date:</span>
                  <span class="value current-date">${currentDateFormatted} (CURRENT DATE APPLIED)</span>
                </div>
                <div class="field">
                  <span class="label">Due Date:</span>
                  <span class="value">${dueDate || 'Not specified'}</span>
                </div>
                <div class="field">
                  <span class="label">Currency:</span>
                  <span class="value">${currency}</span>
                </div>
              </div>

              <div class="section">
                <h4>Seller Details</h4>
                <div class="field">
                  <span class="label">Company Name:</span>
                  <span class="value">${sellerName}</span>
                </div>
                <div class="field">
                  <span class="label">VAT Number:</span>
                  <span class="value">${sellerVAT}</span>
                </div>
                <div class="field">
                  <span class="label">Address:</span>
                  <span class="value">${sellerAddress}</span>
                </div>
              </div>

              <div class="section">
                <h4>Client Details</h4>
                <div class="field">
                  <span class="label">Client Name:</span>
                  <span class="value">${clientName}</span>
                </div>
                <div class="field">
                  <span class="label">Client VAT:</span>
                  <span class="value">${clientVAT || 'Not provided'}</span>
                </div>
                <div class="field">
                  <span class="label">Client Address:</span>
                  <span class="value">${clientAddress}</span>
                </div>
              </div>

              <div class="section">
                <h4>Financial Summary</h4>
                <div class="field">
                  <span class="label">Subtotal:</span>
                  <span class="value">${currency} ${subtotal.toFixed(2)}</span>
                </div>
                ${deductionAmount > 0 ? `
                  <div class="field">
                    <span class="label">Deduction:</span>
                    <span class="value">-${currency} ${deductionAmount.toFixed(2)}</span>
                  </div>
                ` : ''}
                <div class="field">
                  <span class="label">Total VAT (15%):</span>
                  <span class="value">${currency} ${totalVAT.toFixed(2)}</span>
                </div>
                <div class="field">
                  <span class="label">Total Amount:</span>
                  <span class="value current-date">${currency} ${totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </body>
          </html>
        `

        previewWindow.document.write(html)
        previewWindow.document.close()

        toast.success(`Preview opened with current date: ${currentDateFormatted} 👁️`)
      }
    } catch (error) {
      console.error('Preview error:', error)
      toast.error('Failed to open preview')
    }
  }

  // Load previous user data from localStorage
  const loadPreviousData = () => {
    try {
      const savedData = localStorage.getItem('invoice-draft')
      if (savedData) {
        const data = JSON.parse(savedData)

        // Load all the saved data
        if (data.invoiceNumber) setInvoiceNumber(data.invoiceNumber)
        if (data.invoiceDate) setInvoiceDate(data.invoiceDate)
        if (data.dueDate) setDueDate(data.dueDate)
        if (data.currency) setCurrency(data.currency)
        if (data.discount !== undefined) setDiscount(data.discount)
        if (data.deductionAmount !== undefined) setDeductionAmount(data.deductionAmount)
        if (data.deductionReason) setDeductionReason(data.deductionReason)

        // Seller details
        if (data.sellerName) setSellerName(data.sellerName)
        if (data.sellerAddress) setSellerAddress(data.sellerAddress)
        if (data.sellerVAT) setSellerVAT(data.sellerVAT)
        if (data.sellerLogo) setSellerLogo(data.sellerLogo)

        // Client details
        if (data.clientName) setClientName(data.clientName)
        if (data.clientAddress) setClientAddress(data.clientAddress)
        if (data.clientVAT) setClientVAT(data.clientVAT)
        if (data.clientLogo) setClientLogo(data.clientLogo)

        // Additional details
        if (data.notes) setNotes(data.notes)
        if (data.terms) setTerms(data.terms)
        if (data.bankName) setBankName(data.bankName)
        if (data.accountName) setAccountName(data.accountName)
        if (data.accountNumber) setAccountNumber(data.accountNumber)
        if (data.iban) setIban(data.iban)

        // Items
        if (data.items && data.items.length > 0) {
          setItems(data.items)
        }

        toast.success('Previous data loaded successfully! 📋')
      } else {
        toast.info('No previous data found. Start entering your information.')
      }
    } catch (error) {
      console.error('Error loading previous data:', error)
      toast.error('Failed to load previous data')
    }
  }

  // Get recent invoices for quick loading
  const getRecentInvoices = () => {
    try {
      if (typeof window === 'undefined') return [] // Server-side rendering check
      const recentInvoices = []
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {
          const data = localStorage.getItem(key)
          if (data) {
            try {
              const invoice = JSON.parse(data)
              if (invoice.invoiceNumber && invoice.timestamp) {
                recentInvoices.push({
                  key,
                  ...invoice
                })
              }
            } catch (e) {
              // Skip invalid data
            }
          }
        }
      }

      // Sort by timestamp (newest first) and take top 5
      return recentInvoices
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 5)
    } catch (error) {
      console.error('Error getting recent invoices:', error)
      return []
    }
  }

  const loadInvoiceData = (invoice: any) => {
    // Load all the invoice data
    setInvoiceNumber(invoice.invoiceNumber || '')
    setInvoiceDate(invoice.invoiceDate || '')
    setDueDate(invoice.dueDate || '')
    setCurrency(invoice.currency || 'SAR')
    setDiscount(invoice.discount || 0)
    setDeductionAmount(invoice.deductionAmount || 0)
    setDeductionReason(invoice.deductionReason || '')

    // Seller details
    setSellerName(invoice.sellerName || '')
    setSellerAddress(invoice.sellerAddress || '')
    setSellerVAT(invoice.sellerVAT || '')
    setSellerLogo(invoice.sellerLogo || null)

    // Client details
    setClientName(invoice.clientName || '')
    setClientAddress(invoice.clientAddress || '')
    setClientVAT(invoice.clientVAT || '')
    setClientLogo(invoice.clientLogo || null)

    // Additional details
    setNotes(invoice.notes || '')
    setTerms(invoice.terms || '')
    setBankName(invoice.bankName || '')
    setAccountName(invoice.accountName || '')
    setAccountNumber(invoice.accountNumber || '')
    setIban(invoice.iban || '')

    // Items
    if (invoice.items && invoice.items.length > 0) {
      setItems(invoice.items)
    }

    toast.success(`Invoice ${invoice.invoiceNumber} loaded! 📋`)
  }

  // Simple and reliable auto-suggestion component
  const SuggestionInput = ({
    value,
    onChange,
    suggestions,
    placeholder,
    label,
    fieldKey,
    required = false,
    disabled = false
  }: {
    value: string
    onChange: (value: string) => void
    suggestions: string[]
    placeholder: string
    label: string
    fieldKey: string
    required?: boolean
    disabled?: boolean
  }) => {
    const [showDropdown, setShowDropdown] = useState(false)

    const filteredSuggestions = suggestions.filter(suggestion =>
      suggestion.toLowerCase().includes(value.toLowerCase()) && suggestion !== value
    ).slice(0, 5)

    return (
      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <Input
          value={value}
          onChange={(e) => {
            const newValue = e.target.value
            onChange(newValue)
            setShowDropdown(newValue.length > 0 && filteredSuggestions.length > 0)
          }}
          onFocus={() => {
            if (value.length > 0 && filteredSuggestions.length > 0) {
              setShowDropdown(true)
            }
          }}
          onBlur={() => {
            setTimeout(() => setShowDropdown(false), 150)
          }}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className="w-full"
        />
        {showDropdown && filteredSuggestions.length > 0 && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto">
            {filteredSuggestions.map((suggestion, index) => (
              <div
                key={`${fieldKey}-${index}`}
                className="px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0"
                onMouseDown={(e) => e.preventDefault()}
                onClick={() => {
                  onChange(suggestion)
                  setShowDropdown(false)
                  toast.success('Previous data filled! 📋')
                }}
              >
                {suggestion}
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-purple-600">
                  {currentEditingInvoice ? 'Edit Invoice' : 'Create Invoice'}
                </h1>
                <p className="text-sm text-gray-600">
                  {currentEditingInvoice
                    ? 'Modify and update your existing invoice'
                    : 'Generate professional ZATCA-compliant invoices'
                  }
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistoryNavbar(!showHistoryNavbar)}
                className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
              >
                <History className="h-4 w-4 mr-2" />
                History ({historyInvoices.length})
              </Button>
              {currentEditingInvoice && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={createNewInvoice}
                  className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Invoice
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={loadPreviousData}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                📋 Load Previous Data
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setUseSimpleInputs(!useSimpleInputs)
                  toast.info(useSimpleInputs ? 'Auto-suggestions enabled' : 'Simple inputs enabled')
                }}
                className={useSimpleInputs ? "bg-orange-600 hover:bg-orange-700 text-white" : "bg-gray-600 hover:bg-gray-700 text-white"}
              >
                {useSimpleInputs ? '🔧 Simple Mode' : '🎯 Smart Mode'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={saveDraft}
              >
                💾 Save Draft
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={saveAsCompleted}
                className="bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
              >
                ✅ Mark Complete
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={unlockAllSections}
                className="bg-yellow-50 text-yellow-600 hover:bg-yellow-100 border-yellow-200"
              >
                <Unlock className="h-4 w-4 mr-1" />
                Unlock All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={lockAllSections}
                className="bg-red-50 text-red-600 hover:bg-red-100 border-red-200"
              >
                <Lock className="h-4 w-4 mr-1" />
                Lock All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={previewInvoice}
                className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
              >
                <Eye className="h-4 w-4 mr-1" />
                Preview
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={debugLockState}
                className="bg-gray-50 text-gray-600 hover:bg-gray-100 border-gray-200"
                title="Debug lock state"
              >
                🔍 Debug
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={forceReloadLocks}
                className="bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200"
                title="Force reload lock state"
              >
                🔄 Reload
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={forceSave}
                className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200"
                title="Force save lock state"
              >
                🚀 Save
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearLockState}
                className="bg-orange-50 text-orange-600 hover:bg-orange-100 border-orange-200"
                title="Clear all lock state (for testing)"
              >
                🗑️ Clear
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={generatePDF}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <Download className="h-4 w-4 mr-2" />
                Generate PDF
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrintInvoice}
                className="bg-purple-600 hover:bg-purple-700 text-white"
              >
                <Printer className="h-4 w-4 mr-2" />
                Print Invoice
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* History Navbar */}
      {showHistoryNavbar && (
        <div className="bg-white border-b shadow-sm">
          <div className="max-w-7xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <h2 className="text-lg font-semibold text-gray-900">Invoice History</h2>
                <span className="text-sm text-gray-500">({historyInvoices.length} invoices)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="relative">
                  <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search invoices..."
                    value={historySearchTerm}
                    onChange={(e) => setHistorySearchTerm(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <select
                  value={historyFilterStatus}
                  onChange={(e) => setHistoryFilterStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="draft">Draft</option>
                  <option value="completed">Completed</option>
                </select>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowHistoryNavbar(false)}
                >
                  Close
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
              {historyInvoices
                .filter(invoice => {
                  const matchesSearch = !historySearchTerm ||
                    invoice.invoiceNumber?.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
                    invoice.clientName?.toLowerCase().includes(historySearchTerm.toLowerCase()) ||
                    invoice.sellerName?.toLowerCase().includes(historySearchTerm.toLowerCase())

                  const matchesStatus = historyFilterStatus === 'all' || invoice.status === historyFilterStatus

                  return matchesSearch && matchesStatus
                })
                .map((invoice) => (
                  <div
                    key={invoice.key}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-gray-50"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900 truncate">{invoice.invoiceNumber}</h3>
                        <p className="text-sm text-gray-600 truncate">{invoice.clientName || 'No client'}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            invoice.status === 'completed'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {invoice.status || 'draft'}
                          </span>
                          <span className="text-xs text-gray-500">
                            {new Date(invoice.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                        >
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-1">
                        <DollarSign className="h-4 w-4 text-green-600" />
                        <span className="font-semibold text-green-600">
                          {invoice.currency} {(invoice.totalAmount || 0).toFixed(2)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-xs text-gray-500">
                          {invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : 'No due date'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editInvoice(invoice)}
                        className="flex-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => duplicateInvoice(invoice)}
                        className="flex-1 text-green-600 hover:text-green-700 hover:bg-green-50"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => viewInvoiceDetails(invoice)}
                        className="flex-1 text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}

              {historyInvoices.length === 0 && (
                <div className="col-span-full text-center py-8 text-gray-500">
                  <History className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg font-medium">No invoices found</p>
                  <p className="text-sm">Create and save invoices to see them here</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Invoice Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Invoice Details Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-purple-600">
                    <FileText className="h-5 w-5" />
                    <span>Invoice Details</span>
                    {isHydrated && lockedSections.invoiceDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('invoiceDetails')}
                    className={`${
                      isHydrated && lockedSections.invoiceDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : directLockControl.getCurrentState().invoiceDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="invoiceDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Number</label>
                    <Input
                      value={invoiceNumber}
                      onChange={(e) => setInvoiceNumber(e.target.value)}
                      placeholder="INV-2025-001"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Date</label>
                    <div className="flex space-x-2">
                      <Input
                        type="date"
                        value={invoiceDate}
                        onChange={(e) => setInvoiceDate(e.target.value)}
                        className="flex-1"
                        disabled={isHydrated && lockedSections.invoiceDetails}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={setCurrentDate}
                        className="bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200 whitespace-nowrap"
                        title="Set to current date"
                        disabled={isHydrated && lockedSections.invoiceDetails}
                      >
                        <Calendar className="h-4 w-4 mr-1" />
                        Today
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                    <Input
                      type="date"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                    <Input
                      value={currency}
                      onChange={(e) => setCurrency(e.target.value)}
                      placeholder="SAR"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Discount (%)</label>
                    <Input
                      type="number"
                      value={discount}
                      onChange={(e) => setDiscount(Number(e.target.value))}
                      placeholder="0"
                      min="0"
                      max="100"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Deduction Amount ({currency})
                    </label>
                    <Input
                      type="number"
                      value={deductionAmount}
                      onChange={(e) => setDeductionAmount(Number(e.target.value))}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      disabled={isHydrated && lockedSections.invoiceDetails}
                    />
                  </div>
                  {deductionAmount > 0 && (
                    <div className="md:col-span-2">
                      {useSimpleInputs ? (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Deduction Reason <span className="text-red-500">*</span>
                          </label>
                          <Input
                            value={deductionReason}
                            onChange={(e) => setDeductionReason(e.target.value)}
                            placeholder="e.g., Early payment discount, Advance payment received, Material cost adjustment, etc."
                            required={deductionAmount > 0}
                            className="w-full"
                            disabled={isHydrated && lockedSections.invoiceDetails}
                          />
                        </div>
                      ) : (
                        <SuggestionInput
                          value={deductionReason}
                          onChange={setDeductionReason}
                          suggestions={previousDeductionReasons}
                          placeholder="e.g., Early payment discount, Advance payment received, Material cost adjustment, etc."
                          label="Deduction Reason"
                          fieldKey="deductionReason"
                          required={true}
                          disabled={isHydrated && lockedSections.invoiceDetails}
                        />
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Seller Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Seller Details</span>
                    {isHydrated && lockedSections.sellerDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('sellerDetails')}
                    className={`${
                      isHydrated && lockedSections.sellerDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.sellerDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="sellerDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {useSimpleInputs ? (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                      <Input
                        value={sellerName}
                        onChange={(e) => setSellerName(e.target.value)}
                        placeholder="Your Company Name"
                        className="w-full"
                        disabled={isHydrated && lockedSections.sellerDetails}
                      />
                    </div>
                  ) : (
                    <SuggestionInput
                      value={sellerName}
                      onChange={setSellerName}
                      suggestions={previousSellerNames}
                      placeholder="Your Company Name"
                      label="Company Name"
                      fieldKey="sellerName"
                      disabled={isHydrated && lockedSections.sellerDetails}
                    />
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">VAT Number</label>
                    <Input
                      value={sellerVAT}
                      onChange={(e) => setSellerVAT(e.target.value)}
                      placeholder="30**********003"
                      disabled={isHydrated && lockedSections.sellerDetails}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                    <Textarea
                      value={sellerAddress}
                      onChange={(e) => setSellerAddress(e.target.value)}
                      placeholder="Company Address"
                      rows={3}
                      disabled={isHydrated && lockedSections.sellerDetails}
                    />
                  </div>

                  {/* Seller Logo Upload */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Company Logo</label>
                    <div className="flex items-center space-x-4">
                      {sellerLogo ? (
                        <div className="flex items-center space-x-3">
                          <div className="w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                            <img
                              src={sellerLogo}
                              alt="Seller Logo"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <div className="flex flex-col space-y-2">
                            <span className="text-sm text-green-600 font-medium">✓ Logo uploaded & optimized</span>
                            <span className="text-xs text-gray-500">Auto-resized for best quality</span>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={removeSellerLogo}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Remove Logo
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3">
                          <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                            <Upload className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="flex flex-col">
                            <input
                              id="seller-logo-input"
                              type="file"
                              accept="image/*"
                              onChange={handleSellerLogoUpload}
                              className="hidden"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="bg-blue-50 text-blue-600 hover:bg-blue-100"
                              onClick={() => document.getElementById('seller-logo-input')?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Logo
                            </Button>
                            <span className="text-xs text-gray-500 mt-1">PNG, JPG up to 5MB</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Client Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Client Details</span>
                    {isHydrated && lockedSections.clientDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('clientDetails')}
                    className={`${
                      isHydrated && lockedSections.clientDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.clientDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="clientDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {useSimpleInputs ? (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Client Name</label>
                      <Input
                        value={clientName}
                        onChange={(e) => setClientName(e.target.value)}
                        placeholder="Client Company Name"
                        className="w-full"
                        disabled={isHydrated && lockedSections.clientDetails}
                      />
                    </div>
                  ) : (
                    <SuggestionInput
                      value={clientName}
                      onChange={setClientName}
                      suggestions={previousClientNames}
                      placeholder="Client Company Name"
                      label="Client Name"
                      fieldKey="clientName"
                      disabled={isHydrated && lockedSections.clientDetails}
                    />
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Client VAT</label>
                    <Input
                      value={clientVAT}
                      onChange={(e) => setClientVAT(e.target.value)}
                      placeholder="301987654321009"
                      disabled={isHydrated && lockedSections.clientDetails}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Client Address</label>
                    <Textarea
                      value={clientAddress}
                      onChange={(e) => setClientAddress(e.target.value)}
                      placeholder="Client Address"
                      rows={3}
                      disabled={isHydrated && lockedSections.clientDetails}
                    />
                  </div>

                  {/* Client Logo Upload */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Client Logo</label>
                    <div className="flex items-center space-x-4">
                      {clientLogo ? (
                        <div className="flex items-center space-x-3">
                          <div className="w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm">
                            <img
                              src={clientLogo}
                              alt="Client Logo"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <div className="flex flex-col space-y-2">
                            <span className="text-sm text-green-600 font-medium">✓ Logo uploaded & optimized</span>
                            <span className="text-xs text-gray-500">Auto-resized for best quality</span>
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={removeClientLogo}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Remove Logo
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3">
                          <div className="w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                            <Upload className="h-6 w-6 text-gray-400" />
                          </div>
                          <div className="flex flex-col">
                            <input
                              id="client-logo-input"
                              type="file"
                              accept="image/*"
                              onChange={handleClientLogoUpload}
                              className="hidden"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="bg-green-50 text-green-600 hover:bg-green-100"
                              onClick={() => document.getElementById('client-logo-input')?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Logo
                            </Button>
                            <span className="text-xs text-gray-500 mt-1">PNG, JPG up to 5MB</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bank Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Bank Details</span>
                    {isHydrated && lockedSections.bankDetails && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('bankDetails')}
                    className={`${
                      isHydrated && lockedSections.bankDetails
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.bankDetails ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="bankDetails">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                    <Input
                      value={bankName}
                      onChange={(e) => setBankName(e.target.value)}
                      placeholder="Saudi National Bank"
                      disabled={isHydrated && lockedSections.bankDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Account Name</label>
                    <Input
                      value={accountName}
                      onChange={(e) => setAccountName(e.target.value)}
                      placeholder="Company Account Name"
                      disabled={isHydrated && lockedSections.bankDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                    <Input
                      value={accountNumber}
                      onChange={(e) => setAccountNumber(e.target.value)}
                      placeholder="**********"
                      disabled={isHydrated && lockedSections.bankDetails}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">IBAN</label>
                    <Input
                      value={iban}
                      onChange={(e) => setIban(e.target.value)}
                      placeholder="SA********************12"
                      disabled={isHydrated && lockedSections.bankDetails}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Invoice Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Invoice Items</span>
                    {isHydrated && lockedSections.items && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => toggleSectionLock('items')}
                      className={`${
                        isHydrated && lockedSections.items
                          ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                          : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                      }`}
                    >
                      {!isHydrated ? (
                        <>
                          <Lock className="h-4 w-4 mr-1" />
                          Lock
                        </>
                      ) : lockedSections.items ? (
                        <>
                          <Unlock className="h-4 w-4 mr-1" />
                          Unlock
                        </>
                      ) : (
                        <>
                          <Lock className="h-4 w-4 mr-1" />
                          Lock
                        </>
                      )}
                    </Button>
                    <Button
                      size="sm"
                      onClick={addItem}
                      className="bg-blue-600 hover:bg-blue-700"
                      disabled={isHydrated && lockedSections.items}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Item
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="items">
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <div key={item.id} className="grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border rounded-lg relative">
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <Input
                          value={item.description}
                          onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                          placeholder="Item description"
                          disabled={isHydrated && lockedSections.items}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Quantity</label>
                        <Input
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateItem(item.id, 'quantity', Number(e.target.value))}
                          min="1"
                          disabled={isHydrated && lockedSections.items}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Rate ({currency})</label>
                        <Input
                          type="number"
                          value={item.rate}
                          onChange={(e) => updateItem(item.id, 'rate', Number(e.target.value))}
                          min="0"
                          step="0.01"
                          disabled={isHydrated && lockedSections.items}
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Amount ({currency})</label>
                        <Input
                          value={item.amount.toFixed(2)}
                          readOnly
                          className="bg-gray-50"
                        />
                      </div>
                      <div className="flex items-end">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(item.id)}
                          disabled={items.length <= 1 || lockedSections.items}
                          className={`w-full ${
                            items.length <= 1 || lockedSections.items
                              ? 'opacity-50 cursor-not-allowed'
                              : 'text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200'
                          }`}
                          title={
                            lockedSections.items
                              ? 'Items section is locked'
                              : items.length <= 1
                                ? 'At least one item is required'
                                : 'Remove this item'
                          }
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Notes and Terms */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span>Additional Information</span>
                    {isHydrated && lockedSections.notes && (
                      <Lock className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSectionLock('notes')}
                    className={`${
                      isHydrated && lockedSections.notes
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'
                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'
                    }`}
                  >
                    {!isHydrated ? (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    ) : lockedSections.notes ? (
                      <>
                        <Unlock className="h-4 w-4 mr-1" />
                        Unlock
                      </>
                    ) : (
                      <>
                        <Lock className="h-4 w-4 mr-1" />
                        Lock
                      </>
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent data-section="notes">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <Textarea
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Thank you for your business!"
                      rows={4}
                      disabled={isHydrated && lockedSections.notes}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Terms & Conditions</label>
                    <Textarea
                      value={terms}
                      onChange={(e) => setTerms(e.target.value)}
                      placeholder="Payment due within 30 days..."
                      rows={4}
                      disabled={isHydrated && lockedSections.notes}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Summary */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Invoice Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span className="font-semibold">{currency} {subtotal.toFixed(2)}</span>
                  </div>
                  {discount > 0 && (
                    <div className="flex justify-between text-red-600">
                      <span>Discount ({discount}%):</span>
                      <span>-{currency} {discountAmount.toFixed(2)}</span>
                    </div>
                  )}
                  {deductionAmount > 0 && (
                    <div className="flex justify-between text-orange-600">
                      <span>Deduction Amount:</span>
                      <span>-{currency} {deductionAmount.toFixed(2)}</span>
                    </div>
                  )}
                  {deductionAmount > 0 && deductionReason && (
                    <div className="text-sm text-gray-600 bg-orange-50 p-2 rounded">
                      <strong>Deduction Reason:</strong> {deductionReason}
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span>Amount Before VAT:</span>
                    <span className="font-semibold">{currency} {subtotalAfterDeduction.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total VAT (15%):</span>
                    <span className="font-semibold">{currency} {totalVAT.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t pt-3">
                    <span>Total Amount:</span>
                    <span className="text-purple-600">{currency} {totalAmount.toFixed(2)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <QrCode className="h-5 w-5" />
                  <span>Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button
                    onClick={previewInvoice}
                    className="w-full bg-purple-600 hover:bg-purple-700"
                    size="lg"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Preview Invoice
                  </Button>
                  <Button
                    onClick={generatePDF}
                    className="w-full bg-green-600 hover:bg-green-700"
                    size="lg"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Generate & Download PDF
                  </Button>
                  <Button
                    onClick={handlePrintInvoice}
                    variant="outline"
                    className="w-full border-purple-300 text-purple-600 hover:bg-purple-50"
                    size="lg"
                  >
                    <Printer className="h-4 w-4 mr-2" />
                    Print Invoice
                  </Button>
                  <Button
                    onClick={saveDraft}
                    variant="outline"
                    className="w-full"
                    size="lg"
                  >
                    💾 Save Draft
                  </Button>
                  <Button
                    onClick={saveAsCompleted}
                    variant="outline"
                    className="w-full bg-green-50 text-green-600 hover:bg-green-100 border-green-200"
                    size="lg"
                  >
                    ✅ Mark as Completed
                  </Button>
                </div>

                {/* Logo Preview */}
                {(sellerLogo || clientLogo) && (
                  <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-gray-200">
                    <p className="text-sm text-gray-700 mb-3 text-center font-medium">📸 Logo Preview</p>
                    <div className="flex justify-center space-x-6">
                      {sellerLogo && (
                        <div className="text-center">
                          <p className="text-xs text-blue-600 mb-2 font-medium">Seller Logo</p>
                          <div className="w-20 h-16 bg-white border-2 border-blue-200 rounded-lg overflow-hidden shadow-sm">
                            <img
                              src={sellerLogo}
                              alt="Seller Logo Preview"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">Auto-optimized</p>
                        </div>
                      )}
                      {clientLogo && (
                        <div className="text-center">
                          <p className="text-xs text-green-600 mb-2 font-medium">Client Logo</p>
                          <div className="w-20 h-16 bg-white border-2 border-green-200 rounded-lg overflow-hidden shadow-sm">
                            <img
                              src={clientLogo}
                              alt="Client Logo Preview"
                              className="w-full h-full object-contain"
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">Auto-optimized</p>
                        </div>
                      )}
                    </div>
                    <p className="text-xs text-center text-gray-600 mt-3">
                      ✨ Logos are automatically resized and optimized for PDF generation
                    </p>
                  </div>
                )}

                {/* QR Code Preview */}
                {sellerName && clientName && totalAmount > 0 && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-lg text-center">
                    <p className="text-sm text-gray-600 mb-2">ZATCA QR Code will be generated</p>
                    <div className="w-16 h-16 bg-white border-2 border-gray-300 rounded mx-auto flex items-center justify-center">
                      <QrCode className="h-8 w-8 text-gray-400" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">QR code for ZATCA compliance</p>
                    <p className="text-xs text-blue-600 mt-1">
                      📅 Always uses current date: {new Date().toLocaleDateString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Invoices */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <span>📋 Recent Invoices</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {getRecentInvoices().length > 0 ? (
                    getRecentInvoices().map((invoice, index) => (
                      <div
                        key={invoice.key}
                        className="p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors"
                        onClick={() => loadInvoiceData(invoice)}
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="font-medium text-sm text-blue-600">{invoice.invoiceNumber}</p>
                            <p className="text-xs text-gray-600 truncate">{invoice.clientName || 'No client'}</p>
                            <p className="text-xs text-gray-500">
                              {new Date(invoice.timestamp).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-green-600">
                              {invoice.currency} {(invoice.totalAmount || 0).toFixed(2)}
                            </p>
                            {invoice.deductionAmount > 0 && (
                              <p className="text-xs text-orange-600">-{invoice.deductionAmount}</p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      <p className="text-sm">No recent invoices found</p>
                      <p className="text-xs">Create and save invoices to see them here</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Validation Status */}
            <Card>
              <CardHeader>
                <CardTitle>Validation Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Seller Details:</span>
                    <span className={`text-sm ${sellerName && sellerVAT ? 'text-green-600' : 'text-red-600'}`}>
                      {sellerName && sellerVAT ? '✓ Complete' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Client Details:</span>
                    <span className={`text-sm ${clientName && clientVAT ? 'text-green-600' : 'text-red-600'}`}>
                      {clientName && clientVAT ? '✓ Complete' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Invoice Items:</span>
                    <span className={`text-sm ${items.length > 0 && items[0].description ? 'text-green-600' : 'text-red-600'}`}>
                      {items.length > 0 && items[0].description ? '✓ Complete' : '✗ Missing'}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Deduction Reason:</span>
                    <span className={`text-sm ${deductionAmount > 0 ? (deductionReason.trim() ? 'text-green-600' : 'text-red-600') : 'text-gray-400'}`}>
                      {deductionAmount > 0 ? (deductionReason.trim() ? '✓ Complete' : '✗ Required') : 'N/A'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
