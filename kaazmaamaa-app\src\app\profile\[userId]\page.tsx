'use client'

import { useAuth } from '@/contexts/AuthContext'
import { usePosts } from '@/contexts/PostsContext'
import { useOnlineUsers } from '@/contexts/OnlineUsersContext'
import { useSocial } from '@/contexts/SocialContext'
import { useWorkers } from '@/contexts/WorkersContext'
import { useSuppliers } from '@/contexts/SuppliersContext'
import { useCompanies } from '@/contexts/CompaniesContext'
import { useBarta } from '@/contexts/BartaContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState, use } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import {
  ArrowLeft,
  Heart,
  MessageCircle,
  Share,
  Plus,
  Users,
  Building,
  Wrench,
  ThumbsUp,
  Send,
  MapPin,
  Calendar,
  Mail,
  Phone,
  Star,
  CheckCircle,
  UserPlus,
  MessageSquare
} from 'lucide-react'
import { toast } from 'sonner'

interface UserProfilePageProps {
  params: Promise<{
    userId: string
  }>
}

export default function UserProfilePage({ params }: UserProfilePageProps) {
  const resolvedParams = use(params)
  const { user: currentUser, userRole } = useAuth()
  const { posts, addPost, likePost, sharePost, addComment } = usePosts()
  const { onlineUsers, isUserOnline } = useOnlineUsers()
  const { followUser, unfollowUser, isFollowing: checkIsFollowing, recordProfileView, getProfileStats, updateProfileInteraction } = useSocial()
  const { workers } = useWorkers()
  const { suppliers } = useSuppliers()
  const { companies } = useCompanies()
  const { createConversation, getConversationWithUser } = useBarta()
  const router = useRouter()

  const [profileUser, setProfileUser] = useState<any>(null)
  const [userPosts, setUserPosts] = useState<any[]>([])
  const [newPostContent, setNewPostContent] = useState('')
  const [commentContent, setCommentContent] = useState<{ [postId: string]: string }>({})
  const [showComments, setShowComments] = useState<{ [postId: string]: boolean }>({})
  const [userIsFollowing, setUserIsFollowing] = useState(false)
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [messageContent, setMessageContent] = useState('')

  // Check if this is the current user's own profile
  const isOwnProfile = currentUser?.id === resolvedParams.userId

  // Find profile for this user across all profile types
  console.log('🔍 Profile page looking for userId:', resolvedParams.userId)
  console.log('🔍 Current user:', currentUser)
  console.log('🔍 Available workers:', workers.map(w => ({ userId: w.userId, name: w.personalInfo?.workerName })))
  console.log('🔍 Available suppliers:', suppliers.map(s => ({ userId: s.userId, name: s.personalInfo?.supplierName })))
  console.log('🔍 Available companies:', companies.map(c => ({ userId: c.userId, name: c.companyInfo?.companyName })))

  // Try multiple ways to find the profile (in case of URL encoding issues)
  const workerProfile = workers.find(w =>
    w.userId === resolvedParams.userId ||
    w.userId === decodeURIComponent(resolvedParams.userId) ||
    (currentUser && (w.userId === currentUser.id || w.userId === currentUser.email))
  )
  const supplierProfile = suppliers.find(s =>
    s.userId === resolvedParams.userId ||
    s.userId === decodeURIComponent(resolvedParams.userId) ||
    (currentUser && (s.userId === currentUser.id || s.userId === currentUser.email))
  )
  const companyProfile = companies.find(c =>
    c.userId === resolvedParams.userId ||
    c.userId === decodeURIComponent(resolvedParams.userId) ||
    (currentUser && (c.userId === currentUser.id || c.userId === currentUser.email))
  )

  console.log('🔍 Found profiles:', { workerProfile: !!workerProfile, supplierProfile: !!supplierProfile, companyProfile: !!companyProfile })

  // Determine which profile exists for this user
  const userProfile = workerProfile || supplierProfile || companyProfile
  const profileType = workerProfile ? 'worker' : supplierProfile ? 'supplier' : companyProfile ? 'company' : null

  console.log('🔍 Final profile type:', profileType, 'Has profile:', !!userProfile)

  // Utility function to get user's full name from any profile type
  const getUserFullName = (userId: string, fallbackEmail?: string): string => {
    console.log('🔍 Profile page getUserFullName called with:', userId)
    console.log('🔍 Profile page checking against profiles:', {
      workers: workers.map(w => w.userId),
      suppliers: suppliers.map(s => s.userId),
      companies: companies.map(c => c.userId)
    })

    // Check workers with flexible matching
    const workerProfile = workers.find(w =>
      w.userId === userId ||
      w.userId === decodeURIComponent(userId) ||
      (currentUser && (w.userId === currentUser.id || w.userId === currentUser.email))
    )
    if (workerProfile?.personalInfo.workerName) {
      console.log('✅ Found worker profile:', workerProfile.personalInfo.workerName)
      return workerProfile.personalInfo.workerName
    }

    // Check suppliers with flexible matching
    const supplierProfile = suppliers.find(s =>
      s.userId === userId ||
      s.userId === decodeURIComponent(userId) ||
      (currentUser && (s.userId === currentUser.id || s.userId === currentUser.email))
    )
    if (supplierProfile?.personalInfo?.supplierName) {
      console.log('✅ Found supplier profile:', supplierProfile.personalInfo.supplierName)
      return supplierProfile.personalInfo.supplierName
    }

    // Check companies with flexible matching
    const companyProfile = companies.find(c =>
      c.userId === userId ||
      c.userId === decodeURIComponent(userId) ||
      (currentUser && (c.userId === currentUser.id || c.userId === currentUser.email))
    )
    if (companyProfile?.companyInfo?.companyName) {
      console.log('✅ Found company profile:', companyProfile.companyInfo.companyName)
      return companyProfile.companyInfo.companyName
    }

    // Then try to find in online users
    const onlineUser = onlineUsers.find(u => u.id === userId)
    if (onlineUser?.name && !onlineUser.name.includes('@')) {
      console.log('✅ Found online user:', onlineUser.name)
      return onlineUser.name
    }

    // Fallback to email-based name but make it more readable
    if (fallbackEmail) {
      const emailName = fallbackEmail.split('@')[0] || 'Unknown User'
      const readableName = emailName
        .replace(/[0-9]/g, '') // Remove numbers
        .replace(/[._-]/g, ' ') // Replace special chars with spaces
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize
        .join(' ')
        .trim()

      const finalName = readableName || emailName
      console.log('✅ Using fallback name:', finalName)
      return finalName
    }

    console.log('❌ No name found, using Unknown User')
    return 'Unknown User'
  }

  useEffect(() => {
    if (!currentUser) {
      router.push('/')
      return
    }

    // Find the user from actual profiles or online users
    console.log('🔍 Looking for user profile:', resolvedParams.userId)
    console.log('🔍 Available profiles:', {
      workers: workers.length,
      suppliers: suppliers.length,
      companies: companies.length
    })

    // Check if user has an actual profile
    const hasActualProfile = userProfile !== null
    console.log('🔍 Has actual profile:', hasActualProfile, 'Type:', profileType)

    // Find the user from online users (for additional data)
    const foundUser = onlineUsers.find(u => u.id === resolvedParams.userId) ||
                     (hasActualProfile ? {
                       id: resolvedParams.userId,
                       name: getUserFullName(resolvedParams.userId),
                       role: profileType
                     } : null)

    // Create mock profiles for mock users
    const mockProfiles: { [key: string]: any } = {
      'mock-user-1': {
        id: 'mock-user-1',
        name: 'Ahmed Al-Rashid',
        email: '<EMAIL>',
        role: 'worker',
        bio: 'Senior Electrical Technician at KAAZMAAMAA',
        location: 'Riyadh, Saudi Arabia',
        joinedDate: '2024-01-01',
        followers: 125,
        following: 89,
        postsCount: posts.filter(p => p.user_id === 'mock-user-1').length
      },
      'mock-user-2': {
        id: 'mock-user-2',
        name: 'Saudi Construction Co.',
        email: '<EMAIL>',
        role: 'company',
        bio: 'Leading Construction Company in Saudi Arabia',
        location: 'Jeddah, Saudi Arabia',
        joinedDate: '2024-01-01',
        followers: 450,
        following: 120,
        postsCount: posts.filter(p => p.user_id === 'mock-user-2').length
      },
      'mock-user-3': {
        id: 'mock-user-3',
        name: 'Gulf Manpower Solutions',
        email: '<EMAIL>',
        role: 'supplier',
        bio: 'Professional Manpower Supplier',
        location: 'Dammam, Saudi Arabia',
        joinedDate: '2024-01-01',
        followers: 320,
        following: 95,
        postsCount: posts.filter(p => p.user_id === 'mock-user-3').length
      },
      'mock-user-4': {
        id: 'mock-user-4',
        name: 'Mohammed Hassan',
        email: '<EMAIL>',
        role: 'worker',
        bio: 'Safety Certified Oil & Gas Worker',
        location: 'Khobar, Saudi Arabia',
        joinedDate: '2024-01-01',
        followers: 78,
        following: 45,
        postsCount: posts.filter(p => p.user_id === 'mock-user-4').length
      }
    }

    if (foundUser || hasActualProfile) {
      console.log('✅ Creating profile user data')

      // Get profile-specific data
      let profileData = {}
      if (profileType === 'worker' && workerProfile) {
        profileData = {
          bio: `Worker at KAAZMAAMAA - ${workerProfile.personalInfo.profession || 'Professional'}`,
          location: workerProfile.personalInfo.location || 'Saudi Arabia',
          email: workerProfile.personalInfo.email,
          phone: workerProfile.personalInfo.phone,
          experience: workerProfile.personalInfo.experience,
          skills: workerProfile.personalInfo.skills
        }
      } else if (profileType === 'supplier' && supplierProfile) {
        profileData = {
          bio: `Supplier at KAAZMAAMAA - ${supplierProfile.businessInfo?.businessType || 'Business Provider'}`,
          location: supplierProfile.personalInfo?.address || 'Saudi Arabia',
          email: supplierProfile.personalInfo?.email,
          phone: supplierProfile.personalInfo?.phone,
          company: supplierProfile.businessInfo?.companyName
        }
      } else if (profileType === 'company' && companyProfile) {
        profileData = {
          bio: `${companyProfile.companyInfo?.industry || 'Company'} - ${companyProfile.companyInfo?.description || 'Professional Services'}`,
          location: companyProfile.companyInfo?.headquarters || 'Saudi Arabia',
          email: companyProfile.contactInfo?.hrEmail,
          phone: companyProfile.contactInfo?.hrPhone,
          website: companyProfile.companyInfo?.website
        }
      }

      setProfileUser({
        id: resolvedParams.userId,
        name: getUserFullName(resolvedParams.userId),
        role: profileType || foundUser?.role || 'user',
        joinedDate: userProfile?.createdAt?.split('T')[0] || '2024-01-01',
        followers: Math.floor(Math.random() * 500) + 50,
        following: Math.floor(Math.random() * 200) + 20,
        postsCount: posts.filter(p => p.user_id === resolvedParams.userId).length,
        ...profileData
      })
    } else if (mockProfiles[resolvedParams.userId]) {
      // Use mock profile data
      console.log('✅ Using mock profile data')
      setProfileUser(mockProfiles[resolvedParams.userId])
    } else {
      // If user not found, redirect back
      console.log('❌ User not found, redirecting to feed')
      toast.error('User not found')
      router.push('/feed')
    }

    // Filter posts by this user
    const filteredPosts = posts.filter(post => post.user_id === resolvedParams.userId)
    setUserPosts(filteredPosts)

    // Record profile view and check following status
    if (foundUser && currentUser?.id !== resolvedParams.userId) {
      recordProfileView(resolvedParams.userId)
      setUserIsFollowing(checkIsFollowing(resolvedParams.userId))
    }
  }, [resolvedParams.userId, onlineUsers, posts, currentUser, router, recordProfileView, checkIsFollowing])

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'worker':
        return <Wrench className="h-5 w-5 text-blue-600" />
      case 'supplier':
        return <Users className="h-5 w-5 text-green-600" />
      case 'company':
        return <Building className="h-5 w-5 text-purple-600" />
      default:
        return <Users className="h-5 w-5 text-gray-600" />
    }
  }

  const handleCreatePost = () => {
    if (!newPostContent.trim()) {
      toast.error('Please write something to post!')
      return
    }

    const newPost = {
      user_id: currentUser?.id || 'current-user',
      user_name: getUserFullName(currentUser?.id || 'current-user', currentUser?.email),
      user_role: userRole || 'worker',
      content: `@${workerProfile?.personalInfo.workerName || profileUser?.name} ${newPostContent.trim()}`,
      post_type: 'text' as const,
      visibility: 'public' as const
    }

    addPost(newPost)
    setNewPostContent('')
    toast.success('Post created on their profile! 🎉')
  }

  const handleLike = (postId: string) => {
    likePost(postId)
    updateProfileInteraction(resolvedParams.userId, 'like')
    toast.success('Post liked! ❤️')
  }

  const handleShare = (postId: string) => {
    sharePost(postId)
    updateProfileInteraction(resolvedParams.userId, 'share')
    toast.success('Post shared! 🔄')
  }

  const handleComment = (postId: string) => {
    const content = commentContent[postId]?.trim()
    if (!content) {
      toast.error('Please write a comment!')
      return
    }

    addComment(postId, content, {
      id: currentUser?.id || 'current-user',
      name: currentUser?.email?.split('@')[0] || 'Current User',
      role: userRole || 'worker'
    })

    setCommentContent(prev => ({ ...prev, [postId]: '' }))
    updateProfileInteraction(resolvedParams.userId, 'comment')
    toast.success('Comment added! 💬')
  }

  const toggleComments = (postId: string) => {
    setShowComments(prev => ({
      ...prev,
      [postId]: !prev[postId]
    }))
  }

  const handleFollow = () => {
    if (userIsFollowing) {
      unfollowUser(resolvedParams.userId)
      setUserIsFollowing(false)
      toast.success('Unfollowed user')
    } else {
      followUser(resolvedParams.userId, profileUser?.name || 'User', profileUser?.role || 'worker')
      setUserIsFollowing(true)
      toast.success('Following user! 👥')
    }
  }

  const handleSendMessage = () => {
    if (!messageContent.trim()) {
      toast.error('Please write a message!')
      return
    }

    // TODO: Implement actual messaging system
    // For now, just show success message
    console.log('Sending message to:', profileUser?.name, 'Content:', messageContent)

    toast.success(`💬 Message sent to ${profileUser?.name}!`)
    setMessageContent('')
    setShowMessageModal(false)

    // In a real app, this would save to database and send notification
  }

  const handleOpenMessage = () => {
    // Check if conversation already exists
    const existingConversation = getConversationWithUser(resolvedParams.userId)

    if (existingConversation) {
      // Navigate to existing conversation
      router.push(`/barta?conversation=${existingConversation.id}`)
      toast.success(`💬 Opening chat with ${profileUser?.name}...`)
    } else {
      // Create new conversation and navigate
      const conversationId = createConversation(resolvedParams.userId)
      if (conversationId) {
        router.push(`/barta?conversation=${conversationId}`)
        toast.success(`💬 Starting new chat with ${profileUser?.name}...`)
      } else {
        toast.error('Unable to start conversation. Please try again.')
      }
    }

    console.log('Opening Barta conversation with user:', profileUser?.name)
  }

  if (!profileUser) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const isOnline = isUserOnline(profileUser.id)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push('/feed')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Feed
            </Button>
            <h1 className="text-xl font-bold text-gray-900">
              {isOwnProfile ? 'My Profile' : 'User Profile'}
            </h1>
            <div className="flex items-center space-x-2">
              {isOwnProfile && (
                <Button
                  onClick={() => router.push('/feed')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Go to Feed Page
                </Button>
              )}
              {isOnline && (
                <Badge className="bg-green-100 text-green-800">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                  Online
                </Badge>
              )}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Profile Header */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
              <div className="relative">
                <Avatar className="h-32 w-32 bg-gray-100 flex items-center justify-center">
                  {getRoleIcon(profileUser.role)}
                </Avatar>
                {isOnline && (
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                  </div>
                )}
                {profileUser.role === 'supplier' && (
                  <div className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full p-2">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                )}
              </div>

              <div className="flex-1 text-center md:text-left">
                <h1 className="text-3xl font-bold text-gray-900">
                  {workerProfile?.personalInfo.workerName || profileUser.name}
                </h1>
                <p className="text-lg text-gray-600 mt-1">
                  {workerProfile?.professionalInfo.title || profileUser.bio}
                </p>
                {workerProfile?.professionalInfo.specialty && (
                  <p className="text-md text-gray-500 mt-1">
                    {workerProfile.professionalInfo.specialty}
                  </p>
                )}
                <div className="flex items-center justify-center md:justify-start space-x-4 mt-3 text-sm text-gray-500">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {workerProfile?.personalInfo.address || profileUser.location}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Joined {workerProfile ? new Date(workerProfile.lifecycle.joinDate).getFullYear() : new Date(profileUser.joinedDate).getFullYear()}
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-1" />
                    {workerProfile?.personalInfo.email || profileUser.email}
                  </div>
                  {workerProfile?.personalInfo.phone && (
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      {workerProfile.personalInfo.phone}
                    </div>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-center md:justify-start space-x-6 mt-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{profileUser.postsCount}</p>
                    <p className="text-sm text-gray-500">Posts</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{profileUser.followers}</p>
                    <p className="text-sm text-gray-500">Followers</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-gray-900">{profileUser.following}</p>
                    <p className="text-sm text-gray-500">Following</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-center md:justify-start space-x-3 mt-6">
                  {isOwnProfile ? (
                    <>
                      <Button
                        onClick={() => router.push(`/workers/edit/${resolvedParams.userId}`)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <UserPlus className="h-4 w-4 mr-2" />
                        Edit Profile
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => router.push('/settings')}
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Settings
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          const profileUrl = `${window.location.origin}/profile/${resolvedParams.userId}`
                          navigator.clipboard.writeText(profileUrl).then(() => {
                            toast.success(`📋 Your profile link copied to clipboard!`)
                          }).catch(() => {
                            toast.info(`🔗 Your Profile URL: ${profileUrl}`)
                          })
                          console.log('Share own profile button clicked')
                        }}
                      >
                        <Share className="h-4 w-4 mr-2" />
                        Share Profile
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        onClick={handleFollow}
                        className={userIsFollowing ? 'bg-gray-600 hover:bg-gray-700' : 'bg-blue-600 hover:bg-blue-700'}
                      >
                        <UserPlus className="h-4 w-4 mr-2" />
                        {userIsFollowing ? 'Following' : 'Follow'}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleOpenMessage}
                      >
                        <MessageSquare className="h-4 w-4 mr-2" />
                        Message
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          const profileUrl = `${window.location.origin}/profile/${resolvedParams.userId}`
                          navigator.clipboard.writeText(profileUrl).then(() => {
                            toast.success(`📋 Profile link copied to clipboard!`)
                          }).catch(() => {
                            toast.info(`🔗 Profile URL: ${profileUrl}`)
                          })
                          console.log('Share button clicked for user:', profileUser?.name)
                        }}
                      >
                        <Share className="h-4 w-4 mr-2" />
                        Share Profile
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Sidebar - About */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>About</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center">
                    {getRoleIcon(profileUser.role)}
                    <span className="ml-2 capitalize">{profileUser.role}</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    {profileUser.email}
                  </div>
                  {isOnline ? (
                    <div className="flex items-center text-green-600">
                      <div className="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                      Online now
                    </div>
                  ) : (
                    <div className="flex items-center text-gray-500">
                      <div className="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                      Last seen recently
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content - Posts */}
          <div className="lg:col-span-2 space-y-6">
            {/* Create Post on Their Profile */}
            <Card>
              <CardHeader>
                <CardTitle>Write on {profileUser.name}'s profile</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Textarea
                    placeholder={`What's on your mind about ${profileUser.name}?`}
                    value={newPostContent}
                    onChange={(e) => setNewPostContent(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <Button onClick={handleCreatePost} className="w-full">
                    <Send className="h-4 w-4 mr-2" />
                    Post on {profileUser.name}'s Profile
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* User's Posts */}
            <div className="space-y-4">
              <h2 className="text-xl font-bold text-gray-900">{profileUser.name}'s Posts</h2>

              {userPosts.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Posts Yet</h3>
                    <p className="text-gray-600">{profileUser.name} hasn't shared anything yet.</p>
                  </CardContent>
                </Card>
              ) : (
                userPosts.map((post) => (
                  <Card key={post.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start space-x-3">
                        <Avatar className="h-10 w-10 bg-gray-100 flex items-center justify-center">
                          {getRoleIcon(post.user_role)}
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold">{post.user_name}</h3>
                            <Badge variant="secondary" className="text-xs capitalize">
                              {post.user_role}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              {new Date(post.created_at).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      <p className="text-gray-800 mb-4">{post.content}</p>

                      {/* Post Actions */}
                      <div className="flex items-center justify-between pt-3 border-t">
                        <div className="flex items-center space-x-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleLike(post.id)}
                            className="text-gray-600 hover:text-red-600"
                          >
                            <Heart className={`h-4 w-4 mr-1 ${Array.isArray(post.likes) && post.likes.includes(currentUser?.id) ? 'fill-red-500 text-red-500' : ''}`} />
                            {Array.isArray(post.likes) ? post.likes.length : (post.likes || 0)}
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleComments(post.id)}
                            className="text-gray-600 hover:text-blue-600"
                          >
                            <MessageCircle className="h-4 w-4 mr-1" />
                            {Array.isArray(post.comments) ? post.comments.length : (post.comments || 0)}
                          </Button>

                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShare(post.id)}
                            className="text-gray-600 hover:text-green-600"
                          >
                            <Share className="h-4 w-4 mr-1" />
                            {Array.isArray(post.shares) ? post.shares.length : (post.shares || 0)}
                          </Button>
                        </div>
                      </div>

                      {/* Comments Section */}
                      {showComments[post.id] && (
                        <div className="mt-4 pt-4 border-t space-y-3">
                          {/* Add Comment */}
                          <div className="flex space-x-2">
                            <Avatar className="h-8 w-8 bg-gray-100 flex items-center justify-center">
                              {getRoleIcon(userRole || 'worker')}
                            </Avatar>
                            <div className="flex-1 flex space-x-2">
                              <Textarea
                                placeholder="Write a comment..."
                                value={commentContent[post.id] || ''}
                                onChange={(e) => setCommentContent(prev => ({ ...prev, [post.id]: e.target.value }))}
                                className="min-h-[60px] text-sm"
                              />
                              <Button
                                size="sm"
                                onClick={() => handleComment(post.id)}
                                disabled={!commentContent[post.id]?.trim()}
                              >
                                <Send className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {/* Existing Comments */}
                          {post.comments?.map((comment: any, index: number) => (
                            <div key={index} className="flex space-x-2">
                              <Avatar className="h-8 w-8 bg-gray-100 flex items-center justify-center">
                                {getRoleIcon(comment.user.role)}
                              </Avatar>
                              <div className="flex-1 bg-gray-50 rounded-lg p-3">
                                <div className="flex items-center space-x-2 mb-1">
                                  <span className="font-medium text-sm">{comment.user.name}</span>
                                  <Badge variant="secondary" className="text-xs capitalize">
                                    {comment.user.role}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-800">{comment.content}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Message Modal */}
      <Dialog open={showMessageModal} onOpenChange={setShowMessageModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-blue-600" />
              Send Message to {profileUser?.name}
            </DialogTitle>
            <DialogDescription>
              Send a private message to {profileUser?.name}. They will be notified of your message.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Your Message
              </label>
              <Textarea
                id="message"
                value={messageContent}
                onChange={(e) => setMessageContent(e.target.value)}
                placeholder={`Write your message to ${profileUser?.name}...`}
                rows={4}
                className="w-full"
              />
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setShowMessageModal(false)
                  setMessageContent('')
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSendMessage}
                disabled={!messageContent.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Send className="h-4 w-4 mr-2" />
                Send Message
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
