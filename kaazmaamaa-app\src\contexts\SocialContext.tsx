'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'

export interface UserConnection {
  userId: string
  userName: string
  userRole: string
  connectedAt: string
  isFollowing: boolean
  isFollowedBy: boolean
}

export interface ProfileInteraction {
  userId: string
  profileViews: number
  lastViewedAt: string
  totalLikes: number
  totalComments: number
  totalShares: number
}

interface SocialContextType {
  connections: UserConnection[]
  profileInteractions: { [userId: string]: ProfileInteraction }
  followUser: (userId: string, userName: string, userRole: string) => void
  unfollowUser: (userId: string) => void
  isFollowing: (userId: string) => boolean
  getFollowers: (userId: string) => UserConnection[]
  getFollowing: (userId: string) => UserConnection[]
  recordProfileView: (userId: string) => void
  getProfileStats: (userId: string) => ProfileInteraction
  updateProfileInteraction: (userId: string, type: 'like' | 'comment' | 'share') => void
}

const SocialContext = createContext<SocialContextType | undefined>(undefined)

export function SocialProvider({ children }: { children: ReactNode }) {
  const [connections, setConnections] = useState<UserConnection[]>([])
  const [profileInteractions, setProfileInteractions] = useState<{ [userId: string]: ProfileInteraction }>({})

  useEffect(() => {
    // Load social data from localStorage
    const savedConnections = localStorage.getItem('kaazmaamaa-connections')
    const savedInteractions = localStorage.getItem('kaazmaamaa-profile-interactions')
    
    if (savedConnections) {
      setConnections(JSON.parse(savedConnections))
    }
    
    if (savedInteractions) {
      setProfileInteractions(JSON.parse(savedInteractions))
    }
  }, [])

  const saveConnections = useCallback((updatedConnections: UserConnection[]) => {
    localStorage.setItem('kaazmaamaa-connections', JSON.stringify(updatedConnections))
  }, [])

  const saveInteractions = useCallback((updatedInteractions: { [userId: string]: ProfileInteraction }) => {
    localStorage.setItem('kaazmaamaa-profile-interactions', JSON.stringify(updatedInteractions))
  }, [])

  const followUser = useCallback((userId: string, userName: string, userRole: string) => {
    const newConnection: UserConnection = {
      userId,
      userName,
      userRole,
      connectedAt: new Date().toISOString(),
      isFollowing: true,
      isFollowedBy: false
    }

    const updatedConnections = [...connections.filter(c => c.userId !== userId), newConnection]
    setConnections(updatedConnections)
    saveConnections(updatedConnections)
  }, [connections, saveConnections])

  const unfollowUser = useCallback((userId: string) => {
    const updatedConnections = connections.filter(c => c.userId !== userId)
    setConnections(updatedConnections)
    saveConnections(updatedConnections)
  }, [connections, saveConnections])

  const isFollowing = useCallback((userId: string) => {
    return connections.some(c => c.userId === userId && c.isFollowing)
  }, [connections])

  const getFollowers = useCallback((userId: string) => {
    return connections.filter(c => c.userId === userId && c.isFollowedBy)
  }, [connections])

  const getFollowing = useCallback((userId: string) => {
    return connections.filter(c => c.isFollowing)
  }, [connections])

  const recordProfileView = useCallback((userId: string) => {
    setProfileInteractions(prev => {
      const current = prev[userId] || {
        userId,
        profileViews: 0,
        lastViewedAt: new Date().toISOString(),
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0
      }

      const updated = {
        ...prev,
        [userId]: {
          ...current,
          profileViews: current.profileViews + 1,
          lastViewedAt: new Date().toISOString()
        }
      }

      saveInteractions(updated)
      return updated
    })
  }, [saveInteractions])

  const getProfileStats = useCallback((userId: string): ProfileInteraction => {
    return profileInteractions[userId] || {
      userId,
      profileViews: 0,
      lastViewedAt: new Date().toISOString(),
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0
    }
  }, [profileInteractions])

  const updateProfileInteraction = useCallback((userId: string, type: 'like' | 'comment' | 'share') => {
    setProfileInteractions(prev => {
      const current = prev[userId] || {
        userId,
        profileViews: 0,
        lastViewedAt: new Date().toISOString(),
        totalLikes: 0,
        totalComments: 0,
        totalShares: 0
      }

      const updated = {
        ...prev,
        [userId]: {
          ...current,
          [`total${type.charAt(0).toUpperCase() + type.slice(1)}s`]: 
            current[`total${type.charAt(0).toUpperCase() + type.slice(1)}s` as keyof ProfileInteraction] as number + 1
        }
      }

      saveInteractions(updated)
      return updated
    })
  }, [saveInteractions])

  const value = {
    connections,
    profileInteractions,
    followUser,
    unfollowUser,
    isFollowing,
    getFollowers,
    getFollowing,
    recordProfileView,
    getProfileStats,
    updateProfileInteraction
  }

  return (
    <SocialContext.Provider value={value}>
      {children}
    </SocialContext.Provider>
  )
}

export function useSocial() {
  const context = useContext(SocialContext)
  if (context === undefined) {
    throw new Error('useSocial must be used within a SocialProvider')
  }
  return context
}
