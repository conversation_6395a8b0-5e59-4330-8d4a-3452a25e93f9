{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/utils/zatcaQR.ts"], "sourcesContent": ["// ZATCA QR Code Generation Utility\n// Following ZATCA TLV (Tag-Length-Value) format specifications\n\nexport interface ZATCAQRData {\n  sellerName: string\n  vatNumber: string\n  timestamp: string\n  invoiceTotal: string\n  vatTotal: string\n  invoiceHash?: string\n}\n\n// ZATCA TLV Tags as per official specification\nexport const ZATCA_TAGS = {\n  SELLER_NAME: 1,\n  VAT_NUMBER: 2,\n  TIMESTAMP: 3,\n  INVOICE_TOTAL: 4,\n  VAT_TOTAL: 5,\n  INVOICE_HASH: 6\n} as const\n\n/**\n * Generate TLV (Tag-Length-Value) encoded data for ZATCA QR code\n * @param tag - ZATCA tag number\n * @param value - String value to encode\n * @returns Uint8Array containing TLV encoded data\n */\nexport function generateTLV(tag: number, value: string): Uint8Array {\n  const valueBytes = new TextEncoder().encode(value)\n  const length = valueBytes.length\n  \n  // Create TLV structure: [Tag][Length][Value]\n  const tlv = new Uint8Array(2 + length)\n  tlv[0] = tag\n  tlv[1] = length\n  tlv.set(valueBytes, 2)\n  \n  return tlv\n}\n\n/**\n * Generate ZATCA-compliant QR code data in Base64 format\n * @param data - Invoice data for QR code generation\n * @returns Base64 encoded QR code string\n */\nexport function generateZATCAQRCode(data: ZATCAQRData): string {\n  try {\n    // Generate TLV for each required field\n    const sellerNameTLV = generateTLV(ZATCA_TAGS.SELLER_NAME, data.sellerName)\n    const vatNumberTLV = generateTLV(ZATCA_TAGS.VAT_NUMBER, data.vatNumber)\n    const timestampTLV = generateTLV(ZATCA_TAGS.TIMESTAMP, data.timestamp)\n    const invoiceTotalTLV = generateTLV(ZATCA_TAGS.INVOICE_TOTAL, data.invoiceTotal)\n    const vatTotalTLV = generateTLV(ZATCA_TAGS.VAT_TOTAL, data.vatTotal)\n    \n    // Calculate total length\n    let totalLength = sellerNameTLV.length + vatNumberTLV.length + timestampTLV.length + \n                     invoiceTotalTLV.length + vatTotalTLV.length\n    \n    // Add invoice hash if provided\n    let invoiceHashTLV: Uint8Array | null = null\n    if (data.invoiceHash) {\n      invoiceHashTLV = generateTLV(ZATCA_TAGS.INVOICE_HASH, data.invoiceHash)\n      totalLength += invoiceHashTLV.length\n    }\n    \n    // Combine all TLV data\n    const qrData = new Uint8Array(totalLength)\n    let offset = 0\n    \n    qrData.set(sellerNameTLV, offset)\n    offset += sellerNameTLV.length\n    \n    qrData.set(vatNumberTLV, offset)\n    offset += vatNumberTLV.length\n    \n    qrData.set(timestampTLV, offset)\n    offset += timestampTLV.length\n    \n    qrData.set(invoiceTotalTLV, offset)\n    offset += invoiceTotalTLV.length\n    \n    qrData.set(vatTotalTLV, offset)\n    offset += vatTotalTLV.length\n    \n    if (invoiceHashTLV) {\n      qrData.set(invoiceHashTLV, offset)\n    }\n    \n    // Convert to Base64\n    return btoa(String.fromCharCode(...qrData))\n    \n  } catch (error) {\n    console.error('Error generating ZATCA QR code:', error)\n    throw new Error('Failed to generate ZATCA QR code')\n  }\n}\n\n/**\n * Generate SHA-256 hash for invoice (simplified version)\n * In production, this should use proper XML canonicalization\n * @param invoiceData - Invoice data to hash\n * @returns Base64 encoded hash\n */\nexport async function generateInvoiceHash(invoiceData: any): Promise<string> {\n  try {\n    // Convert invoice data to string (in production, use XML canonicalization)\n    const dataString = JSON.stringify(invoiceData)\n    \n    // Generate SHA-256 hash\n    const encoder = new TextEncoder()\n    const data = encoder.encode(dataString)\n    const hashBuffer = await crypto.subtle.digest('SHA-256', data)\n    \n    // Convert to Base64\n    const hashArray = new Uint8Array(hashBuffer)\n    return btoa(String.fromCharCode(...hashArray))\n    \n  } catch (error) {\n    console.error('Error generating invoice hash:', error)\n    throw new Error('Failed to generate invoice hash')\n  }\n}\n\n/**\n * Validate ZATCA QR code data\n * @param data - QR code data to validate\n * @returns Validation result with errors if any\n */\nexport function validateZATCAQRData(data: ZATCAQRData): { isValid: boolean; errors: string[] } {\n  const errors: string[] = []\n  \n  // Validate seller name\n  if (!data.sellerName || data.sellerName.trim().length === 0) {\n    errors.push('Seller name is required')\n  }\n  \n  // Validate VAT number (should be 15 digits)\n  if (!data.vatNumber || !/^\\d{15}$/.test(data.vatNumber)) {\n    errors.push('VAT number must be exactly 15 digits')\n  }\n  \n  // Validate timestamp (ISO format)\n  if (!data.timestamp || isNaN(Date.parse(data.timestamp))) {\n    errors.push('Valid timestamp is required')\n  }\n  \n  // Validate invoice total (should be numeric)\n  if (!data.invoiceTotal || isNaN(parseFloat(data.invoiceTotal))) {\n    errors.push('Valid invoice total is required')\n  }\n  \n  // Validate VAT total (should be numeric)\n  if (!data.vatTotal || isNaN(parseFloat(data.vatTotal))) {\n    errors.push('Valid VAT total is required')\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n/**\n * Generate QR code URL for display (using a QR code service)\n * @param qrData - Base64 encoded QR data\n * @param size - QR code size (default: 200)\n * @returns QR code image URL\n */\nexport function generateQRCodeURL(qrData: string, size: number = 200): string {\n  // Using QR Server API (in production, consider using a local QR generator)\n  const encodedData = encodeURIComponent(qrData)\n  return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodedData}`\n}\n\n/**\n * Format currency amount for ZATCA compliance\n * @param amount - Numeric amount\n * @param currency - Currency code (default: SAR)\n * @returns Formatted currency string\n */\nexport function formatCurrencyForZATCA(amount: number, currency: string = 'SAR'): string {\n  return amount.toFixed(2)\n}\n\n/**\n * Generate ZATCA-compliant timestamp\n * @param date - Date object (default: current date)\n * @returns ISO timestamp string\n */\nexport function generateZATCATimestamp(date: Date = new Date()): string {\n  return date.toISOString()\n}\n\n/**\n * Example usage and test function\n */\nexport function generateSampleZATCAQR(): string {\n  const sampleData: ZATCAQRData = {\n    sellerName: 'KAAZMAAMAA Company',\n    vatNumber: '30**********003',\n    timestamp: generateZATCATimestamp(),\n    invoiceTotal: '1150.00',\n    vatTotal: '150.00'\n  }\n  \n  return generateZATCAQRCode(sampleData)\n}\n\n// Export utility functions for invoice generation\nexport const ZATCAUtils = {\n  generateTLV,\n  generateZATCAQRCode,\n  generateInvoiceHash,\n  validateZATCAQRData,\n  generateQRCodeURL,\n  formatCurrencyForZATCA,\n  generateZATCATimestamp,\n  generateSampleZATCAQR,\n  ZATCA_TAGS\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,+DAA+D;;;;;;;;;;;;;AAYxD,MAAM,aAAa;IACxB,aAAa;IACb,YAAY;IACZ,WAAW;IACX,eAAe;IACf,WAAW;IACX,cAAc;AAChB;AAQO,SAAS,YAAY,GAAW,EAAE,KAAa;IACpD,MAAM,aAAa,IAAI,cAAc,MAAM,CAAC;IAC5C,MAAM,SAAS,WAAW,MAAM;IAEhC,6CAA6C;IAC7C,MAAM,MAAM,IAAI,WAAW,IAAI;IAC/B,GAAG,CAAC,EAAE,GAAG;IACT,GAAG,CAAC,EAAE,GAAG;IACT,IAAI,GAAG,CAAC,YAAY;IAEpB,OAAO;AACT;AAOO,SAAS,oBAAoB,IAAiB;IACnD,IAAI;QACF,uCAAuC;QACvC,MAAM,gBAAgB,YAAY,WAAW,WAAW,EAAE,KAAK,UAAU;QACzE,MAAM,eAAe,YAAY,WAAW,UAAU,EAAE,KAAK,SAAS;QACtE,MAAM,eAAe,YAAY,WAAW,SAAS,EAAE,KAAK,SAAS;QACrE,MAAM,kBAAkB,YAAY,WAAW,aAAa,EAAE,KAAK,YAAY;QAC/E,MAAM,cAAc,YAAY,WAAW,SAAS,EAAE,KAAK,QAAQ;QAEnE,yBAAyB;QACzB,IAAI,cAAc,cAAc,MAAM,GAAG,aAAa,MAAM,GAAG,aAAa,MAAM,GACjE,gBAAgB,MAAM,GAAG,YAAY,MAAM;QAE5D,+BAA+B;QAC/B,IAAI,iBAAoC;QACxC,IAAI,KAAK,WAAW,EAAE;YACpB,iBAAiB,YAAY,WAAW,YAAY,EAAE,KAAK,WAAW;YACtE,eAAe,eAAe,MAAM;QACtC;QAEA,uBAAuB;QACvB,MAAM,SAAS,IAAI,WAAW;QAC9B,IAAI,SAAS;QAEb,OAAO,GAAG,CAAC,eAAe;QAC1B,UAAU,cAAc,MAAM;QAE9B,OAAO,GAAG,CAAC,cAAc;QACzB,UAAU,aAAa,MAAM;QAE7B,OAAO,GAAG,CAAC,cAAc;QACzB,UAAU,aAAa,MAAM;QAE7B,OAAO,GAAG,CAAC,iBAAiB;QAC5B,UAAU,gBAAgB,MAAM;QAEhC,OAAO,GAAG,CAAC,aAAa;QACxB,UAAU,YAAY,MAAM;QAE5B,IAAI,gBAAgB;YAClB,OAAO,GAAG,CAAC,gBAAgB;QAC7B;QAEA,oBAAoB;QACpB,OAAO,KAAK,OAAO,YAAY,IAAI;IAErC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AAQO,eAAe,oBAAoB,WAAgB;IACxD,IAAI;QACF,2EAA2E;QAC3E,MAAM,aAAa,KAAK,SAAS,CAAC;QAElC,wBAAwB;QACxB,MAAM,UAAU,IAAI;QACpB,MAAM,OAAO,QAAQ,MAAM,CAAC;QAC5B,MAAM,aAAa,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW;QAEzD,oBAAoB;QACpB,MAAM,YAAY,IAAI,WAAW;QACjC,OAAO,KAAK,OAAO,YAAY,IAAI;IAErC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IAClB;AACF;AAOO,SAAS,oBAAoB,IAAiB;IACnD,MAAM,SAAmB,EAAE;IAE3B,uBAAuB;IACvB,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC3D,OAAO,IAAI,CAAC;IACd;IAEA,4CAA4C;IAC5C,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,KAAK,SAAS,GAAG;QACvD,OAAO,IAAI,CAAC;IACd;IAEA,kCAAkC;IAClC,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,KAAK,KAAK,CAAC,KAAK,SAAS,IAAI;QACxD,OAAO,IAAI,CAAC;IACd;IAEA,6CAA6C;IAC7C,IAAI,CAAC,KAAK,YAAY,IAAI,MAAM,WAAW,KAAK,YAAY,IAAI;QAC9D,OAAO,IAAI,CAAC;IACd;IAEA,yCAAyC;IACzC,IAAI,CAAC,KAAK,QAAQ,IAAI,MAAM,WAAW,KAAK,QAAQ,IAAI;QACtD,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAQO,SAAS,kBAAkB,MAAc,EAAE,OAAe,GAAG;IAClE,2EAA2E;IAC3E,MAAM,cAAc,mBAAmB;IACvC,OAAO,CAAC,iDAAiD,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,EAAE,aAAa;AAC/F;AAQO,SAAS,uBAAuB,MAAc,EAAE,WAAmB,KAAK;IAC7E,OAAO,OAAO,OAAO,CAAC;AACxB;AAOO,SAAS,uBAAuB,OAAa,IAAI,MAAM;IAC5D,OAAO,KAAK,WAAW;AACzB;AAKO,SAAS;IACd,MAAM,aAA0B;QAC9B,YAAY;QACZ,WAAW;QACX,WAAW;QACX,cAAc;QACd,UAAU;IACZ;IAEA,OAAO,oBAAoB;AAC7B;AAGO,MAAM,aAAa;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/utils/numberToWords.ts"], "sourcesContent": ["// Number to Words Converter for Invoice Amounts\n\nconst ones = [\n  '', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine',\n  'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen',\n  'Seventeen', 'Eighteen', 'Nineteen'\n];\n\nconst tens = [\n  '', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'\n];\n\nconst scales = [\n  '', 'Thousand', 'Million', 'Billion', 'Trillion'\n];\n\n/**\n * Convert a number (0-999) to words\n * @param num - Number to convert\n * @returns String representation\n */\nfunction convertHundreds(num: number): string {\n  let result = '';\n  \n  if (num >= 100) {\n    result += ones[Math.floor(num / 100)] + ' Hundred';\n    num %= 100;\n    if (num > 0) result += ' ';\n  }\n  \n  if (num >= 20) {\n    result += tens[Math.floor(num / 10)];\n    num %= 10;\n    if (num > 0) result += ' ';\n  }\n  \n  if (num > 0) {\n    result += ones[num];\n  }\n  \n  return result;\n}\n\n/**\n * Convert a number to words\n * @param num - Number to convert\n * @returns String representation\n */\nexport function numberToWords(num: number): string {\n  if (num === 0) return 'Zero';\n  \n  let result = '';\n  let scaleIndex = 0;\n  \n  while (num > 0) {\n    const chunk = num % 1000;\n    if (chunk !== 0) {\n      const chunkWords = convertHundreds(chunk);\n      if (scaleIndex > 0) {\n        result = chunkWords + ' ' + scales[scaleIndex] + (result ? ' ' + result : '');\n      } else {\n        result = chunkWords;\n      }\n    }\n    num = Math.floor(num / 1000);\n    scaleIndex++;\n  }\n  \n  return result;\n}\n\n/**\n * Convert currency amount to words with currency name\n * @param amount - Amount to convert\n * @param currency - Currency code (SAR, USD, etc.)\n * @returns Formatted string with currency\n */\nexport function amountToWords(amount: number, currency: string = 'SAR'): string {\n  const wholePart = Math.floor(amount);\n  const decimalPart = Math.round((amount - wholePart) * 100);\n  \n  let result = numberToWords(wholePart);\n  \n  // Add currency name\n  const currencyNames: { [key: string]: { singular: string; plural: string; subunit: string } } = {\n    'SAR': { singular: 'Riyal', plural: 'Riyals', subunit: 'Halala' },\n    'USD': { singular: 'Dollar', plural: 'Dollars', subunit: 'Cent' },\n    'EUR': { singular: 'Euro', plural: 'Euros', subunit: 'Cent' },\n    'AED': { singular: 'Dirham', plural: 'Dirhams', subunit: 'Fils' }\n  };\n  \n  const currencyInfo = currencyNames[currency] || { singular: currency, plural: currency + 's', subunit: 'Cent' };\n  \n  if (wholePart === 1) {\n    result += ' ' + currencyInfo.singular;\n  } else {\n    result += ' ' + currencyInfo.plural;\n  }\n  \n  // Add decimal part if exists\n  if (decimalPart > 0) {\n    result += ' and ' + numberToWords(decimalPart);\n    if (decimalPart === 1) {\n      result += ' ' + currencyInfo.subunit;\n    } else {\n      result += ' ' + currencyInfo.subunit + 's';\n    }\n  }\n  \n  return result + ' Only';\n}\n\n/**\n * Format amount in words for invoice display\n * @param amount - Amount to convert\n * @param currency - Currency code\n * @returns Formatted string for invoice\n */\nexport function formatAmountInWords(amount: number, currency: string = 'SAR'): string {\n  return amountToWords(amount, currency);\n}\n\n// Export utility functions\nexport const NumberUtils = {\n  numberToWords,\n  amountToWords,\n  formatAmountInWords\n}\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;;;;AAEhD,MAAM,OAAO;IACX;IAAI;IAAO;IAAO;IAAS;IAAQ;IAAQ;IAAO;IAAS;IAAS;IACpE;IAAO;IAAU;IAAU;IAAY;IAAY;IAAW;IAC9D;IAAa;IAAY;CAC1B;AAED,MAAM,OAAO;IACX;IAAI;IAAI;IAAU;IAAU;IAAS;IAAS;IAAS;IAAW;IAAU;CAC7E;AAED,MAAM,SAAS;IACb;IAAI;IAAY;IAAW;IAAW;CACvC;AAED;;;;CAIC,GACD,SAAS,gBAAgB,GAAW;IAClC,IAAI,SAAS;IAEb,IAAI,OAAO,KAAK;QACd,UAAU,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;QACxC,OAAO;QACP,IAAI,MAAM,GAAG,UAAU;IACzB;IAEA,IAAI,OAAO,IAAI;QACb,UAAU,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI;QACpC,OAAO;QACP,IAAI,MAAM,GAAG,UAAU;IACzB;IAEA,IAAI,MAAM,GAAG;QACX,UAAU,IAAI,CAAC,IAAI;IACrB;IAEA,OAAO;AACT;AAOO,SAAS,cAAc,GAAW;IACvC,IAAI,QAAQ,GAAG,OAAO;IAEtB,IAAI,SAAS;IACb,IAAI,aAAa;IAEjB,MAAO,MAAM,EAAG;QACd,MAAM,QAAQ,MAAM;QACpB,IAAI,UAAU,GAAG;YACf,MAAM,aAAa,gBAAgB;YACnC,IAAI,aAAa,GAAG;gBAClB,SAAS,aAAa,MAAM,MAAM,CAAC,WAAW,GAAG,CAAC,SAAS,MAAM,SAAS,EAAE;YAC9E,OAAO;gBACL,SAAS;YACX;QACF;QACA,MAAM,KAAK,KAAK,CAAC,MAAM;QACvB;IACF;IAEA,OAAO;AACT;AAQO,SAAS,cAAc,MAAc,EAAE,WAAmB,KAAK;IACpE,MAAM,YAAY,KAAK,KAAK,CAAC;IAC7B,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,SAAS,SAAS,IAAI;IAEtD,IAAI,SAAS,cAAc;IAE3B,oBAAoB;IACpB,MAAM,gBAA0F;QAC9F,OAAO;YAAE,UAAU;YAAS,QAAQ;YAAU,SAAS;QAAS;QAChE,OAAO;YAAE,UAAU;YAAU,QAAQ;YAAW,SAAS;QAAO;QAChE,OAAO;YAAE,UAAU;YAAQ,QAAQ;YAAS,SAAS;QAAO;QAC5D,OAAO;YAAE,UAAU;YAAU,QAAQ;YAAW,SAAS;QAAO;IAClE;IAEA,MAAM,eAAe,aAAa,CAAC,SAAS,IAAI;QAAE,UAAU;QAAU,QAAQ,WAAW;QAAK,SAAS;IAAO;IAE9G,IAAI,cAAc,GAAG;QACnB,UAAU,MAAM,aAAa,QAAQ;IACvC,OAAO;QACL,UAAU,MAAM,aAAa,MAAM;IACrC;IAEA,6BAA6B;IAC7B,IAAI,cAAc,GAAG;QACnB,UAAU,UAAU,cAAc;QAClC,IAAI,gBAAgB,GAAG;YACrB,UAAU,MAAM,aAAa,OAAO;QACtC,OAAO;YACL,UAAU,MAAM,aAAa,OAAO,GAAG;QACzC;IACF;IAEA,OAAO,SAAS;AAClB;AAQO,SAAS,oBAAoB,MAAc,EAAE,WAAmB,KAAK;IAC1E,OAAO,cAAc,QAAQ;AAC/B;AAGO,MAAM,cAAc;IACzB;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/utils/pdfGenerator.ts"], "sourcesContent": ["// PDF Generation Utility for ZATCA Invoices - HD Quality Version\n// Enhanced with professional styling and textbox system\nimport { formatAmountInWords } from './numberToWords'\n\nexport interface InvoiceData {\n  // Invoice Details\n  invoiceNumber: string\n  invoiceDate: string\n  dueDate: string\n  currency: string\n  discount: number\n  deductionAmount: number // NEW: Deduction amount field\n  deductionReason: string // NEW: Deduction reason field\n\n  // Seller Details\n  sellerName: string\n  sellerAddress: string\n  sellerVAT: string\n  sellerLogo?: string | null\n\n  // Client Details\n  clientName: string\n  clientAddress: string\n  clientVAT: string\n  clientLogo?: string | null\n\n  // Items\n  items: Array<{\n    id: string\n    description: string\n    quantity: number\n    rate: number\n    vatPercent: number\n    amount: number\n  }>\n\n  // Additional Details\n  notes: string\n  terms: string\n  bankName: string\n  accountName: string\n  accountNumber: string\n  iban: string\n\n  // Calculations\n  subtotal: number\n  subtotalAfterDeduction: number // NEW: Subtotal after deduction\n  totalVAT: number\n  totalAmount: number\n  qrCode: string\n\n  // Logo\n  logoPreview?: string\n}\n\n/**\n * Generate HTML template for invoice PDF\n * @param data - Invoice data\n * @returns HTML string for PDF generation\n */\nexport function generateInvoiceHTML(data: InvoiceData): string {\n  const discountAmount = (data.subtotal * data.discount) / 100\n  const subtotalAfterDiscount = data.subtotal - discountAmount\n  // NEW: Use the passed subtotalAfterDeduction which includes deduction amount logic\n\n  return `\n    <!DOCTYPE html>\n    <html lang=\"en\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Invoice ${data.invoiceNumber}</title>\n      <style>\n        * {\n          margin: 0;\n          padding: 0;\n          box-sizing: border-box;\n        }\n\n        body {\n          font-family: 'Segoe UI', 'Arial', sans-serif;\n          font-size: 11px;\n          line-height: 1.5;\n          color: #1f2937;\n          background: white;\n          -webkit-font-smoothing: antialiased;\n          -moz-osx-font-smoothing: grayscale;\n          print-color-adjust: exact;\n        }\n\n        .invoice-container {\n          max-width: 850px;\n          margin: 0 auto;\n          padding: 30px;\n          background: white;\n          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n          border-radius: 12px;\n          border: 1px solid #e5e7eb;\n          width: 100%;\n          box-sizing: border-box;\n          overflow-x: hidden;\n        }\n\n        .header {\n          display: grid;\n          grid-template-columns: 1fr auto 1fr;\n          align-items: center;\n          margin-bottom: 35px;\n          padding: 25px;\n          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);\n          border-radius: 12px;\n          gap: 25px;\n          box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);\n        }\n\n        .qr-header {\n          text-align: center;\n          justify-self: center;\n        }\n\n        .qr-code-top {\n          display: flex;\n          justify-content: center;\n          align-items: center;\n        }\n\n        .logo-section h1 {\n          color: white;\n          font-size: 26px;\n          font-weight: 700;\n          margin-bottom: 8px;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n\n        .logo-section p {\n          color: rgba(255, 255, 255, 0.9);\n          font-size: 14px;\n          font-weight: 500;\n        }\n\n        .invoice-title {\n          text-align: right;\n        }\n\n        .invoice-title h2 {\n          color: white;\n          font-size: 32px;\n          font-weight: 800;\n          margin-bottom: 8px;\n          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n          letter-spacing: 1px;\n        }\n\n        .invoice-number {\n          color: rgba(255, 255, 255, 0.9);\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .details-section {\n          display: grid;\n          grid-template-columns: 1fr 1fr 1fr;\n          gap: 30px;\n          margin-bottom: 30px;\n        }\n\n        .detail-card {\n          background: #f8f9fa;\n          padding: 15px;\n          border-radius: 8px;\n          border-left: 4px solid #A78BFA;\n          border: 1px solid #e5e7eb;\n          overflow: hidden;\n          word-wrap: break-word;\n        }\n\n        .detail-card h3 {\n          color: #A78BFA;\n          font-size: 14px;\n          font-weight: bold;\n          margin-bottom: 15px;\n          text-align: center;\n          background: #A78BFA;\n          color: white;\n          padding: 8px;\n          margin: -15px -15px 15px -15px;\n          border-radius: 7px 7px 0 0;\n        }\n\n        .textbox-container {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 10px;\n          margin-bottom: 10px;\n        }\n\n        .textbox-full {\n          grid-column: 1 / -1;\n        }\n\n        .textbox {\n          border: 1.5px solid #d1d5db;\n          border-radius: 6px;\n          padding: 10px 12px;\n          background: #ffffff;\n          font-size: 11px;\n          min-height: 24px;\n          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);\n          font-weight: 500;\n          color: #374151;\n          word-wrap: break-word;\n          word-break: break-word;\n          overflow-wrap: break-word;\n          white-space: normal;\n          overflow: hidden;\n        }\n\n        .textbox-label {\n          font-size: 9px;\n          color: #6b7280;\n          font-weight: 600;\n          margin-bottom: 4px;\n          display: block;\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n\n        .textbox-large {\n          min-height: 50px;\n          line-height: 1.4;\n          padding: 12px;\n          word-wrap: break-word;\n          word-break: break-word;\n          overflow-wrap: break-word;\n          white-space: normal;\n          overflow: hidden;\n          max-height: 80px;\n        }\n\n        .textbox-address {\n          min-height: 45px;\n          line-height: 1.3;\n          word-wrap: break-word;\n          word-break: break-word;\n          overflow-wrap: break-word;\n          white-space: normal;\n          overflow: hidden;\n          max-height: 75px;\n          font-size: 10px;\n          padding: 10px;\n        }\n\n        .textbox-amount {\n          font-weight: 700;\n          color: #059669;\n          font-size: 12px;\n          text-align: right;\n        }\n\n        .seller-container {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          gap: 15px;\n        }\n\n        .seller-info {\n          flex: 1;\n        }\n\n        .seller-logo {\n          width: 80px;\n          text-align: center;\n        }\n\n        .items-table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 30px;\n          background: white;\n          border: 1px solid #e5e7eb;\n        }\n\n        .items-table th {\n          background: #A78BFA;\n          color: white;\n          padding: 12px 8px;\n          text-align: left;\n          font-weight: bold;\n          font-size: 11px;\n        }\n\n        .items-table td {\n          padding: 10px 8px;\n          border-bottom: 1px solid #e5e7eb;\n          font-size: 11px;\n        }\n\n        .items-table tr:nth-child(even) {\n          background: #f9fafb;\n        }\n\n        .summary-section {\n          display: grid;\n          grid-template-columns: 280px 1fr 1fr;\n          gap: 20px;\n          margin-bottom: 30px;\n          max-width: 100%;\n          overflow: hidden;\n        }\n\n        .notes-section, .bank-section {\n          background: #f8f9fa;\n          padding: 15px;\n          border-radius: 8px;\n          border: 1px solid #e5e7eb;\n        }\n\n        .summary-card {\n          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);\n          color: white;\n          padding: 15px;\n          border-radius: 8px;\n          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);\n          max-width: 280px;\n          width: 100%;\n          box-sizing: border-box;\n        }\n\n        .summary-card h4 {\n          font-size: 16px;\n          font-weight: bold;\n          margin-bottom: 15px;\n        }\n\n        .summary-row {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: 8px;\n          font-size: 12px;\n        }\n\n        .summary-total {\n          border-top: 2px solid rgba(255,255,255,0.3);\n          padding-top: 10px;\n          margin-top: 10px;\n          font-size: 16px;\n          font-weight: bold;\n        }\n\n\n\n        .footer {\n          margin-top: 40px;\n          text-align: center;\n          color: #666;\n          font-size: 10px;\n          border-top: 1px solid #e5e7eb;\n          padding-top: 20px;\n        }\n\n        @media print {\n          body { margin: 0; }\n          .invoice-container { padding: 0; }\n          .summary-section {\n            grid-template-columns: 1fr;\n            gap: 15px;\n          }\n          .summary-card {\n            max-width: 100%;\n          }\n        }\n\n        @media (max-width: 768px) {\n          .summary-section {\n            grid-template-columns: 1fr;\n            gap: 15px;\n          }\n          .summary-card {\n            max-width: 100%;\n          }\n          .invoice-container {\n            padding: 15px;\n          }\n          .details-section {\n            grid-template-columns: 1fr;\n            gap: 15px;\n          }\n          .textbox-address {\n            font-size: 9px;\n            max-height: 60px;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"invoice-container\">\n        <!-- Header with QR Code -->\n        <div class=\"header\">\n          <div class=\"logo-section\">\n            <h1>🧾 ZATCA Invoice Generator</h1>\n            <p>Create and manage your invoices with ease</p>\n          </div>\n\n          <!-- QR Code in Center Top -->\n          <div class=\"qr-header\">\n            <div class=\"qr-code-top\">\n              <img src=\"https://api.qrserver.com/v1/create-qr-code/?size=130x130&data=${encodeURIComponent(data.qrCode)}\"\n                   alt=\"ZATCA QR Code\" style=\"width: 130px; height: 130px; border: 3px solid white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\" />\n            </div>\n            <p style=\"font-size: 11px; color: rgba(255, 255, 255, 0.9); margin-top: 8px; text-align: center; font-weight: 600;\">ZATCA QR Code</p>\n          </div>\n\n          <div class=\"invoice-title\">\n            <h2>INVOICE</h2>\n            <p class=\"invoice-number\">${data.invoiceNumber}</p>\n          </div>\n        </div>\n\n        <!-- Details Section -->\n        <div class=\"details-section\">\n          <div class=\"detail-card\">\n            <h3>📋 Invoice Details</h3>\n            <div class=\"textbox-container\">\n              <div>\n                <span class=\"textbox-label\">Invoice Number</span>\n                <div class=\"textbox\">${data.invoiceNumber}</div>\n              </div>\n              <div>\n                <span class=\"textbox-label\">Currency</span>\n                <div class=\"textbox\">${data.currency}</div>\n              </div>\n              <div>\n                <span class=\"textbox-label\">Invoice Date</span>\n                <div class=\"textbox\">${new Date(data.invoiceDate).toLocaleDateString()}</div>\n              </div>\n              <div>\n                <span class=\"textbox-label\">Due Date</span>\n                <div class=\"textbox\">${data.dueDate ? new Date(data.dueDate).toLocaleDateString() : 'N/A'}</div>\n              </div>\n              ${data.discount > 0 ? `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">Discount Applied</span>\n                  <div class=\"textbox\">${data.discount}% discount applied to subtotal</div>\n                </div>\n              ` : ''}\n              ${data.deductionAmount > 0 ? `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">⚠️ Deduction Amount</span>\n                  <div class=\"textbox\" style=\"color: #ea580c; font-weight: 600; background: #fef3f2; border: 1px solid #fecaca;\">${data.currency} ${data.deductionAmount.toFixed(2)} deducted before VAT calculation</div>\n                </div>\n                ${data.deductionReason ? `\n                  <div class=\"textbox-full\">\n                    <span class=\"textbox-label\">📝 Deduction Reason</span>\n                    <div class=\"textbox\" style=\"color: #374151; font-weight: 500; background: #fffbeb; border: 1px solid #fed7aa; padding: 12px; line-height: 1.4;\">${data.deductionReason}</div>\n                  </div>\n                ` : ''}\n              ` : ''}\n            </div>\n          </div>\n\n          <div class=\"detail-card\">\n            <h3>🏢 Billed From (Seller)</h3>\n            <div class=\"seller-container\">\n              <div class=\"seller-info\">\n                <div class=\"textbox-container\">\n                  <div class=\"textbox-full\">\n                    <span class=\"textbox-label\">Company Name</span>\n                    <div class=\"textbox\">${data.sellerName}</div>\n                  </div>\n                  <div class=\"textbox-full\">\n                    <span class=\"textbox-label\">Company Address</span>\n                    <div class=\"textbox textbox-address\">${data.sellerAddress.replace(/\\n/g, '<br>')}</div>\n                  </div>\n                  <div class=\"textbox-full\">\n                    <span class=\"textbox-label\">VAT Registration Number</span>\n                    <div class=\"textbox\">${data.sellerVAT}</div>\n                  </div>\n                </div>\n              </div>\n              ${data.sellerLogo ? `\n                <div class=\"seller-logo\">\n                  <span class=\"textbox-label\">Company Logo</span>\n                  <div class=\"textbox\" style=\"text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;\">\n                    <img src=\"${data.sellerLogo}\" alt=\"Company Logo\"\n                         style=\"max-width: 70px; max-height: 60px; object-fit: contain;\" />\n                  </div>\n                </div>\n              ` : ''}\n            </div>\n          </div>\n\n          <div class=\"detail-card\">\n            <h3>👤 Billed To (Client)</h3>\n            <div class=\"seller-container\">\n              <div class=\"seller-info\">\n                <div class=\"textbox-container\">\n                  <div class=\"textbox-full\">\n                    <span class=\"textbox-label\">Client Name</span>\n                    <div class=\"textbox\">${data.clientName}</div>\n                  </div>\n                  <div class=\"textbox-full\">\n                    <span class=\"textbox-label\">Client Address</span>\n                    <div class=\"textbox textbox-address\">${data.clientAddress.replace(/\\n/g, '<br>')}</div>\n                  </div>\n                  ${data.clientVAT ? `\n                    <div class=\"textbox-full\">\n                      <span class=\"textbox-label\">Client VAT Registration Number</span>\n                      <div class=\"textbox\">${data.clientVAT}</div>\n                    </div>\n                  ` : `\n                    <div class=\"textbox-full\">\n                      <span class=\"textbox-label\">Client VAT Registration Number</span>\n                      <div class=\"textbox\" style=\"color: #9ca3af; font-style: italic;\">Not provided</div>\n                    </div>\n                  `}\n                </div>\n              </div>\n              ${data.clientLogo ? `\n                <div class=\"seller-logo\">\n                  <span class=\"textbox-label\">Client Logo</span>\n                  <div class=\"textbox\" style=\"text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;\">\n                    <img src=\"${data.clientLogo}\" alt=\"Client Logo\"\n                         style=\"max-width: 70px; max-height: 60px; object-fit: contain;\" />\n                  </div>\n                </div>\n              ` : ''}\n            </div>\n          </div>\n        </div>\n\n        <!-- Items Table -->\n        <table class=\"items-table\">\n          <thead>\n            <tr>\n              <th style=\"width: 40%\">Description</th>\n              <th style=\"width: 10%\">Qty</th>\n              <th style=\"width: 15%\">Rate</th>\n              <th style=\"width: 10%\">VAT %</th>\n              <th style=\"width: 15%\">Amount (${data.currency})</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${data.items.map(item => `\n              <tr>\n                <td>${item.description}</td>\n                <td>${item.quantity}</td>\n                <td>${item.rate.toFixed(2)}</td>\n                <td>${item.vatPercent}%</td>\n                <td>${item.amount.toFixed(2)}</td>\n              </tr>\n            `).join('')}\n          </tbody>\n        </table>\n\n        <!-- Summary Section -->\n        <div class=\"summary-section\">\n          <div class=\"summary-card\">\n            <h4 style=\"color: white; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: rgba(255,255,255,0.25); padding: 8px; margin: -15px -15px 15px -15px; border-radius: 8px 8px 0 0;\">💰 Invoice Summary</h4>\n            <div class=\"textbox-container\" style=\"gap: 8px; grid-template-columns: 1fr;\">\n              <div>\n                <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Subtotal</span>\n                <div class=\"textbox textbox-amount\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;\">${data.currency} ${data.subtotal.toFixed(2)}</div>\n              </div>\n              ${data.discount > 0 ? `\n                <div>\n                  <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Discount (${data.discount}%)</span>\n                  <div class=\"textbox textbox-amount\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #dc2626; padding: 8px; font-size: 10px;\">-${data.currency} ${discountAmount.toFixed(2)}</div>\n                </div>\n              ` : ''}\n              ${data.deductionAmount > 0 ? `\n                <div>\n                  <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Deduction Amount</span>\n                  <div class=\"textbox textbox-amount\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #ea580c; padding: 8px; font-size: 10px;\">-${data.currency} ${data.deductionAmount.toFixed(2)}</div>\n                </div>\n                ${data.deductionReason ? `\n                  <div>\n                    <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Deduction Reason</span>\n                    <div class=\"textbox\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #6b7280; font-style: italic; padding: 8px; font-size: 9px; line-height: 1.3;\">${data.deductionReason}</div>\n                  </div>\n                ` : ''}\n              ` : ''}\n              <div>\n                <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Amount Before VAT</span>\n                <div class=\"textbox textbox-amount\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px; font-weight: 600;\">${data.currency} ${data.subtotalAfterDeduction.toFixed(2)}</div>\n              </div>\n              <div>\n                <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Total VAT (15%)</span>\n                <div class=\"textbox textbox-amount\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;\">${data.currency} ${data.totalVAT.toFixed(2)}</div>\n              </div>\n              <div>\n                <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Total Amount</span>\n                <div class=\"textbox\" style=\"background: #10b981; border: 2px solid #059669; color: white; font-weight: 700; font-size: 14px; text-align: center; padding: 10px;\">${data.currency} ${data.totalAmount.toFixed(2)}</div>\n              </div>\n              <div>\n                <span class=\"textbox-label\" style=\"color: rgba(255,255,255,0.8); font-size: 8px;\">Amount in Words</span>\n                <div class=\"textbox\" style=\"background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); font-style: italic; color: #374151; text-align: center; font-weight: 600; padding: 8px; font-size: 9px; line-height: 1.3;\">${formatAmountInWords(data.totalAmount, data.currency)}</div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"bank-section\">\n            <h4 style=\"color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;\">🏦 Bank Details</h4>\n            <div class=\"textbox-container\">\n              ${data.accountName ? `\n                <div>\n                  <span class=\"textbox-label\">Account Name</span>\n                  <div class=\"textbox\">${data.accountName}</div>\n                </div>\n              ` : ''}\n              ${data.bankName ? `\n                <div>\n                  <span class=\"textbox-label\">Bank Name</span>\n                  <div class=\"textbox\">${data.bankName}</div>\n                </div>\n              ` : ''}\n              ${data.accountNumber ? `\n                <div>\n                  <span class=\"textbox-label\">Account Number</span>\n                  <div class=\"textbox\">${data.accountNumber}</div>\n                </div>\n              ` : ''}\n              ${data.iban ? `\n                <div>\n                  <span class=\"textbox-label\">IBAN</span>\n                  <div class=\"textbox\">${data.iban}</div>\n                </div>\n              ` : ''}\n              ${!data.accountName && !data.bankName && !data.accountNumber && !data.iban ? `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">Payment Information</span>\n                  <div class=\"textbox\" style=\"color: #9ca3af; font-style: italic;\">No bank details provided</div>\n                </div>\n              ` : ''}\n            </div>\n          </div>\n\n          <div class=\"notes-section\">\n            <h4 style=\"color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;\">📝 Notes & Terms</h4>\n            <div class=\"textbox-container\" style=\"grid-template-columns: 1fr;\">\n              ${data.deductionAmount > 0 && data.deductionReason ? `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">⚠️ Important: Deduction Applied</span>\n                  <div class=\"textbox\" style=\"background: #fef3f2; border: 2px solid #fecaca; color: #dc2626; font-weight: 600; padding: 12px; line-height: 1.4;\">\n                    A deduction of ${data.currency} ${data.deductionAmount.toFixed(2)} has been applied to this invoice.<br>\n                    <strong>Reason:</strong> ${data.deductionReason}\n                  </div>\n                </div>\n              ` : ''}\n              ${data.notes ? `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">Notes</span>\n                  <div class=\"textbox textbox-large\">${data.notes}</div>\n                </div>\n              ` : `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">Notes</span>\n                  <div class=\"textbox\" style=\"color: #6b7280; font-style: italic; min-height: 30px; padding: 12px;\">Thank you for your business!</div>\n                </div>\n              `}\n              ${data.terms ? `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">Terms & Conditions</span>\n                  <div class=\"textbox textbox-large\">${data.terms}</div>\n                </div>\n              ` : `\n                <div class=\"textbox-full\">\n                  <span class=\"textbox-label\">Terms & Conditions</span>\n                  <div class=\"textbox textbox-large\" style=\"color: #6b7280; font-style: normal; line-height: 1.6; padding: 15px;\">\n                    • Payment due within 30 days of invoice date<br>\n                    • Late payments may incur additional charges<br>\n                    • All prices are inclusive of applicable taxes<br>\n                    • This invoice is computer generated and valid without signature\n                  </div>\n                </div>\n              `}\n            </div>\n          </div>\n        </div>\n\n\n\n        <!-- Footer -->\n        <div class=\"footer\">\n          <p>This invoice is generated by KAAZMAAMAA ZATCA Invoice System</p>\n          <p>Compliant with Saudi Arabia's ZATCA e-invoicing regulations</p>\n        </div>\n      </div>\n    </body>\n    </html>\n  `\n}\n\n/**\n * Generate and download PDF invoice\n * @param data - Invoice data\n * @param filename - PDF filename (optional)\n */\nexport function downloadInvoicePDF(data: InvoiceData, filename?: string): void {\n  const html = generateInvoiceHTML(data)\n  const pdfFilename = filename || `invoice-${data.invoiceNumber}.pdf`\n\n  // Create a new window for PDF generation\n  const printWindow = window.open('', '_blank')\n  if (printWindow) {\n    printWindow.document.write(html)\n    printWindow.document.close()\n\n    // Wait for content to load, then print\n    printWindow.onload = () => {\n      setTimeout(() => {\n        printWindow.print()\n        // Don't close the window automatically - let user close it\n        // printWindow.close()\n      }, 500)\n    }\n  }\n}\n\n/**\n * Generate PDF blob for further processing\n * @param data - Invoice data\n * @returns Promise<Blob> - PDF blob\n */\nexport async function generateInvoicePDFBlob(data: InvoiceData): Promise<Blob> {\n  // This is a simplified version\n  // In production, use libraries like jsPDF or Puppeteer for proper PDF generation\n  const html = generateInvoiceHTML(data)\n  const blob = new Blob([html], { type: 'text/html' })\n  return blob\n}\n\n/**\n * Print invoice directly\n * @param data - Invoice data\n */\nexport function printInvoice(data: InvoiceData): void {\n  const html = generateInvoiceHTML(data)\n\n  // Create a hidden iframe for printing\n  const iframe = document.createElement('iframe')\n  iframe.style.display = 'none'\n  document.body.appendChild(iframe)\n\n  const doc = iframe.contentDocument || iframe.contentWindow?.document\n  if (doc) {\n    doc.write(html)\n    doc.close()\n\n    iframe.onload = () => {\n      iframe.contentWindow?.print()\n      setTimeout(() => {\n        document.body.removeChild(iframe)\n      }, 1000)\n    }\n  }\n}\n\n// Export utility functions\nexport const PDFUtils = {\n  generateInvoiceHTML,\n  downloadInvoicePDF,\n  generateInvoicePDFBlob,\n  printInvoice\n}\n"], "names": [], "mappings": "AAAA,iEAAiE;AACjE,wDAAwD;;;;;;;;AACxD;;AA0DO,SAAS,oBAAoB,IAAiB;IACnD,MAAM,iBAAiB,AAAC,KAAK,QAAQ,GAAG,KAAK,QAAQ,GAAI;IACzD,MAAM,wBAAwB,KAAK,QAAQ,GAAG;IAC9C,mFAAmF;IAEnF,OAAO,CAAC;;;;;;qBAMW,EAAE,KAAK,aAAa,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sFAgV4C,EAAE,mBAAmB,KAAK,MAAM,EAAE;;;;;;;;sCAQlF,EAAE,KAAK,aAAa,CAAC;;;;;;;;;;;qCAWtB,EAAE,KAAK,aAAa,CAAC;;;;qCAIrB,EAAE,KAAK,QAAQ,CAAC;;;;qCAIhB,EAAE,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,GAAG;;;;qCAIlD,EAAE,KAAK,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO,EAAE,kBAAkB,KAAK,MAAM;;cAE5F,EAAE,KAAK,QAAQ,GAAG,IAAI,CAAC;;;uCAGE,EAAE,KAAK,QAAQ,CAAC;;cAEzC,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,eAAe,GAAG,IAAI,CAAC;;;iIAGqF,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,OAAO,CAAC,GAAG;;gBAEpK,EAAE,KAAK,eAAe,GAAG,CAAC;;;oKAG0H,EAAE,KAAK,eAAe,CAAC;;gBAE3K,CAAC,GAAG,GAAG;cACT,CAAC,GAAG,GAAG;;;;;;;;;;;yCAWoB,EAAE,KAAK,UAAU,CAAC;;;;yDAIF,EAAE,KAAK,aAAa,CAAC,OAAO,CAAC,OAAO,QAAQ;;;;yCAI5D,EAAE,KAAK,SAAS,CAAC;;;;cAI5C,EAAE,KAAK,UAAU,GAAG,CAAC;;;;8BAIL,EAAE,KAAK,UAAU,CAAC;;;;cAIlC,CAAC,GAAG,GAAG;;;;;;;;;;;yCAWoB,EAAE,KAAK,UAAU,CAAC;;;;yDAIF,EAAE,KAAK,aAAa,CAAC,OAAO,CAAC,OAAO,QAAQ;;kBAEnF,EAAE,KAAK,SAAS,GAAG,CAAC;;;2CAGK,EAAE,KAAK,SAAS,CAAC;;kBAE1C,CAAC,GAAG,CAAC;;;;;kBAKL,CAAC,CAAC;;;cAGN,EAAE,KAAK,UAAU,GAAG,CAAC;;;;8BAIL,EAAE,KAAK,UAAU,CAAC;;;;cAIlC,CAAC,GAAG,GAAG;;;;;;;;;;;;;6CAawB,EAAE,KAAK,QAAQ,CAAC;;;;YAIjD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;;oBAElB,EAAE,KAAK,WAAW,CAAC;oBACnB,EAAE,KAAK,QAAQ,CAAC;oBAChB,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG;oBACvB,EAAE,KAAK,UAAU,CAAC;oBAClB,EAAE,KAAK,MAAM,CAAC,OAAO,CAAC,GAAG;;YAEjC,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;;;;wKAWgJ,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG;;cAEtM,EAAE,KAAK,QAAQ,GAAG,IAAI,CAAC;;8GAEyE,EAAE,KAAK,QAAQ,CAAC;2LAC6D,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,eAAe,OAAO,CAAC,GAAG;;cAE1N,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,eAAe,GAAG,IAAI,CAAC;;;2LAG+I,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,OAAO,CAAC,GAAG;;gBAE9N,EAAE,KAAK,eAAe,GAAG,CAAC;;;kNAGwK,EAAE,KAAK,eAAe,CAAC;;gBAEzN,CAAC,GAAG,GAAG;cACT,CAAC,GAAG,GAAG;;;0LAGqK,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,sBAAsB,CAAC,OAAO,CAAC,GAAG;;;;wKAI5E,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG;;;;iLAInC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC,GAAG;;;;oPAIoB,EAAE,CAAA,GAAA,6HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,WAAW,EAAE,KAAK,QAAQ,EAAE;;;;;;;;cAQ7R,EAAE,KAAK,WAAW,GAAG,CAAC;;;uCAGG,EAAE,KAAK,WAAW,CAAC;;cAE5C,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,QAAQ,GAAG,CAAC;;;uCAGM,EAAE,KAAK,QAAQ,CAAC;;cAEzC,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,aAAa,GAAG,CAAC;;;uCAGC,EAAE,KAAK,aAAa,CAAC;;cAE9C,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,IAAI,GAAG,CAAC;;;uCAGU,EAAE,KAAK,IAAI,CAAC;;cAErC,CAAC,GAAG,GAAG;cACP,EAAE,CAAC,KAAK,WAAW,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,aAAa,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC;;;;;cAK9E,CAAC,GAAG,GAAG;;;;;;;cAOP,EAAE,KAAK,eAAe,GAAG,KAAK,KAAK,eAAe,GAAG,CAAC;;;;mCAIjC,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,OAAO,CAAC,GAAG;6CACzC,EAAE,KAAK,eAAe,CAAC;;;cAGtD,CAAC,GAAG,GAAG;cACP,EAAE,KAAK,KAAK,GAAG,CAAC;;;qDAGuB,EAAE,KAAK,KAAK,CAAC;;cAEpD,CAAC,GAAG,CAAC;;;;;cAKL,CAAC,CAAC;cACF,EAAE,KAAK,KAAK,GAAG,CAAC;;;qDAGuB,EAAE,KAAK,KAAK,CAAC;;cAEpD,CAAC,GAAG,CAAC;;;;;;;;;;cAUL,CAAC,CAAC;;;;;;;;;;;;;;;EAed,CAAC;AACH;AAOO,SAAS,mBAAmB,IAAiB,EAAE,QAAiB;IACrE,MAAM,OAAO,oBAAoB;IACjC,MAAM,cAAc,YAAY,CAAC,QAAQ,EAAE,KAAK,aAAa,CAAC,IAAI,CAAC;IAEnE,yCAAyC;IACzC,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;IACpC,IAAI,aAAa;QACf,YAAY,QAAQ,CAAC,KAAK,CAAC;QAC3B,YAAY,QAAQ,CAAC,KAAK;QAE1B,uCAAuC;QACvC,YAAY,MAAM,GAAG;YACnB,WAAW;gBACT,YAAY,KAAK;YACjB,2DAA2D;YAC3D,sBAAsB;YACxB,GAAG;QACL;IACF;AACF;AAOO,eAAe,uBAAuB,IAAiB;IAC5D,+BAA+B;IAC/B,iFAAiF;IACjF,MAAM,OAAO,oBAAoB;IACjC,MAAM,OAAO,IAAI,KAAK;QAAC;KAAK,EAAE;QAAE,MAAM;IAAY;IAClD,OAAO;AACT;AAMO,SAAS,aAAa,IAAiB;IAC5C,MAAM,OAAO,oBAAoB;IAEjC,sCAAsC;IACtC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,KAAK,CAAC,OAAO,GAAG;IACvB,SAAS,IAAI,CAAC,WAAW,CAAC;IAE1B,MAAM,MAAM,OAAO,eAAe,IAAI,OAAO,aAAa,EAAE;IAC5D,IAAI,KAAK;QACP,IAAI,KAAK,CAAC;QACV,IAAI,KAAK;QAET,OAAO,MAAM,GAAG;YACd,OAAO,aAAa,EAAE;YACtB,WAAW;gBACT,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,GAAG;QACL;IACF;AACF;AAGO,MAAM,WAAW;IACtB;IACA;IACA;IACA;AACF", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/invoice/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\n\n// G<PERSON><PERSON><PERSON>L WINDOW LOCK SYSTEM - RUNS BEFORE REACT\ndeclare global {\n  interface Window {\n    KAAZMAAMAA_GLOBAL_LOCK_SYSTEM: any\n  }\n}\n\n// SINGLE RELIABLE LOCK SYSTEM - RUNS BEFORE REACT\nif (typeof window !== 'undefined' && !window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n  const LOCK_STORAGE_KEY = 'KAAZMAAMAA_INVOICE_LOCKS_PERSISTENT'\n\n  window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM = {\n    // Default lock state\n    defaultState: {\n      invoiceDetails: false,\n      sellerDetails: false,\n      clientDetails: false,\n      bankDetails: false,\n      items: false,\n      notes: false\n    },\n\n    // RELIABLE SAVE FUNCTION\n    save: function(lockState) {\n      try {\n        const dataString = JSON.stringify(lockState)\n        const timestamp = Date.now()\n\n        console.log('💾 SAVING LOCK STATE:', lockState)\n\n        // Primary storage: localStorage with multiple keys for redundancy\n        localStorage.setItem(LOCK_STORAGE_KEY, dataString)\n        localStorage.setItem(`${LOCK_STORAGE_KEY}_backup1`, dataString)\n        localStorage.setItem(`${LOCK_STORAGE_KEY}_backup2`, dataString)\n        localStorage.setItem(`${LOCK_STORAGE_KEY}_timestamp`, timestamp.toString())\n\n        // Secondary storage: sessionStorage\n        sessionStorage.setItem(LOCK_STORAGE_KEY, dataString)\n        sessionStorage.setItem(`${LOCK_STORAGE_KEY}_backup`, dataString)\n\n        // Tertiary storage: cookies with long expiry\n        const cookieValue = encodeURIComponent(dataString)\n        document.cookie = `${LOCK_STORAGE_KEY}=${cookieValue}; path=/; max-age=31536000; SameSite=Lax`\n        document.cookie = `${LOCK_STORAGE_KEY}_backup=${cookieValue}; path=/; max-age=31536000; SameSite=Lax`\n\n        // Quaternary storage: document attributes\n        document.documentElement.setAttribute('data-invoice-locks', dataString)\n        document.body.setAttribute('data-invoice-locks-backup', dataString)\n\n        // Window object storage\n        window.INVOICE_LOCKS_DATA = lockState\n        window.INVOICE_LOCKS_BACKUP = lockState\n\n        console.log('✅ LOCK STATE SAVED TO MULTIPLE LOCATIONS')\n        return true\n      } catch (error) {\n        console.error('❌ SAVE ERROR:', error)\n        return false\n      }\n    },\n\n    // RELIABLE LOAD FUNCTION\n    load: function() {\n      try {\n        console.log('🔍 LOADING LOCK STATE...')\n\n        // Try primary storage: localStorage\n        let data = localStorage.getItem(LOCK_STORAGE_KEY)\n        if (data) {\n          const parsed = JSON.parse(data)\n          console.log('✅ LOADED FROM localStorage:', parsed)\n          return { ...this.defaultState, ...parsed }\n        }\n\n        // Try backup localStorage keys\n        const backupKeys = [`${LOCK_STORAGE_KEY}_backup1`, `${LOCK_STORAGE_KEY}_backup2`]\n        for (const key of backupKeys) {\n          data = localStorage.getItem(key)\n          if (data) {\n            const parsed = JSON.parse(data)\n            console.log(`✅ LOADED FROM localStorage backup (${key}):`, parsed)\n            return { ...this.defaultState, ...parsed }\n          }\n        }\n\n        // Try sessionStorage\n        data = sessionStorage.getItem(LOCK_STORAGE_KEY)\n        if (data) {\n          const parsed = JSON.parse(data)\n          console.log('✅ LOADED FROM sessionStorage:', parsed)\n          return { ...this.defaultState, ...parsed }\n        }\n\n        // Try sessionStorage backup\n        data = sessionStorage.getItem(`${LOCK_STORAGE_KEY}_backup`)\n        if (data) {\n          const parsed = JSON.parse(data)\n          console.log('✅ LOADED FROM sessionStorage backup:', parsed)\n          return { ...this.defaultState, ...parsed }\n        }\n\n        // Try cookies\n        const cookies = document.cookie.split(';')\n        for (const cookie of cookies) {\n          const [name, value] = cookie.trim().split('=')\n          if (name === LOCK_STORAGE_KEY && value) {\n            try {\n              const parsed = JSON.parse(decodeURIComponent(value))\n              console.log('✅ LOADED FROM cookies:', parsed)\n              return { ...this.defaultState, ...parsed }\n            } catch (e) {\n              continue\n            }\n          }\n        }\n\n        // Try document attributes\n        data = document.documentElement.getAttribute('data-invoice-locks')\n        if (data) {\n          const parsed = JSON.parse(data)\n          console.log('✅ LOADED FROM document attributes:', parsed)\n          return { ...this.defaultState, ...parsed }\n        }\n\n        // Try window object\n        if (window.INVOICE_LOCKS_DATA) {\n          console.log('✅ LOADED FROM window object:', window.INVOICE_LOCKS_DATA)\n          return { ...this.defaultState, ...window.INVOICE_LOCKS_DATA }\n        }\n\n        console.log('ℹ️ NO SAVED LOCK STATE FOUND, USING DEFAULT')\n        return this.defaultState\n      } catch (error) {\n        console.error('❌ LOAD ERROR:', error)\n        return this.defaultState\n      }\n    },\n\n    // APPLY LOCKS TO DOM ELEMENTS (CLIENT-SIDE ONLY)\n    applyToDom: function(lockState) {\n      // Only run on client side to avoid hydration mismatch\n      if (typeof window === 'undefined' || typeof document === 'undefined') {\n        console.log('⚠️ Server-side detected, skipping DOM lock application')\n        return\n      }\n\n      try {\n        console.log('🔒 APPLYING LOCKS TO DOM (CLIENT-SIDE):', lockState)\n\n        const applyLocks = () => {\n          let appliedCount = 0\n\n          Object.entries(lockState).forEach(([section, isLocked]) => {\n            // Find the section container\n            const sectionContainer = document.querySelector(`[data-section=\"${section}\"]`)\n\n            if (sectionContainer) {\n              // Find all form elements in this section, excluding lock buttons\n              const formElements = sectionContainer.querySelectorAll('input, textarea, button:not([data-lock-button=\"true\"])')\n\n              formElements.forEach(element => {\n                const el = element as HTMLInputElement\n\n                if (isLocked) {\n                  // Apply lock styles and disable\n                  el.disabled = true\n                  el.classList.add('opacity-50', 'cursor-not-allowed')\n                  el.style.backgroundColor = '#f3f4f6'\n                  el.style.color = '#6b7280'\n                  el.style.pointerEvents = 'none'\n                } else {\n                  // Remove lock styles and enable\n                  el.disabled = false\n                  el.classList.remove('opacity-50', 'cursor-not-allowed')\n                  el.style.backgroundColor = ''\n                  el.style.color = ''\n                  el.style.pointerEvents = ''\n                }\n\n                appliedCount++\n              })\n\n              console.log(`✅ Section ${section}: ${isLocked ? 'LOCKED' : 'UNLOCKED'} (${formElements.length} elements)`)\n            } else {\n              console.log(`⚠️ Section container not found: ${section}`)\n            }\n          })\n\n          console.log(`🔒 Applied locks to ${appliedCount} elements total`)\n          return appliedCount\n        }\n\n        // Apply locks with a small delay to ensure React has rendered\n        setTimeout(() => {\n          const immediate = applyLocks()\n\n          // If no elements were found, try again after delays\n          if (immediate === 0) {\n            console.log('⏳ No elements found, retrying after delays...')\n            setTimeout(applyLocks, 500)\n            setTimeout(applyLocks, 1000)\n          }\n        }, 50)\n\n        console.log('✅ DOM LOCK APPLICATION SCHEDULED')\n      } catch (error) {\n        console.error('❌ DOM LOCK APPLICATION ERROR:', error)\n      }\n    },\n\n    // TOGGLE SECTION LOCK\n    toggle: function(section) {\n      try {\n        console.log(`🔄 TOGGLING SECTION: ${section}`)\n\n        const currentState = this.load()\n        const newState = { ...currentState, [section]: !currentState[section] }\n\n        console.log(`📝 ${section}: ${currentState[section]} → ${newState[section]}`)\n\n        // Save the new state\n        const saved = this.save(newState)\n        if (!saved) {\n          console.error('❌ Failed to save toggle state')\n          return false\n        }\n\n        // Apply to DOM\n        this.applyToDom(newState)\n\n        console.log(`✅ TOGGLE COMPLETE: ${section} = ${newState[section]}`)\n        return newState[section]\n      } catch (error) {\n        console.error('❌ TOGGLE ERROR:', error)\n        return false\n      }\n    },\n\n    // LOCK ALL SECTIONS\n    lockAll: function() {\n      try {\n        console.log('🔒 LOCKING ALL SECTIONS')\n\n        const allLocked = {\n          invoiceDetails: true,\n          sellerDetails: true,\n          clientDetails: true,\n          bankDetails: true,\n          items: true,\n          notes: true\n        }\n\n        const saved = this.save(allLocked)\n        if (saved) {\n          this.applyToDom(allLocked)\n          console.log('✅ ALL SECTIONS LOCKED')\n        }\n\n        return saved\n      } catch (error) {\n        console.error('❌ LOCK ALL ERROR:', error)\n        return false\n      }\n    },\n\n    // UNLOCK ALL SECTIONS\n    unlockAll: function() {\n      try {\n        console.log('🔓 UNLOCKING ALL SECTIONS')\n\n        const allUnlocked = this.defaultState\n\n        const saved = this.save(allUnlocked)\n        if (saved) {\n          this.applyToDom(allUnlocked)\n          console.log('✅ ALL SECTIONS UNLOCKED')\n        }\n\n        return saved\n      } catch (error) {\n        console.error('❌ UNLOCK ALL ERROR:', error)\n        return false\n      }\n    },\n\n    // CLEAR ALL LOCK DATA\n    clear: function() {\n      try {\n        console.log('🗑️ CLEARING ALL LOCK DATA')\n\n        // Clear localStorage\n        localStorage.removeItem(LOCK_STORAGE_KEY)\n        localStorage.removeItem(`${LOCK_STORAGE_KEY}_backup1`)\n        localStorage.removeItem(`${LOCK_STORAGE_KEY}_backup2`)\n        localStorage.removeItem(`${LOCK_STORAGE_KEY}_timestamp`)\n\n        // Clear sessionStorage\n        sessionStorage.removeItem(LOCK_STORAGE_KEY)\n        sessionStorage.removeItem(`${LOCK_STORAGE_KEY}_backup`)\n\n        // Clear cookies\n        document.cookie = `${LOCK_STORAGE_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n        document.cookie = `${LOCK_STORAGE_KEY}_backup=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`\n\n        // Clear document attributes\n        document.documentElement.removeAttribute('data-invoice-locks')\n        document.body.removeAttribute('data-invoice-locks-backup')\n\n        // Clear window object\n        delete window.INVOICE_LOCKS_DATA\n        delete window.INVOICE_LOCKS_BACKUP\n\n        // Apply default (unlocked) state\n        this.applyToDom(this.defaultState)\n\n        console.log('✅ ALL LOCK DATA CLEARED')\n        return this.defaultState\n      } catch (error) {\n        console.error('❌ CLEAR ERROR:', error)\n        return this.defaultState\n      }\n    }\n  }\n\n  // CLIENT-SIDE ONLY INITIALIZATION\n  if (typeof window !== 'undefined') {\n    // Only run on client side to avoid hydration mismatch\n    const initializeLockSystem = () => {\n      try {\n        console.log('🚀 CLIENT-SIDE LOCK INITIALIZATION...')\n\n        const savedLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n        console.log('📋 LOADED LOCK STATE:', savedLocks)\n\n        // Apply locks with delays to ensure React has rendered\n        setTimeout(() => {\n          console.log('⏰ Applying locks after React hydration...')\n          window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)\n        }, 1000)\n\n        setTimeout(() => {\n          console.log('⏰ Re-applying locks for safety...')\n          window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)\n        }, 2000)\n\n        console.log('✅ CLIENT-SIDE LOCK INITIALIZATION COMPLETE')\n      } catch (error) {\n        console.error('❌ CLIENT-SIDE LOCK INITIALIZATION ERROR:', error)\n      }\n    }\n\n    // Initialize after DOM is ready\n    if (document.readyState === 'loading') {\n      document.addEventListener('DOMContentLoaded', initializeLockSystem)\n    } else {\n      // Delay to ensure React has hydrated\n      setTimeout(initializeLockSystem, 1000)\n    }\n\n    // Save locks before page unload\n    window.addEventListener('beforeunload', () => {\n      try {\n        const currentLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.save(currentLocks)\n        console.log('💾 LOCKS SAVED BEFORE UNLOAD')\n      } catch (error) {\n        console.error('❌ UNLOAD SAVE ERROR:', error)\n      }\n    })\n\n    console.log('✅ CLIENT-SIDE LOCK SYSTEM READY')\n  }\n}\nimport { useRouter } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { ArrowLeft, FileText, Download, Printer, QrCode, Plus, Upload, Trash2, History, Edit, Copy, Search, Calendar, DollarSign, Eye, MoreVertical, Lock, Unlock } from 'lucide-react'\nimport { toast } from 'sonner'\nimport { generateZATCAQRCode, generateZATCATimestamp, formatCurrencyForZATCA, type ZATCAQRData } from '@/utils/zatcaQR'\nimport { downloadInvoicePDF, printInvoice, type InvoiceData } from '@/utils/pdfGenerator'\n\ninterface InvoiceItem {\n  id: string\n  description: string\n  quantity: number\n  rate: number\n  vatPercent: number\n  amount: number\n}\n\nexport default function CreateInvoicePage() {\n  const router = useRouter()\n\n  // Basic form state\n  const [invoiceNumber, setInvoiceNumber] = useState('INV-2025-001')\n  const [invoiceDate, setInvoiceDate] = useState(new Date().toISOString().split('T')[0])\n  const [currency, setCurrency] = useState('SAR')\n  const [discount, setDiscount] = useState(0)\n  const [deductionAmount, setDeductionAmount] = useState(0)\n  const [deductionReason, setDeductionReason] = useState('')\n\n  // Seller Details\n  const [sellerName, setSellerName] = useState('')\n  const [sellerAddress, setSellerAddress] = useState('')\n  const [sellerVAT, setSellerVAT] = useState('')\n  const [sellerLogo, setSellerLogo] = useState<string | null>(null)\n  const [sellerLogoFile, setSellerLogoFile] = useState<File | null>(null)\n\n  // Client Details\n  const [clientName, setClientName] = useState('')\n  const [clientAddress, setClientAddress] = useState('')\n  const [clientVAT, setClientVAT] = useState('')\n  const [clientLogo, setClientLogo] = useState<string | null>(null)\n  const [clientLogoFile, setClientLogoFile] = useState<File | null>(null)\n\n  // Items\n  const [items, setItems] = useState<InvoiceItem[]>([\n    {\n      id: '1',\n      description: '',\n      quantity: 1,\n      rate: 0,\n      vatPercent: 15,\n      amount: 0\n    }\n  ])\n\n  // Additional Details\n  const [notes, setNotes] = useState('')\n  const [terms, setTerms] = useState('')\n  const [bankName, setBankName] = useState('')\n  const [accountName, setAccountName] = useState('')\n  const [accountNumber, setAccountNumber] = useState('')\n  const [iban, setIban] = useState('')\n  const [dueDate, setDueDate] = useState('')\n\n  // Auto-suggestion state for previously used values\n  const [previousSellerNames, setPreviousSellerNames] = useState<string[]>([])\n  const [previousClientNames, setPreviousClientNames] = useState<string[]>([])\n  const [previousDeductionReasons, setPreviousDeductionReasons] = useState<string[]>([])\n  const [showSuggestions, setShowSuggestions] = useState<{[key: string]: boolean}>({})\n  const [useSimpleInputs, setUseSimpleInputs] = useState(true) // Default to simple inputs for reliability\n\n  // History navbar state\n  const [showHistoryNavbar, setShowHistoryNavbar] = useState(false)\n  const [historyInvoices, setHistoryInvoices] = useState<any[]>([])\n  const [historySearchTerm, setHistorySearchTerm] = useState('')\n  const [historyFilterStatus, setHistoryFilterStatus] = useState('all')\n  const [currentEditingInvoice, setCurrentEditingInvoice] = useState<string | null>(null)\n\n\n\n\n\n\n\n\n\n\n\n  // Default lock state\n  const defaultLockState = {\n    invoiceDetails: false,\n    sellerDetails: false,\n    clientDetails: false,\n    bankDetails: false,\n    items: false,\n    notes: false\n  }\n\n\n\n  // LOCK STATE - USES GLOBAL WINDOW SYSTEM\n  const [lockedSections, setLockedSections] = useState(defaultLockState)\n  const [isHydrated, setIsHydrated] = useState(false)\n\n  // REACT INITIALIZATION - CLIENT-SIDE ONLY\n  useEffect(() => {\n    console.log('🔄 REACT LOCK INITIALIZATION...')\n\n    const initializeLocks = () => {\n      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n        try {\n          // Load saved lock state\n          const savedLocks = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n          console.log('🔒 LOADED LOCK STATE:', savedLocks)\n\n          // Update React state for UI display\n          setLockedSections(savedLocks)\n\n          // Mark as hydrated first\n          setIsHydrated(true)\n\n          // Apply locks to DOM after hydration is complete\n          setTimeout(() => {\n            window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(savedLocks)\n          }, 100)\n\n          // Show restoration message if any sections are locked\n          const lockedCount = Object.values(savedLocks).filter(Boolean).length\n          if (lockedCount > 0) {\n            setTimeout(() => {\n              toast.success(`🔒 ${lockedCount} sections restored and locked!`, {\n                duration: 2000,\n                position: 'top-center'\n              })\n            }, 1500)\n          }\n\n          console.log('✅ REACT LOCK INITIALIZATION COMPLETE')\n          return true\n        } catch (error) {\n          console.error('❌ REACT LOCK INITIALIZATION ERROR:', error)\n          setIsHydrated(true) // Mark as hydrated even if lock loading fails\n          return false\n        }\n      } else {\n        setIsHydrated(true) // Mark as hydrated even if global system not available\n        return false\n      }\n    }\n\n    // Initialize with a delay to ensure React has mounted\n    setTimeout(initializeLocks, 100)\n  }, [])\n\n  // SIMPLE TOGGLE FUNCTION\n  const toggleSectionLock = (section: keyof typeof defaultLockState) => {\n    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n      try {\n        const newLockState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.toggle(section)\n\n        // Update React state to reflect the change\n        setLockedSections(prev => ({\n          ...prev,\n          [section]: newLockState\n        }))\n\n        // Show toast notification\n        const sectionName = section.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())\n        const message = newLockState\n          ? `🔒 ${sectionName} locked!`\n          : `🔓 ${sectionName} unlocked!`\n\n        toast.success(message, { duration: 2000 })\n\n        console.log(`✅ TOGGLED ${section}: ${newLockState}`)\n      } catch (error) {\n        console.error('❌ TOGGLE ERROR:', error)\n        toast.error('Failed to toggle lock')\n      }\n    } else {\n      console.error('❌ GLOBAL LOCK SYSTEM NOT AVAILABLE')\n      toast.error('Lock system not available')\n    }\n  }\n\n  // DEBUG FUNCTION\n  const debugLockState = () => {\n    try {\n      const currentReact = lockedSections\n      let currentGlobal = null\n\n      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n        currentGlobal = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n\n        console.log('=== 🔍 LOCK DEBUG ===')\n        console.log('Hydrated:', isHydrated)\n        console.log('React State:', currentReact)\n        console.log('Global State:', currentGlobal)\n        console.log('Global System Available:', !!window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM)\n        console.log('====================')\n\n        const reactCount = Object.values(currentReact).filter(Boolean).length\n        const globalCount = currentGlobal ? Object.values(currentGlobal).filter(Boolean).length : 0\n\n        toast.info(`🔍 React: ${reactCount} locked | Global: ${globalCount} locked`, {\n          duration: 3000\n        })\n      } else {\n        console.log('❌ GLOBAL SYSTEM NOT AVAILABLE')\n        toast.error('Global system not available!')\n      }\n    } catch (error) {\n      console.error('❌ Debug error:', error)\n      toast.error('Debug failed')\n    }\n  }\n\n  // Load previous values for auto-suggestions\n  const loadPreviousValues = () => {\n    try {\n      if (typeof window === 'undefined') return // Server-side rendering check\n      // Load all previous invoices from localStorage\n      const allInvoices = []\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        if (key && key.startsWith('invoice-')) {\n          const data = localStorage.getItem(key)\n          if (data) {\n            try {\n              allInvoices.push(JSON.parse(data))\n            } catch (e) {\n              // Skip invalid data\n            }\n          }\n        }\n      }\n\n      // Extract unique values\n      const sellerNames = [...new Set(allInvoices.map(inv => inv.sellerName).filter(Boolean))]\n      const clientNames = [...new Set(allInvoices.map(inv => inv.clientName).filter(Boolean))]\n      const deductionReasons = [...new Set(allInvoices.map(inv => inv.deductionReason).filter(Boolean))]\n\n      setPreviousSellerNames(sellerNames)\n      setPreviousClientNames(clientNames)\n      setPreviousDeductionReasons(deductionReasons)\n    } catch (error) {\n      console.error('Error loading previous values:', error)\n    }\n  }\n\n  // Load previous values on component mount\n  useEffect(() => {\n    loadPreviousValues()\n    loadInvoiceHistory()\n  }, [])\n\n  // Load invoice history for navbar\n  const loadInvoiceHistory = () => {\n    try {\n      if (typeof window === 'undefined') return\n      const invoices = []\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {\n          const data = localStorage.getItem(key)\n          if (data) {\n            try {\n              const invoice = JSON.parse(data)\n              if (invoice.invoiceNumber && invoice.timestamp) {\n                invoices.push({\n                  key,\n                  id: key,\n                  ...invoice,\n                  status: invoice.status || 'draft'\n                })\n              }\n            } catch (e) {\n              // Skip invalid data\n            }\n          }\n        }\n      }\n\n      // Sort by timestamp (newest first)\n      const sortedInvoices = invoices.sort((a, b) =>\n        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\n      )\n\n      setHistoryInvoices(sortedInvoices)\n    } catch (error) {\n      console.error('Error loading invoice history:', error)\n    }\n  }\n\n  // Logo upload handlers with dynamic auto-sizing\n  const processAndResizeLogo = (file: File, maxWidth: number = 200, maxHeight: number = 150): Promise<string> => {\n    return new Promise((resolve, reject) => {\n      const canvas = document.createElement('canvas')\n      const ctx = canvas.getContext('2d')\n      const img = new Image()\n\n      img.onload = () => {\n        // Calculate new dimensions while maintaining aspect ratio\n        let { width, height } = img\n\n        if (width > maxWidth || height > maxHeight) {\n          const aspectRatio = width / height\n\n          if (width > height) {\n            width = maxWidth\n            height = width / aspectRatio\n          } else {\n            height = maxHeight\n            width = height * aspectRatio\n          }\n        }\n\n        // Set canvas dimensions\n        canvas.width = width\n        canvas.height = height\n\n        // Draw and resize image\n        ctx?.drawImage(img, 0, 0, width, height)\n\n        // Convert to base64 with high quality\n        const resizedDataUrl = canvas.toDataURL('image/jpeg', 0.9)\n        resolve(resizedDataUrl)\n      }\n\n      img.onerror = () => reject(new Error('Failed to load image'))\n      img.src = URL.createObjectURL(file)\n    })\n  }\n\n  const handleSellerLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      if (file.size > 5 * 1024 * 1024) { // 5MB limit\n        toast.error('Logo file size must be less than 5MB')\n        return\n      }\n\n      if (!file.type.startsWith('image/')) {\n        toast.error('Please select a valid image file')\n        return\n      }\n\n      try {\n        toast.loading('Processing seller logo...', { id: 'seller-logo' })\n        const resizedLogo = await processAndResizeLogo(file, 200, 150)\n        setSellerLogo(resizedLogo)\n        setSellerLogoFile(file)\n        toast.success('Seller logo uploaded and optimized! 🖼️✨', { id: 'seller-logo' })\n      } catch (error) {\n        toast.error('Failed to process logo. Please try again.', { id: 'seller-logo' })\n      }\n    }\n  }\n\n  const handleClientLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0]\n    if (file) {\n      if (file.size > 5 * 1024 * 1024) { // 5MB limit\n        toast.error('Logo file size must be less than 5MB')\n        return\n      }\n\n      if (!file.type.startsWith('image/')) {\n        toast.error('Please select a valid image file')\n        return\n      }\n\n      try {\n        toast.loading('Processing client logo...', { id: 'client-logo' })\n        const resizedLogo = await processAndResizeLogo(file, 200, 150)\n        setClientLogo(resizedLogo)\n        setClientLogoFile(file)\n        toast.success('Client logo uploaded and optimized! 🖼️✨', { id: 'client-logo' })\n      } catch (error) {\n        toast.error('Failed to process logo. Please try again.', { id: 'client-logo' })\n      }\n    }\n  }\n\n  const removeSellerLogo = () => {\n    setSellerLogo(null)\n    setSellerLogoFile(null)\n    toast.success('Seller logo removed')\n  }\n\n  const removeClientLogo = () => {\n    setClientLogo(null)\n    setClientLogoFile(null)\n    toast.success('Client logo removed')\n  }\n\n  // Invoice history management functions\n  const editInvoice = (invoice: any) => {\n    try {\n      // Load all invoice data into the form\n      setInvoiceNumber(invoice.invoiceNumber || '')\n      setInvoiceDate(invoice.invoiceDate || new Date().toISOString().split('T')[0])\n      setDueDate(invoice.dueDate || '')\n      setCurrency(invoice.currency || 'SAR')\n      setDiscount(invoice.discount || 0)\n      setDeductionAmount(invoice.deductionAmount || 0)\n      setDeductionReason(invoice.deductionReason || '')\n\n      // Seller details\n      setSellerName(invoice.sellerName || '')\n      setSellerAddress(invoice.sellerAddress || '')\n      setSellerVAT(invoice.sellerVAT || '')\n      setSellerLogo(invoice.sellerLogo || null)\n\n      // Client details\n      setClientName(invoice.clientName || '')\n      setClientAddress(invoice.clientAddress || '')\n      setClientVAT(invoice.clientVAT || '')\n      setClientLogo(invoice.clientLogo || null)\n\n      // Additional details\n      setNotes(invoice.notes || '')\n      setTerms(invoice.terms || '')\n      setBankName(invoice.bankName || '')\n      setAccountName(invoice.accountName || '')\n      setAccountNumber(invoice.accountNumber || '')\n      setIban(invoice.iban || '')\n\n      // Items\n      if (invoice.items && invoice.items.length > 0) {\n        setItems(invoice.items)\n      }\n\n      setCurrentEditingInvoice(invoice.key)\n      setShowHistoryNavbar(false)\n      toast.success(`Editing invoice: ${invoice.invoiceNumber} 📝`)\n    } catch (error) {\n      console.error('Error editing invoice:', error)\n      toast.error('Failed to load invoice for editing')\n    }\n  }\n\n  const duplicateInvoice = (invoice: any) => {\n    try {\n      // Load invoice data but generate new invoice number\n      const newInvoiceNumber = `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`\n\n      editInvoice(invoice)\n      setInvoiceNumber(newInvoiceNumber)\n      setCurrentEditingInvoice(null)\n      toast.success(`Invoice duplicated as: ${newInvoiceNumber} 📋`)\n    } catch (error) {\n      console.error('Error duplicating invoice:', error)\n      toast.error('Failed to duplicate invoice')\n    }\n  }\n\n  const deleteInvoice = (invoiceKey: string, invoiceNumber: string) => {\n    try {\n      localStorage.removeItem(invoiceKey)\n      loadInvoiceHistory()\n      toast.success(`Invoice ${invoiceNumber} deleted successfully! 🗑️`)\n    } catch (error) {\n      console.error('Error deleting invoice:', error)\n      toast.error('Failed to delete invoice')\n    }\n  }\n\n  const viewInvoiceDetails = (invoice: any) => {\n    // Create a preview of the invoice with current date\n    const currentDate = new Date().toISOString().split('T')[0]\n\n    const invoiceData: InvoiceData = {\n      invoiceNumber: invoice.invoiceNumber,\n      invoiceDate: currentDate, // Always use current date for viewing\n      dueDate: invoice.dueDate,\n      currency: invoice.currency,\n      discount: invoice.discount,\n      deductionAmount: invoice.deductionAmount,\n      deductionReason: invoice.deductionReason,\n      sellerName: invoice.sellerName,\n      sellerAddress: invoice.sellerAddress,\n      sellerVAT: invoice.sellerVAT,\n      sellerLogo: invoice.sellerLogo,\n      clientName: invoice.clientName,\n      clientAddress: invoice.clientAddress,\n      clientVAT: invoice.clientVAT,\n      clientLogo: invoice.clientLogo,\n      items: invoice.items || [],\n      notes: invoice.notes,\n      terms: invoice.terms,\n      bankName: invoice.bankName,\n      accountName: invoice.accountName,\n      accountNumber: invoice.accountNumber,\n      iban: invoice.iban,\n      subtotal: invoice.subtotal,\n      subtotalAfterDeduction: invoice.subtotalAfterDeduction,\n      totalVAT: invoice.totalVAT,\n      totalAmount: invoice.totalAmount,\n      qrCode: generateQRCode(), // Generate fresh QR code with current date\n      logoPreview: undefined\n    }\n\n    try {\n      downloadInvoicePDF(invoiceData)\n      toast.success(`Viewing invoice: ${invoice.invoiceNumber} with current date 👁️`)\n    } catch (error) {\n      console.error('Error viewing invoice:', error)\n      toast.error('Failed to view invoice')\n    }\n  }\n\n  // Simple calculations\n  const subtotal = items.reduce((sum, item) => sum + item.amount, 0)\n  const discountAmount = (subtotal * discount) / 100\n  const subtotalAfterDiscount = subtotal - discountAmount\n  const subtotalAfterDeduction = Math.max(0, subtotalAfterDiscount - deductionAmount)\n  const totalVAT = subtotalAfterDeduction * 0.15\n  const totalAmount = subtotalAfterDeduction + totalVAT\n\n  // Simple functions\n  const addItem = () => {\n    const newItem: InvoiceItem = {\n      id: Date.now().toString(),\n      description: '',\n      quantity: 1,\n      rate: 0,\n      vatPercent: 15,\n      amount: 0\n    }\n    setItems([...items, newItem])\n  }\n\n  const removeItem = (itemId: string) => {\n    if (items.length <= 1) {\n      toast.error('At least one item is required')\n      return\n    }\n    setItems(items.filter(item => item.id !== itemId))\n    toast.success('Item removed successfully! 🗑️')\n  }\n\n  const updateItem = (id: string, field: keyof InvoiceItem, value: any) => {\n    setItems(items.map(item => {\n      if (item.id === id) {\n        const updatedItem = { ...item, [field]: value }\n        if (field === 'quantity' || field === 'rate') {\n          updatedItem.amount = updatedItem.quantity * updatedItem.rate\n        }\n        return updatedItem\n      }\n      return item\n    }))\n  }\n\n  // QR Code generation function - ALWAYS uses current date\n  const generateQRCode = () => {\n    // Always use current date for QR code timestamp\n    const currentDate = new Date()\n\n    const qrData: ZATCAQRData = {\n      sellerName: sellerName || 'KAAZMAAMAA Company',\n      vatNumber: sellerVAT || '30**********003',\n      timestamp: generateZATCATimestamp(currentDate),\n      invoiceTotal: formatCurrencyForZATCA(totalAmount),\n      vatTotal: formatCurrencyForZATCA(totalVAT)\n    }\n\n    try {\n      return generateZATCAQRCode(qrData)\n    } catch (error) {\n      console.error('QR Code generation error:', error)\n      toast.error('Failed to generate QR code')\n      return ''\n    }\n  }\n\n  // PDF Generation function\n  const generatePDF = () => {\n    if (!sellerName || !clientName || items.length === 0) {\n      toast.error('Please fill in all required fields before generating PDF')\n      return\n    }\n\n    // Validate deduction reason if deduction amount is specified\n    if (deductionAmount > 0 && !deductionReason.trim()) {\n      toast.error('Please provide a reason for the deduction amount')\n      return\n    }\n\n    toast.info('Generating PDF with ZATCA QR code...')\n\n    // Always use current date for PDF generation\n    const currentDate = new Date().toISOString().split('T')[0]\n\n    const invoiceData: InvoiceData = {\n      invoiceNumber,\n      invoiceDate: currentDate, // Always use current date\n      dueDate,\n      currency,\n      discount,\n      deductionAmount,\n      deductionReason,\n      sellerName,\n      sellerAddress,\n      sellerVAT,\n      sellerLogo,\n      clientName,\n      clientAddress,\n      clientVAT,\n      clientLogo,\n      items,\n      notes,\n      terms,\n      bankName,\n      accountName,\n      accountNumber,\n      iban,\n      subtotal,\n      subtotalAfterDeduction,\n      totalVAT,\n      totalAmount,\n      qrCode: generateQRCode(), // This also uses current date\n      logoPreview: undefined\n    }\n\n    try {\n      downloadInvoicePDF(invoiceData)\n      toast.success(`PDF generated with current date: ${new Date().toLocaleDateString()} 📄`)\n    } catch (error) {\n      console.error('PDF generation error:', error)\n      toast.error('Failed to generate PDF')\n    }\n  }\n\n  // Print Invoice function\n  const handlePrintInvoice = () => {\n    if (!sellerName || !clientName || items.length === 0) {\n      toast.error('Please fill in all required fields before printing')\n      return\n    }\n\n    // Validate deduction reason if deduction amount is specified\n    if (deductionAmount > 0 && !deductionReason.trim()) {\n      toast.error('Please provide a reason for the deduction amount')\n      return\n    }\n\n    toast.info('Opening print dialog...')\n\n    // Always use current date for print as well\n    const currentDate = new Date().toISOString().split('T')[0]\n\n    const invoiceData: InvoiceData = {\n      invoiceNumber,\n      invoiceDate: currentDate, // Always use current date\n      dueDate,\n      currency,\n      discount,\n      deductionAmount,\n      deductionReason,\n      sellerName,\n      sellerAddress,\n      sellerVAT,\n      sellerLogo,\n      clientName,\n      clientAddress,\n      clientVAT,\n      clientLogo,\n      items,\n      notes,\n      terms,\n      bankName,\n      accountName,\n      accountNumber,\n      iban,\n      subtotal,\n      subtotalAfterDeduction,\n      totalVAT,\n      totalAmount,\n      qrCode: generateQRCode(), // This also uses current date\n      logoPreview: undefined\n    }\n\n    try {\n      printInvoice(invoiceData)\n      toast.success('Print dialog opened successfully! 🖨️')\n    } catch (error) {\n      console.error('Print error:', error)\n      toast.error('Failed to open print dialog')\n    }\n  }\n\n  const saveDraft = () => {\n    try {\n      const formData = {\n        invoiceNumber, invoiceDate, dueDate, currency, discount, deductionAmount, deductionReason,\n        sellerName, sellerAddress, sellerVAT, sellerLogo, clientName, clientAddress, clientVAT, clientLogo,\n        notes, terms, bankName, accountName, accountNumber, iban, items,\n        subtotal, subtotalAfterDeduction, totalVAT, totalAmount,\n        status: 'draft',\n        timestamp: new Date().toISOString(),\n        lastModified: new Date().toISOString()\n      }\n\n      // Save as current draft\n      localStorage.setItem('invoice-draft', JSON.stringify(formData))\n\n      // Save with unique key for history\n      if (sellerName || clientName || invoiceNumber) {\n        let historyKey\n        if (currentEditingInvoice) {\n          // Update existing invoice\n          historyKey = currentEditingInvoice\n          formData.lastModified = new Date().toISOString()\n        } else {\n          // Create new invoice\n          historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`\n        }\n\n        localStorage.setItem(historyKey, JSON.stringify(formData))\n\n        // Update previous values and history\n        loadPreviousValues()\n        loadInvoiceHistory()\n      }\n\n      toast.success(currentEditingInvoice ? 'Invoice updated! 📝' : 'Draft saved! 💾')\n    } catch (error) {\n      toast.error('Save failed')\n    }\n  }\n\n  const saveAsCompleted = () => {\n    try {\n      if (!sellerName || !clientName || items.length === 0) {\n        toast.error('Please fill in all required fields before marking as completed')\n        return\n      }\n\n      const formData = {\n        invoiceNumber, invoiceDate, dueDate, currency, discount, deductionAmount, deductionReason,\n        sellerName, sellerAddress, sellerVAT, sellerLogo, clientName, clientAddress, clientVAT, clientLogo,\n        notes, terms, bankName, accountName, accountNumber, iban, items,\n        subtotal, subtotalAfterDeduction, totalVAT, totalAmount,\n        status: 'completed',\n        timestamp: new Date().toISOString(),\n        lastModified: new Date().toISOString(),\n        qrCode: generateQRCode()\n      }\n\n      let historyKey\n      if (currentEditingInvoice) {\n        historyKey = currentEditingInvoice\n      } else {\n        historyKey = `invoice-${invoiceNumber || Date.now()}-${Date.now()}`\n      }\n\n      localStorage.setItem(historyKey, JSON.stringify(formData))\n      loadInvoiceHistory()\n\n      toast.success(`Invoice ${invoiceNumber} marked as completed! ✅`)\n    } catch (error) {\n      toast.error('Failed to save completed invoice')\n    }\n  }\n\n  const createNewInvoice = () => {\n    // Clear all form fields\n    setInvoiceNumber(`INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`)\n    setInvoiceDate(new Date().toISOString().split('T')[0])\n    setDueDate('')\n    setCurrency('SAR')\n    setDiscount(0)\n    setDeductionAmount(0)\n    setDeductionReason('')\n\n    // Clear seller details (but keep for convenience)\n    // setSellerName('')\n    // setSellerAddress('')\n    // setSellerVAT('')\n    setSellerLogo(null)\n\n    // Clear client details\n    setClientName('')\n    setClientAddress('')\n    setClientVAT('')\n    setClientLogo(null)\n\n    // Clear additional details\n    setNotes('')\n    setTerms('')\n    setBankName('')\n    setAccountName('')\n    setAccountNumber('')\n    setIban('')\n\n    // Reset items\n    setItems([{\n      id: '1',\n      description: '',\n      quantity: 1,\n      rate: 0,\n      vatPercent: 15,\n      amount: 0\n    }])\n\n    setCurrentEditingInvoice(null)\n    setShowHistoryNavbar(false)\n    toast.success('New invoice form ready! 📄')\n  }\n\n  // Function to set current date\n  const setCurrentDate = () => {\n    const currentDate = new Date().toISOString().split('T')[0]\n    setInvoiceDate(currentDate)\n    toast.success('Invoice date updated to current date! 📅')\n  }\n\n\n\n  // GLOBAL UNLOCK ALL\n  const unlockAllSections = () => {\n    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.unlockAll()\n\n      // Update React state\n      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n      setLockedSections(newState)\n\n      toast.success('🔓 ALL SECTIONS GLOBAL UNLOCKED!', { duration: 3000 })\n    }\n  }\n\n  // GLOBAL LOCK ALL\n  const lockAllSections = () => {\n    if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n      window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.lockAll()\n\n      // Update React state\n      const newState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n      setLockedSections(newState)\n\n      toast.success('🔒 ALL SECTIONS GLOBAL LOCKED - PERMANENT!', { duration: 3000 })\n    }\n  }\n\n  // GLOBAL FORCE RELOAD\n  const forceReloadLocks = () => {\n    try {\n      console.log('🔄 GLOBAL FORCE RELOAD TRIGGERED')\n\n      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n        const reloadedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n        console.log('🔒 GLOBAL RELOADED STATE:', reloadedState)\n\n        // Update React state\n        setLockedSections(reloadedState)\n\n        // Apply to DOM\n        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.applyToDom(reloadedState)\n\n        const lockedCount = Object.values(reloadedState).filter(Boolean).length\n        toast.success(`🔄 GLOBAL RELOADED: ${lockedCount} sections locked!`, { duration: 2000 })\n      }\n    } catch (error) {\n      console.error('❌ Global reload error:', error)\n      toast.error('Global reload failed!')\n    }\n  }\n\n  // GLOBAL FORCE SAVE\n  const forceSave = () => {\n    try {\n      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n        const currentState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n        console.log('🚀 GLOBAL FORCE SAVE TRIGGERED:', currentState)\n        const saved = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.save(currentState)\n        if (saved) {\n          toast.success('🚀 GLOBAL FORCE SAVE COMPLETE!', { duration: 2000 })\n          console.log('✅ GLOBAL FORCE SAVE SUCCESSFUL')\n\n          // Show verification\n          setTimeout(() => {\n            const verify = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n            console.log('✅ GLOBAL SAVE VERIFIED:', verify)\n          }, 100)\n        } else {\n          toast.error('❌ GLOBAL FORCE SAVE FAILED!')\n          console.error('❌ GLOBAL FORCE SAVE FAILED')\n        }\n      }\n    } catch (error) {\n      console.error('❌ Global force save error:', error)\n      toast.error('Global force save failed!')\n    }\n  }\n\n  // GLOBAL CLEAR (TESTING)\n  const clearLockState = () => {\n    try {\n      console.log('🗑️ GLOBAL CLEAR TRIGGERED')\n\n      if (typeof window !== 'undefined' && window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM) {\n        // Use global clear method\n        window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.clear()\n\n        // Update React state\n        const clearedState = window.KAAZMAAMAA_GLOBAL_LOCK_SYSTEM.load()\n        setLockedSections(clearedState)\n\n        toast.success('🗑️ GLOBAL CLEARED FROM ALL 50+ LOCATIONS!', { duration: 3000 })\n        console.log('✅ GLOBAL CLEAR COMPLETE')\n      }\n    } catch (error) {\n      console.error('❌ Global clear error:', error)\n      toast.error('Global clear failed!')\n    }\n  }\n\n  // Preview function to show current modifications\n  const previewInvoice = () => {\n    if (!sellerName || !clientName || items.length === 0) {\n      toast.error('Please fill in all required fields before preview')\n      return\n    }\n\n    if (deductionAmount > 0 && !deductionReason.trim()) {\n      toast.error('Please provide a reason for the deduction amount')\n      return\n    }\n\n    toast.info('Generating preview with current date...')\n\n    // Always use current date for preview\n    const currentDate = new Date().toISOString().split('T')[0]\n    const currentDateFormatted = new Date().toLocaleDateString()\n\n    const invoiceData: InvoiceData = {\n      invoiceNumber,\n      invoiceDate: currentDate, // Always use current date\n      dueDate,\n      currency,\n      discount,\n      deductionAmount,\n      deductionReason,\n      sellerName,\n      sellerAddress,\n      sellerVAT,\n      sellerLogo,\n      clientName,\n      clientAddress,\n      clientVAT,\n      clientLogo,\n      items,\n      notes,\n      terms,\n      bankName,\n      accountName,\n      accountNumber,\n      iban,\n      subtotal,\n      subtotalAfterDeduction,\n      totalVAT,\n      totalAmount,\n      qrCode: generateQRCode(), // This also uses current date\n      logoPreview: undefined\n    }\n\n    try {\n      // Create preview window\n      const previewWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes')\n      if (previewWindow) {\n        const html = `\n          <!DOCTYPE html>\n          <html>\n          <head>\n            <title>Invoice Preview - Current Date: ${currentDateFormatted}</title>\n            <style>\n              body {\n                font-family: Arial, sans-serif;\n                margin: 20px;\n                background: #f5f5f5;\n              }\n              .preview-header {\n                background: #4f46e5;\n                color: white;\n                padding: 15px;\n                border-radius: 8px;\n                margin-bottom: 20px;\n                text-align: center;\n              }\n              .preview-info {\n                background: #e0f2fe;\n                border: 2px solid #0288d1;\n                padding: 15px;\n                border-radius: 8px;\n                margin-bottom: 20px;\n              }\n              .lock-status {\n                background: #fff3e0;\n                border: 2px solid #ff9800;\n                padding: 15px;\n                border-radius: 8px;\n                margin-bottom: 20px;\n              }\n              .locked { color: #d32f2f; font-weight: bold; }\n              .unlocked { color: #388e3c; font-weight: bold; }\n              .invoice-preview {\n                background: white;\n                padding: 20px;\n                border-radius: 8px;\n                box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n              }\n              .field { margin: 8px 0; }\n              .label { font-weight: bold; color: #333; }\n              .value { color: #666; margin-left: 10px; }\n              .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n              .current-date { color: #4f46e5; font-weight: bold; font-size: 18px; }\n            </style>\n          </head>\n          <body>\n            <div class=\"preview-header\">\n              <h1>📄 Invoice Preview</h1>\n              <p>Preview with Current Date Modifications Applied</p>\n            </div>\n\n            <div class=\"preview-info\">\n              <h3>🔍 Preview Information</h3>\n              <div class=\"field\">\n                <span class=\"label\">Current Date Applied:</span>\n                <span class=\"value current-date\">${currentDateFormatted}</span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">QR Code Date:</span>\n                <span class=\"value current-date\">${currentDateFormatted}</span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">PDF Date:</span>\n                <span class=\"value current-date\">${currentDateFormatted}</span>\n              </div>\n            </div>\n\n            <div class=\"lock-status\">\n              <h3>🔒 Lock Status</h3>\n              <div class=\"field\">\n                <span class=\"label\">Invoice Details:</span>\n                <span class=\"value ${lockedSections.invoiceDetails ? 'locked' : 'unlocked'}\">\n                  ${lockedSections.invoiceDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}\n                </span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">Seller Details:</span>\n                <span class=\"value ${lockedSections.sellerDetails ? 'locked' : 'unlocked'}\">\n                  ${lockedSections.sellerDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}\n                </span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">Client Details:</span>\n                <span class=\"value ${lockedSections.clientDetails ? 'locked' : 'unlocked'}\">\n                  ${lockedSections.clientDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}\n                </span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">Bank Details:</span>\n                <span class=\"value ${lockedSections.bankDetails ? 'locked' : 'unlocked'}\">\n                  ${lockedSections.bankDetails ? '🔒 LOCKED' : '🔓 UNLOCKED'}\n                </span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">Invoice Items:</span>\n                <span class=\"value ${lockedSections.items ? 'locked' : 'unlocked'}\">\n                  ${lockedSections.items ? '🔒 LOCKED' : '🔓 UNLOCKED'}\n                </span>\n              </div>\n              <div class=\"field\">\n                <span class=\"label\">Additional Information:</span>\n                <span class=\"value ${lockedSections.notes ? 'locked' : 'unlocked'}\">\n                  ${lockedSections.notes ? '🔒 LOCKED' : '🔓 UNLOCKED'}\n                </span>\n              </div>\n            </div>\n\n            <div class=\"invoice-preview\">\n              <h3>📋 Invoice Data Preview</h3>\n\n              <div class=\"section\">\n                <h4>Invoice Details</h4>\n                <div class=\"field\">\n                  <span class=\"label\">Invoice Number:</span>\n                  <span class=\"value\">${invoiceNumber}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Invoice Date:</span>\n                  <span class=\"value current-date\">${currentDateFormatted} (CURRENT DATE APPLIED)</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Due Date:</span>\n                  <span class=\"value\">${dueDate || 'Not specified'}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Currency:</span>\n                  <span class=\"value\">${currency}</span>\n                </div>\n              </div>\n\n              <div class=\"section\">\n                <h4>Seller Details</h4>\n                <div class=\"field\">\n                  <span class=\"label\">Company Name:</span>\n                  <span class=\"value\">${sellerName}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">VAT Number:</span>\n                  <span class=\"value\">${sellerVAT}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Address:</span>\n                  <span class=\"value\">${sellerAddress}</span>\n                </div>\n              </div>\n\n              <div class=\"section\">\n                <h4>Client Details</h4>\n                <div class=\"field\">\n                  <span class=\"label\">Client Name:</span>\n                  <span class=\"value\">${clientName}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Client VAT:</span>\n                  <span class=\"value\">${clientVAT || 'Not provided'}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Client Address:</span>\n                  <span class=\"value\">${clientAddress}</span>\n                </div>\n              </div>\n\n              <div class=\"section\">\n                <h4>Financial Summary</h4>\n                <div class=\"field\">\n                  <span class=\"label\">Subtotal:</span>\n                  <span class=\"value\">${currency} ${subtotal.toFixed(2)}</span>\n                </div>\n                ${deductionAmount > 0 ? `\n                  <div class=\"field\">\n                    <span class=\"label\">Deduction:</span>\n                    <span class=\"value\">-${currency} ${deductionAmount.toFixed(2)}</span>\n                  </div>\n                ` : ''}\n                <div class=\"field\">\n                  <span class=\"label\">Total VAT (15%):</span>\n                  <span class=\"value\">${currency} ${totalVAT.toFixed(2)}</span>\n                </div>\n                <div class=\"field\">\n                  <span class=\"label\">Total Amount:</span>\n                  <span class=\"value current-date\">${currency} ${totalAmount.toFixed(2)}</span>\n                </div>\n              </div>\n            </div>\n          </body>\n          </html>\n        `\n\n        previewWindow.document.write(html)\n        previewWindow.document.close()\n\n        toast.success(`Preview opened with current date: ${currentDateFormatted} 👁️`)\n      }\n    } catch (error) {\n      console.error('Preview error:', error)\n      toast.error('Failed to open preview')\n    }\n  }\n\n  // Load previous user data from localStorage\n  const loadPreviousData = () => {\n    try {\n      const savedData = localStorage.getItem('invoice-draft')\n      if (savedData) {\n        const data = JSON.parse(savedData)\n\n        // Load all the saved data\n        if (data.invoiceNumber) setInvoiceNumber(data.invoiceNumber)\n        if (data.invoiceDate) setInvoiceDate(data.invoiceDate)\n        if (data.dueDate) setDueDate(data.dueDate)\n        if (data.currency) setCurrency(data.currency)\n        if (data.discount !== undefined) setDiscount(data.discount)\n        if (data.deductionAmount !== undefined) setDeductionAmount(data.deductionAmount)\n        if (data.deductionReason) setDeductionReason(data.deductionReason)\n\n        // Seller details\n        if (data.sellerName) setSellerName(data.sellerName)\n        if (data.sellerAddress) setSellerAddress(data.sellerAddress)\n        if (data.sellerVAT) setSellerVAT(data.sellerVAT)\n        if (data.sellerLogo) setSellerLogo(data.sellerLogo)\n\n        // Client details\n        if (data.clientName) setClientName(data.clientName)\n        if (data.clientAddress) setClientAddress(data.clientAddress)\n        if (data.clientVAT) setClientVAT(data.clientVAT)\n        if (data.clientLogo) setClientLogo(data.clientLogo)\n\n        // Additional details\n        if (data.notes) setNotes(data.notes)\n        if (data.terms) setTerms(data.terms)\n        if (data.bankName) setBankName(data.bankName)\n        if (data.accountName) setAccountName(data.accountName)\n        if (data.accountNumber) setAccountNumber(data.accountNumber)\n        if (data.iban) setIban(data.iban)\n\n        // Items\n        if (data.items && data.items.length > 0) {\n          setItems(data.items)\n        }\n\n        toast.success('Previous data loaded successfully! 📋')\n      } else {\n        toast.info('No previous data found. Start entering your information.')\n      }\n    } catch (error) {\n      console.error('Error loading previous data:', error)\n      toast.error('Failed to load previous data')\n    }\n  }\n\n  // Get recent invoices for quick loading\n  const getRecentInvoices = () => {\n    try {\n      if (typeof window === 'undefined') return [] // Server-side rendering check\n      const recentInvoices = []\n      for (let i = 0; i < localStorage.length; i++) {\n        const key = localStorage.key(i)\n        if (key && key.startsWith('invoice-') && key !== 'invoice-draft') {\n          const data = localStorage.getItem(key)\n          if (data) {\n            try {\n              const invoice = JSON.parse(data)\n              if (invoice.invoiceNumber && invoice.timestamp) {\n                recentInvoices.push({\n                  key,\n                  ...invoice\n                })\n              }\n            } catch (e) {\n              // Skip invalid data\n            }\n          }\n        }\n      }\n\n      // Sort by timestamp (newest first) and take top 5\n      return recentInvoices\n        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())\n        .slice(0, 5)\n    } catch (error) {\n      console.error('Error getting recent invoices:', error)\n      return []\n    }\n  }\n\n  const loadInvoiceData = (invoice: any) => {\n    // Load all the invoice data\n    setInvoiceNumber(invoice.invoiceNumber || '')\n    setInvoiceDate(invoice.invoiceDate || '')\n    setDueDate(invoice.dueDate || '')\n    setCurrency(invoice.currency || 'SAR')\n    setDiscount(invoice.discount || 0)\n    setDeductionAmount(invoice.deductionAmount || 0)\n    setDeductionReason(invoice.deductionReason || '')\n\n    // Seller details\n    setSellerName(invoice.sellerName || '')\n    setSellerAddress(invoice.sellerAddress || '')\n    setSellerVAT(invoice.sellerVAT || '')\n    setSellerLogo(invoice.sellerLogo || null)\n\n    // Client details\n    setClientName(invoice.clientName || '')\n    setClientAddress(invoice.clientAddress || '')\n    setClientVAT(invoice.clientVAT || '')\n    setClientLogo(invoice.clientLogo || null)\n\n    // Additional details\n    setNotes(invoice.notes || '')\n    setTerms(invoice.terms || '')\n    setBankName(invoice.bankName || '')\n    setAccountName(invoice.accountName || '')\n    setAccountNumber(invoice.accountNumber || '')\n    setIban(invoice.iban || '')\n\n    // Items\n    if (invoice.items && invoice.items.length > 0) {\n      setItems(invoice.items)\n    }\n\n    toast.success(`Invoice ${invoice.invoiceNumber} loaded! 📋`)\n  }\n\n  // Simple and reliable auto-suggestion component\n  const SuggestionInput = ({\n    value,\n    onChange,\n    suggestions,\n    placeholder,\n    label,\n    fieldKey,\n    required = false,\n    disabled = false\n  }: {\n    value: string\n    onChange: (value: string) => void\n    suggestions: string[]\n    placeholder: string\n    label: string\n    fieldKey: string\n    required?: boolean\n    disabled?: boolean\n  }) => {\n    const [showDropdown, setShowDropdown] = useState(false)\n\n    const filteredSuggestions = suggestions.filter(suggestion =>\n      suggestion.toLowerCase().includes(value.toLowerCase()) && suggestion !== value\n    ).slice(0, 5)\n\n    return (\n      <div className=\"relative\">\n        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n          {label} {required && <span className=\"text-red-500\">*</span>}\n        </label>\n        <Input\n          value={value}\n          onChange={(e) => {\n            const newValue = e.target.value\n            onChange(newValue)\n            setShowDropdown(newValue.length > 0 && filteredSuggestions.length > 0)\n          }}\n          onFocus={() => {\n            if (value.length > 0 && filteredSuggestions.length > 0) {\n              setShowDropdown(true)\n            }\n          }}\n          onBlur={() => {\n            setTimeout(() => setShowDropdown(false), 150)\n          }}\n          placeholder={placeholder}\n          required={required}\n          disabled={disabled}\n          className=\"w-full\"\n        />\n        {showDropdown && filteredSuggestions.length > 0 && (\n          <div className=\"absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 overflow-y-auto\">\n            {filteredSuggestions.map((suggestion, index) => (\n              <div\n                key={`${fieldKey}-${index}`}\n                className=\"px-3 py-2 hover:bg-blue-50 cursor-pointer text-sm border-b border-gray-100 last:border-b-0\"\n                onMouseDown={(e) => e.preventDefault()}\n                onClick={() => {\n                  onChange(suggestion)\n                  setShowDropdown(false)\n                  toast.success('Previous data filled! 📋')\n                }}\n              >\n                {suggestion}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => router.push('/invoice')}\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Invoice\n              </Button>\n              <div>\n                <h1 className=\"text-2xl font-bold text-purple-600\">\n                  {currentEditingInvoice ? 'Edit Invoice' : 'Create Invoice'}\n                </h1>\n                <p className=\"text-sm text-gray-600\">\n                  {currentEditingInvoice\n                    ? 'Modify and update your existing invoice'\n                    : 'Generate professional ZATCA-compliant invoices'\n                  }\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => setShowHistoryNavbar(!showHistoryNavbar)}\n                className=\"bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200\"\n              >\n                <History className=\"h-4 w-4 mr-2\" />\n                History ({historyInvoices.length})\n              </Button>\n              {currentEditingInvoice && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={createNewInvoice}\n                  className=\"bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  New Invoice\n                </Button>\n              )}\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={loadPreviousData}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white\"\n              >\n                📋 Load Previous Data\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => {\n                  setUseSimpleInputs(!useSimpleInputs)\n                  toast.info(useSimpleInputs ? 'Auto-suggestions enabled' : 'Simple inputs enabled')\n                }}\n                className={useSimpleInputs ? \"bg-orange-600 hover:bg-orange-700 text-white\" : \"bg-gray-600 hover:bg-gray-700 text-white\"}\n              >\n                {useSimpleInputs ? '🔧 Simple Mode' : '🎯 Smart Mode'}\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={saveDraft}\n              >\n                💾 Save Draft\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={saveAsCompleted}\n                className=\"bg-green-50 text-green-600 hover:bg-green-100 border-green-200\"\n              >\n                ✅ Mark Complete\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={unlockAllSections}\n                className=\"bg-yellow-50 text-yellow-600 hover:bg-yellow-100 border-yellow-200\"\n              >\n                <Unlock className=\"h-4 w-4 mr-1\" />\n                Unlock All\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={lockAllSections}\n                className=\"bg-red-50 text-red-600 hover:bg-red-100 border-red-200\"\n              >\n                <Lock className=\"h-4 w-4 mr-1\" />\n                Lock All\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={previewInvoice}\n                className=\"bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200\"\n              >\n                <Eye className=\"h-4 w-4 mr-1\" />\n                Preview\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={debugLockState}\n                className=\"bg-gray-50 text-gray-600 hover:bg-gray-100 border-gray-200\"\n                title=\"Debug lock state\"\n              >\n                🔍 Debug\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={forceReloadLocks}\n                className=\"bg-purple-50 text-purple-600 hover:bg-purple-100 border-purple-200\"\n                title=\"Force reload lock state\"\n              >\n                🔄 Reload\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={forceSave}\n                className=\"bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200\"\n                title=\"Force save lock state\"\n              >\n                🚀 Save\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={clearLockState}\n                className=\"bg-orange-50 text-orange-600 hover:bg-orange-100 border-orange-200\"\n                title=\"Clear all lock state (for testing)\"\n              >\n                🗑️ Clear\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={generatePDF}\n                className=\"bg-green-600 hover:bg-green-700 text-white\"\n              >\n                <Download className=\"h-4 w-4 mr-2\" />\n                Generate PDF\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={handlePrintInvoice}\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              >\n                <Printer className=\"h-4 w-4 mr-2\" />\n                Print Invoice\n              </Button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* History Navbar */}\n      {showHistoryNavbar && (\n        <div className=\"bg-white border-b shadow-sm\">\n          <div className=\"max-w-7xl mx-auto px-4 py-4\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Invoice History</h2>\n                <span className=\"text-sm text-gray-500\">({historyInvoices.length} invoices)</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"relative\">\n                  <Search className=\"h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\" />\n                  <Input\n                    placeholder=\"Search invoices...\"\n                    value={historySearchTerm}\n                    onChange={(e) => setHistorySearchTerm(e.target.value)}\n                    className=\"pl-10 w-64\"\n                  />\n                </div>\n                <select\n                  value={historyFilterStatus}\n                  onChange={(e) => setHistoryFilterStatus(e.target.value)}\n                  className=\"px-3 py-2 border border-gray-300 rounded-md text-sm\"\n                >\n                  <option value=\"all\">All Status</option>\n                  <option value=\"draft\">Draft</option>\n                  <option value=\"completed\">Completed</option>\n                </select>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => setShowHistoryNavbar(false)}\n                >\n                  Close\n                </Button>\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto\">\n              {historyInvoices\n                .filter(invoice => {\n                  const matchesSearch = !historySearchTerm ||\n                    invoice.invoiceNumber?.toLowerCase().includes(historySearchTerm.toLowerCase()) ||\n                    invoice.clientName?.toLowerCase().includes(historySearchTerm.toLowerCase()) ||\n                    invoice.sellerName?.toLowerCase().includes(historySearchTerm.toLowerCase())\n\n                  const matchesStatus = historyFilterStatus === 'all' || invoice.status === historyFilterStatus\n\n                  return matchesSearch && matchesStatus\n                })\n                .map((invoice) => (\n                  <div\n                    key={invoice.key}\n                    className=\"border rounded-lg p-4 hover:shadow-md transition-shadow bg-gray-50\"\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div className=\"flex-1\">\n                        <h3 className=\"font-medium text-gray-900 truncate\">{invoice.invoiceNumber}</h3>\n                        <p className=\"text-sm text-gray-600 truncate\">{invoice.clientName || 'No client'}</p>\n                        <div className=\"flex items-center space-x-2 mt-1\">\n                          <span className={`px-2 py-1 text-xs rounded-full ${\n                            invoice.status === 'completed'\n                              ? 'bg-green-100 text-green-800'\n                              : 'bg-yellow-100 text-yellow-800'\n                          }`}>\n                            {invoice.status || 'draft'}\n                          </span>\n                          <span className=\"text-xs text-gray-500\">\n                            {new Date(invoice.timestamp).toLocaleDateString()}\n                          </span>\n                        </div>\n                      </div>\n                      <div className=\"relative\">\n                        <Button\n                          variant=\"ghost\"\n                          size=\"sm\"\n                          className=\"h-8 w-8 p-0\"\n                        >\n                          <MoreVertical className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between mb-3\">\n                      <div className=\"flex items-center space-x-1\">\n                        <DollarSign className=\"h-4 w-4 text-green-600\" />\n                        <span className=\"font-semibold text-green-600\">\n                          {invoice.currency} {(invoice.totalAmount || 0).toFixed(2)}\n                        </span>\n                      </div>\n                      <div className=\"flex items-center space-x-1\">\n                        <Calendar className=\"h-4 w-4 text-gray-400\" />\n                        <span className=\"text-xs text-gray-500\">\n                          {invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : 'No due date'}\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => editInvoice(invoice)}\n                        className=\"flex-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50\"\n                      >\n                        <Edit className=\"h-3 w-3 mr-1\" />\n                        Edit\n                      </Button>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => duplicateInvoice(invoice)}\n                        className=\"flex-1 text-green-600 hover:text-green-700 hover:bg-green-50\"\n                      >\n                        <Copy className=\"h-3 w-3 mr-1\" />\n                        Copy\n                      </Button>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={() => viewInvoiceDetails(invoice)}\n                        className=\"flex-1 text-purple-600 hover:text-purple-700 hover:bg-purple-50\"\n                      >\n                        <Eye className=\"h-3 w-3 mr-1\" />\n                        View\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n\n              {historyInvoices.length === 0 && (\n                <div className=\"col-span-full text-center py-8 text-gray-500\">\n                  <History className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n                  <p className=\"text-lg font-medium\">No invoices found</p>\n                  <p className=\"text-sm\">Create and save invoices to see them here</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"max-w-7xl mx-auto px-4 py-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n          {/* Left Column - Invoice Details */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            {/* Invoice Details Section */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2 text-purple-600\">\n                    <FileText className=\"h-5 w-5\" />\n                    <span>Invoice Details</span>\n                    {isHydrated && lockedSections.invoiceDetails && (\n                      <Lock className=\"h-4 w-4 text-red-500\" />\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => toggleSectionLock('invoiceDetails')}\n                    data-lock-button=\"true\"\n                    className={`${\n                      isHydrated && lockedSections.invoiceDetails\n                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'\n                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'\n                    }`}\n                  >\n                    {!isHydrated ? (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    ) : directLockControl.getCurrentState().invoiceDetails ? (\n                      <>\n                        <Unlock className=\"h-4 w-4 mr-1\" />\n                        Unlock\n                      </>\n                    ) : (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    )}\n                  </Button>\n                </CardTitle>\n              </CardHeader>\n              <CardContent data-section=\"invoiceDetails\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Invoice Number</label>\n                    <Input\n                      value={invoiceNumber}\n                      onChange={(e) => setInvoiceNumber(e.target.value)}\n                      placeholder=\"INV-2025-001\"\n                      disabled={isHydrated && lockedSections.invoiceDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Invoice Date</label>\n                    <div className=\"flex space-x-2\">\n                      <Input\n                        type=\"date\"\n                        value={invoiceDate}\n                        onChange={(e) => setInvoiceDate(e.target.value)}\n                        className=\"flex-1\"\n                        disabled={isHydrated && lockedSections.invoiceDetails}\n                      />\n                      <Button\n                        type=\"button\"\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={setCurrentDate}\n                        className=\"bg-blue-50 text-blue-600 hover:bg-blue-100 border-blue-200 whitespace-nowrap\"\n                        title=\"Set to current date\"\n                        disabled={isHydrated && lockedSections.invoiceDetails}\n                      >\n                        <Calendar className=\"h-4 w-4 mr-1\" />\n                        Today\n                      </Button>\n                    </div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Due Date</label>\n                    <Input\n                      type=\"date\"\n                      value={dueDate}\n                      onChange={(e) => setDueDate(e.target.value)}\n                      disabled={isHydrated && lockedSections.invoiceDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Currency</label>\n                    <Input\n                      value={currency}\n                      onChange={(e) => setCurrency(e.target.value)}\n                      placeholder=\"SAR\"\n                      disabled={isHydrated && lockedSections.invoiceDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Discount (%)</label>\n                    <Input\n                      type=\"number\"\n                      value={discount}\n                      onChange={(e) => setDiscount(Number(e.target.value))}\n                      placeholder=\"0\"\n                      min=\"0\"\n                      max=\"100\"\n                      disabled={isHydrated && lockedSections.invoiceDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Deduction Amount ({currency})\n                    </label>\n                    <Input\n                      type=\"number\"\n                      value={deductionAmount}\n                      onChange={(e) => setDeductionAmount(Number(e.target.value))}\n                      placeholder=\"0.00\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      disabled={isHydrated && lockedSections.invoiceDetails}\n                    />\n                  </div>\n                  {deductionAmount > 0 && (\n                    <div className=\"md:col-span-2\">\n                      {useSimpleInputs ? (\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Deduction Reason <span className=\"text-red-500\">*</span>\n                          </label>\n                          <Input\n                            value={deductionReason}\n                            onChange={(e) => setDeductionReason(e.target.value)}\n                            placeholder=\"e.g., Early payment discount, Advance payment received, Material cost adjustment, etc.\"\n                            required={deductionAmount > 0}\n                            className=\"w-full\"\n                            disabled={isHydrated && lockedSections.invoiceDetails}\n                          />\n                        </div>\n                      ) : (\n                        <SuggestionInput\n                          value={deductionReason}\n                          onChange={setDeductionReason}\n                          suggestions={previousDeductionReasons}\n                          placeholder=\"e.g., Early payment discount, Advance payment received, Material cost adjustment, etc.\"\n                          label=\"Deduction Reason\"\n                          fieldKey=\"deductionReason\"\n                          required={true}\n                          disabled={isHydrated && lockedSections.invoiceDetails}\n                        />\n                      )}\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Seller Details */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span>Seller Details</span>\n                    {isHydrated && lockedSections.sellerDetails && (\n                      <Lock className=\"h-4 w-4 text-red-500\" />\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => toggleSectionLock('sellerDetails')}\n                    data-lock-button=\"true\"\n                    className={`${\n                      isHydrated && lockedSections.sellerDetails\n                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'\n                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'\n                    }`}\n                  >\n                    {!isHydrated ? (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    ) : lockedSections.sellerDetails ? (\n                      <>\n                        <Unlock className=\"h-4 w-4 mr-1\" />\n                        Unlock\n                      </>\n                    ) : (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    )}\n                  </Button>\n                </CardTitle>\n              </CardHeader>\n              <CardContent data-section=\"sellerDetails\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {useSimpleInputs ? (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Company Name</label>\n                      <Input\n                        value={sellerName}\n                        onChange={(e) => setSellerName(e.target.value)}\n                        placeholder=\"Your Company Name\"\n                        className=\"w-full\"\n                        disabled={isHydrated && lockedSections.sellerDetails}\n                      />\n                    </div>\n                  ) : (\n                    <SuggestionInput\n                      value={sellerName}\n                      onChange={setSellerName}\n                      suggestions={previousSellerNames}\n                      placeholder=\"Your Company Name\"\n                      label=\"Company Name\"\n                      fieldKey=\"sellerName\"\n                      disabled={isHydrated && lockedSections.sellerDetails}\n                    />\n                  )}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">VAT Number</label>\n                    <Input\n                      value={sellerVAT}\n                      onChange={(e) => setSellerVAT(e.target.value)}\n                      placeholder=\"30**********003\"\n                      disabled={isHydrated && lockedSections.sellerDetails}\n                    />\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Address</label>\n                    <Textarea\n                      value={sellerAddress}\n                      onChange={(e) => setSellerAddress(e.target.value)}\n                      placeholder=\"Company Address\"\n                      rows={3}\n                      disabled={isHydrated && lockedSections.sellerDetails}\n                    />\n                  </div>\n\n                  {/* Seller Logo Upload */}\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Company Logo</label>\n                    <div className=\"flex items-center space-x-4\">\n                      {sellerLogo ? (\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm\">\n                            <img\n                              src={sellerLogo}\n                              alt=\"Seller Logo\"\n                              className=\"w-full h-full object-contain\"\n                            />\n                          </div>\n                          <div className=\"flex flex-col space-y-2\">\n                            <span className=\"text-sm text-green-600 font-medium\">✓ Logo uploaded & optimized</span>\n                            <span className=\"text-xs text-gray-500\">Auto-resized for best quality</span>\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={removeSellerLogo}\n                              className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                            >\n                              <Trash2 className=\"h-3 w-3 mr-1\" />\n                              Remove Logo\n                            </Button>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50\">\n                            <Upload className=\"h-6 w-6 text-gray-400\" />\n                          </div>\n                          <div className=\"flex flex-col\">\n                            <input\n                              id=\"seller-logo-input\"\n                              type=\"file\"\n                              accept=\"image/*\"\n                              onChange={handleSellerLogoUpload}\n                              className=\"hidden\"\n                            />\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              size=\"sm\"\n                              className=\"bg-blue-50 text-blue-600 hover:bg-blue-100\"\n                              onClick={() => document.getElementById('seller-logo-input')?.click()}\n                            >\n                              <Upload className=\"h-4 w-4 mr-2\" />\n                              Upload Logo\n                            </Button>\n                            <span className=\"text-xs text-gray-500 mt-1\">PNG, JPG up to 5MB</span>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Client Details */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span>Client Details</span>\n                    {isHydrated && lockedSections.clientDetails && (\n                      <Lock className=\"h-4 w-4 text-red-500\" />\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => toggleSectionLock('clientDetails')}\n                    data-lock-button=\"true\"\n                    className={`${\n                      isHydrated && lockedSections.clientDetails\n                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'\n                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'\n                    }`}\n                  >\n                    {!isHydrated ? (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    ) : lockedSections.clientDetails ? (\n                      <>\n                        <Unlock className=\"h-4 w-4 mr-1\" />\n                        Unlock\n                      </>\n                    ) : (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    )}\n                  </Button>\n                </CardTitle>\n              </CardHeader>\n              <CardContent data-section=\"clientDetails\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {useSimpleInputs ? (\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-1\">Client Name</label>\n                      <Input\n                        value={clientName}\n                        onChange={(e) => setClientName(e.target.value)}\n                        placeholder=\"Client Company Name\"\n                        className=\"w-full\"\n                        disabled={isHydrated && lockedSections.clientDetails}\n                      />\n                    </div>\n                  ) : (\n                    <SuggestionInput\n                      value={clientName}\n                      onChange={setClientName}\n                      suggestions={previousClientNames}\n                      placeholder=\"Client Company Name\"\n                      label=\"Client Name\"\n                      fieldKey=\"clientName\"\n                      disabled={isHydrated && lockedSections.clientDetails}\n                    />\n                  )}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Client VAT</label>\n                    <Input\n                      value={clientVAT}\n                      onChange={(e) => setClientVAT(e.target.value)}\n                      placeholder=\"301987654321009\"\n                      disabled={isHydrated && lockedSections.clientDetails}\n                    />\n                  </div>\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Client Address</label>\n                    <Textarea\n                      value={clientAddress}\n                      onChange={(e) => setClientAddress(e.target.value)}\n                      placeholder=\"Client Address\"\n                      rows={3}\n                      disabled={isHydrated && lockedSections.clientDetails}\n                    />\n                  </div>\n\n                  {/* Client Logo Upload */}\n                  <div className=\"md:col-span-2\">\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">Client Logo</label>\n                    <div className=\"flex items-center space-x-4\">\n                      {clientLogo ? (\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-20 h-16 border-2 border-gray-300 rounded-lg overflow-hidden bg-white shadow-sm\">\n                            <img\n                              src={clientLogo}\n                              alt=\"Client Logo\"\n                              className=\"w-full h-full object-contain\"\n                            />\n                          </div>\n                          <div className=\"flex flex-col space-y-2\">\n                            <span className=\"text-sm text-green-600 font-medium\">✓ Logo uploaded & optimized</span>\n                            <span className=\"text-xs text-gray-500\">Auto-resized for best quality</span>\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              size=\"sm\"\n                              onClick={removeClientLogo}\n                              className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                            >\n                              <Trash2 className=\"h-3 w-3 mr-1\" />\n                              Remove Logo\n                            </Button>\n                          </div>\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-16 h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50\">\n                            <Upload className=\"h-6 w-6 text-gray-400\" />\n                          </div>\n                          <div className=\"flex flex-col\">\n                            <input\n                              id=\"client-logo-input\"\n                              type=\"file\"\n                              accept=\"image/*\"\n                              onChange={handleClientLogoUpload}\n                              className=\"hidden\"\n                            />\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              size=\"sm\"\n                              className=\"bg-green-50 text-green-600 hover:bg-green-100\"\n                              onClick={() => document.getElementById('client-logo-input')?.click()}\n                            >\n                              <Upload className=\"h-4 w-4 mr-2\" />\n                              Upload Logo\n                            </Button>\n                            <span className=\"text-xs text-gray-500 mt-1\">PNG, JPG up to 5MB</span>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Bank Details */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span>Bank Details</span>\n                    {isHydrated && lockedSections.bankDetails && (\n                      <Lock className=\"h-4 w-4 text-red-500\" />\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => toggleSectionLock('bankDetails')}\n                    data-lock-button=\"true\"\n                    className={`${\n                      isHydrated && lockedSections.bankDetails\n                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'\n                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'\n                    }`}\n                  >\n                    {!isHydrated ? (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    ) : lockedSections.bankDetails ? (\n                      <>\n                        <Unlock className=\"h-4 w-4 mr-1\" />\n                        Unlock\n                      </>\n                    ) : (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    )}\n                  </Button>\n                </CardTitle>\n              </CardHeader>\n              <CardContent data-section=\"bankDetails\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Bank Name</label>\n                    <Input\n                      value={bankName}\n                      onChange={(e) => setBankName(e.target.value)}\n                      placeholder=\"Saudi National Bank\"\n                      disabled={isHydrated && lockedSections.bankDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Account Name</label>\n                    <Input\n                      value={accountName}\n                      onChange={(e) => setAccountName(e.target.value)}\n                      placeholder=\"Company Account Name\"\n                      disabled={isHydrated && lockedSections.bankDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Account Number</label>\n                    <Input\n                      value={accountNumber}\n                      onChange={(e) => setAccountNumber(e.target.value)}\n                      placeholder=\"**********\"\n                      disabled={isHydrated && lockedSections.bankDetails}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">IBAN</label>\n                    <Input\n                      value={iban}\n                      onChange={(e) => setIban(e.target.value)}\n                      placeholder=\"************************\"\n                      disabled={isHydrated && lockedSections.bankDetails}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Invoice Items */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span>Invoice Items</span>\n                    {isHydrated && lockedSections.items && (\n                      <Lock className=\"h-4 w-4 text-red-500\" />\n                    )}\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Button\n                      type=\"button\"\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => toggleSectionLock('items')}\n                      data-lock-button=\"true\"\n                      className={`${\n                        isHydrated && lockedSections.items\n                          ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'\n                          : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'\n                      }`}\n                    >\n                      {!isHydrated ? (\n                        <>\n                          <Lock className=\"h-4 w-4 mr-1\" />\n                          Lock\n                        </>\n                      ) : lockedSections.items ? (\n                        <>\n                          <Unlock className=\"h-4 w-4 mr-1\" />\n                          Unlock\n                        </>\n                      ) : (\n                        <>\n                          <Lock className=\"h-4 w-4 mr-1\" />\n                          Lock\n                        </>\n                      )}\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      onClick={addItem}\n                      className=\"bg-blue-600 hover:bg-blue-700\"\n                      disabled={isHydrated && lockedSections.items}\n                    >\n                      <Plus className=\"h-4 w-4 mr-2\" />\n                      Add Item\n                    </Button>\n                  </div>\n                </CardTitle>\n              </CardHeader>\n              <CardContent data-section=\"items\">\n                <div className=\"space-y-4\">\n                  {items.map((item, index) => (\n                    <div key={item.id} className=\"grid grid-cols-1 md:grid-cols-6 gap-4 p-4 border rounded-lg relative\">\n                      <div className=\"md:col-span-2\">\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Description</label>\n                        <Input\n                          value={item.description}\n                          onChange={(e) => updateItem(item.id, 'description', e.target.value)}\n                          placeholder=\"Item description\"\n                          disabled={isHydrated && lockedSections.items}\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Quantity</label>\n                        <Input\n                          type=\"number\"\n                          value={item.quantity}\n                          onChange={(e) => updateItem(item.id, 'quantity', Number(e.target.value))}\n                          min=\"1\"\n                          disabled={isHydrated && lockedSections.items}\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Rate ({currency})</label>\n                        <Input\n                          type=\"number\"\n                          value={item.rate}\n                          onChange={(e) => updateItem(item.id, 'rate', Number(e.target.value))}\n                          min=\"0\"\n                          step=\"0.01\"\n                          disabled={isHydrated && lockedSections.items}\n                        />\n                      </div>\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">Amount ({currency})</label>\n                        <Input\n                          value={item.amount.toFixed(2)}\n                          readOnly\n                          className=\"bg-gray-50\"\n                        />\n                      </div>\n                      <div className=\"flex items-end\">\n                        <Button\n                          type=\"button\"\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={() => removeItem(item.id)}\n                          disabled={items.length <= 1 || lockedSections.items}\n                          className={`w-full ${\n                            items.length <= 1 || lockedSections.items\n                              ? 'opacity-50 cursor-not-allowed'\n                              : 'text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200'\n                          }`}\n                          title={\n                            lockedSections.items\n                              ? 'Items section is locked'\n                              : items.length <= 1\n                                ? 'At least one item is required'\n                                : 'Remove this item'\n                          }\n                        >\n                          <Trash2 className=\"h-4 w-4 mr-1\" />\n                          Remove\n                        </Button>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Notes and Terms */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span>Additional Information</span>\n                    {isHydrated && lockedSections.notes && (\n                      <Lock className=\"h-4 w-4 text-red-500\" />\n                    )}\n                  </div>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => toggleSectionLock('notes')}\n                    data-lock-button=\"true\"\n                    className={`${\n                      isHydrated && lockedSections.notes\n                        ? 'bg-red-50 text-red-600 hover:bg-red-100 border-red-200'\n                        : 'bg-green-50 text-green-600 hover:bg-green-100 border-green-200'\n                    }`}\n                  >\n                    {!isHydrated ? (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    ) : lockedSections.notes ? (\n                      <>\n                        <Unlock className=\"h-4 w-4 mr-1\" />\n                        Unlock\n                      </>\n                    ) : (\n                      <>\n                        <Lock className=\"h-4 w-4 mr-1\" />\n                        Lock\n                      </>\n                    )}\n                  </Button>\n                </CardTitle>\n              </CardHeader>\n              <CardContent data-section=\"notes\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Notes</label>\n                    <Textarea\n                      value={notes}\n                      onChange={(e) => setNotes(e.target.value)}\n                      placeholder=\"Thank you for your business!\"\n                      rows={4}\n                      disabled={isHydrated && lockedSections.notes}\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">Terms & Conditions</label>\n                    <Textarea\n                      value={terms}\n                      onChange={(e) => setTerms(e.target.value)}\n                      placeholder=\"Payment due within 30 days...\"\n                      rows={4}\n                      disabled={isHydrated && lockedSections.notes}\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Right Column - Summary */}\n          <div className=\"space-y-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Invoice Summary</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between\">\n                    <span>Subtotal:</span>\n                    <span className=\"font-semibold\">{currency} {subtotal.toFixed(2)}</span>\n                  </div>\n                  {discount > 0 && (\n                    <div className=\"flex justify-between text-red-600\">\n                      <span>Discount ({discount}%):</span>\n                      <span>-{currency} {discountAmount.toFixed(2)}</span>\n                    </div>\n                  )}\n                  {deductionAmount > 0 && (\n                    <div className=\"flex justify-between text-orange-600\">\n                      <span>Deduction Amount:</span>\n                      <span>-{currency} {deductionAmount.toFixed(2)}</span>\n                    </div>\n                  )}\n                  {deductionAmount > 0 && deductionReason && (\n                    <div className=\"text-sm text-gray-600 bg-orange-50 p-2 rounded\">\n                      <strong>Deduction Reason:</strong> {deductionReason}\n                    </div>\n                  )}\n                  <div className=\"flex justify-between\">\n                    <span>Amount Before VAT:</span>\n                    <span className=\"font-semibold\">{currency} {subtotalAfterDeduction.toFixed(2)}</span>\n                  </div>\n                  <div className=\"flex justify-between\">\n                    <span>Total VAT (15%):</span>\n                    <span className=\"font-semibold\">{currency} {totalVAT.toFixed(2)}</span>\n                  </div>\n                  <div className=\"flex justify-between text-lg font-bold border-t pt-3\">\n                    <span>Total Amount:</span>\n                    <span className=\"text-purple-600\">{currency} {totalAmount.toFixed(2)}</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Action Buttons */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <QrCode className=\"h-5 w-5\" />\n                  <span>Actions</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <Button\n                    onClick={previewInvoice}\n                    className=\"w-full bg-purple-600 hover:bg-purple-700\"\n                    size=\"lg\"\n                  >\n                    <Eye className=\"h-4 w-4 mr-2\" />\n                    Preview Invoice\n                  </Button>\n                  <Button\n                    onClick={generatePDF}\n                    className=\"w-full bg-green-600 hover:bg-green-700\"\n                    size=\"lg\"\n                  >\n                    <Download className=\"h-4 w-4 mr-2\" />\n                    Generate & Download PDF\n                  </Button>\n                  <Button\n                    onClick={handlePrintInvoice}\n                    variant=\"outline\"\n                    className=\"w-full border-purple-300 text-purple-600 hover:bg-purple-50\"\n                    size=\"lg\"\n                  >\n                    <Printer className=\"h-4 w-4 mr-2\" />\n                    Print Invoice\n                  </Button>\n                  <Button\n                    onClick={saveDraft}\n                    variant=\"outline\"\n                    className=\"w-full\"\n                    size=\"lg\"\n                  >\n                    💾 Save Draft\n                  </Button>\n                  <Button\n                    onClick={saveAsCompleted}\n                    variant=\"outline\"\n                    className=\"w-full bg-green-50 text-green-600 hover:bg-green-100 border-green-200\"\n                    size=\"lg\"\n                  >\n                    ✅ Mark as Completed\n                  </Button>\n                </div>\n\n                {/* Logo Preview */}\n                {(sellerLogo || clientLogo) && (\n                  <div className=\"mt-4 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg border border-gray-200\">\n                    <p className=\"text-sm text-gray-700 mb-3 text-center font-medium\">📸 Logo Preview</p>\n                    <div className=\"flex justify-center space-x-6\">\n                      {sellerLogo && (\n                        <div className=\"text-center\">\n                          <p className=\"text-xs text-blue-600 mb-2 font-medium\">Seller Logo</p>\n                          <div className=\"w-20 h-16 bg-white border-2 border-blue-200 rounded-lg overflow-hidden shadow-sm\">\n                            <img\n                              src={sellerLogo}\n                              alt=\"Seller Logo Preview\"\n                              className=\"w-full h-full object-contain\"\n                            />\n                          </div>\n                          <p className=\"text-xs text-gray-500 mt-1\">Auto-optimized</p>\n                        </div>\n                      )}\n                      {clientLogo && (\n                        <div className=\"text-center\">\n                          <p className=\"text-xs text-green-600 mb-2 font-medium\">Client Logo</p>\n                          <div className=\"w-20 h-16 bg-white border-2 border-green-200 rounded-lg overflow-hidden shadow-sm\">\n                            <img\n                              src={clientLogo}\n                              alt=\"Client Logo Preview\"\n                              className=\"w-full h-full object-contain\"\n                            />\n                          </div>\n                          <p className=\"text-xs text-gray-500 mt-1\">Auto-optimized</p>\n                        </div>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-center text-gray-600 mt-3\">\n                      ✨ Logos are automatically resized and optimized for PDF generation\n                    </p>\n                  </div>\n                )}\n\n                {/* QR Code Preview */}\n                {sellerName && clientName && totalAmount > 0 && (\n                  <div className=\"mt-4 p-3 bg-gray-50 rounded-lg text-center\">\n                    <p className=\"text-sm text-gray-600 mb-2\">ZATCA QR Code will be generated</p>\n                    <div className=\"w-16 h-16 bg-white border-2 border-gray-300 rounded mx-auto flex items-center justify-center\">\n                      <QrCode className=\"h-8 w-8 text-gray-400\" />\n                    </div>\n                    <p className=\"text-xs text-gray-500 mt-2\">QR code for ZATCA compliance</p>\n                    <p className=\"text-xs text-blue-600 mt-1\">\n                      📅 Always uses current date: {new Date().toLocaleDateString()}\n                    </p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Recent Invoices */}\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <span>📋 Recent Invoices</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  {getRecentInvoices().length > 0 ? (\n                    getRecentInvoices().map((invoice, index) => (\n                      <div\n                        key={invoice.key}\n                        className=\"p-3 border rounded-lg hover:bg-blue-50 cursor-pointer transition-colors\"\n                        onClick={() => loadInvoiceData(invoice)}\n                      >\n                        <div className=\"flex justify-between items-start\">\n                          <div className=\"flex-1\">\n                            <p className=\"font-medium text-sm text-blue-600\">{invoice.invoiceNumber}</p>\n                            <p className=\"text-xs text-gray-600 truncate\">{invoice.clientName || 'No client'}</p>\n                            <p className=\"text-xs text-gray-500\">\n                              {new Date(invoice.timestamp).toLocaleDateString()}\n                            </p>\n                          </div>\n                          <div className=\"text-right\">\n                            <p className=\"text-sm font-semibold text-green-600\">\n                              {invoice.currency} {(invoice.totalAmount || 0).toFixed(2)}\n                            </p>\n                            {invoice.deductionAmount > 0 && (\n                              <p className=\"text-xs text-orange-600\">-{invoice.deductionAmount}</p>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <div className=\"text-center py-4 text-gray-500\">\n                      <p className=\"text-sm\">No recent invoices found</p>\n                      <p className=\"text-xs\">Create and save invoices to see them here</p>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Validation Status */}\n            <Card>\n              <CardHeader>\n                <CardTitle>Validation Status</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Seller Details:</span>\n                    <span className={`text-sm ${sellerName && sellerVAT ? 'text-green-600' : 'text-red-600'}`}>\n                      {sellerName && sellerVAT ? '✓ Complete' : '✗ Missing'}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Client Details:</span>\n                    <span className={`text-sm ${clientName && clientVAT ? 'text-green-600' : 'text-red-600'}`}>\n                      {clientName && clientVAT ? '✓ Complete' : '✗ Missing'}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Invoice Items:</span>\n                    <span className={`text-sm ${items.length > 0 && items[0].description ? 'text-green-600' : 'text-red-600'}`}>\n                      {items.length > 0 && items[0].description ? '✓ Complete' : '✗ Missing'}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm\">Deduction Reason:</span>\n                    <span className={`text-sm ${deductionAmount > 0 ? (deductionReason.trim() ? 'text-green-600' : 'text-red-600') : 'text-gray-400'}`}>\n                      {deductionAmount > 0 ? (deductionReason.trim() ? '✓ Complete' : '✗ Required') : 'N/A'}\n                    </span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAuXA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAjYA;;;AAWA,kDAAkD;AAClD,uCAA4E;;AA4W5E;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,mBAAmB;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACrF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,iBAAiB;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAElE,iBAAiB;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAElE,QAAQ;IACR,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChD;YACE,IAAI;YACJ,aAAa;YACb,UAAU;YACV,MAAM;YACN,YAAY;YACZ,QAAQ;QACV;KACD;IAED,qBAAqB;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,mDAAmD;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC3E,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,CAAC;IAClF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,2CAA2C;;IAExG,uBAAuB;IACvB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAYlF,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,aAAa;QACb,OAAO;QACP,OAAO;IACT;IAIA,yCAAyC;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QAEZ,MAAM,kBAAkB;YACtB,uCAA2E;;YAmC3E,OAAO;gBACL,cAAc,MAAM,uDAAuD;;gBAC3E,OAAO;YACT;QACF;QAEA,sDAAsD;QACtD,WAAW,iBAAiB;IAC9B,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,oBAAoB,CAAC;QACzB,uCAA2E;;QAuB3E,OAAO;YACL,QAAQ,KAAK,CAAC;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,eAAe;YACrB,IAAI,gBAAgB;YAEpB,uCAA2E;;YAgB3E,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,4CAA4C;IAC5C,MAAM,qBAAqB;QACzB,IAAI;YACF,wCAAmC,QAAO,8BAA8B;;YACxE,+CAA+C;YAC/C,MAAM;YACD,IAAI;YAcT,wBAAwB;YACxB,MAAM;YACN,MAAM;YACN,MAAM;QAKR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,qBAAqB;QACzB,IAAI;YACF,wCAAmC;;YACnC,MAAM;YACD,IAAI;YAsBT,mCAAmC;YACnC,MAAM;QAKR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,gDAAgD;IAChD,MAAM,uBAAuB,CAAC,MAAY,WAAmB,GAAG,EAAE,YAAoB,GAAG;QACvF,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,MAAM,MAAM,IAAI;YAEhB,IAAI,MAAM,GAAG;gBACX,0DAA0D;gBAC1D,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;gBAExB,IAAI,QAAQ,YAAY,SAAS,WAAW;oBAC1C,MAAM,cAAc,QAAQ;oBAE5B,IAAI,QAAQ,QAAQ;wBAClB,QAAQ;wBACR,SAAS,QAAQ;oBACnB,OAAO;wBACL,SAAS;wBACT,QAAQ,SAAS;oBACnB;gBACF;gBAEA,wBAAwB;gBACxB,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG;gBAEhB,wBAAwB;gBACxB,KAAK,UAAU,KAAK,GAAG,GAAG,OAAO;gBAEjC,sCAAsC;gBACtC,MAAM,iBAAiB,OAAO,SAAS,CAAC,cAAc;gBACtD,QAAQ;YACV;YAEA,IAAI,OAAO,GAAG,IAAM,OAAO,IAAI,MAAM;YACrC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;QAChC;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;oBAAE,IAAI;gBAAc;gBAC/D,MAAM,cAAc,MAAM,qBAAqB,MAAM,KAAK;gBAC1D,cAAc;gBACd,kBAAkB;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,4CAA4C;oBAAE,IAAI;gBAAc;YAChF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,6CAA6C;oBAAE,IAAI;gBAAc;YAC/E;QACF;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,6BAA6B;oBAAE,IAAI;gBAAc;gBAC/D,MAAM,cAAc,MAAM,qBAAqB,MAAM,KAAK;gBAC1D,cAAc;gBACd,kBAAkB;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,4CAA4C;oBAAE,IAAI;gBAAc;YAChF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,6CAA6C;oBAAE,IAAI;gBAAc;YAC/E;QACF;IACF;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,kBAAkB;QAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB;QACvB,cAAc;QACd,kBAAkB;QAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,uCAAuC;IACvC,MAAM,cAAc,CAAC;QACnB,IAAI;YACF,sCAAsC;YACtC,iBAAiB,QAAQ,aAAa,IAAI;YAC1C,eAAe,QAAQ,WAAW,IAAI,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5E,WAAW,QAAQ,OAAO,IAAI;YAC9B,YAAY,QAAQ,QAAQ,IAAI;YAChC,YAAY,QAAQ,QAAQ,IAAI;YAChC,mBAAmB,QAAQ,eAAe,IAAI;YAC9C,mBAAmB,QAAQ,eAAe,IAAI;YAE9C,iBAAiB;YACjB,cAAc,QAAQ,UAAU,IAAI;YACpC,iBAAiB,QAAQ,aAAa,IAAI;YAC1C,aAAa,QAAQ,SAAS,IAAI;YAClC,cAAc,QAAQ,UAAU,IAAI;YAEpC,iBAAiB;YACjB,cAAc,QAAQ,UAAU,IAAI;YACpC,iBAAiB,QAAQ,aAAa,IAAI;YAC1C,aAAa,QAAQ,SAAS,IAAI;YAClC,cAAc,QAAQ,UAAU,IAAI;YAEpC,qBAAqB;YACrB,SAAS,QAAQ,KAAK,IAAI;YAC1B,SAAS,QAAQ,KAAK,IAAI;YAC1B,YAAY,QAAQ,QAAQ,IAAI;YAChC,eAAe,QAAQ,WAAW,IAAI;YACtC,iBAAiB,QAAQ,aAAa,IAAI;YAC1C,QAAQ,QAAQ,IAAI,IAAI;YAExB,QAAQ;YACR,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;gBAC7C,SAAS,QAAQ,KAAK;YACxB;YAEA,yBAAyB,QAAQ,GAAG;YACpC,qBAAqB;YACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ,aAAa,CAAC,GAAG,CAAC;QAC9D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI;YACF,oDAAoD;YACpD,MAAM,mBAAmB,CAAC,IAAI,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI;YAE1F,YAAY;YACZ,iBAAiB;YACjB,yBAAyB;YACzB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,uBAAuB,EAAE,iBAAiB,GAAG,CAAC;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,CAAC,YAAoB;QACzC,IAAI;YACF,aAAa,UAAU,CAAC;YACxB;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,cAAc,0BAA0B,CAAC;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oDAAoD;QACpD,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAE1D,MAAM,cAA2B;YAC/B,eAAe,QAAQ,aAAa;YACpC,aAAa;YACb,SAAS,QAAQ,OAAO;YACxB,UAAU,QAAQ,QAAQ;YAC1B,UAAU,QAAQ,QAAQ;YAC1B,iBAAiB,QAAQ,eAAe;YACxC,iBAAiB,QAAQ,eAAe;YACxC,YAAY,QAAQ,UAAU;YAC9B,eAAe,QAAQ,aAAa;YACpC,WAAW,QAAQ,SAAS;YAC5B,YAAY,QAAQ,UAAU;YAC9B,YAAY,QAAQ,UAAU;YAC9B,eAAe,QAAQ,aAAa;YACpC,WAAW,QAAQ,SAAS;YAC5B,YAAY,QAAQ,UAAU;YAC9B,OAAO,QAAQ,KAAK,IAAI,EAAE;YAC1B,OAAO,QAAQ,KAAK;YACpB,OAAO,QAAQ,KAAK;YACpB,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,eAAe,QAAQ,aAAa;YACpC,MAAM,QAAQ,IAAI;YAClB,UAAU,QAAQ,QAAQ;YAC1B,wBAAwB,QAAQ,sBAAsB;YACtD,UAAU,QAAQ,QAAQ;YAC1B,aAAa,QAAQ,WAAW;YAChC,QAAQ;YACR,aAAa;QACf;QAEA,IAAI;YACF,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;YACnB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iBAAiB,EAAE,QAAQ,aAAa,CAAC,sBAAsB,CAAC;QACjF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,sBAAsB;IACtB,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,MAAM,EAAE;IAChE,MAAM,iBAAiB,AAAC,WAAW,WAAY;IAC/C,MAAM,wBAAwB,WAAW;IACzC,MAAM,yBAAyB,KAAK,GAAG,CAAC,GAAG,wBAAwB;IACnE,MAAM,WAAW,yBAAyB;IAC1C,MAAM,cAAc,yBAAyB;IAE7C,mBAAmB;IACnB,MAAM,UAAU;QACd,MAAM,UAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,aAAa;YACb,UAAU;YACV,MAAM;YACN,YAAY;YACZ,QAAQ;QACV;QACA,SAAS;eAAI;YAAO;SAAQ;IAC9B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,MAAM,MAAM,IAAI,GAAG;YACrB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAC1C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,aAAa,CAAC,IAAY,OAA0B;QACxD,SAAS,MAAM,GAAG,CAAC,CAAA;YACjB,IAAI,KAAK,EAAE,KAAK,IAAI;gBAClB,MAAM,cAAc;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAM;gBAC9C,IAAI,UAAU,cAAc,UAAU,QAAQ;oBAC5C,YAAY,MAAM,GAAG,YAAY,QAAQ,GAAG,YAAY,IAAI;gBAC9D;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,yDAAyD;IACzD,MAAM,iBAAiB;QACrB,gDAAgD;QAChD,MAAM,cAAc,IAAI;QAExB,MAAM,SAAsB;YAC1B,YAAY,cAAc;YAC1B,WAAW,aAAa;YACxB,WAAW,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;YAClC,cAAc,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;YACrC,UAAU,CAAA,GAAA,uHAAA,CAAA,yBAAsB,AAAD,EAAE;QACnC;QAEA,IAAI;YACF,OAAO,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc,CAAC,cAAc,MAAM,MAAM,KAAK,GAAG;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,6DAA6D;QAC7D,IAAI,kBAAkB,KAAK,CAAC,gBAAgB,IAAI,IAAI;YAClD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,6CAA6C;QAC7C,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAE1D,MAAM,cAA2B;YAC/B;YACA,aAAa;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,aAAa;QACf;QAEA,IAAI;YACF,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;YACnB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,iCAAiC,EAAE,IAAI,OAAO,kBAAkB,GAAG,GAAG,CAAC;QACxF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc,CAAC,cAAc,MAAM,MAAM,KAAK,GAAG;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,6DAA6D;QAC7D,IAAI,kBAAkB,KAAK,CAAC,gBAAgB,IAAI,IAAI;YAClD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,4CAA4C;QAC5C,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAE1D,MAAM,cAA2B;YAC/B;YACA,aAAa;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,aAAa;QACf;QAEA,IAAI;YACF,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;YACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW;gBACf;gBAAe;gBAAa;gBAAS;gBAAU;gBAAU;gBAAiB;gBAC1E;gBAAY;gBAAe;gBAAW;gBAAY;gBAAY;gBAAe;gBAAW;gBACxF;gBAAO;gBAAO;gBAAU;gBAAa;gBAAe;gBAAM;gBAC1D;gBAAU;gBAAwB;gBAAU;gBAC5C,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,cAAc,IAAI,OAAO,WAAW;YACtC;YAEA,wBAAwB;YACxB,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;YAErD,mCAAmC;YACnC,IAAI,cAAc,cAAc,eAAe;gBAC7C,IAAI;gBACJ,IAAI,uBAAuB;oBACzB,0BAA0B;oBAC1B,aAAa;oBACb,SAAS,YAAY,GAAG,IAAI,OAAO,WAAW;gBAChD,OAAO;oBACL,qBAAqB;oBACrB,aAAa,CAAC,QAAQ,EAAE,iBAAiB,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI;gBACrE;gBAEA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;gBAEhD,qCAAqC;gBACrC;gBACA;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,wBAAwB,wBAAwB;QAChE,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,CAAC,cAAc,CAAC,cAAc,MAAM,MAAM,KAAK,GAAG;gBACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,WAAW;gBACf;gBAAe;gBAAa;gBAAS;gBAAU;gBAAU;gBAAiB;gBAC1E;gBAAY;gBAAe;gBAAW;gBAAY;gBAAY;gBAAe;gBAAW;gBACxF;gBAAO;gBAAO;gBAAU;gBAAa;gBAAe;gBAAM;gBAC1D;gBAAU;gBAAwB;gBAAU;gBAC5C,QAAQ;gBACR,WAAW,IAAI,OAAO,WAAW;gBACjC,cAAc,IAAI,OAAO,WAAW;gBACpC,QAAQ;YACV;YAEA,IAAI;YACJ,IAAI,uBAAuB;gBACzB,aAAa;YACf,OAAO;gBACL,aAAa,CAAC,QAAQ,EAAE,iBAAiB,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI;YACrE;YAEA,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAChD;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,cAAc,uBAAuB,CAAC;QACjE,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB;QACvB,wBAAwB;QACxB,iBAAiB,CAAC,IAAI,EAAE,IAAI,OAAO,WAAW,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,IAAI;QAClF,eAAe,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACrD,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,mBAAmB;QACnB,mBAAmB;QAEnB,kDAAkD;QAClD,oBAAoB;QACpB,uBAAuB;QACvB,mBAAmB;QACnB,cAAc;QAEd,uBAAuB;QACvB,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,cAAc;QAEd,2BAA2B;QAC3B,SAAS;QACT,SAAS;QACT,YAAY;QACZ,eAAe;QACf,iBAAiB;QACjB,QAAQ;QAER,cAAc;QACd,SAAS;YAAC;gBACR,IAAI;gBACJ,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,YAAY;gBACZ,QAAQ;YACV;SAAE;QAEF,yBAAyB;QACzB,qBAAqB;QACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,eAAe;QACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAIA,oBAAoB;IACpB,MAAM,oBAAoB;QACxB,uCAA2E;;QAQ3E;IACF;IAEA,kBAAkB;IAClB,MAAM,kBAAkB;QACtB,uCAA2E;;QAQ3E;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,uCAA2E;;YAY3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,oBAAoB;IACpB,MAAM,YAAY;QAChB,IAAI;YACF,uCAA2E;;YAiB3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,uCAA2E;;YAU3E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,iDAAiD;IACjD,MAAM,iBAAiB;QACrB,IAAI,CAAC,cAAc,CAAC,cAAc,MAAM,MAAM,KAAK,GAAG;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,kBAAkB,KAAK,CAAC,gBAAgB,IAAI,IAAI;YAClD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;QAEX,sCAAsC;QACtC,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC1D,MAAM,uBAAuB,IAAI,OAAO,kBAAkB;QAE1D,MAAM,cAA2B;YAC/B;YACA,aAAa;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,QAAQ;YACR,aAAa;QACf;QAEA,IAAI;YACF,wBAAwB;YACxB,MAAM,gBAAgB,OAAO,IAAI,CAAC,IAAI,UAAU;YAChD,IAAI,eAAe;gBACjB,MAAM,OAAO,CAAC;;;;mDAI6B,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iDAsDzB,EAAE,qBAAqB;;;;iDAIvB,EAAE,qBAAqB;;;;iDAIvB,EAAE,qBAAqB;;;;;;;;mCAQrC,EAAE,eAAe,cAAc,GAAG,WAAW,WAAW;kBACzE,EAAE,eAAe,cAAc,GAAG,cAAc,cAAc;;;;;mCAK7C,EAAE,eAAe,aAAa,GAAG,WAAW,WAAW;kBACxE,EAAE,eAAe,aAAa,GAAG,cAAc,cAAc;;;;;mCAK5C,EAAE,eAAe,aAAa,GAAG,WAAW,WAAW;kBACxE,EAAE,eAAe,aAAa,GAAG,cAAc,cAAc;;;;;mCAK5C,EAAE,eAAe,WAAW,GAAG,WAAW,WAAW;kBACtE,EAAE,eAAe,WAAW,GAAG,cAAc,cAAc;;;;;mCAK1C,EAAE,eAAe,KAAK,GAAG,WAAW,WAAW;kBAChE,EAAE,eAAe,KAAK,GAAG,cAAc,cAAc;;;;;mCAKpC,EAAE,eAAe,KAAK,GAAG,WAAW,WAAW;kBAChE,EAAE,eAAe,KAAK,GAAG,cAAc,cAAc;;;;;;;;;;;;sCAYjC,EAAE,cAAc;;;;mDAIH,EAAE,qBAAqB;;;;sCAIpC,EAAE,WAAW,gBAAgB;;;;sCAI7B,EAAE,SAAS;;;;;;;;sCAQX,EAAE,WAAW;;;;sCAIb,EAAE,UAAU;;;;sCAIZ,EAAE,cAAc;;;;;;;;sCAQhB,EAAE,WAAW;;;;sCAIb,EAAE,aAAa,eAAe;;;;sCAI9B,EAAE,cAAc;;;;;;;;sCAQhB,EAAE,SAAS,CAAC,EAAE,SAAS,OAAO,CAAC,GAAG;;gBAExD,EAAE,kBAAkB,IAAI,CAAC;;;yCAGA,EAAE,SAAS,CAAC,EAAE,gBAAgB,OAAO,CAAC,GAAG;;gBAElE,CAAC,GAAG,GAAG;;;sCAGe,EAAE,SAAS,CAAC,EAAE,SAAS,OAAO,CAAC,GAAG;;;;mDAIrB,EAAE,SAAS,CAAC,EAAE,YAAY,OAAO,CAAC,GAAG;;;;;;QAMhF,CAAC;gBAED,cAAc,QAAQ,CAAC,KAAK,CAAC;gBAC7B,cAAc,QAAQ,CAAC,KAAK;gBAE5B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,kCAAkC,EAAE,qBAAqB,IAAI,CAAC;YAC/E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,MAAM,OAAO,KAAK,KAAK,CAAC;gBAExB,0BAA0B;gBAC1B,IAAI,KAAK,aAAa,EAAE,iBAAiB,KAAK,aAAa;gBAC3D,IAAI,KAAK,WAAW,EAAE,eAAe,KAAK,WAAW;gBACrD,IAAI,KAAK,OAAO,EAAE,WAAW,KAAK,OAAO;gBACzC,IAAI,KAAK,QAAQ,EAAE,YAAY,KAAK,QAAQ;gBAC5C,IAAI,KAAK,QAAQ,KAAK,WAAW,YAAY,KAAK,QAAQ;gBAC1D,IAAI,KAAK,eAAe,KAAK,WAAW,mBAAmB,KAAK,eAAe;gBAC/E,IAAI,KAAK,eAAe,EAAE,mBAAmB,KAAK,eAAe;gBAEjE,iBAAiB;gBACjB,IAAI,KAAK,UAAU,EAAE,cAAc,KAAK,UAAU;gBAClD,IAAI,KAAK,aAAa,EAAE,iBAAiB,KAAK,aAAa;gBAC3D,IAAI,KAAK,SAAS,EAAE,aAAa,KAAK,SAAS;gBAC/C,IAAI,KAAK,UAAU,EAAE,cAAc,KAAK,UAAU;gBAElD,iBAAiB;gBACjB,IAAI,KAAK,UAAU,EAAE,cAAc,KAAK,UAAU;gBAClD,IAAI,KAAK,aAAa,EAAE,iBAAiB,KAAK,aAAa;gBAC3D,IAAI,KAAK,SAAS,EAAE,aAAa,KAAK,SAAS;gBAC/C,IAAI,KAAK,UAAU,EAAE,cAAc,KAAK,UAAU;gBAElD,qBAAqB;gBACrB,IAAI,KAAK,KAAK,EAAE,SAAS,KAAK,KAAK;gBACnC,IAAI,KAAK,KAAK,EAAE,SAAS,KAAK,KAAK;gBACnC,IAAI,KAAK,QAAQ,EAAE,YAAY,KAAK,QAAQ;gBAC5C,IAAI,KAAK,WAAW,EAAE,eAAe,KAAK,WAAW;gBACrD,IAAI,KAAK,aAAa,EAAE,iBAAiB,KAAK,aAAa;gBAC3D,IAAI,KAAK,IAAI,EAAE,QAAQ,KAAK,IAAI;gBAEhC,QAAQ;gBACR,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,SAAS,KAAK,KAAK;gBACrB;gBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wCAAwC;IACxC,MAAM,oBAAoB;QACxB,IAAI;YACF,wCAAmC,OAAO,EAAE,CAAC,8BAA8B;;;YAC3E,MAAM;YACD,IAAI;QAwBX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,EAAE;QACX;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,4BAA4B;QAC5B,iBAAiB,QAAQ,aAAa,IAAI;QAC1C,eAAe,QAAQ,WAAW,IAAI;QACtC,WAAW,QAAQ,OAAO,IAAI;QAC9B,YAAY,QAAQ,QAAQ,IAAI;QAChC,YAAY,QAAQ,QAAQ,IAAI;QAChC,mBAAmB,QAAQ,eAAe,IAAI;QAC9C,mBAAmB,QAAQ,eAAe,IAAI;QAE9C,iBAAiB;QACjB,cAAc,QAAQ,UAAU,IAAI;QACpC,iBAAiB,QAAQ,aAAa,IAAI;QAC1C,aAAa,QAAQ,SAAS,IAAI;QAClC,cAAc,QAAQ,UAAU,IAAI;QAEpC,iBAAiB;QACjB,cAAc,QAAQ,UAAU,IAAI;QACpC,iBAAiB,QAAQ,aAAa,IAAI;QAC1C,aAAa,QAAQ,SAAS,IAAI;QAClC,cAAc,QAAQ,UAAU,IAAI;QAEpC,qBAAqB;QACrB,SAAS,QAAQ,KAAK,IAAI;QAC1B,SAAS,QAAQ,KAAK,IAAI;QAC1B,YAAY,QAAQ,QAAQ,IAAI;QAChC,eAAe,QAAQ,WAAW,IAAI;QACtC,iBAAiB,QAAQ,aAAa,IAAI;QAC1C,QAAQ,QAAQ,IAAI,IAAI;QAExB,QAAQ;QACR,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,CAAC,MAAM,GAAG,GAAG;YAC7C,SAAS,QAAQ,KAAK;QACxB;QAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC,WAAW,CAAC;IAC7D;IAEA,gDAAgD;IAChD,MAAM,kBAAkB,CAAC,EACvB,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,EACX,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,WAAW,KAAK,EAUjB;QACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAEjD,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA,aAC7C,WAAW,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OAAO,eAAe,OACzE,KAAK,CAAC,GAAG;QAEX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAM,WAAU;;wBACd;wBAAM;wBAAE,0BAAY,8OAAC;4BAAK,WAAU;sCAAe;;;;;;;;;;;;8BAEtD,8OAAC,iIAAA,CAAA,QAAK;oBACJ,OAAO;oBACP,UAAU,CAAC;wBACT,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC/B,SAAS;wBACT,gBAAgB,SAAS,MAAM,GAAG,KAAK,oBAAoB,MAAM,GAAG;oBACtE;oBACA,SAAS;wBACP,IAAI,MAAM,MAAM,GAAG,KAAK,oBAAoB,MAAM,GAAG,GAAG;4BACtD,gBAAgB;wBAClB;oBACF;oBACA,QAAQ;wBACN,WAAW,IAAM,gBAAgB,QAAQ;oBAC3C;oBACA,aAAa;oBACb,UAAU;oBACV,UAAU;oBACV,WAAU;;;;;;gBAEX,gBAAgB,oBAAoB,MAAM,GAAG,mBAC5C,8OAAC;oBAAI,WAAU;8BACZ,oBAAoB,GAAG,CAAC,CAAC,YAAY,sBACpC,8OAAC;4BAEC,WAAU;4BACV,aAAa,CAAC,IAAM,EAAE,cAAc;4BACpC,SAAS;gCACP,SAAS;gCACT,gBAAgB;gCAChB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4BAChB;sCAEC;2BATI,GAAG,SAAS,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;IAgBzC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;;0DAE3B,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DACX,wBAAwB,iBAAiB;;;;;;0DAE5C,8OAAC;gDAAE,WAAU;0DACV,wBACG,4CACA;;;;;;;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,qBAAqB,CAAC;wCACrC,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;4CAC1B,gBAAgB,MAAM;4CAAC;;;;;;;oCAElC,uCACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAIrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,mBAAmB,CAAC;4CACpB,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC,kBAAkB,6BAA6B;wCAC5D;wCACA,WAAW,kBAAkB,iDAAiD;kDAE7E,kBAAkB,mBAAmB;;;;;;kDAExC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;kDACV;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,4MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGrC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAM;kDACP;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAM;kDACP;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAM;kDACP;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;wCACV,OAAM;kDACP;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS7C,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAK,WAAU;;gDAAwB;gDAAE,gBAAgB,MAAM;gDAAC;;;;;;;;;;;;;8CAEnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,iIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;oDACpD,WAAU;;;;;;;;;;;;sDAGd,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,uBAAuB,EAAE,MAAM,CAAC,KAAK;4CACtD,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAM;;;;;;8DACpB,8OAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;;;;;;;sDAE5B,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,qBAAqB;sDACrC;;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;gCACZ,gBACE,MAAM,CAAC,CAAA;oCACN,MAAM,gBAAgB,CAAC,qBACrB,QAAQ,aAAa,EAAE,cAAc,SAAS,kBAAkB,WAAW,OAC3E,QAAQ,UAAU,EAAE,cAAc,SAAS,kBAAkB,WAAW,OACxE,QAAQ,UAAU,EAAE,cAAc,SAAS,kBAAkB,WAAW;oCAE1E,MAAM,gBAAgB,wBAAwB,SAAS,QAAQ,MAAM,KAAK;oCAE1E,OAAO,iBAAiB;gCAC1B,GACC,GAAG,CAAC,CAAC,wBACJ,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAsC,QAAQ,aAAa;;;;;;0EACzE,8OAAC;gEAAE,WAAU;0EAAkC,QAAQ,UAAU,IAAI;;;;;;0EACrE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,MAAM,KAAK,cACf,gCACA,iCACJ;kFACC,QAAQ,MAAM,IAAI;;;;;;kFAErB,8OAAC;wEAAK,WAAU;kFACb,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;kEAIrD,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAK9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kNAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;0EACtB,8OAAC;gEAAK,WAAU;;oEACb,QAAQ,QAAQ;oEAAC;oEAAE,CAAC,QAAQ,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;kEAG3D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;gEAAK,WAAU;0EACb,QAAQ,OAAO,GAAG,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;0DAK1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,YAAY;wDAC3B,WAAU;;0EAEV,8OAAC,2MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,iBAAiB;wDAChC,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGnC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,mBAAmB;wDAClC,WAAU;;0EAEV,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;uCAvE/B,QAAQ,GAAG;;;;;gCA8ErB,gBAAgB,MAAM,KAAK,mBAC1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAK;;;;;;4DACL,cAAc,eAAe,cAAc,kBAC1C,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;kEAGpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,oBAAiB;wDACjB,WAAW,GACT,cAAc,eAAe,cAAc,GACvC,2DACA,kEACJ;kEAED,CAAC,2BACA;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;2EAGjC,kBAAkB,eAAe,GAAG,cAAc,iBACpD;;8EACE,8OAAC,4MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;yFAIrC;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAO3C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,gBAAa;sDACxB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,aAAY;gEACZ,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO;wEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wEAC9C,WAAU;wEACV,UAAU,cAAc,eAAe,cAAc;;;;;;kFAEvD,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS;wEACT,WAAU;wEACV,OAAM;wEACN,UAAU,cAAc,eAAe,cAAc;;0FAErD,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;;kEAK3C,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gEAC1C,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC3C,aAAY;gEACZ,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;gEAClD,aAAY;gEACZ,KAAI;gEACJ,KAAI;gEACJ,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;;kEAGzD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;;oEAA+C;oEAC3C;oEAAS;;;;;;;0EAE9B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,MAAK;gEACL,OAAO;gEACP,UAAU,CAAC,IAAM,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;gEACzD,aAAY;gEACZ,KAAI;gEACJ,MAAK;gEACL,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;;oDAGxD,kBAAkB,mBACjB,8OAAC;wDAAI,WAAU;kEACZ,gCACC,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;;wEAA+C;sFAC7C,8OAAC;4EAAK,WAAU;sFAAe;;;;;;;;;;;;8EAElD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,OAAO;oEACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oEAClD,aAAY;oEACZ,UAAU,kBAAkB;oEAC5B,WAAU;oEACV,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;iFAIzD,8OAAC;4DACC,OAAO;4DACP,UAAU;4DACV,aAAa;4DACb,aAAY;4DACZ,OAAM;4DACN,UAAS;4DACT,UAAU;4DACV,UAAU,cAAc,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAUnE,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;4DACL,cAAc,eAAe,aAAa,kBACzC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;kEAGpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,oBAAiB;wDACjB,WAAW,GACT,cAAc,eAAe,aAAa,GACtC,2DACA,kEACJ;kEAED,CAAC,2BACA;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;2EAGjC,eAAe,aAAa,iBAC9B;;8EACE,8OAAC,4MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;yFAIrC;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAO3C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,gBAAa;sDACxB,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,gCACC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,aAAY;gEACZ,WAAU;gEACV,UAAU,cAAc,eAAe,aAAa;;;;;;;;;;;6EAIxD,8OAAC;wDACC,OAAO;wDACP,UAAU;wDACV,aAAa;wDACb,aAAY;wDACZ,OAAM;wDACN,UAAS;wDACT,UAAU,cAAc,eAAe,aAAa;;;;;;kEAGxD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,aAAY;gEACZ,UAAU,cAAc,eAAe,aAAa;;;;;;;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,oIAAA,CAAA,WAAQ;gEACP,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,aAAY;gEACZ,MAAM;gEACN,UAAU,cAAc,eAAe,aAAa;;;;;;;;;;;;kEAKxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEAAI,WAAU;0EACZ,2BACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,KAAK;gFACL,KAAI;gFACJ,WAAU;;;;;;;;;;;sFAGd,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAqC;;;;;;8FACrD,8OAAC;oFAAK,WAAU;8FAAwB;;;;;;8FACxC,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS;oFACT,WAAU;;sGAEV,8OAAC,0MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;yFAMzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;sFAEpB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,IAAG;oFACH,MAAK;oFACL,QAAO;oFACP,UAAU;oFACV,WAAU;;;;;;8FAEZ,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAU;oFACV,SAAS,IAAM,SAAS,cAAc,CAAC,sBAAsB;;sGAE7D,8OAAC,sMAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGrC,8OAAC;oFAAK,WAAU;8FAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW7D,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;4DACL,cAAc,eAAe,aAAa,kBACzC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;kEAGpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,oBAAiB;wDACjB,WAAW,GACT,cAAc,eAAe,aAAa,GACtC,2DACA,kEACJ;kEAED,CAAC,2BACA;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;2EAGjC,eAAe,aAAa,iBAC9B;;8EACE,8OAAC,4MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;yFAIrC;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAO3C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,gBAAa;sDACxB,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,gCACC,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gEAC7C,aAAY;gEACZ,WAAU;gEACV,UAAU,cAAc,eAAe,aAAa;;;;;;;;;;;6EAIxD,8OAAC;wDACC,OAAO;wDACP,UAAU;wDACV,aAAa;wDACb,aAAY;wDACZ,OAAM;wDACN,UAAS;wDACT,UAAU,cAAc,eAAe,aAAa;;;;;;kEAGxD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,aAAY;gEACZ,UAAU,cAAc,eAAe,aAAa;;;;;;;;;;;;kEAGxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,oIAAA,CAAA,WAAQ;gEACP,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,aAAY;gEACZ,MAAM;gEACN,UAAU,cAAc,eAAe,aAAa;;;;;;;;;;;;kEAKxD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC;gEAAI,WAAU;0EACZ,2BACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,KAAK;gFACL,KAAI;gFACJ,WAAU;;;;;;;;;;;sFAGd,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;8FAAqC;;;;;;8FACrD,8OAAC;oFAAK,WAAU;8FAAwB;;;;;;8FACxC,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS;oFACT,WAAU;;sGAEV,8OAAC,0MAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;yFAMzC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;sFAEpB,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFACC,IAAG;oFACH,MAAK;oFACL,QAAO;oFACP,UAAU;oFACV,WAAU;;;;;;8FAEZ,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,WAAU;oFACV,SAAS,IAAM,SAAS,cAAc,CAAC,sBAAsB;;sGAE7D,8OAAC,sMAAA,CAAA,SAAM;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGrC,8OAAC;oFAAK,WAAU;8FAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW7D,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;4DACL,cAAc,eAAe,WAAW,kBACvC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;kEAGpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,oBAAiB;wDACjB,WAAW,GACT,cAAc,eAAe,WAAW,GACpC,2DACA,kEACJ;kEAED,CAAC,2BACA;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;2EAGjC,eAAe,WAAW,iBAC5B;;8EACE,8OAAC,4MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;yFAIrC;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAO3C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,gBAAa;sDACxB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gEAC3C,aAAY;gEACZ,UAAU,cAAc,eAAe,WAAW;;;;;;;;;;;;kEAGtD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gEAC9C,aAAY;gEACZ,UAAU,cAAc,eAAe,WAAW;;;;;;;;;;;;kEAGtD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gEAChD,aAAY;gEACZ,UAAU,cAAc,eAAe,WAAW;;;;;;;;;;;;kEAGtD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,iIAAA,CAAA,QAAK;gEACJ,OAAO;gEACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gEACvC,aAAY;gEACZ,UAAU,cAAc,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ5D,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;4DACL,cAAc,eAAe,KAAK,kBACjC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;kEAGpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,kBAAkB;gEACjC,oBAAiB;gEACjB,WAAW,GACT,cAAc,eAAe,KAAK,GAC9B,2DACA,kEACJ;0EAED,CAAC,2BACA;;sFACE,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;mFAGjC,eAAe,KAAK,iBACtB;;sFACE,8OAAC,4MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;iGAIrC;;sFACE,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;0EAKvC,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAS;gEACT,WAAU;gEACV,UAAU,cAAc,eAAe,KAAK;;kFAE5C,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;sDAMzC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,gBAAa;sDACxB,cAAA,8OAAC;gDAAI,WAAU;0DACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wDAAkB,WAAU;;0EAC3B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC,iIAAA,CAAA,QAAK;wEACJ,OAAO,KAAK,WAAW;wEACvB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wEAClE,aAAY;wEACZ,UAAU,cAAc,eAAe,KAAK;;;;;;;;;;;;0EAGhD,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAChE,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,KAAK,QAAQ;wEACpB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wEACtE,KAAI;wEACJ,UAAU,cAAc,eAAe,KAAK;;;;;;;;;;;;0EAGhD,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;;4EAA+C;4EAAO;4EAAS;;;;;;;kFAChF,8OAAC,iIAAA,CAAA,QAAK;wEACJ,MAAK;wEACL,OAAO,KAAK,IAAI;wEAChB,UAAU,CAAC,IAAM,WAAW,KAAK,EAAE,EAAE,QAAQ,OAAO,EAAE,MAAM,CAAC,KAAK;wEAClE,KAAI;wEACJ,MAAK;wEACL,UAAU,cAAc,eAAe,KAAK;;;;;;;;;;;;0EAGhD,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;;4EAA+C;4EAAS;4EAAS;;;;;;;kFAClF,8OAAC,iIAAA,CAAA,QAAK;wEACJ,OAAO,KAAK,MAAM,CAAC,OAAO,CAAC;wEAC3B,QAAQ;wEACR,WAAU;;;;;;;;;;;;0EAGd,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,WAAW,KAAK,EAAE;oEACjC,UAAU,MAAM,MAAM,IAAI,KAAK,eAAe,KAAK;oEACnD,WAAW,CAAC,OAAO,EACjB,MAAM,MAAM,IAAI,KAAK,eAAe,KAAK,GACrC,kCACA,kEACJ;oEACF,OACE,eAAe,KAAK,GAChB,4BACA,MAAM,MAAM,IAAI,IACd,kCACA;;sFAGR,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;uDA3D/B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;8CAsEzB,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;4DACL,cAAc,eAAe,KAAK,kBACjC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;kEAGpB,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,kBAAkB;wDACjC,oBAAiB;wDACjB,WAAW,GACT,cAAc,eAAe,KAAK,GAC9B,2DACA,kEACJ;kEAED,CAAC,2BACA;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;2EAGjC,eAAe,KAAK,iBACtB;;8EACE,8OAAC,4MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAiB;;yFAIrC;;8EACE,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;sDAO3C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,gBAAa;sDACxB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,oIAAA,CAAA,WAAQ;gEACP,OAAO;gEACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,aAAY;gEACZ,MAAM;gEACN,UAAU,cAAc,eAAe,KAAK;;;;;;;;;;;;kEAGhD,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAChE,8OAAC,oIAAA,CAAA,WAAQ;gEACP,OAAO;gEACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gEACxC,aAAY;gEACZ,MAAM;gEACN,UAAU,cAAc,eAAe,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASxD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB;oEAAS;oEAAE,SAAS,OAAO,CAAC;;;;;;;;;;;;;oDAE9D,WAAW,mBACV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;oEAAK;oEAAW;oEAAS;;;;;;;0EAC1B,8OAAC;;oEAAK;oEAAE;oEAAS;oEAAE,eAAe,OAAO,CAAC;;;;;;;;;;;;;oDAG7C,kBAAkB,mBACjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAK;oEAAE;oEAAS;oEAAE,gBAAgB,OAAO,CAAC;;;;;;;;;;;;;oDAG9C,kBAAkB,KAAK,iCACtB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAO;;;;;;4DAA0B;4DAAE;;;;;;;kEAGxC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB;oEAAS;oEAAE,uBAAuB,OAAO,CAAC;;;;;;;;;;;;;kEAE7E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAiB;oEAAS;oEAAE,SAAS,OAAO,CAAC;;;;;;;;;;;;;kEAE/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAmB;oEAAS;oEAAE,YAAY,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1E,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;kEAAK;;;;;;;;;;;;;;;;;sDAGV,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAU;4DACV,MAAK;;8EAEL,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGlC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAU;4DACV,MAAK;;8EAEL,8OAAC,0MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,SAAQ;4DACR,WAAU;4DACV,MAAK;;8EAEL,8OAAC,wMAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGtC,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,SAAQ;4DACR,WAAU;4DACV,MAAK;sEACN;;;;;;sEAGD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,SAAQ;4DACR,WAAU;4DACV,MAAK;sEACN;;;;;;;;;;;;gDAMF,CAAC,cAAc,UAAU,mBACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAqD;;;;;;sEAClE,8OAAC;4DAAI,WAAU;;gEACZ,4BACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAyC;;;;;;sFACtD,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,KAAK;gFACL,KAAI;gFACJ,WAAU;;;;;;;;;;;sFAGd,8OAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;gEAG7C,4BACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAA0C;;;;;;sFACvD,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,KAAK;gFACL,KAAI;gFACJ,WAAU;;;;;;;;;;;sFAGd,8OAAC;4EAAE,WAAU;sFAA6B;;;;;;;;;;;;;;;;;;sEAIhD,8OAAC;4DAAE,WAAU;sEAAyC;;;;;;;;;;;;gDAOzD,cAAc,cAAc,cAAc,mBACzC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,8OAAC;4DAAE,WAAU;;gEAA6B;gEACV,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;8CAQrE,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,8OAAC;8DAAK;;;;;;;;;;;;;;;;sDAGV,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;0DACZ,oBAAoB,MAAM,GAAG,IAC5B,oBAAoB,GAAG,CAAC,CAAC,SAAS,sBAChC,8OAAC;wDAEC,WAAU;wDACV,SAAS,IAAM,gBAAgB;kEAE/B,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAqC,QAAQ,aAAa;;;;;;sFACvE,8OAAC;4EAAE,WAAU;sFAAkC,QAAQ,UAAU,IAAI;;;;;;sFACrE,8OAAC;4EAAE,WAAU;sFACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;;;;;;;;;;;;8EAGnD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;;gFACV,QAAQ,QAAQ;gFAAC;gFAAE,CAAC,QAAQ,WAAW,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;wEAExD,QAAQ,eAAe,GAAG,mBACzB,8OAAC;4EAAE,WAAU;;gFAA0B;gFAAE,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;uDAjBjE,QAAQ,GAAG;;;;8EAwBpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAU;;;;;;sEACvB,8OAAC;4DAAE,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQjC,8OAAC,gIAAA,CAAA,OAAI;;sDACH,8OAAC,gIAAA,CAAA,aAAU;sDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,cAAc,YAAY,mBAAmB,gBAAgB;0EACtF,cAAc,YAAY,eAAe;;;;;;;;;;;;kEAG9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,cAAc,YAAY,mBAAmB,gBAAgB;0EACtF,cAAc,YAAY,eAAe;;;;;;;;;;;;kEAG9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,mBAAmB,gBAAgB;0EACvG,MAAM,MAAM,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC,WAAW,GAAG,eAAe;;;;;;;;;;;;kEAG/D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAW,CAAC,QAAQ,EAAE,kBAAkB,IAAK,gBAAgB,IAAI,KAAK,mBAAmB,iBAAkB,iBAAiB;0EAC/H,kBAAkB,IAAK,gBAAgB,IAAI,KAAK,eAAe,eAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtG", "debugId": null}}]}