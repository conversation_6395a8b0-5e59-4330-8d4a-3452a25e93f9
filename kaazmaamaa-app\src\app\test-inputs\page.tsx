'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'

export default function TestInputsPage() {
  const [sellerName, setSellerName] = useState('')
  const [clientName, setClientName] = useState('')
  const [deductionReason, setDeductionReason] = useState('')
  const [deductionAmount, setDeductionAmount] = useState('')

  const testInputs = () => {
    console.log('Seller Name:', sellerName)
    console.log('Client Name:', clientName)
    console.log('Deduction Reason:', deductionReason)
    console.log('Deduction Amount:', deductionAmount)
    
    toast.success(`Data captured: Seller: ${sellerName}, Client: ${clientName}, Reason: ${deductionReason}`)
  }

  const deductionAmountNumber = Number(deductionAmount) || 0

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-2xl text-purple-600">
              🧪 Input Fields Test
            </CardTitle>
            <p className="text-center text-gray-600">
              Test the basic input functionality
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Seller Company Name
                </label>
                <Input
                  value={sellerName}
                  onChange={(e) => setSellerName(e.target.value)}
                  placeholder="Enter seller company name"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Client Company Name
                </label>
                <Input
                  value={clientName}
                  onChange={(e) => setClientName(e.target.value)}
                  placeholder="Enter client company name"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Deduction Amount
                </label>
                <Input
                  type="number"
                  value={deductionAmount}
                  onChange={(e) => setDeductionAmount(e.target.value)}
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                  className="w-full"
                />
              </div>

              {deductionAmountNumber > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Deduction Reason <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={deductionReason}
                    onChange={(e) => setDeductionReason(e.target.value)}
                    placeholder="Enter reason for deduction"
                    required={deductionAmountNumber > 0}
                    className="w-full"
                  />
                </div>
              )}

              <Button
                onClick={testInputs}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                Test Input Values
              </Button>

              <div className="mt-4 p-4 bg-gray-100 rounded-lg">
                <h3 className="font-semibold mb-2">Current Values:</h3>
                <p><strong>Seller:</strong> "{sellerName}" (Length: {sellerName.length})</p>
                <p><strong>Client:</strong> "{clientName}" (Length: {clientName.length})</p>
                <p><strong>Deduction Amount:</strong> {deductionAmount}</p>
                {deductionAmountNumber > 0 && (
                  <p><strong>Deduction Reason:</strong> "{deductionReason}" (Length: {deductionReason.length})</p>
                )}
              </div>

              <div className="mt-4 p-4 bg-blue-100 rounded-lg">
                <h3 className="font-semibold mb-2">Debug Info:</h3>
                <p><strong>Seller onChange calls:</strong> {sellerName.split('').length}</p>
                <p><strong>Client onChange calls:</strong> {clientName.split('').length}</p>
                <p><strong>Reason onChange calls:</strong> {deductionReason.split('').length}</p>
                <p className="text-sm text-gray-600 mt-2">
                  If inputs stop after 1 character, the onChange calls should show only 1.
                </p>
              </div>

              <div className="text-center">
                <Button
                  onClick={() => window.location.href = '/invoice/create'}
                  variant="outline"
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  🔙 Back to Invoice Creator
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
