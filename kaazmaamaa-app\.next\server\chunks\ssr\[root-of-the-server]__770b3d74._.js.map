{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        onChange={props.onChange}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACL,UAAU,MAAM,QAAQ;QACvB,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/network/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Avatar } from '@/components/ui/avatar'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Users, \n  Building, \n  Wrench, \n  Search, \n  MessageCircle, \n  Phone,\n  ArrowLeft,\n  Filter,\n  MapPin,\n  Star\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface NetworkUser {\n  id: string\n  name: string\n  role: 'worker' | 'supplier' | 'company'\n  location: string\n  specialty: string\n  rating: number\n  experience: string\n  whatsapp?: string\n  isConnected: boolean\n}\n\nconst mockNetworkUsers: NetworkUser[] = [\n  {\n    id: '1',\n    name: '<PERSON>',\n    role: 'worker',\n    location: 'Riyadh',\n    specialty: 'Electrical Work',\n    rating: 4.8,\n    experience: '5 years',\n    whatsapp: '+966501234567',\n    isConnected: false\n  },\n  {\n    id: '2',\n    name: 'Saudi Construction Co.',\n    role: 'company',\n    location: 'Jeddah',\n    specialty: 'Construction Projects',\n    rating: 4.9,\n    experience: '15 years',\n    whatsapp: '+966507654321',\n    isConnected: true\n  },\n  {\n    id: '3',\n    name: 'Gulf Manpower Solutions',\n    role: 'supplier',\n    location: 'Dammam',\n    specialty: 'Skilled Workers Supply',\n    rating: 4.7,\n    experience: '8 years',\n    whatsapp: '+966509876543',\n    isConnected: false\n  },\n  {\n    id: '4',\n    name: 'Mohammed Hassan',\n    role: 'worker',\n    location: 'Riyadh',\n    specialty: 'Welding & Fabrication',\n    rating: 4.6,\n    experience: '7 years',\n    whatsapp: '+966502468135',\n    isConnected: false\n  },\n  {\n    id: '5',\n    name: 'Al-Rajhi Construction',\n    role: 'company',\n    location: 'Mecca',\n    specialty: 'Infrastructure Development',\n    rating: 4.9,\n    experience: '20 years',\n    whatsapp: '+966508642097',\n    isConnected: false\n  }\n]\n\nexport default function NetworkPage() {\n  const { user, userRole } = useAuth()\n  const router = useRouter()\n  const [searchTerm, setSearchTerm] = useState('')\n  const [selectedRole, setSelectedRole] = useState<'all' | 'worker' | 'supplier' | 'company'>('all')\n  const [networkUsers, setNetworkUsers] = useState<NetworkUser[]>(mockNetworkUsers)\n\n  useEffect(() => {\n    if (!user) {\n      router.push('/')\n    }\n  }, [user, router])\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return <Wrench className=\"h-4 w-4 text-blue-600\" />\n      case 'supplier':\n        return <Users className=\"h-4 w-4 text-green-600\" />\n      case 'company':\n        return <Building className=\"h-4 w-4 text-purple-600\" />\n      default:\n        return <Users className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return 'bg-blue-100 text-blue-800'\n      case 'supplier':\n        return 'bg-green-100 text-green-800'\n      case 'company':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const handleConnect = (userId: string) => {\n    setNetworkUsers(prev =>\n      prev.map(user =>\n        user.id === userId\n          ? { ...user, isConnected: !user.isConnected }\n          : user\n      )\n    )\n    toast.success('Connection status updated!')\n  }\n\n  const handleWhatsAppContact = (whatsapp?: string, name?: string) => {\n    if (whatsapp) {\n      const message = `Hello ${name}, I found your profile on KAAZMAAMAA and would like to connect.`\n      const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`\n      window.open(whatsappUrl, '_blank')\n    } else {\n      toast.error('WhatsApp number not available')\n    }\n  }\n\n  const filteredUsers = networkUsers.filter(user => {\n    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.specialty.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         user.location.toLowerCase().includes(searchTerm.toLowerCase())\n    const matchesRole = selectedRole === 'all' || user.role === selectedRole\n    return matchesSearch && matchesRole\n  })\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Button \n                variant=\"ghost\" \n                size=\"sm\"\n                onClick={() => router.push('/feed')}\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Feed\n              </Button>\n              <h1 className=\"text-2xl font-bold text-blue-600\">Professional Network</h1>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-6xl mx-auto px-4 py-6\">\n        {/* Search and Filters */}\n        <Card className=\"mb-6\">\n          <CardContent className=\"pt-6\">\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              <div className=\"flex-1 relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search by name, specialty, or location...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              \n              <div className=\"flex gap-2\">\n                {(['all', 'worker', 'supplier', 'company'] as const).map((role) => (\n                  <Button\n                    key={role}\n                    variant={selectedRole === role ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setSelectedRole(role)}\n                    className=\"capitalize\"\n                  >\n                    {role === 'all' ? 'All' : role}\n                  </Button>\n                ))}\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Network Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredUsers.map((networkUser) => (\n            <Card key={networkUser.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"flex items-center space-x-3\">\n                  <Avatar className=\"h-12 w-12 bg-gray-100 flex items-center justify-center\">\n                    {getRoleIcon(networkUser.role)}\n                  </Avatar>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-semibold\">{networkUser.name}</h3>\n                    <Badge className={getRoleColor(networkUser.role)}>\n                      {networkUser.role}\n                    </Badge>\n                  </div>\n                </div>\n              </CardHeader>\n              \n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center text-sm text-gray-600\">\n                    <MapPin className=\"h-4 w-4 mr-2\" />\n                    {networkUser.location}\n                  </div>\n                  \n                  <div className=\"text-sm\">\n                    <p className=\"font-medium text-gray-900\">{networkUser.specialty}</p>\n                    <p className=\"text-gray-600\">{networkUser.experience} experience</p>\n                  </div>\n                  \n                  <div className=\"flex items-center\">\n                    <Star className=\"h-4 w-4 text-yellow-400 fill-current mr-1\" />\n                    <span className=\"text-sm font-medium\">{networkUser.rating}</span>\n                  </div>\n                  \n                  <div className=\"flex gap-2 pt-2\">\n                    <Button\n                      size=\"sm\"\n                      variant={networkUser.isConnected ? 'outline' : 'default'}\n                      onClick={() => handleConnect(networkUser.id)}\n                      className=\"flex-1\"\n                    >\n                      {networkUser.isConnected ? 'Connected' : 'Connect'}\n                    </Button>\n                    \n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleWhatsAppContact(networkUser.whatsapp, networkUser.name)}\n                    >\n                      <MessageCircle className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {filteredUsers.length === 0 && (\n          <Card>\n            <CardContent className=\"text-center py-12\">\n              <Users className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 mb-4\">No professionals found matching your criteria.</p>\n              <Button onClick={() => {\n                setSearchTerm('')\n                setSelectedRole('all')\n              }}>\n                Clear Filters\n              </Button>\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAtBA;;;;;;;;;;;;AAoCA,MAAM,mBAAkC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;QACV,aAAa;IACf;CACD;AAEc,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IAC5F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,8OAAC,0MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,gBAAgB,CAAA,OACd,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,aAAa,CAAC,KAAK,WAAW;gBAAC,IAC1C;QAGR,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,wBAAwB,CAAC,UAAmB;QAChD,IAAI,UAAU;YACZ,MAAM,UAAU,CAAC,MAAM,EAAE,KAAK,+DAA+D,CAAC;YAC9F,MAAM,cAAc,CAAC,cAAc,EAAE,SAAS,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,mBAAmB,UAAU;YAC1G,OAAO,IAAI,CAAC,aAAa;QAC3B,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB,aAAa,MAAM,CAAC,CAAA;QACxC,MAAM,gBAAgB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAChF,MAAM,cAAc,iBAAiB,SAAS,KAAK,IAAI,KAAK;QAC5D,OAAO,iBAAiB;IAC1B;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;;sDAE3B,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;kDACZ,AAAC;4CAAC;4CAAO;4CAAU;4CAAY;yCAAU,CAAW,GAAG,CAAC,CAAC,qBACxD,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,iBAAiB,OAAO,YAAY;gDAC7C,MAAK;gDACL,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DAET,SAAS,QAAQ,QAAQ;+CANrB;;;;;;;;;;;;;;;;;;;;;;;;;;kCAejB,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,4BAClB,8OAAC,gIAAA,CAAA,OAAI;gCAAsB,WAAU;;kDACnC,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;8DACf,YAAY,YAAY,IAAI;;;;;;8DAE/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAiB,YAAY,IAAI;;;;;;sEAC/C,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAW,aAAa,YAAY,IAAI;sEAC5C,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;kDAMzB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,YAAY,QAAQ;;;;;;;8DAGvB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA6B,YAAY,SAAS;;;;;;sEAC/D,8OAAC;4DAAE,WAAU;;gEAAiB,YAAY,UAAU;gEAAC;;;;;;;;;;;;;8DAGvD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAuB,YAAY,MAAM;;;;;;;;;;;;8DAG3D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,YAAY,WAAW,GAAG,YAAY;4DAC/C,SAAS,IAAM,cAAc,YAAY,EAAE;4DAC3C,WAAU;sEAET,YAAY,WAAW,GAAG,cAAc;;;;;;sEAG3C,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,sBAAsB,YAAY,QAAQ,EAAE,YAAY,IAAI;sEAE3E,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA/CxB,YAAY,EAAE;;;;;;;;;;oBAwD5B,cAAc,MAAM,KAAK,mBACxB,8OAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;wCACf,cAAc;wCACd,gBAAgB;oCAClB;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjB", "debugId": null}}]}