// ZATCA QR Code Generation Utility
// Following ZATCA TLV (Tag-Length-Value) format specifications

export interface ZATCAQRData {
  sellerName: string
  vatNumber: string
  timestamp: string
  invoiceTotal: string
  vatTotal: string
  invoiceHash?: string
}

// ZATCA TLV Tags as per official specification
export const ZATCA_TAGS = {
  SELLER_NAME: 1,
  VAT_NUMBER: 2,
  TIMESTAMP: 3,
  INVOICE_TOTAL: 4,
  VAT_TOTAL: 5,
  INVOICE_HASH: 6
} as const

/**
 * Generate TLV (Tag-Length-Value) encoded data for ZATCA QR code
 * @param tag - ZATCA tag number
 * @param value - String value to encode
 * @returns Uint8Array containing TLV encoded data
 */
export function generateTLV(tag: number, value: string): Uint8Array {
  const valueBytes = new TextEncoder().encode(value)
  const length = valueBytes.length
  
  // Create TLV structure: [Tag][Length][Value]
  const tlv = new Uint8Array(2 + length)
  tlv[0] = tag
  tlv[1] = length
  tlv.set(valueBytes, 2)
  
  return tlv
}

/**
 * Generate ZATCA-compliant QR code data in Base64 format
 * @param data - Invoice data for QR code generation
 * @returns Base64 encoded QR code string
 */
export function generateZATCAQRCode(data: ZATCAQRData): string {
  try {
    // Generate TLV for each required field
    const sellerNameTLV = generateTLV(ZATCA_TAGS.SELLER_NAME, data.sellerName)
    const vatNumberTLV = generateTLV(ZATCA_TAGS.VAT_NUMBER, data.vatNumber)
    const timestampTLV = generateTLV(ZATCA_TAGS.TIMESTAMP, data.timestamp)
    const invoiceTotalTLV = generateTLV(ZATCA_TAGS.INVOICE_TOTAL, data.invoiceTotal)
    const vatTotalTLV = generateTLV(ZATCA_TAGS.VAT_TOTAL, data.vatTotal)
    
    // Calculate total length
    let totalLength = sellerNameTLV.length + vatNumberTLV.length + timestampTLV.length + 
                     invoiceTotalTLV.length + vatTotalTLV.length
    
    // Add invoice hash if provided
    let invoiceHashTLV: Uint8Array | null = null
    if (data.invoiceHash) {
      invoiceHashTLV = generateTLV(ZATCA_TAGS.INVOICE_HASH, data.invoiceHash)
      totalLength += invoiceHashTLV.length
    }
    
    // Combine all TLV data
    const qrData = new Uint8Array(totalLength)
    let offset = 0
    
    qrData.set(sellerNameTLV, offset)
    offset += sellerNameTLV.length
    
    qrData.set(vatNumberTLV, offset)
    offset += vatNumberTLV.length
    
    qrData.set(timestampTLV, offset)
    offset += timestampTLV.length
    
    qrData.set(invoiceTotalTLV, offset)
    offset += invoiceTotalTLV.length
    
    qrData.set(vatTotalTLV, offset)
    offset += vatTotalTLV.length
    
    if (invoiceHashTLV) {
      qrData.set(invoiceHashTLV, offset)
    }
    
    // Convert to Base64
    return btoa(String.fromCharCode(...qrData))
    
  } catch (error) {
    console.error('Error generating ZATCA QR code:', error)
    throw new Error('Failed to generate ZATCA QR code')
  }
}

/**
 * Generate SHA-256 hash for invoice (simplified version)
 * In production, this should use proper XML canonicalization
 * @param invoiceData - Invoice data to hash
 * @returns Base64 encoded hash
 */
export async function generateInvoiceHash(invoiceData: any): Promise<string> {
  try {
    // Convert invoice data to string (in production, use XML canonicalization)
    const dataString = JSON.stringify(invoiceData)
    
    // Generate SHA-256 hash
    const encoder = new TextEncoder()
    const data = encoder.encode(dataString)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    
    // Convert to Base64
    const hashArray = new Uint8Array(hashBuffer)
    return btoa(String.fromCharCode(...hashArray))
    
  } catch (error) {
    console.error('Error generating invoice hash:', error)
    throw new Error('Failed to generate invoice hash')
  }
}

/**
 * Validate ZATCA QR code data
 * @param data - QR code data to validate
 * @returns Validation result with errors if any
 */
export function validateZATCAQRData(data: ZATCAQRData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []
  
  // Validate seller name
  if (!data.sellerName || data.sellerName.trim().length === 0) {
    errors.push('Seller name is required')
  }
  
  // Validate VAT number (should be 15 digits)
  if (!data.vatNumber || !/^\d{15}$/.test(data.vatNumber)) {
    errors.push('VAT number must be exactly 15 digits')
  }
  
  // Validate timestamp (ISO format)
  if (!data.timestamp || isNaN(Date.parse(data.timestamp))) {
    errors.push('Valid timestamp is required')
  }
  
  // Validate invoice total (should be numeric)
  if (!data.invoiceTotal || isNaN(parseFloat(data.invoiceTotal))) {
    errors.push('Valid invoice total is required')
  }
  
  // Validate VAT total (should be numeric)
  if (!data.vatTotal || isNaN(parseFloat(data.vatTotal))) {
    errors.push('Valid VAT total is required')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Generate QR code URL for display (using a QR code service)
 * @param qrData - Base64 encoded QR data
 * @param size - QR code size (default: 200)
 * @returns QR code image URL
 */
export function generateQRCodeURL(qrData: string, size: number = 200): string {
  // Using QR Server API (in production, consider using a local QR generator)
  const encodedData = encodeURIComponent(qrData)
  return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodedData}`
}

/**
 * Format currency amount for ZATCA compliance
 * @param amount - Numeric amount
 * @param currency - Currency code (default: SAR)
 * @returns Formatted currency string
 */
export function formatCurrencyForZATCA(amount: number, currency: string = 'SAR'): string {
  return amount.toFixed(2)
}

/**
 * Generate ZATCA-compliant timestamp
 * @param date - Date object (default: current date)
 * @returns ISO timestamp string
 */
export function generateZATCATimestamp(date: Date = new Date()): string {
  return date.toISOString()
}

/**
 * Example usage and test function
 */
export function generateSampleZATCAQR(): string {
  const sampleData: ZATCAQRData = {
    sellerName: 'KAAZMAAMAA Company',
    vatNumber: '301234567890003',
    timestamp: generateZATCATimestamp(),
    invoiceTotal: '1150.00',
    vatTotal: '150.00'
  }
  
  return generateZATCAQRCode(sampleData)
}

// Export utility functions for invoice generation
export const ZATCAUtils = {
  generateTLV,
  generateZATCAQRCode,
  generateInvoiceHash,
  validateZATCAQRData,
  generateQRCodeURL,
  formatCurrencyForZATCA,
  generateZATCATimestamp,
  generateSampleZATCAQR,
  ZATCA_TAGS
}
