'use client'

import { useState } from 'react'
import { usePayment, SAUDI_PAYMENT_METHODS } from '@/contexts/PaymentContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  CreditCard,
  Shield,
  Lock,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  DollarSign
} from 'lucide-react'
import { toast } from 'sonner'

interface PaymentGatewayProps {
  profileId: string
  profileType: 'worker' | 'supplier' | 'company'
  profileName: string
  accessType: 'contact' | 'documents' | 'premium'
  onSuccess: () => void
  onCancel: () => void
}

export default function PaymentGateway({
  profileId,
  profileType,
  profileName,
  accessType,
  onSuccess,
  onCancel
}: PaymentGatewayProps) {
  const { initiatePayment, processPayment } = usePayment()
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('mada')
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentStep, setCurrentStep] = useState<'select' | 'details' | 'processing' | 'success' | 'failed'>('select')
  const [transactionId, setTransactionId] = useState<string>('')

  // Payment form data
  const [cardNumber, setCardNumber] = useState('')
  const [expiryDate, setExpiryDate] = useState('')
  const [cvv, setCvv] = useState('')
  const [cardholderName, setCardholderName] = useState('')

  // Test card details for quick fill
  const testCardDetails = {
    mada: {
      cardNumber: '4847 8385 6294 4100',
      cardholderName: 'MD BELAL HOSSAIN',
      expiryDate: '12/28',
      cvv: '123',
      bankName: 'AlRajhi Bank'
    }
  }

  const getAccessDetails = () => {
    switch (accessType) {
      case 'contact':
        return {
          title: 'Contact Information Access',
          description: 'Get access to phone number, email, and WhatsApp contact',
          amount: 25,
          purpose: 'contact_access' as const
        }
      case 'documents':
        return {
          title: 'Document Download Access',
          description: 'Download all CV, certificates, and professional documents',
          amount: 50,
          purpose: 'document_download' as const
        }
      case 'premium':
        return {
          title: 'Premium Access',
          description: 'Full access to contact information and document downloads',
          amount: 75,
          purpose: 'premium_access' as const
        }
      default:
        return {
          title: 'Access',
          description: 'Profile access',
          amount: 25,
          purpose: 'contact_access' as const
        }
    }
  }

  const accessDetails = getAccessDetails()

  const fillTestCardDetails = () => {
    if (selectedPaymentMethod === 'mada') {
      const details = testCardDetails.mada
      setCardNumber(details.cardNumber)
      setCardholderName(details.cardholderName)
      setExpiryDate(details.expiryDate)
      setCvv(details.cvv)
      toast.success(`Auto-filled ${details.bankName} MADA card details`)
    }
  }

  const handleInitiatePayment = async () => {
    try {
      setIsProcessing(true)

      // Validate inputs
      if (!profileId || !profileType || !accessDetails.purpose) {
        throw new Error('Missing payment information')
      }

      const txnId = await initiatePayment(profileId, profileType, accessDetails.purpose)
      setTransactionId(txnId)
      setCurrentStep('details')

      // Auto-fill card details if MADA is selected
      if (selectedPaymentMethod === 'mada') {
        setTimeout(() => {
          fillTestCardDetails()
        }, 500)
      }

      toast.success('Payment initiated successfully')
    } catch (error) {
      console.error('Payment initiation error:', error)
      toast.error(`Failed to initiate payment: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleProcessPayment = async () => {
    if (!cardNumber || !expiryDate || !cvv || !cardholderName) {
      toast.error('Please fill in all payment details')
      return
    }

    setIsProcessing(true)
    setCurrentStep('processing')

    try {
      const paymentDetails = {
        cardNumber: cardNumber.replace(/\s/g, ''),
        expiryDate,
        cvv,
        cardholderName,
        paymentMethod: selectedPaymentMethod
      }

      const success = await processPayment(transactionId, selectedPaymentMethod, paymentDetails)

      if (success) {
        setCurrentStep('success')
        if (selectedPaymentMethod === 'mada' && cardNumber.includes('4847')) {
          toast.success('Payment completed with AlRajhi Bank MADA! 🎉💳')
        } else {
          toast.success('Payment completed successfully! 🎉')
        }
        setTimeout(() => {
          onSuccess()
        }, 2000)
      } else {
        setCurrentStep('failed')
        toast.error('Payment failed. Please try again.')
      }
    } catch (error) {
      setCurrentStep('failed')
      toast.error('Payment processing error')
    } finally {
      setIsProcessing(false)
    }
  }

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    const matches = v.match(/\d{4,16}/g)
    const match = matches && matches[0] || ''
    const parts = []
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4))
    }
    if (parts.length) {
      return parts.join(' ')
    } else {
      return v
    }
  }

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '')
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4)
    }
    return v
  }

  if (currentStep === 'processing') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <Loader2 className="h-16 w-16 animate-spin text-blue-600 mx-auto" />
            <h3 className="text-lg font-semibold">Processing Payment</h3>
            <p className="text-gray-600">Please wait while we process your payment...</p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-sm text-yellow-800">
                🔒 Your payment is being processed securely through Saudi Payment Gateway
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (currentStep === 'success') {
    const isMADAPayment = selectedPaymentMethod === 'mada' && cardNumber.includes('4847')
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
            <h3 className="text-lg font-semibold text-green-600">Payment Successful!</h3>
            <p className="text-gray-600">
              You now have access to {profileName}'s {accessType === 'premium' ? 'contact information and documents' : accessType}
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-800">
                ✅ Transaction ID: {transactionId}
              </p>
              {isMADAPayment && (
                <p className="text-sm text-green-800 mt-1">
                  💳 Paid with AlRajhi Bank MADA • MD BELAL HOSSAIN
                </p>
              )}
            </div>
            {isMADAPayment && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <p className="text-sm text-blue-800">
                  🎉 Test payment completed successfully with your MADA card!
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (currentStep === 'failed') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="pt-6">
          <div className="text-center space-y-4">
            <AlertCircle className="h-16 w-16 text-red-600 mx-auto" />
            <h3 className="text-lg font-semibold text-red-600">Payment Failed</h3>
            <p className="text-gray-600">
              Your payment could not be processed. Please try again.
            </p>
            <div className="flex gap-2">
              <Button onClick={() => setCurrentStep('select')} className="flex-1">
                Try Again
              </Button>
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-4">
      {/* Payment Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="h-5 w-5 mr-2 text-green-600" />
            Secure Payment
          </CardTitle>
          <CardDescription>
            Saudi Arabian Payment Gateway - Secure & Encrypted
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Profile:</span>
              <span className="font-medium">{profileName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Access Type:</span>
              <span className="font-medium">{accessDetails.title}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Amount:</span>
              <span className="font-bold text-lg">{accessDetails.amount} SAR</span>
            </div>
            <div className="text-sm text-gray-500 border-t pt-2">
              {accessDetails.description}
            </div>
          </div>
        </CardContent>
      </Card>

      {currentStep === 'select' && (
        <Card>
          <CardHeader>
            <CardTitle>Select Payment Method</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {SAUDI_PAYMENT_METHODS.map((method) => (
                <div
                  key={method.id}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    selectedPaymentMethod === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  } ${method.id === 'mada' ? 'ring-2 ring-green-200' : ''}`}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                >
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{method.icon}</span>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{method.name}</p>
                        {method.id === 'mada' && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            Test Ready
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{method.description}</p>
                      {method.id === 'mada' && (
                        <p className="text-xs text-green-600 mt-1">
                          💳 AlRajhi Bank • MD BELAL HOSSAIN
                        </p>
                      )}
                    </div>
                    {selectedPaymentMethod === method.id && (
                      <CheckCircle className="h-5 w-5 text-blue-600 ml-auto" />
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex gap-2 mt-6">
              <Button
                onClick={handleInitiatePayment}
                disabled={isProcessing}
                className="flex-1"
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CreditCard className="h-4 w-4 mr-2" />
                )}
                Continue to Payment
              </Button>
              <Button variant="outline" onClick={onCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {currentStep === 'details' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Lock className="h-5 w-5 mr-2" />
                Payment Details
              </div>
              {selectedPaymentMethod === 'mada' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fillTestCardDetails}
                  className="text-xs"
                >
                  Use Test Card
                </Button>
              )}
            </CardTitle>
            <CardDescription>
              Enter your {SAUDI_PAYMENT_METHODS.find(m => m.id === selectedPaymentMethod)?.name} details
              {selectedPaymentMethod === 'mada' && (
                <span className="block text-green-600 text-sm mt-1">
                  💳 AlRajhi Bank MADA card ready for testing
                </span>
              )}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cardholder Name
                </label>
                <Input
                  type="text"
                  placeholder="Enter cardholder name"
                  value={cardholderName}
                  onChange={(e) => setCardholderName(e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Card Number
                </label>
                <Input
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  value={cardNumber}
                  onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                  maxLength={19}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Expiry Date
                  </label>
                  <Input
                    type="text"
                    placeholder="MM/YY"
                    value={expiryDate}
                    onChange={(e) => setExpiryDate(formatExpiryDate(e.target.value))}
                    maxLength={5}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    CVV
                  </label>
                  <Input
                    type="text"
                    placeholder="123"
                    value={cvv}
                    onChange={(e) => setCvv(e.target.value.replace(/\D/g, ''))}
                    maxLength={4}
                  />
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-center">
                  <Shield className="h-4 w-4 text-blue-600 mr-2" />
                  <p className="text-sm text-blue-800">
                    Your payment information is encrypted and secure
                  </p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleProcessPayment}
                  disabled={isProcessing}
                  className="flex-1"
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <DollarSign className="h-4 w-4 mr-2" />
                  )}
                  Pay {accessDetails.amount} SAR
                </Button>
                <Button variant="outline" onClick={() => setCurrentStep('select')}>
                  Back
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
