'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Globe, Send, CheckCircle, AlertCircle, Code, FileText, Users, Building } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep6() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<number[]>([])

  const markSectionComplete = (sectionId: number) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section ${sectionId} completed! ✅`)
    }
  }

  const apiSteps = [
    {
      id: 1,
      title: "B2B Clearance API",
      description: "Real-time invoice clearance for business transactions",
      icon: <Building className="h-5 w-5" />,
      details: [
        "Submit invoice for clearance",
        "Receive clearance status",
        "Handle validation errors",
        "Process clearance response"
      ],
      endpoint: "/invoices/clearance/single",
      method: "POST"
    },
    {
      id: 2,
      title: "B2C Reporting API",
      description: "Batch reporting for consumer transactions",
      icon: <Users className="h-5 w-5" />,
      details: [
        "Batch invoice submission",
        "Simplified reporting process",
        "Consumer transaction handling",
        "Periodic reporting schedule"
      ],
      endpoint: "/invoices/reporting/single",
      method: "POST"
    },
    {
      id: 3,
      title: "Invoice Status API",
      description: "Check invoice processing status",
      icon: <CheckCircle className="h-5 w-5" />,
      details: [
        "Query invoice status",
        "Track processing progress",
        "Handle status updates",
        "Monitor compliance state"
      ],
      endpoint: "/invoices/status/{uuid}",
      method: "GET"
    },
    {
      id: 4,
      title: "Error Handling",
      description: "Manage API errors and validation issues",
      icon: <AlertCircle className="h-5 w-5" />,
      details: [
        "Parse error responses",
        "Handle validation failures",
        "Implement retry logic",
        "Log error details"
      ],
      endpoint: "Various endpoints",
      method: "ALL"
    }
  ]

  const apiFlows = [
    {
      type: "B2B Clearance",
      description: "Business-to-Business invoice clearance flow",
      steps: [
        "Generate signed invoice",
        "Submit to clearance API",
        "Receive clearance UUID",
        "Check clearance status",
        "Handle clearance result"
      ],
      color: "blue"
    },
    {
      type: "B2C Reporting",
      description: "Business-to-Consumer reporting flow",
      steps: [
        "Generate simplified invoice",
        "Submit to reporting API",
        "Receive reporting UUID",
        "Batch process invoices",
        "Confirm reporting status"
      ],
      color: "green"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-green-600">Step 6: Clearance & Reporting APIs</h1>
              <Badge className="bg-green-100 text-green-800">
                Integration
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Proceeding to Step 7...')
                  router.push('/invoice/step7')
                }}
                className="bg-green-600 hover:bg-green-700"
              >
                Next: VAT Groups
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <Globe className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-green-800 mb-2">🌐 Clearance & Reporting APIs</h3>
              <p className="text-green-600 mb-4">
                Implement B2B Clearance and B2C Reporting flows for ZATCA compliance
              </p>
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-800">{completedSections.length}</div>
                  <div className="text-sm text-green-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{apiSteps.length - completedSections.length}</div>
                  <div className="text-sm text-blue-600">Remaining</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Flows Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {apiFlows.map((flow, index) => (
            <Card key={index} className={`bg-${flow.color}-50 border-${flow.color}-200`}>
              <CardHeader>
                <CardTitle className={`text-${flow.color}-800`}>
                  {flow.type}
                </CardTitle>
                <p className={`text-${flow.color}-600 text-sm`}>
                  {flow.description}
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {flow.steps.map((step, stepIndex) => (
                    <div key={stepIndex} className="flex items-center space-x-2">
                      <div className={`w-6 h-6 rounded-full bg-${flow.color}-100 text-${flow.color}-600 flex items-center justify-center text-xs font-semibold`}>
                        {stepIndex + 1}
                      </div>
                      <span className="text-sm text-gray-700">{step}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* API Implementation Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {apiSteps.map((step) => (
            <Card
              key={step.id}
              className={`transition-all hover:shadow-lg ${
                completedSections.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSections.includes(step.id) ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                  }`}>
                    {completedSections.includes(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">
                        {step.title}
                      </h3>
                      {completedSections.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                    <div className="mt-2 flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {step.method}
                      </Badge>
                      <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {step.endpoint}
                      </code>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-4">
                  {step.details.map((detail, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{detail}</span>
                    </li>
                  ))}
                </ul>

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast.info(`Learning about ${step.title}...`)
                    }}
                  >
                    <Code className="h-4 w-4 mr-2" />
                    View Code
                  </Button>

                  {!completedSections.includes(step.id) && (
                    <Button
                      size="sm"
                      onClick={() => markSectionComplete(step.id)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* API Examples */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="h-5 w-5 mr-2 text-green-600" />
              API Implementation Examples
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* B2B Clearance Example */}
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">B2B Clearance Request</h4>
                <code className="text-sm bg-gray-100 p-3 rounded block whitespace-pre">
{`POST /invoices/clearance/single
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "invoiceHash": "sha256_hash_value",
  "uuid": "invoice_uuid",
  "invoice": "base64_encoded_xml"
}`}
                </code>
              </div>

              {/* B2C Reporting Example */}
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">B2C Reporting Request</h4>
                <code className="text-sm bg-gray-100 p-3 rounded block whitespace-pre">
{`POST /invoices/reporting/single
Content-Type: application/json
Authorization: Bearer {access_token}

{
  "invoiceHash": "sha256_hash_value",
  "uuid": "invoice_uuid",
  "invoice": "base64_encoded_xml"
}`}
                </code>
              </div>

              {/* Status Check Example */}
              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-semibold text-yellow-800 mb-2">Status Check Request</h4>
                <code className="text-sm bg-gray-100 p-3 rounded block whitespace-pre">
{`GET /invoices/status/{uuid}
Authorization: Bearer {access_token}

Response:
{
  "status": "CLEARED",
  "clearanceStatus": "CLEARED",
  "reportingStatus": "REPORTED"
}`}
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Handling Guide */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-red-600" />
              Error Handling & Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800">Common Error Codes</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive" className="text-xs">400</Badge>
                    <span className="text-sm">Bad Request - Invalid data</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive" className="text-xs">401</Badge>
                    <span className="text-sm">Unauthorized - Invalid token</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive" className="text-xs">422</Badge>
                    <span className="text-sm">Validation Error - Schema issues</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="destructive" className="text-xs">500</Badge>
                    <span className="text-sm">Server Error - System issues</span>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-800">Best Practices</h4>
                <div className="space-y-2">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Implement exponential backoff</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Log all API interactions</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Handle timeout scenarios</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span className="text-sm">Validate responses thoroughly</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
