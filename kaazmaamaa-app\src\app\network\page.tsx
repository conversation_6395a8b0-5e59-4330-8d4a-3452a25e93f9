'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Users, 
  Building, 
  Wrench, 
  Search, 
  MessageCircle, 
  Phone,
  ArrowLeft,
  Filter,
  MapPin,
  Star
} from 'lucide-react'
import { toast } from 'sonner'

interface NetworkUser {
  id: string
  name: string
  role: 'worker' | 'supplier' | 'company'
  location: string
  specialty: string
  rating: number
  experience: string
  whatsapp?: string
  isConnected: boolean
}

const mockNetworkUsers: NetworkUser[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'worker',
    location: 'Riyadh',
    specialty: 'Electrical Work',
    rating: 4.8,
    experience: '5 years',
    whatsapp: '+966501234567',
    isConnected: false
  },
  {
    id: '2',
    name: 'Saudi Construction Co.',
    role: 'company',
    location: 'Jeddah',
    specialty: 'Construction Projects',
    rating: 4.9,
    experience: '15 years',
    whatsapp: '+966507654321',
    isConnected: true
  },
  {
    id: '3',
    name: 'Gulf Manpower Solutions',
    role: 'supplier',
    location: 'Dammam',
    specialty: 'Skilled Workers Supply',
    rating: 4.7,
    experience: '8 years',
    whatsapp: '+966509876543',
    isConnected: false
  },
  {
    id: '4',
    name: 'Mohammed Hassan',
    role: 'worker',
    location: 'Riyadh',
    specialty: 'Welding & Fabrication',
    rating: 4.6,
    experience: '7 years',
    whatsapp: '+966502468135',
    isConnected: false
  },
  {
    id: '5',
    name: 'Al-Rajhi Construction',
    role: 'company',
    location: 'Mecca',
    specialty: 'Infrastructure Development',
    rating: 4.9,
    experience: '20 years',
    whatsapp: '+966508642097',
    isConnected: false
  }
]

export default function NetworkPage() {
  const { user, userRole } = useAuth()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState<'all' | 'worker' | 'supplier' | 'company'>('all')
  const [networkUsers, setNetworkUsers] = useState<NetworkUser[]>(mockNetworkUsers)

  useEffect(() => {
    if (!user) {
      router.push('/')
    }
  }, [user, router])

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'worker':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'supplier':
        return <Users className="h-4 w-4 text-green-600" />
      case 'company':
        return <Building className="h-4 w-4 text-purple-600" />
      default:
        return <Users className="h-4 w-4 text-gray-600" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'worker':
        return 'bg-blue-100 text-blue-800'
      case 'supplier':
        return 'bg-green-100 text-green-800'
      case 'company':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleConnect = (userId: string) => {
    setNetworkUsers(prev =>
      prev.map(user =>
        user.id === userId
          ? { ...user, isConnected: !user.isConnected }
          : user
      )
    )
    toast.success('Connection status updated!')
  }

  const handleWhatsAppContact = (whatsapp?: string, name?: string) => {
    if (whatsapp) {
      const message = `Hello ${name}, I found your profile on KAAZMAAMAA and would like to connect.`
      const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
      window.open(whatsappUrl, '_blank')
    } else {
      toast.error('WhatsApp number not available')
    }
  }

  const filteredUsers = networkUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.specialty.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = selectedRole === 'all' || user.role === selectedRole
    return matchesSearch && matchesRole
  })

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => router.push('/feed')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Feed
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Professional Network</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, specialty, or location..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <div className="flex gap-2">
                {(['all', 'worker', 'supplier', 'company'] as const).map((role) => (
                  <Button
                    key={role}
                    variant={selectedRole === role ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedRole(role)}
                    className="capitalize"
                  >
                    {role === 'all' ? 'All' : role}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Network Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map((networkUser) => (
            <Card key={networkUser.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <Avatar className="h-12 w-12 bg-gray-100 flex items-center justify-center">
                    {getRoleIcon(networkUser.role)}
                  </Avatar>
                  <div className="flex-1">
                    <h3 className="font-semibold">{networkUser.name}</h3>
                    <Badge className={getRoleColor(networkUser.role)}>
                      {networkUser.role}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="h-4 w-4 mr-2" />
                    {networkUser.location}
                  </div>
                  
                  <div className="text-sm">
                    <p className="font-medium text-gray-900">{networkUser.specialty}</p>
                    <p className="text-gray-600">{networkUser.experience} experience</p>
                  </div>
                  
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                    <span className="text-sm font-medium">{networkUser.rating}</span>
                  </div>
                  
                  <div className="flex gap-2 pt-2">
                    <Button
                      size="sm"
                      variant={networkUser.isConnected ? 'outline' : 'default'}
                      onClick={() => handleConnect(networkUser.id)}
                      className="flex-1"
                    >
                      {networkUser.isConnected ? 'Connected' : 'Connect'}
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleWhatsAppContact(networkUser.whatsapp, networkUser.name)}
                    >
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">No professionals found matching your criteria.</p>
              <Button onClick={() => {
                setSearchTerm('')
                setSelectedRole('all')
              }}>
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
