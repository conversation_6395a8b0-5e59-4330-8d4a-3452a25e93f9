"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PostsContext */ \"(app-pages-browser)/./src/contexts/PostsContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FeedPage() {\n    _s();\n    const { user, userRole, getUserProfile, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { posts, addPost, likePost, addComment, sharePost } = (0,_contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Create Post States\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postText, setPostText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFeeling, setSelectedFeeling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taggedUsers, setTaggedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postPrivacy, setPostPrivacy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('public');\n    // Comments States\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [commentTexts, setCommentTexts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAllComments, setShowAllComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Share States\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shareText, setShareText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Other States\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeStory, setActiveStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLocationPicker, setShowLocationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTagPicker, setShowTagPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (user && userRole) {\n                const profile = getUserProfile();\n                setUserProfile(profile);\n                console.log('User profile loaded:', profile);\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        userRole,\n        getUserProfile\n    ]);\n    // Redirect to home if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/');\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Helper function to get user's display name\n    const getUserDisplayName = ()=>{\n        if (!userProfile) return 'User';\n        if (userRole === 'worker') {\n            var _userProfile_personalInfo;\n            return ((_userProfile_personalInfo = userProfile.personalInfo) === null || _userProfile_personalInfo === void 0 ? void 0 : _userProfile_personalInfo.workerName) || 'Worker';\n        } else if (userRole === 'supplier') {\n            var _userProfile_personalInfo1;\n            return ((_userProfile_personalInfo1 = userProfile.personalInfo) === null || _userProfile_personalInfo1 === void 0 ? void 0 : _userProfile_personalInfo1.supplierName) || 'Supplier';\n        } else if (userRole === 'company') {\n            var _userProfile_companyInfo;\n            return ((_userProfile_companyInfo = userProfile.companyInfo) === null || _userProfile_companyInfo === void 0 ? void 0 : _userProfile_companyInfo.companyName) || 'Company';\n        }\n        return 'User';\n    };\n    // Helper function to get user's avatar/initial\n    const getUserInitial = ()=>{\n        const name = getUserDisplayName();\n        return name.charAt(0).toUpperCase();\n    };\n    // Helper function to get user's role emoji\n    const getUserRoleEmoji = ()=>{\n        switch(userRole){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Helper functions for Facebook-style features\n    const handleLikePost = (postId)=>{\n        likePost(postId);\n    };\n    const handleCommentSubmit = (postId)=>{\n        const commentText = commentTexts[postId];\n        if (!(commentText === null || commentText === void 0 ? void 0 : commentText.trim()) || !user) return;\n        addComment(postId, commentText, {\n            id: user.email,\n            name: getUserDisplayName(),\n            role: userRole || 'worker'\n        });\n        setCommentTexts((prev)=>({\n                ...prev,\n                [postId]: ''\n            }));\n    };\n    const handleSharePost = (postId)=>{\n        if (!shareText.trim()) {\n            sharePost(postId);\n        } else {\n            // Create a new post that shares the original\n            const originalPost = posts.find((p)=>p.id === postId);\n            if (originalPost) {\n                const newPost = {\n                    user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                    user_name: getUserDisplayName(),\n                    user_role: userRole || 'worker',\n                    content: shareText,\n                    post_type: 'shared',\n                    visibility: 'public'\n                };\n                addPost(newPost);\n            }\n        }\n        setShowShareModal(null);\n        setShareText('');\n    };\n    const toggleComments = (postId)=>{\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !prev[postId]\n            }));\n    };\n    const formatTimeAgo = (dateString)=>{\n        const now = new Date();\n        const postDate = new Date(dateString);\n        const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d\");\n        const diffInWeeks = Math.floor(diffInDays / 7);\n        return \"\".concat(diffInWeeks, \"w\");\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'worker':\n                return '#2563eb';\n            case 'supplier':\n                return '#059669';\n            case 'company':\n                return '#7c3aed';\n            default:\n                return '#6b7280';\n        }\n    };\n    const getRoleEmoji = (role)=>{\n        switch(role){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Show loading if still authenticating\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#f0f2f5'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '2rem',\n                            marginBottom: '1rem'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#65676b'\n                        },\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: '#f0f2f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: '#1877f2',\n                    padding: '0.75rem 1rem',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: 'white',\n                                        margin: 0\n                                    },\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'rgba(255,255,255,0.2)',\n                                        borderRadius: '20px',\n                                        padding: '0.5rem 1rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search KAAZMAAMAA...\",\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            color: 'white',\n                                            outline: 'none',\n                                            fontSize: '0.875rem'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.75rem',\n                                        color: 'white'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                borderRadius: '50%',\n                                                width: '2rem',\n                                                height: '2rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '0.875rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: getUserInitial()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const profilePath = userRole === 'worker' ? '/workers/profile' : userRole === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                router.push(profilePath);\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Go Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                await signOut();\n                                                router.push('/');\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 2fr 1fr',\n                    gap: '1rem',\n                    padding: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Quick Access\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/workers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDD27\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Workers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/suppliers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e8f5e8',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Suppliers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/companies\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#f3e8ff',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFE2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/barta\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#fff3cd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Barta Messenger\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem',\n                                        overflowX: 'auto',\n                                        paddingBottom: '0.5rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: '120px',\n                                                height: '200px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(45deg, #1877f2, #42a5f5)',\n                                                position: 'relative',\n                                                cursor: 'pointer',\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                justifyContent: 'flex-end',\n                                                padding: '1rem',\n                                                color: 'white'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: 'absolute',\n                                                        top: '1rem',\n                                                        left: '1rem',\n                                                        backgroundColor: 'rgba(255,255,255,0.3)',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '0.875rem',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"Create Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            'Ahmed Al-Rashid',\n                                            'Saudi Building Co.',\n                                            'Tech Solutions',\n                                            'Construction Pro'\n                                        ].map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    minWidth: '120px',\n                                                    height: '200px',\n                                                    borderRadius: '12px',\n                                                    background: \"linear-gradient(45deg, \".concat([\n                                                        '#ff6b6b',\n                                                        '#4ecdc4',\n                                                        '#45b7d1',\n                                                        '#96ceb4'\n                                                    ][index], \", \").concat([\n                                                        '#ffa726',\n                                                        '#26a69a',\n                                                        '#42a5f5',\n                                                        '#81c784'\n                                                    ][index], \")\"),\n                                                    position: 'relative',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    justifyContent: 'space-between',\n                                                    padding: '1rem',\n                                                    color: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: 'rgba(255,255,255,0.3)',\n                                                            borderRadius: '50%',\n                                                            width: '2.5rem',\n                                                            height: '2.5rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: '600'\n                                                        },\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.75rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    color: 'white',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: getUserInitial()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"What's on your mind, \".concat(getUserDisplayName(), \"?\"),\n                                                onClick: ()=>setShowCreatePost(true),\n                                                readOnly: true,\n                                                style: {\n                                                    flex: 1,\n                                                    backgroundColor: '#f0f2f5',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '0.75rem 1rem',\n                                                    outline: 'none',\n                                                    fontSize: '1rem',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-around',\n                                            paddingTop: '0.75rem',\n                                            borderTop: '1px solid #e4e6ea'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDCF7 Photo/Video\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDE0A Feeling/Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            posts.map((post)=>{\n                                // Helper function to get role color\n                                const getRoleColor = (role)=>{\n                                    switch(role){\n                                        case 'worker':\n                                            return '#2563eb';\n                                        case 'supplier':\n                                            return '#059669';\n                                        case 'company':\n                                            return '#7c3aed';\n                                        default:\n                                            return '#6b7280';\n                                    }\n                                };\n                                // Helper function to get role emoji\n                                const getRoleEmoji = (role)=>{\n                                    switch(role){\n                                        case 'worker':\n                                            return '🔧';\n                                        case 'supplier':\n                                            return '🏭';\n                                        case 'company':\n                                            return '🏢';\n                                        default:\n                                            return '👤';\n                                    }\n                                };\n                                // Helper function to format time\n                                const getTimeAgo = (dateString)=>{\n                                    const now = new Date();\n                                    const postDate = new Date(dateString);\n                                    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n                                    if (diffInMinutes < 1) return 'Just now';\n                                    if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \" minutes ago\");\n                                    const diffInHours = Math.floor(diffInMinutes / 60);\n                                    if (diffInHours < 24) return \"\".concat(diffInHours, \" hours ago\");\n                                    const diffInDays = Math.floor(diffInHours / 24);\n                                    return \"\".concat(diffInDays, \" days ago\");\n                                };\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '8px',\n                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(post.user_role),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: post.user_name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // Navigate to user's profile based on their role\n                                                                const profilePath = post.user_role === 'worker' ? '/workers/profile' : post.user_role === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                                router.push(\"\".concat(profilePath, \"?user=\").concat(encodeURIComponent(post.user_id)));\n                                                            },\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                padding: 0,\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21',\n                                                                cursor: 'pointer',\n                                                                fontSize: 'inherit',\n                                                                textAlign: 'left'\n                                                            },\n                                                            onMouseEnter: (e)=>e.target.style.textDecoration = 'underline',\n                                                            onMouseLeave: (e)=>e.target.style.textDecoration = 'none',\n                                                            children: post.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                getTimeAgo(post.created_at),\n                                                                \" • \",\n                                                                getRoleEmoji(post.user_role),\n                                                                \" \",\n                                                                post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 508,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: '1rem',\n                                                paddingRight: '1rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#1c1e21',\n                                                    lineHeight: '1.5',\n                                                    margin: 0\n                                                },\n                                                children: post.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 17\n                                        }, this),\n                                        post.media_urls && post.media_urls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f2f5',\n                                                height: '250px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                color: '#65676b',\n                                                fontSize: '3rem'\n                                            },\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '0.75rem 1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '0.75rem'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#65676b',\n                                                            fontSize: '0.875rem'\n                                                        },\n                                                        children: [\n                                                            \"\\uD83D\\uDC4D \",\n                                                            post.likes,\n                                                            \" likes • \\uD83D\\uDCAC \",\n                                                            post.comments,\n                                                            \" comments\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-around',\n                                                        paddingTop: '0.75rem',\n                                                        borderTop: '1px solid #e4e6ea'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>likePost(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: post.liked_by_user ? '#1877f2' : '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDC4D Like\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDCAC Comment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDCE4 Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 556,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this);\n                            }),\n                            posts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\n                                    padding: '3rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#1c1e21',\n                                            marginBottom: '0.5rem'\n                                        },\n                                        children: \"No posts yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Be the first to share something with your professional network!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Online Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#7c3aed',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Modern Tech Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#2563eb',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 596,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Fatima Al-Zahra\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#059669',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 603,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Khalid Construction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '2rem',\n                                    paddingTop: '1rem',\n                                    borderTop: '1px solid #e4e6ea'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    style: {\n                                        color: '#1877f2',\n                                        textDecoration: 'none',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 612,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            showCreatePost && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 648,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreatePost(false),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.75rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        color: 'white',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: getUserInitial()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 687,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                color: '#65676b',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                getUserRoleEmoji(),\n                                                \" \",\n                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                \" • \\uD83C\\uDF0D Public\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 688,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 672,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: postText,\n                                onChange: (e)=>setPostText(e.target.value),\n                                placeholder: \"What's on your mind?\",\n                                style: {\n                                    width: '100%',\n                                    minHeight: '120px',\n                                    border: 'none',\n                                    outline: 'none',\n                                    fontSize: '1.5rem',\n                                    color: '#1c1e21',\n                                    resize: 'none',\n                                    fontFamily: 'inherit'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 13\n                        }, this),\n                        selectedFeeling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '20px',\n                                    padding: '0.5rem 1rem',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: selectedFeeling\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedFeeling(''),\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            color: '#65676b'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 725,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 15\n                        }, this),\n                        selectedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'relative',\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '8px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: [\n                                            \"Image Preview (\",\n                                            selectedImages.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedImages([]),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '0.5rem',\n                                            right: '0.5rem',\n                                            backgroundColor: 'rgba(0,0,0,0.5)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '2rem',\n                                            height: '2rem',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderTop: '1px solid #e4e6ea'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: \"Add to your post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '0.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImage('image'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Photo/Video\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedFeeling('😊 feeling happy'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Feeling/Activity\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Tag People\",\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 814,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Check In\",\n                                                    children: \"\\uD83D\\uDCCD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (!postText.trim()) return;\n                                        // Create the post using the posts context\n                                        const newPost = {\n                                            user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                                            user_name: getUserDisplayName(),\n                                            user_role: userRole || 'worker',\n                                            content: postText,\n                                            media_urls: selectedImage ? [\n                                                selectedImage\n                                            ] : undefined,\n                                            post_type: selectedImage ? 'image' : 'text',\n                                            visibility: 'public'\n                                        };\n                                        addPost(newPost);\n                                        // Reset form\n                                        setPostText('');\n                                        setSelectedFeeling('');\n                                        setSelectedImage(null);\n                                        setShowCreatePost(false);\n                                        // Show success message\n                                        alert('Post created successfully!');\n                                    },\n                                    disabled: !postText.trim(),\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                                        color: postText.trim() ? 'white' : '#bcc0c4',\n                                        border: 'none',\n                                        borderRadius: '6px',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '600',\n                                        cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                                    },\n                                    children: \"Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 852,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 774,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 620,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"mG1is2bfnznMFmJKz7POH9TiagY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});