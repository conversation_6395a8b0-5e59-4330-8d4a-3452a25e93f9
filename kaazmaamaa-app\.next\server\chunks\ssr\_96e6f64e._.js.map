{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/feed/page.tsx"], "sourcesContent": ["import React from 'react'\n\nexport default function FeedPage() {\n  return (\n    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>\n      {/* Top Header with Sign In and Register buttons */}\n      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#f59e0b', margin: 0 }}>\n              KAAZMAAMAA\n            </h2>\n            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Social Feed</span>\n          </div>\n          \n          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>\n            <a \n              href=\"/auth/login\"\n              style={{ \n                color: '#f59e0b', \n                textDecoration: 'none',\n                padding: '0.5rem 1rem',\n                border: '1px solid #f59e0b',\n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Sign In\n            </a>\n            \n            <a \n              href=\"/auth/signup\"\n              style={{ \n                backgroundColor: '#f59e0b', \n                color: 'white', \n                textDecoration: 'none',\n                padding: '0.5rem 1rem', \n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Register\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div style={{ padding: '2rem' }}>\n        <div style={{ maxWidth: '900px', margin: '0 auto' }}>\n          {/* Feed Header */}\n          <div style={{ textAlign: 'center', marginBottom: '3rem' }}>\n            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📱</div>\n            <h1 style={{ fontSize: '3rem', fontWeight: 'bold', color: '#f59e0b', marginBottom: '1rem' }}>\n              Professional Social Feed\n            </h1>\n            <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '2rem' }}>\n              Connect, Share, and Engage with Your Business Network\n            </p>\n          </div>\n\n          {/* Social Features */}\n          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem', marginBottom: '3rem' }}>\n            {/* Share Posts */}\n            <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>\n              <div style={{ backgroundColor: '#fef3c7', borderRadius: '50%', width: '4rem', height: '4rem', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto 1rem' }}>\n                <span style={{ fontSize: '1.5rem' }}>📝</span>\n              </div>\n              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#92400e', marginBottom: '0.75rem' }}>\n                Share Professional Updates\n              </h3>\n              <p style={{ color: '#78350f', marginBottom: '1.5rem', lineHeight: '1.5' }}>\n                Post about your work, achievements, projects, and professional insights to engage with your network.\n              </p>\n              <button style={{ \n                backgroundColor: '#f59e0b', \n                color: 'white', \n                padding: '0.75rem 1.5rem', \n                border: 'none', \n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                fontWeight: '500'\n              }}>\n                Create Post\n              </button>\n            </div>\n\n            {/* Engage & Network */}\n            <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>\n              <div style={{ backgroundColor: '#dbeafe', borderRadius: '50%', width: '4rem', height: '4rem', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto 1rem' }}>\n                <span style={{ fontSize: '1.5rem' }}>💬</span>\n              </div>\n              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1e40af', marginBottom: '0.75rem' }}>\n                Engage & Comment\n              </h3>\n              <p style={{ color: '#1e3a8a', marginBottom: '1.5rem', lineHeight: '1.5' }}>\n                Like, comment, and share posts from workers, suppliers, and companies in your professional network.\n              </p>\n              <button style={{ \n                backgroundColor: '#2563eb', \n                color: 'white', \n                padding: '0.75rem 1.5rem', \n                border: 'none', \n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                fontWeight: '500'\n              }}>\n                Browse Feed\n              </button>\n            </div>\n\n            {/* Build Network */}\n            <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>\n              <div style={{ backgroundColor: '#dcfce7', borderRadius: '50%', width: '4rem', height: '4rem', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto 1rem' }}>\n                <span style={{ fontSize: '1.5rem' }}>🔗</span>\n              </div>\n              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#166534', marginBottom: '0.75rem' }}>\n                Build Your Network\n              </h3>\n              <p style={{ color: '#14532d', marginBottom: '1.5rem', lineHeight: '1.5' }}>\n                Connect with professionals, follow industry leaders, and expand your business relationships.\n              </p>\n              <button style={{ \n                backgroundColor: '#059669', \n                color: 'white', \n                padding: '0.75rem 1.5rem', \n                border: 'none', \n                borderRadius: '0.5rem',\n                cursor: 'pointer',\n                fontWeight: '500'\n              }}>\n                Find Connections\n              </button>\n            </div>\n          </div>\n\n          {/* Quick Access to Directories */}\n          <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '3rem' }}>\n            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1.5rem', textAlign: 'center' }}>\n              Connect with Professionals\n            </h3>\n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>\n              <a \n                href=\"/workers\"\n                style={{ \n                  display: 'block',\n                  padding: '1rem', \n                  backgroundColor: '#eff6ff', \n                  borderRadius: '0.5rem', \n                  textDecoration: 'none',\n                  textAlign: 'center',\n                  transition: 'all 0.2s'\n                }}\n              >\n                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🔧</div>\n                <div style={{ fontWeight: '600', color: '#2563eb' }}>Workers</div>\n                <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>Skilled Professionals</div>\n              </a>\n              \n              <a \n                href=\"/suppliers\"\n                style={{ \n                  display: 'block',\n                  padding: '1rem', \n                  backgroundColor: '#f0fdf4', \n                  borderRadius: '0.5rem', \n                  textDecoration: 'none',\n                  textAlign: 'center',\n                  transition: 'all 0.2s'\n                }}\n              >\n                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🏭</div>\n                <div style={{ fontWeight: '600', color: '#059669' }}>Suppliers</div>\n                <div style={{ fontSize: '0.875rem', color: '#047857' }}>Reliable Vendors</div>\n              </a>\n              \n              <a \n                href=\"/companies\"\n                style={{ \n                  display: 'block',\n                  padding: '1rem', \n                  backgroundColor: '#faf5ff', \n                  borderRadius: '0.5rem', \n                  textDecoration: 'none',\n                  textAlign: 'center',\n                  transition: 'all 0.2s'\n                }}\n              >\n                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🏢</div>\n                <div style={{ fontWeight: '600', color: '#7c3aed' }}>Companies</div>\n                <div style={{ fontSize: '0.875rem', color: '#6d28d9' }}>Business Partners</div>\n              </a>\n            </div>\n          </div>\n\n          {/* Barta Messaging */}\n          <div style={{ backgroundColor: '#f59e0b', borderRadius: '0.75rem', padding: '2rem', textAlign: 'center', color: 'white' }}>\n            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💬</div>\n            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>\n              Barta Messenger\n            </h3>\n            <p style={{ marginBottom: '2rem', color: '#fef3c7', fontSize: '1.125rem' }}>\n              Start private conversations, share files, and build stronger professional relationships.\n            </p>\n            <a \n              href=\"/barta\"\n              style={{ \n                display: 'inline-block',\n                backgroundColor: 'white', \n                color: '#f59e0b', \n                padding: '1rem 2rem', \n                borderRadius: '0.5rem', \n                textDecoration: 'none',\n                fontSize: '1.125rem',\n                fontWeight: '600'\n              }}\n            >\n              Open Barta Messenger\n            </a>\n          </div>\n\n          {/* Navigation Footer */}\n          <div style={{ marginTop: '3rem', textAlign: 'center', padding: '2rem' }}>\n            <p style={{ color: '#9ca3af' }}>\n              <a href=\"/\" style={{ color: '#f59e0b', textDecoration: 'none' }}>← Back to Home</a> | \n              <a href=\"/workers\" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Workers</a> | \n              <a href=\"/suppliers\" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Suppliers</a> | \n              <a href=\"/companies\" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Companies</a> | \n              <a href=\"/barta\" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Barta</a>\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,OAAO;YAAE,WAAW;YAAS,iBAAiB;QAAU;;0BAE3D,8OAAC;gBAAI,OAAO;oBAAE,iBAAiB;oBAAS,WAAW;oBAA6B,SAAS;gBAAY;0BACnG,cAAA,8OAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAU,QAAQ;wBAAU,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCACzH,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAO;;8CAC/D,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAQ,OAAO;wCAAW,QAAQ;oCAAE;8CAAG;;;;;;8CAGpF,8OAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;oCAAW;8CAAG;;;;;;;;;;;;sCAG3D,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAQ,YAAY;4BAAS;;8CAC/D,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,OAAO;wCACP,gBAAgB;wCAChB,SAAS;wCACT,QAAQ;wCACR,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;8CAID,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,iBAAiB;wCACjB,OAAO;wCACP,gBAAgB;wCAChB,SAAS;wCACT,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAO;0BAC5B,cAAA,8OAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAS,QAAQ;oBAAS;;sCAEhD,8OAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAU,cAAc;4BAAO;;8CACtD,8OAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,cAAc;oCAAO;8CAAG;;;;;;8CACxD,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAQ,YAAY;wCAAQ,OAAO;wCAAW,cAAc;oCAAO;8CAAG;;;;;;8CAG7F,8OAAC;oCAAE,OAAO;wCAAE,UAAU;wCAAW,OAAO;wCAAW,cAAc;oCAAO;8CAAG;;;;;;;;;;;;sCAM7E,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,qBAAqB;gCAAwC,KAAK;gCAAQ,cAAc;4BAAO;;8CAE5H,8OAAC;oCAAI,OAAO;wCAAE,iBAAiB;wCAAS,cAAc;wCAAW,WAAW;wCAA6B,SAAS;wCAAQ,WAAW;oCAAS;;sDAC5I,8OAAC;4CAAI,OAAO;gDAAE,iBAAiB;gDAAW,cAAc;gDAAO,OAAO;gDAAQ,QAAQ;gDAAQ,SAAS;gDAAQ,YAAY;gDAAU,gBAAgB;gDAAU,QAAQ;4CAAc;sDACnL,cAAA,8OAAC;gDAAK,OAAO;oDAAE,UAAU;gDAAS;0DAAG;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,OAAO;gDAAE,UAAU;gDAAW,YAAY;gDAAO,OAAO;gDAAW,cAAc;4CAAU;sDAAG;;;;;;sDAGlG,8OAAC;4CAAE,OAAO;gDAAE,OAAO;gDAAW,cAAc;gDAAU,YAAY;4CAAM;sDAAG;;;;;;sDAG3E,8OAAC;4CAAO,OAAO;gDACb,iBAAiB;gDACjB,OAAO;gDACP,SAAS;gDACT,QAAQ;gDACR,cAAc;gDACd,QAAQ;gDACR,YAAY;4CACd;sDAAG;;;;;;;;;;;;8CAML,8OAAC;oCAAI,OAAO;wCAAE,iBAAiB;wCAAS,cAAc;wCAAW,WAAW;wCAA6B,SAAS;wCAAQ,WAAW;oCAAS;;sDAC5I,8OAAC;4CAAI,OAAO;gDAAE,iBAAiB;gDAAW,cAAc;gDAAO,OAAO;gDAAQ,QAAQ;gDAAQ,SAAS;gDAAQ,YAAY;gDAAU,gBAAgB;gDAAU,QAAQ;4CAAc;sDACnL,cAAA,8OAAC;gDAAK,OAAO;oDAAE,UAAU;gDAAS;0DAAG;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,OAAO;gDAAE,UAAU;gDAAW,YAAY;gDAAO,OAAO;gDAAW,cAAc;4CAAU;sDAAG;;;;;;sDAGlG,8OAAC;4CAAE,OAAO;gDAAE,OAAO;gDAAW,cAAc;gDAAU,YAAY;4CAAM;sDAAG;;;;;;sDAG3E,8OAAC;4CAAO,OAAO;gDACb,iBAAiB;gDACjB,OAAO;gDACP,SAAS;gDACT,QAAQ;gDACR,cAAc;gDACd,QAAQ;gDACR,YAAY;4CACd;sDAAG;;;;;;;;;;;;8CAML,8OAAC;oCAAI,OAAO;wCAAE,iBAAiB;wCAAS,cAAc;wCAAW,WAAW;wCAA6B,SAAS;wCAAQ,WAAW;oCAAS;;sDAC5I,8OAAC;4CAAI,OAAO;gDAAE,iBAAiB;gDAAW,cAAc;gDAAO,OAAO;gDAAQ,QAAQ;gDAAQ,SAAS;gDAAQ,YAAY;gDAAU,gBAAgB;gDAAU,QAAQ;4CAAc;sDACnL,cAAA,8OAAC;gDAAK,OAAO;oDAAE,UAAU;gDAAS;0DAAG;;;;;;;;;;;sDAEvC,8OAAC;4CAAG,OAAO;gDAAE,UAAU;gDAAW,YAAY;gDAAO,OAAO;gDAAW,cAAc;4CAAU;sDAAG;;;;;;sDAGlG,8OAAC;4CAAE,OAAO;gDAAE,OAAO;gDAAW,cAAc;gDAAU,YAAY;4CAAM;sDAAG;;;;;;sDAG3E,8OAAC;4CAAO,OAAO;gDACb,iBAAiB;gDACjB,OAAO;gDACP,SAAS;gDACT,QAAQ;gDACR,cAAc;gDACd,QAAQ;gDACR,YAAY;4CACd;sDAAG;;;;;;;;;;;;;;;;;;sCAOP,8OAAC;4BAAI,OAAO;gCAAE,iBAAiB;gCAAS,cAAc;gCAAW,WAAW;gCAA6B,SAAS;gCAAQ,cAAc;4BAAO;;8CAC7I,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAO,OAAO;wCAAW,cAAc;wCAAU,WAAW;oCAAS;8CAAG;;;;;;8CAGrH,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,qBAAqB;wCAAwC,KAAK;oCAAO;;sDACtG,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,SAAS;gDACT,SAAS;gDACT,iBAAiB;gDACjB,cAAc;gDACd,gBAAgB;gDAChB,WAAW;gDACX,YAAY;4CACd;;8DAEA,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAU,cAAc;oDAAS;8DAAG;;;;;;8DAC5D,8OAAC;oDAAI,OAAO;wDAAE,YAAY;wDAAO,OAAO;oDAAU;8DAAG;;;;;;8DACrD,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,OAAO;oDAAU;8DAAG;;;;;;;;;;;;sDAG1D,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,SAAS;gDACT,SAAS;gDACT,iBAAiB;gDACjB,cAAc;gDACd,gBAAgB;gDAChB,WAAW;gDACX,YAAY;4CACd;;8DAEA,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAU,cAAc;oDAAS;8DAAG;;;;;;8DAC5D,8OAAC;oDAAI,OAAO;wDAAE,YAAY;wDAAO,OAAO;oDAAU;8DAAG;;;;;;8DACrD,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,OAAO;oDAAU;8DAAG;;;;;;;;;;;;sDAG1D,8OAAC;4CACC,MAAK;4CACL,OAAO;gDACL,SAAS;gDACT,SAAS;gDACT,iBAAiB;gDACjB,cAAc;gDACd,gBAAgB;gDAChB,WAAW;gDACX,YAAY;4CACd;;8DAEA,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAU,cAAc;oDAAS;8DAAG;;;;;;8DAC5D,8OAAC;oDAAI,OAAO;wDAAE,YAAY;wDAAO,OAAO;oDAAU;8DAAG;;;;;;8DACrD,8OAAC;oDAAI,OAAO;wDAAE,UAAU;wDAAY,OAAO;oDAAU;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;sCAM9D,8OAAC;4BAAI,OAAO;gCAAE,iBAAiB;gCAAW,cAAc;gCAAW,SAAS;gCAAQ,WAAW;gCAAU,OAAO;4BAAQ;;8CACtH,8OAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,cAAc;oCAAO;8CAAG;;;;;;8CACxD,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAO,cAAc;oCAAO;8CAAG;;;;;;8CAG5E,8OAAC;oCAAE,OAAO;wCAAE,cAAc;wCAAQ,OAAO;wCAAW,UAAU;oCAAW;8CAAG;;;;;;8CAG5E,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,SAAS;wCACT,iBAAiB;wCACjB,OAAO;wCACP,SAAS;wCACT,cAAc;wCACd,gBAAgB;wCAChB,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,OAAO;gCAAE,WAAW;gCAAQ,WAAW;gCAAU,SAAS;4BAAO;sCACpE,cAAA,8OAAC;gCAAE,OAAO;oCAAE,OAAO;gCAAU;;kDAC3B,8OAAC;wCAAE,MAAK;wCAAI,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;wCAAO;kDAAG;;;;;;oCAAkB;kDACnF,8OAAC;wCAAE,MAAK;wCAAW,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;4CAAQ,YAAY;wCAAS;kDAAG;;;;;;oCAAW;kDACzG,8OAAC;wCAAE,MAAK;wCAAa,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;4CAAQ,YAAY;wCAAS;kDAAG;;;;;;oCAAa;kDAC7G,8OAAC;wCAAE,MAAK;wCAAa,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;4CAAQ,YAAY;wCAAS;kDAAG;;;;;;oCAAa;kDAC7G,8OAAC;wCAAE,MAAK;wCAAS,OAAO;4CAAE,OAAO;4CAAW,gBAAgB;4CAAQ,YAAY;wCAAS;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1G", "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,OAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}