{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/feed/page.tsx"], "sourcesContent": ["import React from 'react'\n\nexport default function FeedPage() {\n  return (\n    <div style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>\n      {/* Facebook-style Header */}\n      <div style={{ backgroundColor: '#1877f2', padding: '0.75rem 1rem', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white', margin: 0 }}>\n              KAAZMAAMAA\n            </h1>\n            <div style={{ backgroundColor: 'rgba(255,255,255,0.2)', borderRadius: '20px', padding: '0.5rem 1rem' }}>\n              <input \n                type=\"text\" \n                placeholder=\"Search KAAZMAAMAA...\" \n                style={{ \n                  backgroundColor: 'transparent', \n                  border: 'none', \n                  color: 'white', \n                  outline: 'none',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n          </div>\n          \n          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>\n            <a \n              href=\"/auth/login\"\n              style={{ \n                color: 'white', \n                textDecoration: 'none',\n                padding: '0.5rem 1rem',\n                border: '1px solid white',\n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Sign In\n            </a>\n            \n            <a \n              href=\"/auth/signup\"\n              style={{ \n                backgroundColor: 'white', \n                color: '#1877f2', \n                textDecoration: 'none',\n                padding: '0.5rem 1rem', \n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Register\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Feed Layout */}\n      <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'grid', gridTemplateColumns: '1fr 2fr 1fr', gap: '1rem', padding: '1rem' }}>\n        \n        {/* Left Sidebar */}\n        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>\n            Quick Access\n          </h3>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n            <a href=\"/workers\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#e3f2fd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                🔧\n              </div>\n              <span style={{ fontWeight: '500' }}>Workers</span>\n            </a>\n            \n            <a href=\"/suppliers\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#e8f5e8', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                🏭\n              </div>\n              <span style={{ fontWeight: '500' }}>Suppliers</span>\n            </a>\n            \n            <a href=\"/companies\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#f3e8ff', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                🏢\n              </div>\n              <span style={{ fontWeight: '500' }}>Companies</span>\n            </a>\n            \n            <a href=\"/barta\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#fff3cd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                💬\n              </div>\n              <span style={{ fontWeight: '500' }}>Barta Messenger</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Center Feed */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          \n          {/* Create Post */}\n          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n            <div style={{ display: 'flex', gap: '0.75rem', marginBottom: '1rem' }}>\n              <div style={{ backgroundColor: '#1877f2', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>\n                U\n              </div>\n              <input \n                type=\"text\" \n                placeholder=\"What's on your mind?\" \n                style={{ \n                  flex: 1, \n                  backgroundColor: '#f0f2f5', \n                  border: 'none', \n                  borderRadius: '20px', \n                  padding: '0.75rem 1rem',\n                  outline: 'none',\n                  fontSize: '1rem'\n                }}\n              />\n            </div>\n            <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>\n              <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}>\n                📷 Photo/Video\n              </button>\n              <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}>\n                😊 Feeling/Activity\n              </button>\n            </div>\n          </div>\n\n          {/* Sample Post 1 */}\n          <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ backgroundColor: '#2563eb', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>\n                A\n              </div>\n              <div>\n                <div style={{ fontWeight: '600', color: '#1c1e21' }}>Ahmed Al-Rashid</div>\n                <div style={{ fontSize: '0.8rem', color: '#65676b' }}>2 hours ago • 🔧 Worker</div>\n              </div>\n            </div>\n            \n            <div style={{ paddingLeft: '1rem', paddingRight: '1rem', marginBottom: '1rem' }}>\n              <p style={{ color: '#1c1e21', lineHeight: '1.5', margin: 0 }}>\n                Just completed a major electrical installation project in Riyadh! 💡 Looking for new opportunities in commercial electrical work. \n                Feel free to reach out if you need a certified electrician. #ElectricalWork #Riyadh #Professional\n              </p>\n            </div>\n            \n            <div style={{ backgroundColor: '#f0f2f5', height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#65676b', fontSize: '3rem' }}>\n              🔌⚡\n            </div>\n            \n            <div style={{ padding: '0.75rem 1rem' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>\n                <span style={{ color: '#65676b', fontSize: '0.875rem' }}>👍 15 likes • 💬 3 comments</span>\n              </div>\n              <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>\n                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                  👍 Like\n                </button>\n                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                  💬 Comment\n                </button>\n                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                  📤 Share\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Sample Post 2 */}\n          <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ backgroundColor: '#059669', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>\n                S\n              </div>\n              <div>\n                <div style={{ fontWeight: '600', color: '#1c1e21' }}>Saudi Building Supplies Co.</div>\n                <div style={{ fontSize: '0.8rem', color: '#65676b' }}>5 hours ago • 🏭 Supplier</div>\n              </div>\n            </div>\n            \n            <div style={{ paddingLeft: '1rem', paddingRight: '1rem', marginBottom: '1rem' }}>\n              <p style={{ color: '#1c1e21', lineHeight: '1.5', margin: 0 }}>\n                🏗️ New shipment of premium construction materials just arrived! High-quality cement, steel, and building supplies now available. \n                Contact us for bulk orders and competitive pricing. #Construction #BuildingSupplies #Quality\n              </p>\n            </div>\n            \n            <div style={{ backgroundColor: '#f0f2f5', height: '250px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#65676b', fontSize: '3rem' }}>\n              🏗️🧱\n            </div>\n            \n            <div style={{ padding: '0.75rem 1rem' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>\n                <span style={{ color: '#65676b', fontSize: '0.875rem' }}>👍 28 likes • 💬 7 comments</span>\n              </div>\n              <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>\n                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                  👍 Like\n                </button>\n                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                  💬 Comment\n                </button>\n                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                  📤 Share\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Right Sidebar */}\n        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>\n            Online Now\n          </h3>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ position: 'relative' }}>\n                <div style={{ backgroundColor: '#7c3aed', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>\n                  M\n                </div>\n                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>\n              </div>\n              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Modern Tech Solutions</span>\n            </div>\n            \n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ position: 'relative' }}>\n                <div style={{ backgroundColor: '#2563eb', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>\n                  F\n                </div>\n                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>\n              </div>\n              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Fatima Al-Zahra</span>\n            </div>\n            \n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ position: 'relative' }}>\n                <div style={{ backgroundColor: '#059669', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>\n                  K\n                </div>\n                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>\n              </div>\n              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Khalid Construction</span>\n            </div>\n          </div>\n          \n          <div style={{ marginTop: '2rem', paddingTop: '1rem', borderTop: '1px solid #e4e6ea' }}>\n            <a href=\"/\" style={{ color: '#1877f2', textDecoration: 'none', fontSize: '0.875rem' }}>← Back to Home</a>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,OAAO;YAAE,WAAW;YAAS,iBAAiB;QAAU;;0BAE3D,8OAAC;gBAAI,OAAO;oBAAE,iBAAiB;oBAAW,SAAS;oBAAgB,WAAW;gBAA4B;0BACxG,cAAA,8OAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAU,QAAQ;wBAAU,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCACzH,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAO;;8CAC/D,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAQ,OAAO;wCAAS,QAAQ;oCAAE;8CAAG;;;;;;8CAGlF,8OAAC;oCAAI,OAAO;wCAAE,iBAAiB;wCAAyB,cAAc;wCAAQ,SAAS;oCAAc;8CACnG,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;4CACL,iBAAiB;4CACjB,QAAQ;4CACR,OAAO;4CACP,SAAS;4CACT,UAAU;wCACZ;;;;;;;;;;;;;;;;;sCAKN,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAQ,YAAY;4BAAS;;8CAC/D,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,OAAO;wCACP,gBAAgB;wCAChB,SAAS;wCACT,QAAQ;wCACR,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;8CAID,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,iBAAiB;wCACjB,OAAO;wCACP,gBAAgB;wCAChB,SAAS;wCACT,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAU,QAAQ;oBAAU,SAAS;oBAAQ,qBAAqB;oBAAe,KAAK;oBAAQ,SAAS;gBAAO;;kCAGpI,8OAAC;wBAAI,OAAO;4BAAE,iBAAiB;4BAAS,cAAc;4BAAO,SAAS;4BAAQ,QAAQ;4BAAe,WAAW;wBAA4B;;0CAC1I,8OAAC;gCAAG,OAAO;oCAAE,UAAU;oCAAY,YAAY;oCAAO,OAAO;oCAAW,cAAc;gCAAO;0CAAG;;;;;;0CAGhG,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAU;;kDACrE,8OAAC;wCAAE,MAAK;wCAAW,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DAClK,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,8OAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;kDAGtC,8OAAC;wCAAE,MAAK;wCAAa,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DACpK,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,8OAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;kDAGtC,8OAAC;wCAAE,MAAK;wCAAa,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DACpK,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,8OAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;kDAGtC,8OAAC;wCAAE,MAAK;wCAAS,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DAChK,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,8OAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,8OAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,eAAe;4BAAU,KAAK;wBAAO;;0CAGlE,8OAAC;gCAAI,OAAO;oCAAE,iBAAiB;oCAAS,cAAc;oCAAO,SAAS;oCAAQ,WAAW;gCAA4B;;kDACnH,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,KAAK;4CAAW,cAAc;wCAAO;;0DAClE,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;oDAAU,OAAO;oDAAS,YAAY;gDAAO;0DAAG;;;;;;0DAGzM,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;oDACL,MAAM;oDACN,iBAAiB;oDACjB,QAAQ;oDACR,cAAc;oDACd,SAAS;oDACT,SAAS;oDACT,UAAU;gDACZ;;;;;;;;;;;;kDAGJ,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,gBAAgB;4CAAgB,YAAY;4CAAW,WAAW;wCAAoB;;0DACnH,8OAAC;gDAAO,OAAO;oDAAE,SAAS;oDAAQ,YAAY;oDAAU,KAAK;oDAAU,iBAAiB;oDAAe,QAAQ;oDAAQ,SAAS;oDAAe,cAAc;oDAAO,QAAQ;oDAAW,OAAO;gDAAU;0DAAG;;;;;;0DAG3M,8OAAC;gDAAO,OAAO;oDAAE,SAAS;oDAAQ,YAAY;oDAAU,KAAK;oDAAU,iBAAiB;oDAAe,QAAQ;oDAAQ,SAAS;oDAAe,cAAc;oDAAO,QAAQ;oDAAW,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAO/M,8OAAC;gCAAI,OAAO;oCAAE,iBAAiB;oCAAS,cAAc;oCAAO,WAAW;gCAA4B;;kDAClG,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DACnF,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;oDAAU,OAAO;oDAAS,YAAY;gDAAO;0DAAG;;;;;;0DAGzM,8OAAC;;kEACC,8OAAC;wDAAI,OAAO;4DAAE,YAAY;4DAAO,OAAO;wDAAU;kEAAG;;;;;;kEACrD,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAU,OAAO;wDAAU;kEAAG;;;;;;;;;;;;;;;;;;kDAI1D,8OAAC;wCAAI,OAAO;4CAAE,aAAa;4CAAQ,cAAc;4CAAQ,cAAc;wCAAO;kDAC5E,cAAA,8OAAC;4CAAE,OAAO;gDAAE,OAAO;gDAAW,YAAY;gDAAO,QAAQ;4CAAE;sDAAG;;;;;;;;;;;kDAMhE,8OAAC;wCAAI,OAAO;4CAAE,iBAAiB;4CAAW,QAAQ;4CAAS,SAAS;4CAAQ,YAAY;4CAAU,gBAAgB;4CAAU,OAAO;4CAAW,UAAU;wCAAO;kDAAG;;;;;;kDAIlK,8OAAC;wCAAI,OAAO;4CAAE,SAAS;wCAAe;;0DACpC,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,gBAAgB;oDAAiB,YAAY;oDAAU,cAAc;gDAAU;0DAC5G,cAAA,8OAAC;oDAAK,OAAO;wDAAE,OAAO;wDAAW,UAAU;oDAAW;8DAAG;;;;;;;;;;;0DAE3D,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,gBAAgB;oDAAgB,YAAY;oDAAW,WAAW;gDAAoB;;kEACnH,8OAAC;wDAAO,OAAO;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;4DAAU,iBAAiB;4DAAe,QAAQ;4DAAQ,SAAS;4DAAe,cAAc;4DAAO,QAAQ;4DAAW,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;kEAG9N,8OAAC;wDAAO,OAAO;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;4DAAU,iBAAiB;4DAAe,QAAQ;4DAAQ,SAAS;4DAAe,cAAc;4DAAO,QAAQ;4DAAW,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;kEAG9N,8OAAC;wDAAO,OAAO;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;4DAAU,iBAAiB;4DAAe,QAAQ;4DAAQ,SAAS;4DAAe,cAAc;4DAAO,QAAQ;4DAAW,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;0CAQpO,8OAAC;gCAAI,OAAO;oCAAE,iBAAiB;oCAAS,cAAc;oCAAO,WAAW;gCAA4B;;kDAClG,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DACnF,8OAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;oDAAU,OAAO;oDAAS,YAAY;gDAAO;0DAAG;;;;;;0DAGzM,8OAAC;;kEACC,8OAAC;wDAAI,OAAO;4DAAE,YAAY;4DAAO,OAAO;wDAAU;kEAAG;;;;;;kEACrD,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAU,OAAO;wDAAU;kEAAG;;;;;;;;;;;;;;;;;;kDAI1D,8OAAC;wCAAI,OAAO;4CAAE,aAAa;4CAAQ,cAAc;4CAAQ,cAAc;wCAAO;kDAC5E,cAAA,8OAAC;4CAAE,OAAO;gDAAE,OAAO;gDAAW,YAAY;gDAAO,QAAQ;4CAAE;sDAAG;;;;;;;;;;;kDAMhE,8OAAC;wCAAI,OAAO;4CAAE,iBAAiB;4CAAW,QAAQ;4CAAS,SAAS;4CAAQ,YAAY;4CAAU,gBAAgB;4CAAU,OAAO;4CAAW,UAAU;wCAAO;kDAAG;;;;;;kDAIlK,8OAAC;wCAAI,OAAO;4CAAE,SAAS;wCAAe;;0DACpC,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,gBAAgB;oDAAiB,YAAY;oDAAU,cAAc;gDAAU;0DAC5G,cAAA,8OAAC;oDAAK,OAAO;wDAAE,OAAO;wDAAW,UAAU;oDAAW;8DAAG;;;;;;;;;;;0DAE3D,8OAAC;gDAAI,OAAO;oDAAE,SAAS;oDAAQ,gBAAgB;oDAAgB,YAAY;oDAAW,WAAW;gDAAoB;;kEACnH,8OAAC;wDAAO,OAAO;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;4DAAU,iBAAiB;4DAAe,QAAQ;4DAAQ,SAAS;4DAAe,cAAc;4DAAO,QAAQ;4DAAW,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;kEAG9N,8OAAC;wDAAO,OAAO;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;4DAAU,iBAAiB;4DAAe,QAAQ;4DAAQ,SAAS;4DAAe,cAAc;4DAAO,QAAQ;4DAAW,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;kEAG9N,8OAAC;wDAAO,OAAO;4DAAE,SAAS;4DAAQ,YAAY;4DAAU,KAAK;4DAAU,iBAAiB;4DAAe,QAAQ;4DAAQ,SAAS;4DAAe,cAAc;4DAAO,QAAQ;4DAAW,OAAO;4DAAW,YAAY;wDAAM;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAStO,8OAAC;wBAAI,OAAO;4BAAE,iBAAiB;4BAAS,cAAc;4BAAO,SAAS;4BAAQ,QAAQ;4BAAe,WAAW;wBAA4B;;0CAC1I,8OAAC;gCAAG,OAAO;oCAAE,UAAU;oCAAY,YAAY;oCAAO,OAAO;oCAAW,cAAc;gCAAO;0CAAG;;;;;;0CAGhG,8OAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAU;;kDACrE,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DAClE,8OAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,8OAAC;wDAAI,OAAO;4DAAE,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAQ,QAAQ;4DAAQ,SAAS;4DAAQ,YAAY;4DAAU,gBAAgB;4DAAU,OAAO;4DAAS,UAAU;4DAAY,YAAY;wDAAO;kEAAG;;;;;;kEAG3N,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAY,QAAQ;4DAAQ,OAAO;4DAAQ,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAW,QAAQ;4DAAW,QAAQ;wDAAkB;;;;;;;;;;;;0DAErL,8OAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAG3D,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DAClE,8OAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,8OAAC;wDAAI,OAAO;4DAAE,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAQ,QAAQ;4DAAQ,SAAS;4DAAQ,YAAY;4DAAU,gBAAgB;4DAAU,OAAO;4DAAS,UAAU;4DAAY,YAAY;wDAAO;kEAAG;;;;;;kEAG3N,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAY,QAAQ;4DAAQ,OAAO;4DAAQ,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAW,QAAQ;4DAAW,QAAQ;wDAAkB;;;;;;;;;;;;0DAErL,8OAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAG3D,8OAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DAClE,8OAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,8OAAC;wDAAI,OAAO;4DAAE,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAQ,QAAQ;4DAAQ,SAAS;4DAAQ,YAAY;4DAAU,gBAAgB;4DAAU,OAAO;4DAAS,UAAU;4DAAY,YAAY;wDAAO;kEAAG;;;;;;kEAG3N,8OAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAY,QAAQ;4DAAQ,OAAO;4DAAQ,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAW,QAAQ;4DAAW,QAAQ;wDAAkB;;;;;;;;;;;;0DAErL,8OAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAI7D,8OAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAQ,YAAY;oCAAQ,WAAW;gCAAoB;0CAClF,cAAA,8OAAC;oCAAE,MAAK;oCAAI,OAAO;wCAAE,OAAO;wCAAW,gBAAgB;wCAAQ,UAAU;oCAAW;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnG", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1311, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,OAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}