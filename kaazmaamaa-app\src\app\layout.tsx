import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import { PostsProvider } from "@/contexts/PostsContext";
import { OnlineUsersProvider } from "@/contexts/OnlineUsersContext";
import { WorkersProvider } from "@/contexts/WorkersContext";
import { SuppliersProvider } from "@/contexts/SuppliersContext";
import { CompaniesProvider } from "@/contexts/CompaniesContext";
import { SocialProvider } from "@/contexts/SocialContext";
import { PaymentProvider } from "@/contexts/PaymentContext";
import { BartaProvider } from "@/contexts/BartaContext";
import { Toaster } from "@/components/ui/sonner";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "KAAZMAAMAA - Social Network for Workers, Suppliers & Companies",
  description: "Connect, share, and collaborate in the professional community",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <OnlineUsersProvider>
            <PaymentProvider>
              <SocialProvider>
                <CompaniesProvider>
                  <SuppliersProvider>
                    <WorkersProvider>
                      <BartaProvider>
                        <PostsProvider>
                          {children}
                          <Toaster />
                        </PostsProvider>
                      </BartaProvider>
                    </WorkersProvider>
                  </SuppliersProvider>
                </CompaniesProvider>
              </SocialProvider>
            </PaymentProvider>
          </OnlineUsersProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
