'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useWorkers } from '@/contexts/WorkersContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  User,
  Briefcase,
  FileCheck,
  Heart,
  Shield,
  Building,
  CreditCard,
  Award
} from 'lucide-react'
import { toast } from 'sonner'

interface DocumentUpload {
  type: string
  file: File | null
  uploaded: boolean
  required: boolean
  icon: any
  label: string
  description: string
}

export default function CreateWorkerProfilePage() {
  const router = useRouter()
  const { user } = useAuth()
  const { createWorkerProfile } = useWorkers()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Personal Information
  const [personalInfo, setPersonalInfo] = useState({
    workerName: '',
    nationality: '',
    kofilName: '',
    kofilPhone: '',
    email: '',
    phone: '',
    whatsapp: '',
    address: '',
    age: '',
    profileImage: ''
  })

  // Identification Information
  const [identificationInfo, setIdentificationInfo] = useState({
    iqamaNumber: '',
    passportNumber: '',
    crNumber: ''
  })

  // Professional Information
  const [professionalInfo, setProfessionalInfo] = useState({
    workerCategory: '',
    title: '',
    specialty: '',
    experience: '',
    skills: '',
    languages: '',
    expectedSalaryPerHour: '',
    currency: 'SAR',
    salaryPayingDuration: 'monthly',
    availability: 'immediate'
  })

  // Document uploads with all required documents
  const [documents, setDocuments] = useState<DocumentUpload[]>([
    {
      type: 'absher_iqama',
      file: null,
      uploaded: false,
      required: true,
      icon: CreditCard,
      label: 'Absher IQAMA Copy',
      description: 'Official IQAMA document from Absher platform (PDF only)'
    },
    {
      type: 'muqeem',
      file: null,
      uploaded: false,
      required: true,
      icon: FileText,
      label: 'Muqeem Copy',
      description: 'Muqeem residence document (PDF only)'
    },
    {
      type: 'medical',
      file: null,
      uploaded: false,
      required: true,
      icon: Heart,
      label: 'Medical Copy',
      description: 'Medical examination certificate (PDF only)'
    },
    {
      type: 'insurance',
      file: null,
      uploaded: false,
      required: true,
      icon: Shield,
      label: 'Insurance Copy',
      description: 'Health insurance document (PDF only)'
    },
    {
      type: 'chamber',
      file: null,
      uploaded: false,
      required: false,
      icon: Building,
      label: 'Chamber Copy',
      description: 'Chamber of Commerce certificate (PDF only) - If Available'
    },
    {
      type: 'ajeer',
      file: null,
      uploaded: false,
      required: false,
      icon: Briefcase,
      label: 'Ajeer Copy',
      description: 'Ajeer work permit document (PDF only) - If Available'
    },
    {
      type: 'experience',
      file: null,
      uploaded: false,
      required: true,
      icon: Award,
      label: 'All Experience Copies',
      description: 'All work experience certificates (PDF only)'
    },
    {
      type: 'fitness',
      file: null,
      uploaded: false,
      required: true,
      icon: CheckCircle,
      label: 'Fitness Copy',
      description: 'Physical fitness certificate (PDF only)'
    },
    {
      type: 'passport',
      file: null,
      uploaded: false,
      required: true,
      icon: CreditCard,
      label: 'Passport Copy',
      description: 'Valid passport document (PDF only)'
    },
    {
      type: 'visa',
      file: null,
      uploaded: false,
      required: true,
      icon: FileCheck,
      label: 'Visa Copy',
      description: 'Current visa document (PDF only)'
    },
    {
      type: 'gosi',
      file: null,
      uploaded: false,
      required: false,
      icon: Shield,
      label: 'GOSI Copy',
      description: 'GOSI social insurance document (PDF only) - If Available'
    }
  ])

  // Preferences
  const [preferences, setPreferences] = useState({
    workType: 'full_time' as 'full_time' | 'part_time' | 'contract' | 'freelance',
    sideLocationInterest: [] as string[],
    companyNameInterest: [] as string[],
    preferredLocations: [] as string[],
    willingToRelocate: false,
    remoteWork: false,
    neededDocuments: [] as string[],
    otherRequirements: ''
  })

  const handlePersonalInfoChange = (field: string, value: string) => {
    setPersonalInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleIdentificationChange = (field: string, value: string) => {
    setIdentificationInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleProfessionalChange = (field: string, value: string) => {
    setProfessionalInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleDocumentUpload = (index: number, file: File) => {
    if (file.type !== 'application/pdf') {
      toast.error('Only PDF files are allowed')
      return
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error('File size must be less than 10MB')
      return
    }

    const updatedDocuments = [...documents]
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      file,
      uploaded: true
    }
    setDocuments(updatedDocuments)
    toast.success(`${updatedDocuments[index].label} uploaded successfully!`)
  }

  const removeDocument = (index: number) => {
    const updatedDocuments = [...documents]
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      file: null,
      uploaded: false
    }
    setDocuments(updatedDocuments)
    toast.info('Document removed')
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(personalInfo.workerName && personalInfo.phone && personalInfo.email)
      case 2:
        return !!(identificationInfo.iqamaNumber && identificationInfo.passportNumber)
      case 3:
        return !!(professionalInfo.workerCategory && professionalInfo.title)
      case 4:
        const requiredDocs = documents.filter(doc => doc.required)
        return requiredDocs.every(doc => doc.uploaded)
      case 5:
        return true
      default:
        return false
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 5))
    } else {
      toast.error('Please fill in all required fields before proceeding')
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSubmit = async () => {
    if (!validateStep(5)) {
      toast.error('Please complete all required sections')
      return
    }

    setIsSubmitting(true)

    try {
      // Convert uploaded files to document objects
      const workerDocuments = documents
        .filter(doc => doc.uploaded && doc.file)
        .map(doc => ({
          name: doc.file!.name,
          type: doc.type as any,
          url: URL.createObjectURL(doc.file!), // In real app, upload to cloud storage
          uploadedAt: new Date().toISOString(),
          isValidated: false,
          fileSize: doc.file!.size,
          fileName: doc.file!.name
        }))

      const newProfile = {
        userId: user?.email || personalInfo.email || 'current-user',
        personalInfo: {
          ...personalInfo,
          age: personalInfo.age ? parseInt(personalInfo.age) : undefined
        },
        identificationInfo,
        professionalInfo,
        documents: workerDocuments,
        videos: [],
        workExperience: [],
        lifecycle: {
          status: 'active' as const,
          joinDate: new Date().toISOString(),
          vacationDates: [],
          leaveDate: null,
          notes: ''
        },
        preferences,
        isLiveStreaming: false,
        isVerified: false,
        rating: 0,
        totalReviews: 0
      }

      createWorkerProfile(newProfile)
      toast.success('Worker profile created successfully! 🎉 Please sign in to continue.')

      // Redirect to login page after profile creation
      router.push('/auth/login')
    } catch (error) {
      console.error('Error creating profile:', error)
      toast.error('Failed to create profile. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Personal Information'
      case 2: return 'Identification Details'
      case 3: return 'Professional Information'
      case 4: return 'Document Upload'
      case 5: return 'Work Preferences'
      default: return 'Profile Setup'
    }
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return User
      case 2: return CreditCard
      case 3: return Briefcase
      case 4: return FileText
      case 5: return CheckCircle
      default: return User
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/workers')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Workers
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Create Worker Profile</h1>
            </div>
            <Badge variant="outline" className="text-blue-600">
              Step {currentStep} of 5
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {[1, 2, 3, 4, 5].map((step) => {
              const StepIcon = getStepIcon(step)
              const isActive = step === currentStep
              const isCompleted = step < currentStep
              const isValid = validateStep(step)

              return (
                <div key={step} className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                      isCompleted
                        ? 'bg-green-600 border-green-600 text-white'
                        : isActive
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : isValid
                        ? 'bg-gray-100 border-gray-300 text-gray-600'
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-6 w-6" />
                    ) : (
                      <StepIcon className="h-6 w-6" />
                    )}
                  </div>
                  <span className={`text-xs mt-2 text-center ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                    {getStepTitle(step)}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Form Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {(() => {
                const StepIcon = getStepIcon(currentStep)
                return <StepIcon className="h-5 w-5 text-blue-600" />
              })()}
              <span>{getStepTitle(currentStep)}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="workerName">Full Name *</Label>
                    <Input
                      id="workerName"
                      value={personalInfo.workerName}
                      onChange={(e) => handlePersonalInfoChange('workerName', e.target.value)}
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationality">Nationality *</Label>
                    <Input
                      id="nationality"
                      value={personalInfo.nationality}
                      onChange={(e) => handlePersonalInfoChange('nationality', e.target.value)}
                      placeholder="Your nationality"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="kofilName">Kofil Name</Label>
                    <Input
                      id="kofilName"
                      value={personalInfo.kofilName}
                      onChange={(e) => handlePersonalInfoChange('kofilName', e.target.value)}
                      placeholder="Kofil sponsor name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="kofilPhone">Kofil Phone</Label>
                    <Input
                      id="kofilPhone"
                      value={personalInfo.kofilPhone}
                      onChange={(e) => handlePersonalInfoChange('kofilPhone', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={personalInfo.email}
                      onChange={(e) => handlePersonalInfoChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={personalInfo.phone}
                      onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp">WhatsApp Number</Label>
                    <Input
                      id="whatsapp"
                      value={personalInfo.whatsapp}
                      onChange={(e) => handlePersonalInfoChange('whatsapp', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="age">Age</Label>
                    <Input
                      id="age"
                      type="number"
                      value={personalInfo.age}
                      onChange={(e) => handlePersonalInfoChange('age', e.target.value)}
                      placeholder="Your age"
                      min="18"
                      max="65"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={personalInfo.address}
                    onChange={(e) => handlePersonalInfoChange('address', e.target.value)}
                    placeholder="Your complete address"
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Step 2: Identification Information */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="iqamaNumber">IQAMA Number *</Label>
                    <Input
                      id="iqamaNumber"
                      value={identificationInfo.iqamaNumber}
                      onChange={(e) => handleIdentificationChange('iqamaNumber', e.target.value)}
                      placeholder="Enter IQAMA number"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="passportNumber">Passport Number *</Label>
                    <Input
                      id="passportNumber"
                      value={identificationInfo.passportNumber}
                      onChange={(e) => handleIdentificationChange('passportNumber', e.target.value)}
                      placeholder="Enter passport number"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="crNumber">C.R. Number (Optional)</Label>
                    <Input
                      id="crNumber"
                      value={identificationInfo.crNumber}
                      onChange={(e) => handleIdentificationChange('crNumber', e.target.value)}
                      placeholder="Commercial registration number"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Professional Information */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="workerCategory">Worker Category *</Label>
                    <Input
                      id="workerCategory"
                      value={professionalInfo.workerCategory}
                      onChange={(e) => handleProfessionalChange('workerCategory', e.target.value)}
                      placeholder="e.g., Construction, IT, Healthcare"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="title">Job Title *</Label>
                    <Input
                      id="title"
                      value={professionalInfo.title}
                      onChange={(e) => handleProfessionalChange('title', e.target.value)}
                      placeholder="e.g., Senior Developer, Electrician"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="specialty">Specialty</Label>
                    <Input
                      id="specialty"
                      value={professionalInfo.specialty}
                      onChange={(e) => handleProfessionalChange('specialty', e.target.value)}
                      placeholder="Your area of specialization"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="experience">Years of Experience</Label>
                    <Input
                      id="experience"
                      type="number"
                      value={professionalInfo.experience}
                      onChange={(e) => handleProfessionalChange('experience', e.target.value)}
                      placeholder="Years of experience"
                      min="0"
                      max="50"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Document Upload */}
            {currentStep === 4 && (
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-blue-800 mb-2">📄 Document Upload Requirements</h3>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• All documents must be in PDF format only</li>
                    <li>• Maximum file size: 10MB per document</li>
                    <li>• Required documents must be uploaded to proceed</li>
                    <li>• Optional documents can be uploaded later</li>
                  </ul>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {documents.map((doc, index) => {
                    const IconComponent = doc.icon
                    return (
                      <div key={doc.type} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <IconComponent className="h-5 w-5 text-blue-600" />
                            <span className="font-medium">{doc.label}</span>
                            {doc.required && <span className="text-red-500">*</span>}
                          </div>
                          {doc.uploaded && (
                            <CheckCircle className="h-5 w-5 text-green-600" />
                          )}
                        </div>

                        <p className="text-sm text-gray-600">{doc.description}</p>

                        {!doc.uploaded ? (
                          <div className="relative">
                            <input
                              type="file"
                              accept=".pdf"
                              onChange={(e) => {
                                const file = e.target.files?.[0]
                                if (file) {
                                  handleDocumentUpload(index, file)
                                }
                              }}
                              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              className="w-full flex items-center justify-center space-x-2"
                            >
                              <Upload className="h-4 w-4" />
                              <span>Upload PDF</span>
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-green-600 flex items-center">
                              <CheckCircle className="h-4 w-4 mr-1" />
                              {doc.file?.name}
                            </span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeDocument(index)}
                              className="text-red-600 hover:text-red-700"
                            >
                              Remove
                            </Button>
                          </div>
                        )}
                      </div>
                    )
                  })}
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="font-semibold text-yellow-800 mb-2">💰 Document Access Information</h3>
                  <p className="text-sm text-yellow-700">
                    Once you complete your profile, your documents will be available for download by suppliers and companies.
                    They will need to complete a payment process to access your documents and an auto-generated CV.
                  </p>
                </div>
              </div>
            )}

            {/* Step 5: Work Preferences */}
            {currentStep === 5 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="workType">Work Type</Label>
                    <select
                      id="workType"
                      value={preferences.workType}
                      onChange={(e) => setPreferences(prev => ({ ...prev, workType: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="full_time">Full Time</option>
                      <option value="part_time">Part Time</option>
                      <option value="contract">Contract</option>
                      <option value="freelance">Freelance</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label>
                      <input
                        type="checkbox"
                        checked={preferences.willingToRelocate}
                        onChange={(e) => setPreferences(prev => ({ ...prev, willingToRelocate: e.target.checked }))}
                        className="mr-2"
                      />
                      Willing to Relocate
                    </Label>
                  </div>
                  <div className="space-y-2">
                    <Label>
                      <input
                        type="checkbox"
                        checked={preferences.remoteWork}
                        onChange={(e) => setPreferences(prev => ({ ...prev, remoteWork: e.target.checked }))}
                        className="mr-2"
                      />
                      Available for Remote Work
                    </Label>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="otherRequirements">Other Requirements</Label>
                  <Textarea
                    id="otherRequirements"
                    value={preferences.otherRequirements}
                    onChange={(e) => setPreferences(prev => ({ ...prev, otherRequirements: e.target.value }))}
                    placeholder="Any additional requirements or notes"
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
              >
                Previous
              </Button>

              {currentStep < 5 ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep(currentStep)}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleSubmit}
                  disabled={isSubmitting || !validateStep(5)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isSubmitting ? 'Creating Profile...' : 'Create Profile'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
