"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PostsContext */ \"(app-pages-browser)/./src/contexts/PostsContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FeedPage() {\n    _s();\n    const { user, userRole, getUserProfile, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { posts, addPost, likePost, addComment, sharePost } = (0,_contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Create Post States\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postText, setPostText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFeeling, setSelectedFeeling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taggedUsers, setTaggedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postPrivacy, setPostPrivacy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('public');\n    // Comments States\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [commentTexts, setCommentTexts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAllComments, setShowAllComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Share States\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shareText, setShareText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Other States\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeStory, setActiveStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLocationPicker, setShowLocationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTagPicker, setShowTagPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (user && userRole) {\n                const profile = getUserProfile();\n                setUserProfile(profile);\n                console.log('User profile loaded:', profile);\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        userRole,\n        getUserProfile\n    ]);\n    // Redirect to home if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/');\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Helper function to get user's display name\n    const getUserDisplayName = ()=>{\n        if (!userProfile) return 'User';\n        if (userRole === 'worker') {\n            var _userProfile_personalInfo;\n            return ((_userProfile_personalInfo = userProfile.personalInfo) === null || _userProfile_personalInfo === void 0 ? void 0 : _userProfile_personalInfo.workerName) || 'Worker';\n        } else if (userRole === 'supplier') {\n            var _userProfile_personalInfo1;\n            return ((_userProfile_personalInfo1 = userProfile.personalInfo) === null || _userProfile_personalInfo1 === void 0 ? void 0 : _userProfile_personalInfo1.supplierName) || 'Supplier';\n        } else if (userRole === 'company') {\n            var _userProfile_companyInfo;\n            return ((_userProfile_companyInfo = userProfile.companyInfo) === null || _userProfile_companyInfo === void 0 ? void 0 : _userProfile_companyInfo.companyName) || 'Company';\n        }\n        return 'User';\n    };\n    // Helper function to get user's avatar/initial\n    const getUserInitial = ()=>{\n        const name = getUserDisplayName();\n        return name.charAt(0).toUpperCase();\n    };\n    // Helper function to get user's role emoji\n    const getUserRoleEmoji = ()=>{\n        switch(userRole){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Helper functions for Facebook-style features\n    const handleLikePost = (postId)=>{\n        likePost(postId);\n    };\n    const handleCommentSubmit = (postId)=>{\n        const commentText = commentTexts[postId];\n        if (!(commentText === null || commentText === void 0 ? void 0 : commentText.trim()) || !user) return;\n        addComment(postId, commentText, {\n            id: user.email,\n            name: getUserDisplayName(),\n            role: userRole || 'worker'\n        });\n        setCommentTexts((prev)=>({\n                ...prev,\n                [postId]: ''\n            }));\n    };\n    const handleSharePost = (postId)=>{\n        if (!shareText.trim()) {\n            sharePost(postId);\n        } else {\n            // Create a new post that shares the original\n            const originalPost = posts.find((p)=>p.id === postId);\n            if (originalPost) {\n                const newPost = {\n                    user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                    user_name: getUserDisplayName(),\n                    user_role: userRole || 'worker',\n                    content: shareText,\n                    post_type: 'shared',\n                    visibility: 'public'\n                };\n                addPost(newPost);\n            }\n        }\n        setShowShareModal(null);\n        setShareText('');\n    };\n    const toggleComments = (postId)=>{\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !prev[postId]\n            }));\n    };\n    const formatTimeAgo = (dateString)=>{\n        const now = new Date();\n        const postDate = new Date(dateString);\n        const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d\");\n        const diffInWeeks = Math.floor(diffInDays / 7);\n        return \"\".concat(diffInWeeks, \"w\");\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'worker':\n                return '#2563eb';\n            case 'supplier':\n                return '#059669';\n            case 'company':\n                return '#7c3aed';\n            default:\n                return '#6b7280';\n        }\n    };\n    const getRoleEmoji = (role)=>{\n        switch(role){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Show loading if still authenticating\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#f0f2f5'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '2rem',\n                            marginBottom: '1rem'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#65676b'\n                        },\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: '#f0f2f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: '#1877f2',\n                    padding: '0.75rem 1rem',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: 'white',\n                                        margin: 0\n                                    },\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'rgba(255,255,255,0.2)',\n                                        borderRadius: '20px',\n                                        padding: '0.5rem 1rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search KAAZMAAMAA...\",\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            color: 'white',\n                                            outline: 'none',\n                                            fontSize: '0.875rem'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.75rem',\n                                        color: 'white'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                borderRadius: '50%',\n                                                width: '2rem',\n                                                height: '2rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '0.875rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: getUserInitial()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const profilePath = userRole === 'worker' ? '/workers/profile' : userRole === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                router.push(profilePath);\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Go Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                await signOut();\n                                                router.push('/');\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 2fr 1fr',\n                    gap: '1rem',\n                    padding: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Quick Access\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/workers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDD27\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Workers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/suppliers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e8f5e8',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Suppliers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/companies\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#f3e8ff',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFE2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/barta\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#fff3cd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Barta Messenger\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.75rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    color: 'white',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: getUserInitial()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"What's on your mind, \".concat(getUserDisplayName(), \"?\"),\n                                                onClick: ()=>setShowCreatePost(true),\n                                                readOnly: true,\n                                                style: {\n                                                    flex: 1,\n                                                    backgroundColor: '#f0f2f5',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '0.75rem 1rem',\n                                                    outline: 'none',\n                                                    fontSize: '1rem',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-around',\n                                            paddingTop: '0.75rem',\n                                            borderTop: '1px solid #e4e6ea'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDCF7 Photo/Video\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDE0A Feeling/Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            posts.map((post)=>{\n                                // Helper function to get role color\n                                const getRoleColor = (role)=>{\n                                    switch(role){\n                                        case 'worker':\n                                            return '#2563eb';\n                                        case 'supplier':\n                                            return '#059669';\n                                        case 'company':\n                                            return '#7c3aed';\n                                        default:\n                                            return '#6b7280';\n                                    }\n                                };\n                                // Helper function to get role emoji\n                                const getRoleEmoji = (role)=>{\n                                    switch(role){\n                                        case 'worker':\n                                            return '🔧';\n                                        case 'supplier':\n                                            return '🏭';\n                                        case 'company':\n                                            return '🏢';\n                                        default:\n                                            return '👤';\n                                    }\n                                };\n                                // Helper function to format time\n                                const getTimeAgo = (dateString)=>{\n                                    const now = new Date();\n                                    const postDate = new Date(dateString);\n                                    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n                                    if (diffInMinutes < 1) return 'Just now';\n                                    if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \" minutes ago\");\n                                    const diffInHours = Math.floor(diffInMinutes / 60);\n                                    if (diffInHours < 24) return \"\".concat(diffInHours, \" hours ago\");\n                                    const diffInDays = Math.floor(diffInHours / 24);\n                                    return \"\".concat(diffInDays, \" days ago\");\n                                };\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '8px',\n                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(post.user_role),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: post.user_name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // Navigate to user's profile based on their role\n                                                                const profilePath = post.user_role === 'worker' ? '/workers/profile' : post.user_role === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                                router.push(\"\".concat(profilePath, \"?user=\").concat(encodeURIComponent(post.user_id)));\n                                                            },\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                padding: 0,\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21',\n                                                                cursor: 'pointer',\n                                                                fontSize: 'inherit',\n                                                                textAlign: 'left'\n                                                            },\n                                                            onMouseEnter: (e)=>e.target.style.textDecoration = 'underline',\n                                                            onMouseLeave: (e)=>e.target.style.textDecoration = 'none',\n                                                            children: post.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 416,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                getTimeAgo(post.created_at),\n                                                                \" • \",\n                                                                getRoleEmoji(post.user_role),\n                                                                \" \",\n                                                                post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: '1rem',\n                                                paddingRight: '1rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#1c1e21',\n                                                    lineHeight: '1.5',\n                                                    margin: 0\n                                                },\n                                                children: post.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this),\n                                        post.media_urls && post.media_urls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f2f5',\n                                                height: '250px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                color: '#65676b',\n                                                fontSize: '3rem'\n                                            },\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '0.75rem 1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '0.75rem'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#65676b',\n                                                            fontSize: '0.875rem'\n                                                        },\n                                                        children: [\n                                                            \"\\uD83D\\uDC4D \",\n                                                            post.likes,\n                                                            \" likes • \\uD83D\\uDCAC \",\n                                                            post.comments,\n                                                            \" comments\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-around',\n                                                        paddingTop: '0.75rem',\n                                                        borderTop: '1px solid #e4e6ea'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>likePost(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: post.liked_by_user ? '#1877f2' : '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDC4D Like\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDCAC Comment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDCE4 Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 487,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this);\n                            }),\n                            posts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\n                                    padding: '3rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#1c1e21',\n                                            marginBottom: '0.5rem'\n                                        },\n                                        children: \"No posts yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Be the first to share something with your professional network!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Online Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#7c3aed',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Modern Tech Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 519,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#2563eb',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Fatima Al-Zahra\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#059669',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Khalid Construction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '2rem',\n                                    paddingTop: '1rem',\n                                    borderTop: '1px solid #e4e6ea'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    style: {\n                                        color: '#1877f2',\n                                        textDecoration: 'none',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 544,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            showCreatePost && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreatePost(false),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 582,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.75rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        color: 'white',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: getUserInitial()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 618,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                color: '#65676b',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                getUserRoleEmoji(),\n                                                \" \",\n                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                \" • \\uD83C\\uDF0D Public\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: postText,\n                                onChange: (e)=>setPostText(e.target.value),\n                                placeholder: \"What's on your mind?\",\n                                style: {\n                                    width: '100%',\n                                    minHeight: '120px',\n                                    border: 'none',\n                                    outline: 'none',\n                                    fontSize: '1.5rem',\n                                    color: '#1c1e21',\n                                    resize: 'none',\n                                    fontFamily: 'inherit'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 626,\n                            columnNumber: 13\n                        }, this),\n                        selectedFeeling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '20px',\n                                    padding: '0.5rem 1rem',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: selectedFeeling\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedFeeling(''),\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            color: '#65676b'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 647,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 646,\n                            columnNumber: 15\n                        }, this),\n                        selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'relative',\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '8px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Image Preview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedImage(null),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '0.5rem',\n                                            right: '0.5rem',\n                                            backgroundColor: 'rgba(0,0,0,0.5)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '2rem',\n                                            height: '2rem',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 683,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 674,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 673,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderTop: '1px solid #e4e6ea'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: \"Add to your post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '0.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImage('image'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Photo/Video\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedFeeling('😊 feeling happy'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Feeling/Activity\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Tag People\",\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 745,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Check In\",\n                                                    children: \"\\uD83D\\uDCCD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 762,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (!postText.trim()) return;\n                                        // Create the post using the posts context\n                                        const newPost = {\n                                            user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                                            user_name: getUserDisplayName(),\n                                            user_role: userRole || 'worker',\n                                            content: postText,\n                                            media_urls: selectedImage ? [\n                                                selectedImage\n                                            ] : undefined,\n                                            post_type: selectedImage ? 'image' : 'text',\n                                            visibility: 'public'\n                                        };\n                                        addPost(newPost);\n                                        // Reset form\n                                        setPostText('');\n                                        setSelectedFeeling('');\n                                        setSelectedImage(null);\n                                        setShowCreatePost(false);\n                                        // Show success message\n                                        alert('Post created successfully!');\n                                    },\n                                    disabled: !postText.trim(),\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                                        color: postText.trim() ? 'white' : '#bcc0c4',\n                                        border: 'none',\n                                        borderRadius: '6px',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '600',\n                                        cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                                    },\n                                    children: \"Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 783,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 563,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 551,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"mG1is2bfnznMFmJKz7POH9TiagY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});