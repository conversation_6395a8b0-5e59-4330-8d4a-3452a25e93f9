'use client'

import { createContext, useContext, useState, useEffect } from 'react'

export type UserRole = 'worker' | 'supplier' | 'company'

interface User {
  id: string
  email: string
}

interface AuthContextType {
  user: User | null
  userRole: UserRole | null
  loading: boolean
  signUp: (email: string, password: string, role: UserRole) => Promise<any>
  signIn: (email: string, password: string) => Promise<any>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true) // Start with loading true
  const [isInitialized, setIsInitialized] = useState(false)

  // Load authentication state from localStorage on mount
  useEffect(() => {
    const loadAuthState = () => {
      try {
        const savedUser = localStorage.getItem('kaazmaamaa-user')
        const savedRole = localStorage.getItem('kaazmaamaa-user-role')

        if (savedUser && savedRole) {
          const parsedUser = JSON.parse(savedUser)
          setUser(parsedUser)
          setUserRole(savedRole as UserRole)
          console.log('Loaded user from localStorage:', parsedUser.email, savedRole)
        } else {
          console.log('No saved authentication found')
        }
      } catch (error) {
        console.error('Error loading auth state from localStorage:', error)
      } finally {
        setLoading(false)
        setIsInitialized(true)
      }
    }

    loadAuthState()
  }, [])

  const signUp = async (email: string, password: string, role: UserRole) => {
    // Mock implementation for now
    console.log('Sign up:', { email, password, role })
    const mockUser = { id: Date.now().toString(), email }

    // Save to state
    setUser(mockUser)
    setUserRole(role)

    // Save to localStorage
    try {
      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))
      localStorage.setItem('kaazmaamaa-user-role', role)
      console.log('User authentication saved to localStorage')
    } catch (error) {
      console.error('Error saving auth to localStorage:', error)
    }

    return { data: { user: mockUser }, error: null }
  }

  const signIn = async (email: string, password: string) => {
    // Mock implementation for now
    console.log('Sign in:', { email, password })
    const mockUser = { id: Date.now().toString(), email }
    const defaultRole: UserRole = 'worker' // Default role for demo

    // Save to state
    setUser(mockUser)
    setUserRole(defaultRole)

    // Save to localStorage
    try {
      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))
      localStorage.setItem('kaazmaamaa-user-role', defaultRole)
      console.log('User authentication saved to localStorage')
    } catch (error) {
      console.error('Error saving auth to localStorage:', error)
    }

    return { data: { user: mockUser }, error: null }
  }

  const signOut = async () => {
    // Clear state
    setUser(null)
    setUserRole(null)

    // Clear localStorage
    try {
      localStorage.removeItem('kaazmaamaa-user')
      localStorage.removeItem('kaazmaamaa-user-role')
      console.log('User authentication cleared from localStorage')
    } catch (error) {
      console.error('Error clearing auth from localStorage:', error)
    }
  }

  const value = {
    user,
    userRole,
    loading,
    signUp,
    signIn,
    signOut,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
