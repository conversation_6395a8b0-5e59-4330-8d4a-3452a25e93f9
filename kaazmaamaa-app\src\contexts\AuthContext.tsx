'use client'

import { createContext, useContext, useState, useEffect } from 'react'

export type UserRole = 'worker' | 'supplier' | 'company'

interface User {
  id: string
  email: string
}

interface AuthContextType {
  user: User | null
  userRole: UserRole | null
  loading: boolean
  signUp: (email: string, password: string, role: UserRole) => Promise<any>
  signIn: (email: string, password: string) => Promise<any>
  signOut: () => Promise<void>
  getUserProfile: () => any | null
  debugProfiles: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userRole, setUserRole] = useState<UserRole | null>(null)
  const [loading, setLoading] = useState(true) // Start with loading true
  const [isInitialized, setIsInitialized] = useState(false)

  // Load authentication state from localStorage on mount
  useEffect(() => {
    const loadAuthState = () => {
      try {
        const savedUser = localStorage.getItem('kaazmaamaa-user')
        const savedRole = localStorage.getItem('kaazmaamaa-user-role')

        if (savedUser && savedRole) {
          const parsedUser = JSON.parse(savedUser)
          setUser(parsedUser)
          setUserRole(savedRole as UserRole)
          console.log('Loaded user from localStorage:', parsedUser.email, savedRole)
        } else {
          console.log('No saved authentication found')
        }
      } catch (error) {
        console.error('Error loading auth state from localStorage:', error)
      } finally {
        setLoading(false)
        setIsInitialized(true)
      }
    }

    loadAuthState()
  }, [])

  const signUp = async (email: string, password: string, role: UserRole) => {
    // Mock implementation for now
    console.log('Sign up:', { email, password, role })
    const mockUser = { id: Date.now().toString(), email }

    // Save to state
    setUser(mockUser)
    setUserRole(role)

    // Save to localStorage
    try {
      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))
      localStorage.setItem('kaazmaamaa-user-role', role)
      console.log('User authentication saved to localStorage')
    } catch (error) {
      console.error('Error saving auth to localStorage:', error)
    }

    return { data: { user: mockUser }, error: null }
  }

  // Function to detect user's profile type by checking all profile contexts
  const detectUserProfileType = (userEmail: string): UserRole => {
    console.log('🔍 Detecting profile type for email:', userEmail)
    console.log('🔍 All localStorage keys:', Object.keys(localStorage))

    try {
      // Check Workers profiles
      const workersData = localStorage.getItem('kaazmaamaa-workers')
      console.log('📋 Workers data:', workersData ? 'Found' : 'Not found')
      if (workersData) {
        const workers = JSON.parse(workersData)
        console.log('👷 Checking', workers.length, 'worker profiles')
        const workerProfile = workers.find((worker: any) => {
          const emailMatch = worker.personalInfo?.email === userEmail
          const userIdMatch = worker.userId === userEmail
          console.log('👷 Worker check:', worker.personalInfo?.workerName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)
          return emailMatch || userIdMatch
        })
        if (workerProfile) {
          console.log('✅ Found worker profile for:', userEmail, workerProfile.personalInfo?.workerName)
          return 'worker'
        }
      }

      // Check Suppliers profiles
      const suppliersData = localStorage.getItem('kaazmaamaa-suppliers')
      console.log('📋 Suppliers data:', suppliersData ? 'Found' : 'Not found')
      if (suppliersData) {
        const suppliers = JSON.parse(suppliersData)
        console.log('🏭 Checking', suppliers.length, 'supplier profiles')
        const supplierProfile = suppliers.find((supplier: any) => {
          const emailMatch = supplier.personalInfo?.email === userEmail
          const userIdMatch = supplier.userId === userEmail
          console.log('🏭 Supplier check:', supplier.personalInfo?.supplierName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)
          console.log('🏭 Supplier data structure:', { personalInfo: supplier.personalInfo, userId: supplier.userId })
          return emailMatch || userIdMatch
        })
        if (supplierProfile) {
          console.log('✅ Found supplier profile for:', userEmail, supplierProfile.personalInfo?.supplierName)
          return 'supplier'
        }
      }

      // Check Companies profiles
      const companiesData = localStorage.getItem('kaazmaamaa-companies')
      console.log('📋 Companies data:', companiesData ? 'Found' : 'Not found')
      if (companiesData) {
        const companies = JSON.parse(companiesData)
        console.log('🏢 Checking', companies.length, 'company profiles')
        const companyProfile = companies.find((company: any) => {
          const emailMatch = company.contactInfo?.hrEmail === userEmail
          const userIdMatch = company.userId === userEmail
          console.log('🏢 Company check:', company.companyInfo?.companyName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)
          return emailMatch || userIdMatch
        })
        if (companyProfile) {
          console.log('✅ Found company profile for:', userEmail, companyProfile.companyInfo?.companyName)
          return 'company'
        }
      }

      console.log('❌ No profile found for:', userEmail, 'defaulting to worker')
      return 'worker' // Default fallback
    } catch (error) {
      console.error('💥 Error detecting profile type:', error)
      return 'worker' // Default fallback
    }
  }

  const signIn = async (email: string, password: string) => {
    // Mock implementation for now
    console.log('Sign in:', { email, password })
    // Use email as the consistent user ID
    const mockUser = { id: email, email }

    // Detect the user's actual profile type
    const detectedRole = detectUserProfileType(email)
    console.log('Detected user role:', detectedRole, 'for email:', email)

    // Save to state
    setUser(mockUser)
    setUserRole(detectedRole)

    // Save to localStorage
    try {
      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))
      localStorage.setItem('kaazmaamaa-user-role', detectedRole)
      console.log('User authentication saved to localStorage with role:', detectedRole)
    } catch (error) {
      console.error('Error saving auth to localStorage:', error)
    }

    return { data: { user: mockUser }, error: null }
  }

  const signOut = async () => {
    // Clear state
    setUser(null)
    setUserRole(null)

    // Clear localStorage
    try {
      localStorage.removeItem('kaazmaamaa-user')
      localStorage.removeItem('kaazmaamaa-user-role')
      console.log('User authentication cleared from localStorage')
    } catch (error) {
      console.error('Error clearing auth from localStorage:', error)
    }
  }

  const getUserProfile = () => {
    if (!user || !userRole) return null

    try {
      if (userRole === 'worker') {
        const workersData = localStorage.getItem('kaazmaamaa-workers')
        if (workersData) {
          const workers = JSON.parse(workersData)
          return workers.find((worker: any) =>
            worker.personalInfo?.email === user.email || worker.userId === user.email
          )
        }
      } else if (userRole === 'supplier') {
        const suppliersData = localStorage.getItem('kaazmaamaa-suppliers')
        if (suppliersData) {
          const suppliers = JSON.parse(suppliersData)
          return suppliers.find((supplier: any) =>
            supplier.personalInfo?.email === user.email ||
            supplier.userId === user.email
          )
        }
      } else if (userRole === 'company') {
        const companiesData = localStorage.getItem('kaazmaamaa-companies')
        if (companiesData) {
          const companies = JSON.parse(companiesData)
          return companies.find((company: any) =>
            company.contactInfo?.hrEmail === user.email || company.userId === user.email
          )
        }
      }
    } catch (error) {
      console.error('Error getting user profile:', error)
    }

    return null
  }

  const debugProfiles = () => {
    console.log('🐛 DEBUG: All localStorage data:')
    console.log('Workers:', localStorage.getItem('kaazmaamaa-workers'))
    console.log('Suppliers:', localStorage.getItem('kaazmaamaa-suppliers'))
    console.log('Companies:', localStorage.getItem('kaazmaamaa-companies'))

    try {
      const workers = JSON.parse(localStorage.getItem('kaazmaamaa-workers') || '[]')
      const suppliers = JSON.parse(localStorage.getItem('kaazmaamaa-suppliers') || '[]')
      const companies = JSON.parse(localStorage.getItem('kaazmaamaa-companies') || '[]')

      console.log('🐛 Parsed Workers:', workers)
      console.log('🐛 Parsed Suppliers:', suppliers)
      console.log('🐛 Parsed Companies:', companies)
    } catch (error) {
      console.error('🐛 Error parsing localStorage data:', error)
    }
  }

  const value = {
    user,
    userRole,
    loading,
    signUp,
    signIn,
    signOut,
    getUserProfile,
    debugProfiles,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
