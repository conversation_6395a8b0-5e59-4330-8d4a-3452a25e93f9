{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/barta/MessageBubble.tsx"], "sourcesContent": ["'use client'\n\nimport { Avatar } from '@/components/ui/avatar'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { BartaMessage } from '@/contexts/BartaContext'\nimport { Users, Building, Wrench, MoreVertical, Reply, Heart, Copy } from 'lucide-react'\nimport { useState } from 'react'\n\ninterface MessageBubbleProps {\n  message: BartaMessage\n  isOwnMessage: boolean\n  showSenderInfo?: boolean\n  onReply?: (message: BartaMessage) => void\n  onReact?: (messageId: string, reaction: string) => void\n  onDelete?: (messageId: string) => void\n}\n\nexport function MessageBubble({ \n  message, \n  isOwnMessage, \n  showSenderInfo = true,\n  onReply,\n  onReact,\n  onDelete \n}: MessageBubbleProps) {\n  const [showActions, setShowActions] = useState(false)\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return <Wrench className=\"h-4 w-4 text-blue-600\" />\n      case 'supplier':\n        return <Building className=\"h-4 w-4 text-green-600\" />\n      case 'company':\n        return <Users className=\"h-4 w-4 text-purple-600\" />\n      default:\n        return <Users className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return 'bg-blue-100 text-blue-800'\n      case 'supplier':\n        return 'bg-green-100 text-green-800'\n      case 'company':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatMessageTime = (timestamp: string) => {\n    const date = new Date(timestamp)\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n  }\n\n  const handleCopyMessage = () => {\n    navigator.clipboard.writeText(message.content)\n    setShowActions(false)\n  }\n\n  return (\n    <div\n      className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} group`}\n      onMouseEnter={() => setShowActions(true)}\n      onMouseLeave={() => setShowActions(false)}\n    >\n      <div className={`max-w-xs lg:max-w-md ${isOwnMessage ? 'order-2' : 'order-1'}`}>\n        {/* Sender Info */}\n        {!isOwnMessage && showSenderInfo && (\n          <div className=\"flex items-center space-x-2 mb-1\">\n            <Avatar className=\"h-6 w-6 bg-gray-100 flex items-center justify-center\">\n              {getRoleIcon(message.senderRole)}\n            </Avatar>\n            <span className=\"text-xs font-medium text-gray-700\">{message.senderName}</span>\n            <Badge variant=\"secondary\" className={`text-xs ${getRoleColor(message.senderRole)}`}>\n              {message.senderRole}\n            </Badge>\n          </div>\n        )}\n        \n        {/* Message Bubble */}\n        <div className=\"relative\">\n          <div\n            className={`rounded-lg px-4 py-2 ${\n              isOwnMessage\n                ? 'bg-blue-600 text-white'\n                : 'bg-gray-100 text-gray-900'\n            }`}\n          >\n            {/* Reply Reference */}\n            {message.replyToMessage && (\n              <div className={`text-xs mb-2 p-2 rounded border-l-2 ${\n                isOwnMessage\n                  ? 'bg-blue-500 border-blue-300 text-blue-100'\n                  : 'bg-gray-50 border-gray-300 text-gray-600'\n              }`}>\n                <p className=\"font-medium\">Replying to {message.replyToMessage.senderName}</p>\n                <p className=\"truncate\">{message.replyToMessage.content}</p>\n              </div>\n            )}\n            \n            {/* Message Content */}\n            <div className=\"space-y-1\">\n              {message.type === 'text' && (\n                <p className=\"text-sm whitespace-pre-wrap\">{message.content}</p>\n              )}\n              \n              {message.type === 'emoji' && (\n                <p className=\"text-2xl\">{message.content}</p>\n              )}\n              \n              {message.type === 'image' && (\n                <div className=\"space-y-2\">\n                  {message.content && <p className=\"text-sm\">{message.content}</p>}\n                  {message.attachments?.map((attachment, index) => (\n                    <img\n                      key={index}\n                      src={attachment.url}\n                      alt={attachment.name}\n                      className=\"max-w-full h-auto rounded\"\n                    />\n                  ))}\n                </div>\n              )}\n              \n              {message.type === 'file' && (\n                <div className=\"space-y-2\">\n                  {message.content && <p className=\"text-sm\">{message.content}</p>}\n                  {message.attachments?.map((attachment, index) => (\n                    <div\n                      key={index}\n                      className={`flex items-center space-x-2 p-2 rounded ${\n                        isOwnMessage ? 'bg-blue-500' : 'bg-gray-200'\n                      }`}\n                    >\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm font-medium\">{attachment.name}</p>\n                        {attachment.size && (\n                          <p className=\"text-xs opacity-75\">\n                            {(attachment.size / 1024).toFixed(1)} KB\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n            \n            {/* Message Time & Status */}\n            <div className={`flex items-center justify-between mt-1 text-xs ${\n              isOwnMessage ? 'text-blue-100' : 'text-gray-500'\n            }`}>\n              <div className=\"flex items-center space-x-2\">\n                <span>{formatMessageTime(message.timestamp)}</span>\n                {message.isEdited && <span className=\"italic\">(edited)</span>}\n              </div>\n              {isOwnMessage && (\n                <div className=\"flex items-center space-x-1\">\n                  {message.isDelivered && <span>✓</span>}\n                  {message.isRead && <span>✓</span>}\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Message Reactions */}\n          {message.reactions && Object.keys(message.reactions).length > 0 && (\n            <div className=\"flex flex-wrap gap-1 mt-2\">\n              {Object.entries(message.reactions).map(([emoji, reaction]) => (\n                <button\n                  key={emoji}\n                  onClick={() => onReact?.(message.id, emoji)}\n                  className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs border transition-colors ${\n                    isOwnMessage\n                      ? 'bg-blue-100 border-blue-200 hover:bg-blue-200'\n                      : 'bg-gray-100 border-gray-200 hover:bg-gray-200'\n                  }`}\n                >\n                  <span>{emoji}</span>\n                  <span>{reaction.count}</span>\n                </button>\n              ))}\n            </div>\n          )}\n          \n          {/* Message Actions */}\n          {showActions && (\n            <div className={`absolute top-0 ${isOwnMessage ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'} flex items-center space-x-1 bg-white shadow-lg rounded-lg p-1 border`}>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => onReact?.(message.id, '❤️')}\n                className=\"h-8 w-8 p-0\"\n                title=\"React with heart\"\n              >\n                <Heart className=\"h-3 w-3\" />\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => onReply?.(message)}\n                className=\"h-8 w-8 p-0\"\n                title=\"Reply to message\"\n              >\n                <Reply className=\"h-3 w-3\" />\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleCopyMessage}\n                className=\"h-8 w-8 p-0\"\n                title=\"Copy message\"\n              >\n                <Copy className=\"h-3 w-3\" />\n              </Button>\n              \n              {isOwnMessage && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => onDelete?.(message.id)}\n                  className=\"h-8 w-8 p-0 text-red-600 hover:text-red-700\"\n                  title=\"Delete message\"\n                >\n                  <MoreVertical className=\"h-3 w-3\" />\n                </Button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;AAkBO,SAAS,cAAc,EAC5B,OAAO,EACP,YAAY,EACZ,iBAAiB,IAAI,EACrB,OAAO,EACP,OAAO,EACP,QAAQ,EACW;;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC1E;IAEA,MAAM,oBAAoB;QACxB,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,OAAO;QAC7C,eAAe;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,KAAK,EAAE,eAAe,gBAAgB,gBAAgB,MAAM,CAAC;QACzE,cAAc,IAAM,eAAe;QACnC,cAAc,IAAM,eAAe;kBAEnC,cAAA,6LAAC;YAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,YAAY,WAAW;;gBAE3E,CAAC,gBAAgB,gCAChB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCACf,YAAY,QAAQ,UAAU;;;;;;sCAEjC,6LAAC;4BAAK,WAAU;sCAAqC,QAAQ,UAAU;;;;;;sCACvE,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAW,CAAC,QAAQ,EAAE,aAAa,QAAQ,UAAU,GAAG;sCAChF,QAAQ,UAAU;;;;;;;;;;;;8BAMzB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,WAAW,CAAC,qBAAqB,EAC/B,eACI,2BACA,6BACJ;;gCAGD,QAAQ,cAAc,kBACrB,6LAAC;oCAAI,WAAW,CAAC,oCAAoC,EACnD,eACI,8CACA,4CACJ;;sDACA,6LAAC;4CAAE,WAAU;;gDAAc;gDAAa,QAAQ,cAAc,CAAC,UAAU;;;;;;;sDACzE,6LAAC;4CAAE,WAAU;sDAAY,QAAQ,cAAc,CAAC,OAAO;;;;;;;;;;;;8CAK3D,6LAAC;oCAAI,WAAU;;wCACZ,QAAQ,IAAI,KAAK,wBAChB,6LAAC;4CAAE,WAAU;sDAA+B,QAAQ,OAAO;;;;;;wCAG5D,QAAQ,IAAI,KAAK,yBAChB,6LAAC;4CAAE,WAAU;sDAAY,QAAQ,OAAO;;;;;;wCAGzC,QAAQ,IAAI,KAAK,yBAChB,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBAAI,6LAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;gDAC1D,QAAQ,WAAW,EAAE,IAAI,CAAC,YAAY,sBACrC,6LAAC;wDAEC,KAAK,WAAW,GAAG;wDACnB,KAAK,WAAW,IAAI;wDACpB,WAAU;uDAHL;;;;;;;;;;;wCASZ,QAAQ,IAAI,KAAK,wBAChB,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,OAAO,kBAAI,6LAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;gDAC1D,QAAQ,WAAW,EAAE,IAAI,CAAC,YAAY,sBACrC,6LAAC;wDAEC,WAAW,CAAC,wCAAwC,EAClD,eAAe,gBAAgB,eAC/B;kEAEF,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAuB,WAAW,IAAI;;;;;;gEAClD,WAAW,IAAI,kBACd,6LAAC;oEAAE,WAAU;;wEACV,CAAC,WAAW,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;uDATtC;;;;;;;;;;;;;;;;;8CAoBf,6LAAC;oCAAI,WAAW,CAAC,+CAA+C,EAC9D,eAAe,kBAAkB,iBACjC;;sDACA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAM,kBAAkB,QAAQ,SAAS;;;;;;gDACzC,QAAQ,QAAQ,kBAAI,6LAAC;oDAAK,WAAU;8DAAS;;;;;;;;;;;;wCAE/C,8BACC,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,WAAW,kBAAI,6LAAC;8DAAK;;;;;;gDAC7B,QAAQ,MAAM,kBAAI,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;wBAOhC,QAAQ,SAAS,IAAI,OAAO,IAAI,CAAC,QAAQ,SAAS,EAAE,MAAM,GAAG,mBAC5D,6LAAC;4BAAI,WAAU;sCACZ,OAAO,OAAO,CAAC,QAAQ,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,SAAS,iBACvD,6LAAC;oCAEC,SAAS,IAAM,UAAU,QAAQ,EAAE,EAAE;oCACrC,WAAW,CAAC,oFAAoF,EAC9F,eACI,kDACA,iDACJ;;sDAEF,6LAAC;sDAAM;;;;;;sDACP,6LAAC;sDAAM,SAAS,KAAK;;;;;;;mCAThB;;;;;;;;;;wBAgBZ,6BACC,6LAAC;4BAAI,WAAW,CAAC,eAAe,EAAE,eAAe,6BAA6B,2BAA2B,qEAAqE,CAAC;;8CAC7K,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,UAAU,QAAQ,EAAE,EAAE;oCACrC,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAGnB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,UAAU;oCACzB,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAGnB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;gCAGjB,8BACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,WAAW,QAAQ,EAAE;oCACpC,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,6NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1C;GA9NgB;KAAA", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/barta/ConversationItem.tsx"], "sourcesContent": ["'use client'\n\nimport { Avatar } from '@/components/ui/avatar'\nimport { Badge } from '@/components/ui/badge'\nimport { BartaConversation, BartaUser } from '@/contexts/BartaContext'\nimport { Users, Building, Wrench, Circle } from 'lucide-react'\n\ninterface ConversationItemProps {\n  conversation: BartaConversation\n  otherParticipant: BartaUser | null\n  isActive: boolean\n  onClick: () => void\n}\n\nexport function ConversationItem({ \n  conversation, \n  otherParticipant, \n  isActive, \n  onClick \n}: ConversationItemProps) {\n  \n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return <Wrench className=\"h-4 w-4 text-blue-600\" />\n      case 'supplier':\n        return <Building className=\"h-4 w-4 text-green-600\" />\n      case 'company':\n        return <Users className=\"h-4 w-4 text-purple-600\" />\n      default:\n        return <Circle className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return 'bg-blue-100 text-blue-800'\n      case 'supplier':\n        return 'bg-green-100 text-green-800'\n      case 'company':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatConversationTime = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    } else {\n      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })\n    }\n  }\n\n  const truncateMessage = (message: string, maxLength: number = 50) => {\n    if (message.length <= maxLength) return message\n    return message.substring(0, maxLength) + '...'\n  }\n\n  if (!otherParticipant) return null\n\n  return (\n    <div\n      className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${\n        isActive ? 'bg-blue-50 border-l-4 border-l-blue-600' : ''\n      }`}\n      onClick={onClick}\n    >\n      <div className=\"flex items-center space-x-3\">\n        {/* Avatar with Online Status */}\n        <div className=\"relative flex-shrink-0\">\n          <Avatar className=\"h-12 w-12 bg-gray-100 flex items-center justify-center\">\n            {getRoleIcon(otherParticipant.role)}\n          </Avatar>\n          {otherParticipant.isOnline && (\n            <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full\"></div>\n          )}\n        </div>\n        \n        {/* Conversation Info */}\n        <div className=\"flex-1 min-w-0\">\n          {/* Header Row */}\n          <div className=\"flex items-center justify-between mb-1\">\n            <h3 className=\"font-medium text-sm truncate text-gray-900\">\n              {conversation.isGroup ? conversation.groupName : otherParticipant.name}\n            </h3>\n            <span className=\"text-xs text-gray-500 flex-shrink-0\">\n              {conversation.lastMessage && formatConversationTime(conversation.lastMessage.timestamp)}\n            </span>\n          </div>\n          \n          {/* Last Message Row */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm text-gray-600 truncate\">\n                {conversation.lastMessage ? (\n                  <>\n                    {conversation.lastMessage.type === 'emoji' ? (\n                      <span className=\"text-lg\">{conversation.lastMessage.content}</span>\n                    ) : conversation.lastMessage.type === 'image' ? (\n                      <span className=\"italic\">📷 Image</span>\n                    ) : conversation.lastMessage.type === 'file' ? (\n                      <span className=\"italic\">📎 File</span>\n                    ) : (\n                      truncateMessage(conversation.lastMessage.content)\n                    )}\n                  </>\n                ) : (\n                  <span className=\"italic text-gray-400\">No messages yet</span>\n                )}\n              </p>\n            </div>\n            \n            {/* Unread Badge */}\n            {conversation.unreadCount > 0 && (\n              <Badge className=\"bg-blue-600 text-white text-xs ml-2 flex-shrink-0\">\n                {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}\n              </Badge>\n            )}\n          </div>\n          \n          {/* Role Badge and Status */}\n          <div className=\"flex items-center justify-between mt-1\">\n            <Badge variant=\"secondary\" className={`text-xs ${getRoleColor(otherParticipant.role)}`}>\n              {otherParticipant.role}\n            </Badge>\n            \n            {/* Online Status Text */}\n            <span className={`text-xs ${otherParticipant.isOnline ? 'text-green-600' : 'text-gray-400'}`}>\n              {otherParticipant.isOnline ? 'Online' : 'Offline'}\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AALA;;;;;AAcO,SAAS,iBAAiB,EAC/B,YAAY,EACZ,gBAAgB,EAChB,QAAQ,EACR,OAAO,EACe;IAEtB,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC1E,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU;QACtE;IACF;IAEA,MAAM,kBAAkB,CAAC,SAAiB,YAAoB,EAAE;QAC9D,IAAI,QAAQ,MAAM,IAAI,WAAW,OAAO;QACxC,OAAO,QAAQ,SAAS,CAAC,GAAG,aAAa;IAC3C;IAEA,IAAI,CAAC,kBAAkB,OAAO;IAE9B,qBACE,6LAAC;QACC,WAAW,CAAC,+EAA+E,EACzF,WAAW,4CAA4C,IACvD;QACF,SAAS;kBAET,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;sCACf,YAAY,iBAAiB,IAAI;;;;;;wBAEnC,iBAAiB,QAAQ,kBACxB,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,aAAa,OAAO,GAAG,aAAa,SAAS,GAAG,iBAAiB,IAAI;;;;;;8CAExE,6LAAC;oCAAK,WAAU;8CACb,aAAa,WAAW,IAAI,uBAAuB,aAAa,WAAW,CAAC,SAAS;;;;;;;;;;;;sCAK1F,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDACV,aAAa,WAAW,iBACvB;sDACG,aAAa,WAAW,CAAC,IAAI,KAAK,wBACjC,6LAAC;gDAAK,WAAU;0DAAW,aAAa,WAAW,CAAC,OAAO;;;;;uDACzD,aAAa,WAAW,CAAC,IAAI,KAAK,wBACpC,6LAAC;gDAAK,WAAU;0DAAS;;;;;uDACvB,aAAa,WAAW,CAAC,IAAI,KAAK,uBACpC,6LAAC;gDAAK,WAAU;0DAAS;;;;;uDAEzB,gBAAgB,aAAa,WAAW,CAAC,OAAO;0EAIpD,6LAAC;4CAAK,WAAU;sDAAuB;;;;;;;;;;;;;;;;gCAM5C,aAAa,WAAW,GAAG,mBAC1B,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAU;8CACd,aAAa,WAAW,GAAG,KAAK,QAAQ,aAAa,WAAW;;;;;;;;;;;;sCAMvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAW,CAAC,QAAQ,EAAE,aAAa,iBAAiB,IAAI,GAAG;8CACnF,iBAAiB,IAAI;;;;;;8CAIxB,6LAAC;oCAAK,WAAW,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,GAAG,mBAAmB,iBAAiB;8CACzF,iBAAiB,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtD;KA/HgB", "debugId": null}}, {"offset": {"line": 927, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        onChange={props.onChange}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACL,UAAU,MAAM,QAAQ;QACvB,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1079, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/barta/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useBarta } from '@/contexts/BartaContext'\nimport { useSearchParams } from 'next/navigation'\nimport { MessageBubble } from '@/components/barta/MessageBubble'\nimport { ConversationItem } from '@/components/barta/ConversationItem'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Avatar } from '@/components/ui/avatar'\nimport { Badge } from '@/components/ui/badge'\nimport { Textarea } from '@/components/ui/textarea'\nimport { \n  MessageCircle, \n  Search, \n  Send, \n  Phone, \n  Video, \n  MoreVertical,\n  ArrowLeft,\n  Users,\n  Building,\n  Wrench,\n  Smile,\n  Paperclip,\n  Image,\n  Plus,\n  Circle,\n  X,\n  Reply\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\nexport default function BartaPage() {\n  const { user, userRole } = useAuth()\n  const searchParams = useSearchParams()\n  const {\n    conversations,\n    messages,\n    activeConversation,\n    onlineUsers,\n    sendMessage,\n    createConversation,\n    setActiveConversation,\n    markAsRead,\n    searchUsers,\n    getConversationWithUser,\n    getUserById,\n    getUnreadCount,\n    addReaction,\n    removeReaction\n  } = useBarta()\n\n  // State\n  const [searchQuery, setSearchQuery] = useState('')\n  const [messageInput, setMessageInput] = useState('')\n  const [showUserSearch, setShowUserSearch] = useState(false)\n  const [searchResults, setSearchResults] = useState(onlineUsers)\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false)\n  const [isTyping, setIsTyping] = useState(false)\n  const [messageSearchQuery, setMessageSearchQuery] = useState('')\n  const [showMessageSearch, setShowMessageSearch] = useState(false)\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([])\n  const [showFilePreview, setShowFilePreview] = useState(false)\n  const [replyingTo, setReplyingTo] = useState<any>(null)\n  const messagesEndRef = useRef<HTMLDivElement>(null)\n  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null)\n  const fileInputRef = useRef<HTMLInputElement>(null)\n  const imageInputRef = useRef<HTMLInputElement>(null)\n\n  // Common emojis for quick access\n  const quickEmojis = ['😀', '😂', '❤️', '👍', '👎', '😢', '😮', '😡', '🎉', '🔥', '💯', '👏']\n\n  // Handle URL parameters for direct conversation access\n  useEffect(() => {\n    const conversationParam = searchParams.get('conversation')\n    if (conversationParam && conversations.length > 0) {\n      const conversation = conversations.find(conv => conv.id === conversationParam)\n      if (conversation) {\n        setActiveConversation(conversationParam)\n        markAsRead(conversationParam)\n        toast.success('Conversation opened! 💬')\n      }\n    }\n  }, [searchParams, conversations, setActiveConversation, markAsRead])\n\n  // Auto-scroll to bottom of messages\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })\n  }, [messages, activeConversation])\n\n  // Update search results when query changes\n  useEffect(() => {\n    setSearchResults(searchUsers(searchQuery))\n  }, [searchQuery, searchUsers])\n\n  // Get role icon\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return <Wrench className=\"h-4 w-4 text-blue-600\" />\n      case 'supplier':\n        return <Building className=\"h-4 w-4 text-green-600\" />\n      case 'company':\n        return <Users className=\"h-4 w-4 text-purple-600\" />\n      default:\n        return <Circle className=\"h-4 w-4 text-gray-600\" />\n    }\n  }\n\n  // Get role color\n  const getRoleColor = (role: string) => {\n    switch (role) {\n      case 'worker':\n        return 'bg-blue-100 text-blue-800'\n      case 'supplier':\n        return 'bg-green-100 text-green-800'\n      case 'company':\n        return 'bg-purple-100 text-purple-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  // Handle sending message\n  const handleSendMessage = () => {\n    if (!messageInput.trim() || !activeConversation) return\n\n    sendMessage(activeConversation, messageInput)\n    setMessageInput('')\n    setShowEmojiPicker(false)\n    setIsTyping(false)\n\n    // Clear typing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current)\n      typingTimeoutRef.current = null\n    }\n\n    toast.success('Message sent! 💬')\n  }\n\n  // Handle emoji selection\n  const handleEmojiSelect = (emoji: string) => {\n    setMessageInput(prev => prev + emoji)\n    setShowEmojiPicker(false)\n  }\n\n  // Handle typing indicator\n  const handleTyping = (value: string) => {\n    setMessageInput(value)\n\n    if (!isTyping && value.trim()) {\n      setIsTyping(true)\n    }\n\n    // Clear existing timeout\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current)\n    }\n\n    // Set new timeout to stop typing indicator\n    typingTimeoutRef.current = setTimeout(() => {\n      setIsTyping(false)\n    }, 2000)\n  }\n\n  // Handle file selection\n  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(event.target.files || [])\n    if (files.length > 0) {\n      setSelectedFiles(files)\n      setShowFilePreview(true)\n    }\n  }\n\n  // Handle image selection\n  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const files = Array.from(event.target.files || [])\n    if (files.length > 0) {\n      setSelectedFiles(files)\n      setShowFilePreview(true)\n    }\n  }\n\n  // Send message with files\n  const handleSendWithFiles = () => {\n    if (!activeConversation) return\n\n    if (selectedFiles.length > 0) {\n      // Create mock URLs for files (in real app, upload to server first)\n      const attachments = selectedFiles.map(file => ({\n        type: file.type.startsWith('image/') ? 'image' as const : 'file' as const,\n        url: URL.createObjectURL(file), // Mock URL\n        name: file.name,\n        size: file.size\n      }))\n\n      const messageType = selectedFiles[0].type.startsWith('image/') ? 'image' : 'file'\n      const content = messageInput.trim() || (messageType === 'image' ? '📷 Image' : '📎 File')\n\n      // Send message with attachments and reply\n      sendMessage(activeConversation, content, messageType, replyingTo?.id)\n\n      // In a real implementation, you would store attachments in the message\n      // For now, we'll just show a success message\n      toast.success(`${messageType === 'image' ? 'Image' : 'File'} sent! 📎`)\n    } else if (messageInput.trim()) {\n      sendMessage(activeConversation, messageInput, 'text', replyingTo?.id)\n    }\n\n    // Reset state\n    setMessageInput('')\n    setSelectedFiles([])\n    setShowFilePreview(false)\n    setShowEmojiPicker(false)\n    setIsTyping(false)\n    setReplyingTo(null)\n\n    if (typingTimeoutRef.current) {\n      clearTimeout(typingTimeoutRef.current)\n      typingTimeoutRef.current = null\n    }\n  }\n\n  // Handle message reply\n  const handleReply = (message: any) => {\n    setReplyingTo(message)\n    toast.info(`Replying to ${message.senderName}`)\n  }\n\n  // Handle message reaction\n  const handleReaction = (messageId: string, emoji: string) => {\n    if (!activeConversation) return\n    addReaction(messageId, activeConversation, emoji)\n    toast.success(`Reacted with ${emoji}`)\n  }\n\n  // Remove selected file\n  const removeSelectedFile = (index: number) => {\n    setSelectedFiles(prev => prev.filter((_, i) => i !== index))\n    if (selectedFiles.length === 1) {\n      setShowFilePreview(false)\n    }\n  }\n\n  // Handle starting new conversation\n  const handleStartConversation = (userId: string) => {\n    const conversationId = createConversation(userId)\n    if (conversationId) {\n      setActiveConversation(conversationId)\n      setShowUserSearch(false)\n      setSearchQuery('')\n      toast.success('Conversation started! 🎉')\n    }\n  }\n\n  // Handle selecting conversation\n  const handleSelectConversation = (conversationId: string) => {\n    setActiveConversation(conversationId)\n    markAsRead(conversationId)\n  }\n\n  // Format message time\n  const formatMessageTime = (timestamp: string) => {\n    const date = new Date(timestamp)\n    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n  }\n\n  // Format conversation time\n  const formatConversationTime = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)\n    \n    if (diffInHours < 24) {\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    } else {\n      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })\n    }\n  }\n\n  // Get active conversation details\n  const activeConversationDetails = activeConversation\n    ? conversations.find(conv => conv.id === activeConversation)\n    : null\n\n  const activeConversationMessages = activeConversation\n    ? messages[activeConversation] || []\n    : []\n\n  // Filter messages based on search query\n  const filteredMessages = messageSearchQuery.trim()\n    ? activeConversationMessages.filter(msg =>\n        msg.content.toLowerCase().includes(messageSearchQuery.toLowerCase())\n      )\n    : activeConversationMessages\n\n  // Get other participant in conversation\n  const getOtherParticipant = (conversation: any) => {\n    if (!user?.id) return null\n    const otherParticipantId = conversation.participants.find((id: string) => id !== user.id)\n    return getUserById(otherParticipantId || '')\n  }\n\n  if (!user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Card className=\"w-96\">\n          <CardContent className=\"text-center py-12\">\n            <MessageCircle className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">Please Sign In</h3>\n            <p className=\"text-gray-600\">You need to be signed in to use Barta messaging.</p>\n          </CardContent>\n        </Card>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b border-gray-200 px-4 py-3\">\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <MessageCircle className=\"h-8 w-8 text-blue-600\" />\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Barta</h1>\n              <p className=\"text-sm text-gray-600\">KAAZMAAMAA Messenger</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-4\">\n            <Badge variant=\"secondary\" className=\"bg-blue-100 text-blue-800\">\n              {getUnreadCount()} unread\n            </Badge>\n            <Badge variant=\"secondary\" className=\"bg-green-100 text-green-800\">\n              {onlineUsers.length} users\n            </Badge>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto h-[calc(100vh-80px)] flex\">\n        {/* Sidebar - Conversations List */}\n        <div className={`${activeConversation ? 'hidden lg:flex' : 'flex'} w-full lg:w-1/3 bg-white border-r border-gray-200 flex-col`}>\n          {/* Search and New Chat */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex space-x-2 mb-3\">\n              <div className=\"flex-1 relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                <Input\n                  placeholder=\"Search conversations...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10\"\n                />\n              </div>\n              <Button\n                onClick={() => setShowUserSearch(!showUserSearch)}\n                className=\"bg-blue-600 hover:bg-blue-700\"\n              >\n                <Plus className=\"h-4 w-4\" />\n              </Button>\n            </div>\n            \n            {/* User Search Results */}\n            {showUserSearch && (\n              <div className=\"mt-3 max-h-48 overflow-y-auto border border-gray-200 rounded-lg\">\n                {searchResults.filter(u => u.id !== user?.id).map((searchUser) => (\n                  <div\n                    key={searchUser.id}\n                    className=\"p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0\"\n                    onClick={() => handleStartConversation(searchUser.id)}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <Avatar className=\"h-10 w-10 bg-gray-100 flex items-center justify-center\">\n                        {getRoleIcon(searchUser.role)}\n                      </Avatar>\n                      <div className=\"flex-1\">\n                        <p className=\"font-medium text-sm\">{searchUser.name}</p>\n                        <div className=\"flex items-center space-x-2\">\n                          <Badge variant=\"secondary\" className={`text-xs ${getRoleColor(searchUser.role)}`}>\n                            {searchUser.role}\n                          </Badge>\n                          {searchUser.isOnline && (\n                            <div className=\"flex items-center\">\n                              <div className=\"w-2 h-2 bg-green-500 rounded-full mr-1\"></div>\n                              <span className=\"text-xs text-green-600\">Online</span>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* Conversations List */}\n          <div className=\"flex-1 overflow-y-auto\">\n            {conversations.length === 0 ? (\n              <div className=\"p-8 text-center\">\n                <MessageCircle className=\"h-12 w-12 text-gray-400 mx-auto mb-3\" />\n                <p className=\"text-gray-600\">No conversations yet</p>\n                <p className=\"text-sm text-gray-500\">Start a new conversation above</p>\n              </div>\n            ) : (\n              conversations.map((conversation) => {\n                const otherParticipant = getOtherParticipant(conversation)\n                return (\n                  <ConversationItem\n                    key={conversation.id}\n                    conversation={conversation}\n                    otherParticipant={otherParticipant}\n                    isActive={activeConversation === conversation.id}\n                    onClick={() => handleSelectConversation(conversation.id)}\n                  />\n                )\n              })\n            )}\n          </div>\n        </div>\n\n        {/* Main Chat Area */}\n        <div className={`${activeConversation ? 'flex' : 'hidden lg:flex'} flex-1 flex-col`}>\n          {!activeConversation ? (\n            /* Welcome Screen */\n            <div className=\"flex-1 flex items-center justify-center bg-gray-50\">\n              <div className=\"text-center\">\n                <MessageCircle className=\"h-24 w-24 text-gray-400 mx-auto mb-6\" />\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Welcome to Barta</h2>\n                <p className=\"text-gray-600 mb-4\">Select a conversation to start messaging</p>\n                <Button\n                  onClick={() => setShowUserSearch(true)}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  <Plus className=\"h-4 w-4 mr-2\" />\n                  Start New Conversation\n                </Button>\n              </div>\n            </div>\n          ) : (\n            /* Active Conversation */\n            <>\n              {/* Chat Header */}\n              {activeConversationDetails && (\n                <div className=\"bg-white border-b border-gray-200 p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-3\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => setActiveConversation(null)}\n                        className=\"lg:hidden\"\n                        title=\"Back to conversations\"\n                      >\n                        <ArrowLeft className=\"h-4 w-4\" />\n                      </Button>\n                      \n                      {(() => {\n                        const otherParticipant = getOtherParticipant(activeConversationDetails)\n                        if (!otherParticipant) return null\n                        \n                        return (\n                          <>\n                            <div className=\"relative\">\n                              <Avatar className=\"h-10 w-10 bg-gray-100 flex items-center justify-center\">\n                                {getRoleIcon(otherParticipant.role)}\n                              </Avatar>\n                              {otherParticipant.isOnline && (\n                                <div className=\"absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></div>\n                              )}\n                            </div>\n                            \n                            <div>\n                              <h3 className=\"font-semibold text-gray-900\">{otherParticipant.name}</h3>\n                              <div className=\"flex items-center space-x-2\">\n                                <Badge variant=\"secondary\" className={`text-xs ${getRoleColor(otherParticipant.role)}`}>\n                                  {otherParticipant.role}\n                                </Badge>\n                                <span className=\"text-xs text-gray-500\">\n                                  {otherParticipant.isOnline ? 'Online' : 'Offline'}\n                                </span>\n                              </div>\n                            </div>\n                          </>\n                        )\n                      })()}\n                    </div>\n                    \n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => setShowMessageSearch(!showMessageSearch)}\n                        className={showMessageSearch ? 'bg-blue-100 text-blue-600' : ''}\n                      >\n                        <Search className=\"h-4 w-4\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" title=\"Voice Call\">\n                        <Phone className=\"h-4 w-4\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" title=\"Video Call\">\n                        <Video className=\"h-4 w-4\" />\n                      </Button>\n                      <Button variant=\"ghost\" size=\"sm\" title=\"More Options\">\n                        <MoreVertical className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Message Search */}\n              {showMessageSearch && (\n                <div className=\"bg-gray-50 border-b border-gray-200 p-3\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n                    <Input\n                      placeholder=\"Search messages...\"\n                      value={messageSearchQuery}\n                      onChange={(e) => setMessageSearchQuery(e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                  {messageSearchQuery && (\n                    <p className=\"text-xs text-gray-600 mt-2\">\n                      Found {filteredMessages.length} message(s) containing \"{messageSearchQuery}\"\n                    </p>\n                  )}\n                </div>\n              )}\n\n              {/* Messages Area */}\n              <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n                {filteredMessages.length === 0 ? (\n                  <div className=\"text-center py-12\">\n                    <MessageCircle className=\"h-12 w-12 text-gray-400 mx-auto mb-3\" />\n                    {messageSearchQuery ? (\n                      <>\n                        <p className=\"text-gray-600\">No messages found</p>\n                        <p className=\"text-sm text-gray-500\">Try a different search term</p>\n                      </>\n                    ) : (\n                      <>\n                        <p className=\"text-gray-600\">No messages yet</p>\n                        <p className=\"text-sm text-gray-500\">Send the first message to start the conversation</p>\n                      </>\n                    )}\n                  </div>\n                ) : (\n                  filteredMessages.map((message) => {\n                    const isOwnMessage = message.senderId === user?.id\n\n                    return (\n                      <MessageBubble\n                        key={message.id}\n                        message={message}\n                        isOwnMessage={isOwnMessage}\n                        showSenderInfo={!isOwnMessage}\n                        onReply={handleReply}\n                        onReact={handleReaction}\n                        onDelete={(messageId) => {\n                          // TODO: Implement delete\n                          toast.success('Message deleted')\n                        }}\n                      />\n                    )\n                  })\n                )}\n                <div ref={messagesEndRef} />\n              </div>\n\n              {/* Message Input */}\n              <div className=\"bg-white border-t border-gray-200 p-4\">\n                {/* Reply Preview */}\n                {replyingTo && (\n                  <div className=\"mb-3 p-3 bg-blue-50 rounded-lg border border-blue-200\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Reply className=\"h-4 w-4 text-blue-600\" />\n                        <span className=\"text-sm font-medium text-blue-800\">\n                          Replying to {replyingTo.senderName}\n                        </span>\n                      </div>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => setReplyingTo(null)}\n                        className=\"text-blue-600 hover:text-blue-700\"\n                      >\n                        <X className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                    <p className=\"text-sm text-blue-700 truncate\">{replyingTo.content}</p>\n                  </div>\n                )}\n\n                {/* Typing Indicator */}\n                {isTyping && (\n                  <div className=\"mb-2 text-xs text-gray-500 flex items-center\">\n                    <div className=\"flex space-x-1 mr-2\">\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                      <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                    </div>\n                    You are typing...\n                  </div>\n                )}\n\n                {/* File Preview */}\n                {showFilePreview && selectedFiles.length > 0 && (\n                  <div className=\"mb-3 p-3 bg-gray-50 rounded-lg border\">\n                    <div className=\"flex items-center justify-between mb-2\">\n                      <h4 className=\"text-sm font-medium text-gray-700\">Selected Files</h4>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={() => {\n                          setSelectedFiles([])\n                          setShowFilePreview(false)\n                        }}\n                      >\n                        <X className=\"h-4 w-4\" />\n                      </Button>\n                    </div>\n                    <div className=\"space-y-2\">\n                      {selectedFiles.map((file, index) => (\n                        <div key={index} className=\"flex items-center space-x-3 p-2 bg-white rounded border\">\n                          {file.type.startsWith('image/') ? (\n                            <div className=\"flex-shrink-0\">\n                              <img\n                                src={URL.createObjectURL(file)}\n                                alt={file.name}\n                                className=\"w-12 h-12 object-cover rounded\"\n                              />\n                            </div>\n                          ) : (\n                            <div className=\"flex-shrink-0 w-12 h-12 bg-gray-100 rounded flex items-center justify-center\">\n                              <Paperclip className=\"h-6 w-6 text-gray-500\" />\n                            </div>\n                          )}\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-medium text-gray-900 truncate\">{file.name}</p>\n                            <p className=\"text-xs text-gray-500\">{(file.size / 1024).toFixed(1)} KB</p>\n                          </div>\n                          <Button\n                            variant=\"ghost\"\n                            size=\"sm\"\n                            onClick={() => removeSelectedFile(index)}\n                            className=\"text-red-600 hover:text-red-700\"\n                          >\n                            <X className=\"h-4 w-4\" />\n                          </Button>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Emoji Picker */}\n                {showEmojiPicker && (\n                  <div className=\"mb-3 p-3 bg-gray-50 rounded-lg border\">\n                    <div className=\"flex flex-wrap gap-2\">\n                      {quickEmojis.map((emoji, index) => (\n                        <button\n                          key={index}\n                          onClick={() => handleEmojiSelect(emoji)}\n                          className=\"text-2xl hover:bg-gray-200 rounded p-1 transition-colors\"\n                        >\n                          {emoji}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n\n                {/* Hidden File Inputs */}\n                <input\n                  ref={fileInputRef}\n                  type=\"file\"\n                  multiple\n                  onChange={handleFileSelect}\n                  className=\"hidden\"\n                  accept=\".pdf,.doc,.docx,.txt,.zip,.rar\"\n                />\n                <input\n                  ref={imageInputRef}\n                  type=\"file\"\n                  multiple\n                  onChange={handleImageSelect}\n                  className=\"hidden\"\n                  accept=\"image/*\"\n                />\n\n                <div className=\"flex items-center space-x-2\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    title=\"Attach File\"\n                    onClick={() => fileInputRef.current?.click()}\n                  >\n                    <Paperclip className=\"h-4 w-4\" />\n                  </Button>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    title=\"Send Image\"\n                    onClick={() => imageInputRef.current?.click()}\n                  >\n                    <Image className=\"h-4 w-4\" />\n                  </Button>\n\n                  <div className=\"flex-1 relative\">\n                    <Textarea\n                      value={messageInput}\n                      onChange={(e) => handleTyping(e.target.value)}\n                      placeholder={selectedFiles.length > 0 ? \"Add a caption...\" : \"Type a message...\"}\n                      rows={1}\n                      className=\"resize-none pr-12\"\n                      onKeyPress={(e) => {\n                        if (e.key === 'Enter' && !e.shiftKey) {\n                          e.preventDefault()\n                          handleSendWithFiles()\n                        }\n                      }}\n                    />\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => setShowEmojiPicker(!showEmojiPicker)}\n                      className=\"absolute right-2 top-1/2 transform -translate-y-1/2\"\n                      title=\"Add Emoji\"\n                    >\n                      <Smile className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n\n                  <Button\n                    onClick={handleSendWithFiles}\n                    disabled={!messageInput.trim() && selectedFiles.length === 0}\n                    className=\"bg-blue-600 hover:bg-blue-700\"\n                    title=\"Send Message\"\n                  >\n                    <Send className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAmBA;;;AAjCA;;;;;;;;;;;;;;;AAmCe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EACJ,aAAa,EACb,QAAQ,EACR,kBAAkB,EAClB,WAAW,EACX,WAAW,EACX,kBAAkB,EAClB,qBAAqB,EACrB,UAAU,EACV,WAAW,EACX,uBAAuB,EACvB,WAAW,EACX,cAAc,EACd,WAAW,EACX,cAAc,EACf,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEX,QAAQ;IACR,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAClD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE/C,iCAAiC;IACjC,MAAM,cAAc;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK;IAE5F,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,oBAAoB,aAAa,GAAG,CAAC;YAC3C,IAAI,qBAAqB,cAAc,MAAM,GAAG,GAAG;gBACjD,MAAM,eAAe,cAAc,IAAI;wDAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;;gBAC5D,IAAI,cAAc;oBAChB,sBAAsB;oBACtB,WAAW;oBACX,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF;QACF;8BAAG;QAAC;QAAc;QAAe;QAAuB;KAAW;IAEnE,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;8BAAG;QAAC;QAAU;KAAmB;IAEjC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,iBAAiB,YAAY;QAC/B;8BAAG;QAAC;QAAa;KAAY;IAE7B,gBAAgB;IAChB,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,iBAAiB;IACjB,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,yBAAyB;IACzB,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,CAAC,oBAAoB;QAEjD,YAAY,oBAAoB;QAChC,gBAAgB;QAChB,mBAAmB;QACnB,YAAY;QAEZ,uBAAuB;QACvB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,yBAAyB;IACzB,MAAM,oBAAoB,CAAC;QACzB,gBAAgB,CAAA,OAAQ,OAAO;QAC/B,mBAAmB;IACrB;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAC;QACpB,gBAAgB;QAEhB,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI;YAC7B,YAAY;QACd;QAEA,yBAAyB;QACzB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,2CAA2C;QAC3C,iBAAiB,OAAO,GAAG,WAAW;YACpC,YAAY;QACd,GAAG;IACL;IAEA,wBAAwB;IACxB,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,EAAE;QACjD,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB;YACjB,mBAAmB;QACrB;IACF;IAEA,yBAAyB;IACzB,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,KAAK,IAAI,EAAE;QACjD,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,iBAAiB;YACjB,mBAAmB;QACrB;IACF;IAEA,0BAA0B;IAC1B,MAAM,sBAAsB;QAC1B,IAAI,CAAC,oBAAoB;QAEzB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,mEAAmE;YACnE,MAAM,cAAc,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7C,MAAM,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,UAAmB;oBAC1D,KAAK,IAAI,eAAe,CAAC;oBACzB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB,CAAC;YAED,MAAM,cAAc,aAAa,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,UAAU;YAC3E,MAAM,UAAU,aAAa,IAAI,MAAM,CAAC,gBAAgB,UAAU,aAAa,SAAS;YAExF,0CAA0C;YAC1C,YAAY,oBAAoB,SAAS,aAAa,YAAY;YAElE,uEAAuE;YACvE,6CAA6C;YAC7C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,gBAAgB,UAAU,UAAU,OAAO,SAAS,CAAC;QACxE,OAAO,IAAI,aAAa,IAAI,IAAI;YAC9B,YAAY,oBAAoB,cAAc,QAAQ,YAAY;QACpE;QAEA,cAAc;QACd,gBAAgB;QAChB,iBAAiB,EAAE;QACnB,mBAAmB;QACnB,mBAAmB;QACnB,YAAY;QACZ,cAAc;QAEd,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAc,CAAC;QACnB,cAAc;QACd,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,UAAU,EAAE;IAChD;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB,CAAC,WAAmB;QACzC,IAAI,CAAC,oBAAoB;QACzB,YAAY,WAAW,oBAAoB;QAC3C,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,OAAO;IACvC;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,mBAAmB;QACrB;IACF;IAEA,mCAAmC;IACnC,MAAM,0BAA0B,CAAC;QAC/B,MAAM,iBAAiB,mBAAmB;QAC1C,IAAI,gBAAgB;YAClB,sBAAsB;YACtB,kBAAkB;YAClB,eAAe;YACf,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;IAEA,gCAAgC;IAChC,MAAM,2BAA2B,CAAC;QAChC,sBAAsB;QACtB,WAAW;IACb;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;YAAE,MAAM;YAAW,QAAQ;QAAU;IAC1E;IAEA,2BAA2B;IAC3B,MAAM,yBAAyB,CAAC;QAC9B,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEtE,IAAI,cAAc,IAAI;YACpB,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC1E,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,OAAO;gBAAS,KAAK;YAAU;QACtE;IACF;IAEA,kCAAkC;IAClC,MAAM,4BAA4B,qBAC9B,cAAc,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,sBACvC;IAEJ,MAAM,6BAA6B,qBAC/B,QAAQ,CAAC,mBAAmB,IAAI,EAAE,GAClC,EAAE;IAEN,wCAAwC;IACxC,MAAM,mBAAmB,mBAAmB,IAAI,KAC5C,2BAA2B,MAAM,CAAC,CAAA,MAChC,IAAI,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAAmB,WAAW,OAEnE;IAEJ,wCAAwC;IACxC,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,MAAM,IAAI,OAAO;QACtB,MAAM,qBAAqB,aAAa,YAAY,CAAC,IAAI,CAAC,CAAC,KAAe,OAAO,KAAK,EAAE;QACxF,OAAO,YAAY,sBAAsB;IAC3C;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;IAKvC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAIzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC;wCAAiB;;;;;;;8CAEpB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;wCAClC,YAAY,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,GAAG,qBAAqB,mBAAmB,OAAO,2DAA2D,CAAC;;0CAE5H,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC,oIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;;;;;;;;;;;;0DAGd,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,kBAAkB,CAAC;gDAClC,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;;oCAKnB,gCACC,6LAAC;wCAAI,WAAU;kDACZ,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,IAAI,GAAG,CAAC,CAAC,2BACjD,6LAAC;gDAEC,WAAU;gDACV,SAAS,IAAM,wBAAwB,WAAW,EAAE;0DAEpD,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;sEACf,YAAY,WAAW,IAAI;;;;;;sEAE9B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAAuB,WAAW,IAAI;;;;;;8EACnD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAW,CAAC,QAAQ,EAAE,aAAa,WAAW,IAAI,GAAG;sFAC7E,WAAW,IAAI;;;;;;wEAEjB,WAAW,QAAQ,kBAClB,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAI,WAAU;;;;;;8FACf,6LAAC;oFAAK,WAAU;8FAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAjB9C,WAAW,EAAE;;;;;;;;;;;;;;;;0CA8B5B,6LAAC;gCAAI,WAAU;0CACZ,cAAc,MAAM,KAAK,kBACxB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;sDAC7B,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;2CAGvC,cAAc,GAAG,CAAC,CAAC;oCACjB,MAAM,mBAAmB,oBAAoB;oCAC7C,qBACE,6LAAC,kJAAA,CAAA,mBAAgB;wCAEf,cAAc;wCACd,kBAAkB;wCAClB,UAAU,uBAAuB,aAAa,EAAE;wCAChD,SAAS,IAAM,yBAAyB,aAAa,EAAE;uCAJlD,aAAa,EAAE;;;;;gCAO1B;;;;;;;;;;;;kCAMN,6LAAC;wBAAI,WAAW,GAAG,qBAAqB,SAAS,iBAAiB,gBAAgB,CAAC;kCAChF,CAAC,qBACA,kBAAkB,iBAClB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;mCAMvC,uBAAuB,iBACvB;;gCAEG,2CACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;oDAGtB,CAAC;wDACA,MAAM,mBAAmB,oBAAoB;wDAC7C,IAAI,CAAC,kBAAkB,OAAO;wDAE9B,qBACE;;8EACE,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EAAC,WAAU;sFACf,YAAY,iBAAiB,IAAI;;;;;;wEAEnC,iBAAiB,QAAQ,kBACxB,6LAAC;4EAAI,WAAU;;;;;;;;;;;;8EAInB,6LAAC;;sFACC,6LAAC;4EAAG,WAAU;sFAA+B,iBAAiB,IAAI;;;;;;sFAClE,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAY,WAAW,CAAC,QAAQ,EAAE,aAAa,iBAAiB,IAAI,GAAG;8FACnF,iBAAiB,IAAI;;;;;;8FAExB,6LAAC;oFAAK,WAAU;8FACb,iBAAiB,QAAQ,GAAG,WAAW;;;;;;;;;;;;;;;;;;;;oDAMpD,CAAC;;;;;;;0DAGH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,qBAAqB,CAAC;wDACrC,WAAW,oBAAoB,8BAA8B;kEAE7D,cAAA,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;kEAEpB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,OAAM;kEACtC,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,OAAM;kEACtC,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,OAAM;kEACtC,cAAA,6LAAC,6NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQjC,mCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,sBAAsB,EAAE,MAAM,CAAC,KAAK;oDACrD,WAAU;;;;;;;;;;;;wCAGb,oCACC,6LAAC;4CAAE,WAAU;;gDAA6B;gDACjC,iBAAiB,MAAM;gDAAC;gDAAyB;gDAAmB;;;;;;;;;;;;;8CAOnF,6LAAC;oCAAI,WAAU;;wCACZ,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDACxB,mCACC;;sEACE,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;iFAGvC;;sEACE,6LAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,6LAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;mDAK3C,iBAAiB,GAAG,CAAC,CAAC;4CACpB,MAAM,eAAe,QAAQ,QAAQ,KAAK,MAAM;4CAEhD,qBACE,6LAAC,+IAAA,CAAA,gBAAa;gDAEZ,SAAS;gDACT,cAAc;gDACd,gBAAgB,CAAC;gDACjB,SAAS;gDACT,SAAS;gDACT,UAAU,CAAC;oDACT,yBAAyB;oDACzB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gDAChB;+CATK,QAAQ,EAAE;;;;;wCAYrB;sDAEF,6LAAC;4CAAI,KAAK;;;;;;;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;wCAEZ,4BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;8EACjB,6LAAC;oEAAK,WAAU;;wEAAoC;wEACrC,WAAW,UAAU;;;;;;;;;;;;;sEAGtC,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,cAAc;4DAC7B,WAAU;sEAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGjB,6LAAC;oDAAE,WAAU;8DAAkC,WAAW,OAAO;;;;;;;;;;;;wCAKpE,0BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;sEACjG,6LAAC;4DAAI,WAAU;4DAAkD,OAAO;gEAAE,gBAAgB;4DAAO;;;;;;;;;;;;gDAC7F;;;;;;;wCAMT,mBAAmB,cAAc,MAAM,GAAG,mBACzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAoC;;;;;;sEAClD,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;gEACP,iBAAiB,EAAE;gEACnB,mBAAmB;4DACrB;sEAEA,cAAA,6LAAC,+LAAA,CAAA,IAAC;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGjB,6LAAC;oDAAI,WAAU;8DACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;4DAAgB,WAAU;;gEACxB,KAAK,IAAI,CAAC,UAAU,CAAC,0BACpB,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,KAAK,IAAI,eAAe,CAAC;wEACzB,KAAK,KAAK,IAAI;wEACd,WAAU;;;;;;;;;;yFAId,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;;;;;;8EAGzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;sFAA8C,KAAK,IAAI;;;;;;sFACpE,6LAAC;4EAAE,WAAU;;gFAAyB,CAAC,KAAK,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;gFAAG;;;;;;;;;;;;;8EAEtE,6LAAC,qIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,mBAAmB;oEAClC,WAAU;8EAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wEAAC,WAAU;;;;;;;;;;;;2DAxBP;;;;;;;;;;;;;;;;wCAiCjB,iCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;wDAEC,SAAS,IAAM,kBAAkB;wDACjC,WAAU;kEAET;uDAJI;;;;;;;;;;;;;;;sDAYf,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,UAAU;4CACV,WAAU;4CACV,QAAO;;;;;;sDAET,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,QAAQ;4CACR,UAAU;4CACV,WAAU;4CACV,QAAO;;;;;;sDAGT,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,OAAM;oDACN,SAAS,IAAM,aAAa,OAAO,EAAE;8DAErC,cAAA,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,OAAM;oDACN,SAAS,IAAM,cAAc,OAAO,EAAE;8DAEtC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAGnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uIAAA,CAAA,WAAQ;4DACP,OAAO;4DACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,aAAa,cAAc,MAAM,GAAG,IAAI,qBAAqB;4DAC7D,MAAM;4DACN,WAAU;4DACV,YAAY,CAAC;gEACX,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;oEACpC,EAAE,cAAc;oEAChB;gEACF;4DACF;;;;;;sEAEF,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS,IAAM,mBAAmB,CAAC;4DACnC,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAIrB,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS;oDACT,UAAU,CAAC,aAAa,IAAI,MAAM,cAAc,MAAM,KAAK;oDAC3D,WAAU;oDACV,OAAM;8DAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpC;GAntBwB;;QACK,kIAAA,CAAA,UAAO;QACb,qIAAA,CAAA,kBAAe;QAgBhC,mIAAA,CAAA,WAAQ;;;KAlBU", "debugId": null}}]}