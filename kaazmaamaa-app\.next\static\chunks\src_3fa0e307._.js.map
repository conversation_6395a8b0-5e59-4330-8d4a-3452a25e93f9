{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/avatar.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = \"Avatar\"\n\nconst AvatarImage = React.forwardRef<\n  HTMLImageElement,\n  React.ImgHTMLAttributes<HTMLImageElement>\n>(({ className, ...props }, ref) => (\n  <img\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = \"AvatarImage\"\n\nconst AvatarFallback = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = \"AvatarFallback\"\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG;AAErB,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        onChange={props.onChange}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACL,UAAU,MAAM,QAAQ;QACvB,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/suppliers/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useSuppliers } from '@/contexts/SuppliersContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Avatar } from '@/components/ui/avatar'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Building,\n  Search,\n  MessageCircle,\n  Phone,\n  ArrowLeft,\n  MapPin,\n  Star,\n  Plus,\n  FileText,\n  Video,\n  Calendar,\n  Briefcase,\n  Eye,\n  Download,\n  Play,\n  Radio,\n  CheckCircle,\n  Clock,\n  DollarSign,\n  Globe,\n  Users,\n  Award,\n  Factory\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\nexport default function SuppliersPage() {\n  const { suppliers } = useSuppliers()\n  const router = useRouter()\n  const [isLoading, setIsLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n\n  useEffect(() => {\n    // Simple loading timer\n    const timer = setTimeout(() => {\n      setIsLoading(false)\n    }, 1000)\n    return () => clearTimeout(timer)\n  }, [])\n\n  const handleWhatsAppContact = (whatsapp: string, supplierName: string) => {\n    const message = `Hello ${supplierName}, I found your company on KAAZMAAMAA Suppliers Block and would like to discuss job opportunities.`\n    const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`\n    window.open(whatsappUrl, '_blank')\n  }\n\n  // Filter suppliers based on search term\n  const filteredSuppliers = suppliers.filter(supplier =>\n    supplier.personalInfo?.supplierName?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    supplier.businessInfo?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    supplier.businessInfo?.businessType?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <h2 className=\"text-xl font-semibold text-gray-700\">Loading Suppliers...</h2>\n          <p className=\"text-gray-500 mt-2\">Please wait while we fetch the suppliers data</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => {\n                  toast.info('Navigating back to Feed...')\n                  router.push('/feed')\n                }}\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Feed\n              </Button>\n              <h1 className=\"text-2xl font-bold text-green-600\">Suppliers Block</h1>\n              <Badge className=\"bg-green-100 text-green-800\">\n                {filteredSuppliers.length} Suppliers Available\n              </Badge>\n            </div>\n            <Button\n              onClick={() => router.push('/suppliers/create')}\n              className=\"bg-green-600 hover:bg-green-700\"\n            >\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Create Suppliers Profile\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-7xl mx-auto px-4 py-6\">\n        {/* Quick Stats */}\n        <Card className=\"mb-6 bg-green-50 border-green-200\">\n          <CardContent className=\"pt-6\">\n            <div className=\"text-center\">\n              <h3 className=\"text-lg font-semibold text-green-800 mb-2\">🏭 Professional Suppliers Directory</h3>\n              <p className=\"text-green-600 mb-4\">Browse {suppliers.length} verified suppliers across different business types</p>\n              <div className=\"grid grid-cols-3 gap-4 max-w-md mx-auto\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-green-800\">{suppliers.filter(s => s.jobDemands?.length > 0).length}</div>\n                  <div className=\"text-sm text-green-600\">Active Jobs</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-blue-800\">{suppliers.filter(s => s.isVerified).length}</div>\n                  <div className=\"text-sm text-blue-600\">Verified</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-purple-800\">{suppliers.filter(s => s.isLiveStreaming).length}</div>\n                  <div className=\"text-sm text-purple-600\">Live</div>\n                </div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Search */}\n        <Card className=\"mb-6\">\n          <CardContent className=\"pt-6\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"Search by supplier name, company, or business type...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n              />\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Suppliers Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredSuppliers.map((supplier) => (\n            <Card key={supplier.id} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"relative\">\n                    <Avatar className=\"h-16 w-16 bg-green-100 flex items-center justify-center\">\n                      {supplier.personalInfo.profileImage ? (\n                        <img\n                          src={supplier.personalInfo.profileImage}\n                          alt={supplier.personalInfo.supplierName}\n                          className=\"w-full h-full object-cover rounded-full\"\n                        />\n                      ) : (\n                        <Factory className=\"h-8 w-8 text-green-600\" />\n                      )}\n                    </Avatar>\n                    {supplier.isLiveStreaming && (\n                      <div className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center\">\n                        <Radio className=\"h-3 w-3 mr-1\" />\n                        LIVE\n                      </div>\n                    )}\n                    {supplier.isVerified && (\n                      <div className=\"absolute -bottom-1 -right-1 bg-green-500 text-white rounded-full p-1\">\n                        <CheckCircle className=\"h-3 w-3\" />\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <h3 className=\"font-semibold text-lg truncate\">{supplier.personalInfo?.supplierName || 'Unknown Supplier'}</h3>\n                    <p className=\"text-green-600 font-medium\">{supplier.businessInfo?.companyName || 'No Company Name'}</p>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <Badge className=\"bg-green-100 text-green-800\">\n                        {supplier.businessInfo?.businessType || 'Business'}\n                      </Badge>\n                      <div className=\"flex items-center text-yellow-500\">\n                        <Star className=\"h-4 w-4 fill-current\" />\n                        <span className=\"text-sm ml-1\">{supplier.rating || 0}</span>\n                        <span className=\"text-gray-500 text-sm ml-1\">({supplier.totalReviews || 0})</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardHeader>\n\n              <CardContent>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <MapPin className=\"h-4 w-4 mr-2\" />\n                    {supplier.personalInfo?.address || 'Address not specified'}\n                  </div>\n\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <Building className=\"h-4 w-4 mr-2\" />\n                    CR: {supplier.businessInfo?.crNumber || 'Not provided'}\n                  </div>\n\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <Award className=\"h-4 w-4 mr-2\" />\n                    License: {supplier.businessInfo?.licenceNumber || 'Not provided'}\n                  </div>\n\n                  <div className=\"flex items-center text-gray-600 text-sm\">\n                    <Users className=\"h-4 w-4 mr-2\" />\n                    {supplier.businessInfo?.employeeCount || 'Not specified'} employees\n                  </div>\n\n                  {/* Job Demands */}\n                  <div className=\"pt-2 border-t\">\n                    <p className=\"text-sm font-medium text-gray-700 mb-2\">Active Job Demands:</p>\n                    {supplier.jobDemands.slice(0, 2).map((job) => (\n                      <div key={job.id} className=\"bg-gray-50 rounded-lg p-2 mb-2\">\n                        <p className=\"font-medium text-sm\">{job.position}</p>\n                        <p className=\"text-xs text-gray-600\">{job.category}</p>\n                        <div className=\"flex items-center justify-between mt-1\">\n                          <span className=\"text-xs text-green-600\">{job.salaryPerHour} {job.currency}/hr</span>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {job.urgency.replace('_', ' ')}\n                          </Badge>\n                        </div>\n                      </div>\n                    ))}\n                    {supplier.jobDemands.length > 2 && (\n                      <p className=\"text-xs text-gray-500\">+{supplier.jobDemands.length - 2} more jobs</p>\n                    )}\n                  </div>\n\n                  {/* Operating Locations */}\n                  <div className=\"flex flex-wrap gap-1 mt-2\">\n                    {supplier.operatingLocations.slice(0, 3).map((location, index) => (\n                      <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                        {location}\n                      </Badge>\n                    ))}\n                    {supplier.operatingLocations.length > 3 && (\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        +{supplier.operatingLocations.length - 3} more\n                      </Badge>\n                    )}\n                  </div>\n\n                  {/* Media Indicators */}\n                  <div className=\"flex items-center space-x-4 text-sm text-gray-500 pt-2 border-t\">\n                    <div className=\"flex items-center\">\n                      <FileText className=\"h-4 w-4 mr-1\" />\n                      {supplier.documents.length} docs\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Video className=\"h-4 w-4 mr-1\" />\n                      {supplier.videos.length} videos\n                    </div>\n                    <div className=\"flex items-center\">\n                      <Briefcase className=\"h-4 w-4 mr-1\" />\n                      {supplier.jobDemands.length} jobs\n                    </div>\n                  </div>\n\n                  <div className=\"flex gap-2 pt-3\">\n                    <Button\n                      size=\"sm\"\n                      onClick={() => router.push(`/suppliers/${supplier.id}`)}\n                      className=\"flex-1 bg-green-600 hover:bg-green-700\"\n                    >\n                      <Eye className=\"h-4 w-4 mr-2\" />\n                      View Profile\n                    </Button>\n\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => handleWhatsAppContact(supplier.personalInfo.whatsapp, supplier.personalInfo.supplierName)}\n                      className=\"border-green-300 text-green-600 hover:bg-green-50\"\n                    >\n                      <MessageCircle className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n\n        {filteredSuppliers.length === 0 && (\n          <Card>\n            <CardContent className=\"text-center py-12\">\n              <Factory className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n                {suppliers.length === 0 ? 'No Suppliers Available' : 'No Suppliers Found'}\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                {suppliers.length === 0\n                  ? 'There are currently no suppliers registered in the system.'\n                  : 'Try adjusting your search criteria to find suppliers.'\n                }\n              </p>\n              {suppliers.length > 0 && (\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setSearchTerm('')}\n                >\n                  Clear Search\n                </Button>\n              )}\n              {suppliers.length === 0 && (\n                <Button\n                  onClick={() => {\n                    toast.info('Refreshing suppliers data...')\n                    window.location.reload()\n                  }}\n                  variant=\"outline\"\n                >\n                  Refresh Page\n                </Button>\n              )}\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;;;AApCA;;;;;;;;;;;AAsCe,SAAS;;IACtB,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,uBAAuB;YACvB,MAAM,QAAQ;iDAAW;oBACvB,aAAa;gBACf;gDAAG;YACH;2CAAO,IAAM,aAAa;;QAC5B;kCAAG,EAAE;IAEL,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,UAAU,CAAC,MAAM,EAAE,aAAa,iGAAiG,CAAC;QACxI,MAAM,cAAc,CAAC,cAAc,EAAE,SAAS,OAAO,CAAC,WAAW,IAAI,MAAM,EAAE,mBAAmB,UAAU;QAC1G,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA,WACzC,SAAS,YAAY,EAAE,cAAc,cAAc,SAAS,WAAW,WAAW,OAClF,SAAS,YAAY,EAAE,aAAa,cAAc,SAAS,WAAW,WAAW,OACjF,SAAS,YAAY,EAAE,cAAc,cAAc,SAAS,WAAW,WAAW;IAGpF,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;4CACX,OAAO,IAAI,CAAC;wCACd;;0DAEA,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,6LAAC,oIAAA,CAAA,QAAK;wCAAC,WAAU;;4CACd,kBAAkB,MAAM;4CAAC;;;;;;;;;;;;;0CAG9B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4C;;;;;;kDAC1D,6LAAC;wCAAE,WAAU;;4CAAsB;4CAAQ,UAAU,MAAM;4CAAC;;;;;;;kDAC5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,SAAS,GAAG,MAAM;;;;;;kEAC1G,6LAAC;wDAAI,WAAU;kEAAyB;;;;;;;;;;;;0DAE1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;;;;;;kEAC7F,6LAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAsC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,eAAe,EAAE,MAAM;;;;;;kEACpG,6LAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOlB,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,6LAAC,mIAAA,CAAA,OAAI;gCAAmB,WAAU;;kDAChC,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;sEACf,SAAS,YAAY,CAAC,YAAY,iBACjC,6LAAC;gEACC,KAAK,SAAS,YAAY,CAAC,YAAY;gEACvC,KAAK,SAAS,YAAY,CAAC,YAAY;gEACvC,WAAU;;;;;qFAGZ,6LAAC,2MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;wDAGtB,SAAS,eAAe,kBACvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAIrC,SAAS,UAAU,kBAClB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAI7B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAkC,SAAS,YAAY,EAAE,gBAAgB;;;;;;sEACvF,6LAAC;4DAAE,WAAU;sEAA8B,SAAS,YAAY,EAAE,eAAe;;;;;;sEACjF,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EACd,SAAS,YAAY,EAAE,gBAAgB;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,6LAAC;4EAAK,WAAU;sFAAgB,SAAS,MAAM,IAAI;;;;;;sFACnD,6LAAC;4EAAK,WAAU;;gFAA6B;gFAAE,SAAS,YAAY,IAAI;gFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpF,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACjB,SAAS,YAAY,EAAE,WAAW;;;;;;;8DAGrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;wDAChC,SAAS,YAAY,EAAE,YAAY;;;;;;;8DAG1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;wDACxB,SAAS,YAAY,EAAE,iBAAiB;;;;;;;8DAGpD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,SAAS,YAAY,EAAE,iBAAiB;wDAAgB;;;;;;;8DAI3D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC;;;;;;wDACrD,SAAS,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACpC,6LAAC;gEAAiB,WAAU;;kFAC1B,6LAAC;wEAAE,WAAU;kFAAuB,IAAI,QAAQ;;;;;;kFAChD,6LAAC;wEAAE,WAAU;kFAAyB,IAAI,QAAQ;;;;;;kFAClD,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;oFAA0B,IAAI,aAAa;oFAAC;oFAAE,IAAI,QAAQ;oFAAC;;;;;;;0FAC3E,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;0FAClC,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;+DANtB,IAAI,EAAE;;;;;wDAWjB,SAAS,UAAU,CAAC,MAAM,GAAG,mBAC5B,6LAAC;4DAAE,WAAU;;gEAAwB;gEAAE,SAAS,UAAU,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAK1E,6LAAC;oDAAI,WAAU;;wDACZ,SAAS,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACtD,6LAAC,oIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAY,WAAU;0EAC9C;+DADS;;;;;wDAIb,SAAS,kBAAkB,CAAC,MAAM,GAAG,mBACpC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;;gEAAU;gEAC3C,SAAS,kBAAkB,CAAC,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAM/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEACnB,SAAS,SAAS,CAAC,MAAM;gEAAC;;;;;;;sEAE7B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,SAAS,MAAM,CAAC,MAAM;gEAAC;;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+MAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEACpB,SAAS,UAAU,CAAC,MAAM;gEAAC;;;;;;;;;;;;;8DAIhC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,EAAE;4DACtD,WAAU;;8EAEV,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAIlC,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,sBAAsB,SAAS,YAAY,CAAC,QAAQ,EAAE,SAAS,YAAY,CAAC,YAAY;4DACvG,WAAU;sEAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BApIxB,SAAS,EAAE;;;;;;;;;;oBA6IzB,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,2MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,6LAAC;oCAAG,WAAU;8CACX,UAAU,MAAM,KAAK,IAAI,2BAA2B;;;;;;8CAEvD,6LAAC;oCAAE,WAAU;8CACV,UAAU,MAAM,KAAK,IAClB,+DACA;;;;;;gCAGL,UAAU,MAAM,GAAG,mBAClB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,cAAc;8CAC9B;;;;;;gCAIF,UAAU,MAAM,KAAK,mBACpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;wCACX,OAAO,QAAQ,CAAC,MAAM;oCACxB;oCACA,SAAQ;8CACT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAvSwB;;QACA,uIAAA,CAAA,eAAY;QACnB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}