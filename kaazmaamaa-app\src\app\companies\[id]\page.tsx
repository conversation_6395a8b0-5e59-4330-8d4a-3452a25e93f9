'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useCompanies } from '@/contexts/CompaniesContext'
import { usePayment } from '@/contexts/PaymentContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState, use } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  ArrowLeft,
  MapPin,
  Star,
  MessageCircle,
  Phone,
  Download,
  Play,
  Radio,
  CheckCircle,
  Calendar,
  Briefcase,
  DollarSign,
  Globe,
  FileText,
  Video,
  Building,
  Mail,
  Users,
  Award,
  Target,
  TrendingUp,
  Clock,
  Heart,
  Share,
  Send,
  Lock,
  CreditCard
} from 'lucide-react'
import PaymentGateway from '@/components/PaymentGateway'
import { toast } from 'sonner'

interface CompanyProfilePageProps {
  params: Promise<{
    id: string
  }>
}

export default function CompanyProfilePage({ params }: CompanyProfilePageProps) {
  const { user } = useAuth()
  const { companies } = useCompanies()
  const { hasAccess } = usePayment()
  const router = useRouter()
  const resolvedParams = use(params)
  const [company, setCompany] = useState(companies.find(c => c.id === resolvedParams.id))
  const [showPaymentGateway, setShowPaymentGateway] = useState(false)
  const [paymentAccessType, setPaymentAccessType] = useState<'contact' | 'documents' | 'premium'>('contact')

  useEffect(() => {
    if (!user) {
      router.push('/')
    }

    const foundCompany = companies.find(c => c.id === resolvedParams.id)
    if (!foundCompany) {
      router.push('/companies')
    } else {
      setCompany(foundCompany)
    }
  }, [user, resolvedParams.id, companies, router])

  const handleWhatsAppContact = () => {
    if (company?.contactInfo?.hrWhatsapp) {
      const message = `Hello ${company.companyInfo?.companyName}, I found your company on KAAZMAAMAA Companies Block and would like to discuss career opportunities.`
      const whatsappUrl = `https://wa.me/${company.contactInfo.hrWhatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
      window.open(whatsappUrl, '_blank')
    } else {
      toast.error('WhatsApp contact not available')
    }
  }

  const handleContactAccess = () => {
    if (hasAccess(resolvedParams.id, 'contact')) {
      return true
    } else {
      setPaymentAccessType('contact')
      setShowPaymentGateway(true)
      return false
    }
  }

  const handleDocumentDownload = (doc: any) => {
    if (hasAccess(resolvedParams.id, 'documents')) {
      toast.success(`Downloading ${doc.name}...`)
    } else {
      setPaymentAccessType('documents')
      setShowPaymentGateway(true)
    }
  }

  const handlePaymentSuccess = () => {
    setShowPaymentGateway(false)
    toast.success('Access granted! You can now view contact information and download documents.')
  }

  const handleApplyToJob = (jobId: string, jobTitle: string) => {
    toast.success(`Application submitted for ${jobTitle}! 🎉`)
  }

  if (!company) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Company Not Found</h1>
          <Button onClick={() => router.push('/companies')}>
            Back to Companies
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/companies')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Companies
              </Button>
              <h1 className="text-2xl font-bold text-purple-600">Company Profile</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Company Header */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6">
              <div className="relative">
                <Avatar className="h-32 w-32 bg-purple-100 flex items-center justify-center">
                  <Building className="h-16 w-16 text-purple-600" />
                </Avatar>
                {company.isLiveStreaming && (
                  <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                    <Radio className="h-3 w-3 mr-1" />
                    LIVE
                  </div>
                )}
                {company.isVerified && (
                  <div className="absolute -bottom-2 -right-2 bg-purple-500 text-white rounded-full p-2">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                )}
                {company.isHiring && (
                  <div className="absolute -top-2 -left-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                    HIRING
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">{company.companyInfo.companyName}</h1>
                    <p className="text-xl text-purple-600 font-semibold mt-1">{company.companyInfo.industry}</p>
                    <p className="text-gray-600 mt-1">{company.companyInfo.description}</p>

                    <div className="flex items-center space-x-4 mt-3">
                      <Badge className="bg-purple-100 text-purple-800">
                        {company.companyInfo.companySize} employees
                      </Badge>
                      <div className="flex items-center text-yellow-500">
                        <Star className="h-5 w-5 fill-current" />
                        <span className="ml-1 font-semibold">{company.rating}</span>
                        <span className="text-gray-500 ml-1">({company.totalReviews} reviews)</span>
                      </div>
                      {company.isHiring && (
                        <Badge className="bg-green-100 text-green-800">
                          🔥 Actively Hiring
                        </Badge>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="flex items-center text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        {company.companyInfo.headquarters}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Calendar className="h-4 w-4 mr-2" />
                        Founded: {company.companyInfo.foundedYear}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Users className="h-4 w-4 mr-2" />
                        {company.totalEmployees} total employees
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Globe className="h-4 w-4 mr-2" />
                        {company.companyInfo.website || 'No website'}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2 mt-4 md:mt-0">
                    <Button onClick={handleWhatsAppContact} className="bg-green-600 hover:bg-green-700">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Contact HR
                    </Button>
                    {hasAccess(resolvedParams.id, 'contact') ? (
                      <>
                        <Button variant="outline">
                          <Phone className="h-4 w-4 mr-2" />
                          Call: {company.contactInfo?.companyPhone}
                        </Button>
                        <Button variant="outline">
                          <Mail className="h-4 w-4 mr-2" />
                          Email: {company.contactInfo?.hrEmail}
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          onClick={handleContactAccess}
                          className="border-orange-300 text-orange-600 hover:bg-orange-50"
                        >
                          <Lock className="h-4 w-4 mr-2" />
                          Unlock Contact Info (25 SAR)
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setPaymentAccessType('premium')
                            setShowPaymentGateway(true)
                          }}
                          className="border-purple-300 text-purple-600 hover:bg-purple-50"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Premium Access (75 SAR)
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Jobs ({company.jobOpenings.length})</TabsTrigger>
            <TabsTrigger value="culture">Culture</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="videos">Videos</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building className="h-5 w-5 mr-2" />
                    Company Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Industry</label>
                      <p className="text-gray-900">{company.companyInfo.industry}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company Size</label>
                      <p className="text-gray-900">{company.companyInfo.companySize}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Founded</label>
                      <p className="text-gray-900">{company.companyInfo.foundedYear}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Headquarters</label>
                      <p className="text-gray-900">{company.companyInfo.headquarters}</p>
                    </div>
                  </div>
                  {company.companyInfo.mission && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Mission</label>
                      <p className="text-gray-900 mt-1">{company.companyInfo.mission}</p>
                    </div>
                  )}
                  {company.companyInfo.vision && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Vision</label>
                      <p className="text-gray-900 mt-1">{company.companyInfo.vision}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    Work Environment
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center">
                        <Globe className="h-4 w-4 mr-2 text-blue-500" />
                        <span className="text-sm">Remote Friendly: {company.workEnvironment.isRemoteFriendly ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-green-500" />
                        <span className="text-sm">Flexible Hours: {company.workEnvironment.hasFlexibleHours ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex items-center">
                        <Award className="h-4 w-4 mr-2 text-purple-500" />
                        <span className="text-sm">Training: {company.workEnvironment.providesTraining ? 'Yes' : 'No'}</span>
                      </div>
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 mr-2 text-orange-500" />
                        <span className="text-sm">Career Growth: {company.workEnvironment.hasCareerGrowth ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Work-Life Balance</label>
                      <div className="flex items-center mt-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className={`h-4 w-4 ${
                              star <= company.workEnvironment.workLifeBalance
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="ml-2 text-sm text-gray-600">
                          {company.workEnvironment.workLifeBalance}/5
                        </span>
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company Benefits</label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {company.companyBenefits.map((benefit, index) => (
                          <Badge key={index} variant="secondary">{benefit}</Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="jobs">
            <div className="space-y-4">
              {company.jobOpenings.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Open Positions</h3>
                    <p className="text-gray-600">This company is not currently hiring.</p>
                  </CardContent>
                </Card>
              ) : (
                company.jobOpenings.map((job) => (
                  <Card key={job.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-xl font-semibold">{job.title}</h3>
                          <p className="text-purple-600 font-medium">{job.department}</p>
                          <div className="flex items-center space-x-2 mt-2">
                            <Badge>{job.employmentType.replace('_', ' ')}</Badge>
                            <Badge variant="secondary">{job.experienceLevel}</Badge>
                            <Badge className="bg-green-100 text-green-800">{job.urgency.replace('_', ' ')}</Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold text-purple-600">
                            {job.salaryRange.min}-{job.salaryRange.max} {job.salaryRange.currency}
                          </p>
                          <p className="text-sm text-gray-500">{job.applicationsCount} applications</p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Job Description</h4>
                          <p className="text-gray-700">{job.description}</p>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Requirements</h4>
                          <ul className="list-disc list-inside space-y-1">
                            {job.requirements.map((req, index) => (
                              <li key={index} className="text-gray-700 text-sm">{req}</li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Benefits</h4>
                          <div className="flex flex-wrap gap-2">
                            {job.benefits.map((benefit, index) => (
                              <Badge key={index} variant="outline">{benefit}</Badge>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <MapPin className="h-4 w-4 mr-1" />
                              {job.location}
                            </div>
                            {job.isRemote && (
                              <div className="flex items-center">
                                <Globe className="h-4 w-4 mr-1" />
                                Remote OK
                              </div>
                            )}
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              Posted: {new Date(job.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                          <Button
                            onClick={() => handleApplyToJob(job.id, job.title)}
                            className="bg-purple-600 hover:bg-purple-700"
                          >
                            <Send className="h-4 w-4 mr-2" />
                            Apply Now
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="culture">
            <Card>
              <CardHeader>
                <CardTitle>Company Culture & Values</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <h4 className="font-medium mb-2">Operating Locations</h4>
                    <div className="flex flex-wrap gap-2">
                      {company.operatingLocations.map((location, index) => (
                        <Badge key={index} variant="outline">{location}</Badge>
                      ))}
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Preferred Worker Categories</h4>
                    <div className="flex flex-wrap gap-2">
                      {company.preferredWorkerCategories.map((category, index) => (
                        <Badge key={index} className="bg-blue-100 text-blue-800">{category}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Company Documents ({(company.documents || []).length})
                </CardTitle>
                {!hasAccess(resolvedParams.id, 'documents') && (
                  <CardDescription className="bg-orange-50 border border-orange-200 rounded-lg p-3 mt-2">
                    <Lock className="h-4 w-4 inline mr-2" />
                    Document downloads require payment. Pay 50 SAR for document access or 75 SAR for premium access (contact + documents).
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(company.documents || []).map((doc) => (
                    <div key={doc.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{doc.name}</h4>
                          <p className="text-sm text-gray-500 capitalize">{doc.type.replace('_', ' ')}</p>
                          <p className="text-xs text-gray-400 mt-1">
                            Uploaded: {new Date(doc.uploadedAt).toLocaleDateString()}
                          </p>
                        </div>
                        {hasAccess(resolvedParams.id, 'documents') ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDocumentDownload(doc)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDocumentDownload(doc)}
                            className="border-orange-300 text-orange-600 hover:bg-orange-50"
                          >
                            <Lock className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="videos">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Video className="h-5 w-5 mr-2" />
                  Company Videos ({company.videos.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {company.videos.map((video) => (
                    <div key={video.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                        <Button
                          variant="ghost"
                          size="lg"
                          onClick={() => toast.success(`Playing ${video.title}...`)}
                        >
                          <Play className="h-8 w-8" />
                        </Button>
                      </div>
                      <h4 className="font-medium">{video.title}</h4>
                      <p className="text-sm text-gray-500 capitalize">{video.type.replace('_', ' ')}</p>
                      {video.duration && (
                        <p className="text-xs text-gray-400">Duration: {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contact">
            {hasAccess(resolvedParams.id, 'contact') ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>HR Contact Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">HR Manager</label>
                      <p className="text-gray-900">{company.contactInfo?.hrManagerName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">HR Email</label>
                      <p className="text-gray-900">{company.contactInfo?.hrEmail}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">HR Phone</label>
                      <p className="text-gray-900">{company.contactInfo?.hrPhone}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">WhatsApp</label>
                      <p className="text-gray-900">{company.contactInfo?.hrWhatsapp}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Company Contact</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company Email</label>
                      <p className="text-gray-900">{company.contactInfo?.companyEmail}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company Phone</label>
                      <p className="text-gray-900">{company.contactInfo?.companyPhone}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Address</label>
                      <p className="text-gray-900">{company.contactInfo?.address}</p>
                    </div>
                    {company.contactInfo?.poBox && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">P.O. Box</label>
                        <p className="text-gray-900">{company.contactInfo.poBox}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <Lock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Contact Information Protected</h3>
                  <p className="text-gray-600 mb-4">
                    Pay 25 SAR to access contact information or 75 SAR for premium access.
                  </p>
                  <div className="flex gap-2 justify-center">
                    <Button onClick={handleContactAccess}>
                      Unlock Contact (25 SAR)
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setPaymentAccessType('premium')
                        setShowPaymentGateway(true)
                      }}
                    >
                      Premium Access (75 SAR)
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Payment Gateway Modal */}
      {showPaymentGateway && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <PaymentGateway
                profileId={resolvedParams.id}
                profileType="company"
                profileName={company?.companyInfo?.companyName || 'Company'}
                accessType={paymentAccessType}
                onSuccess={handlePaymentSuccess}
                onCancel={() => setShowPaymentGateway(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
