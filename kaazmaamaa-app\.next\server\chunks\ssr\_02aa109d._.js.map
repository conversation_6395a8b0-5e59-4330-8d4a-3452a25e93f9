{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/workers/page.tsx"], "sourcesContent": ["import React from 'react'\n\nexport default function WorkersPage() {\n  return (\n    <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>\n      {/* Top Header with Sign In and Register buttons */}\n      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#2563eb', margin: 0 }}>\n              KAAZMAAMAA\n            </h2>\n            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Workers</span>\n          </div>\n          \n          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>\n            <a \n              href=\"/auth/login\"\n              style={{ \n                color: '#2563eb', \n                textDecoration: 'none',\n                padding: '0.5rem 1rem',\n                border: '1px solid #2563eb',\n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Sign In\n            </a>\n            \n            <a \n              href=\"/auth/signup\"\n              style={{ \n                backgroundColor: '#2563eb', \n                color: 'white', \n                textDecoration: 'none',\n                padding: '0.5rem 1rem', \n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500'\n              }}\n            >\n              Register\n            </a>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div style={{ padding: '2rem' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>\n          <h1 style={{ fontSize: '3rem', fontWeight: 'bold', color: '#2563eb', textAlign: 'center', marginBottom: '2rem' }}>\n            Workers Directory\n          </h1>\n          \n          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>\n            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🔧</div>\n            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1rem' }}>\n              Professional Workers Network\n            </h2>\n            <p style={{ color: '#6b7280', marginBottom: '2rem' }}>\n              Connect with skilled professionals and workers for your projects and business needs.\n            </p>\n            \n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>\n              <div style={{ padding: '1.5rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem' }}>\n                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#2563eb', marginBottom: '0.5rem' }}>\n                  Browse Workers\n                </h3>\n                <p style={{ color: '#6b7280', marginBottom: '1rem' }}>Find skilled professionals</p>\n                <button style={{ \n                  backgroundColor: '#2563eb', \n                  color: 'white', \n                  padding: '0.5rem 1rem', \n                  border: 'none', \n                  borderRadius: '0.25rem',\n                  cursor: 'pointer'\n                }}>\n                  View All Workers\n                </button>\n              </div>\n              \n              <div style={{ padding: '1.5rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem' }}>\n                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#059669', marginBottom: '0.5rem' }}>\n                  Find Services\n                </h3>\n                <p style={{ color: '#6b7280', marginBottom: '1rem' }}>Search by skills</p>\n                <button style={{ \n                  backgroundColor: '#059669', \n                  color: 'white', \n                  padding: '0.5rem 1rem', \n                  border: 'none', \n                  borderRadius: '0.25rem',\n                  cursor: 'pointer'\n                }}>\n                  Browse Services\n                </button>\n              </div>\n              \n              <div style={{ padding: '1.5rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem' }}>\n                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#dc2626', marginBottom: '0.5rem' }}>\n                  Create Profile\n                </h3>\n                <p style={{ color: '#6b7280', marginBottom: '1rem' }}>Join as a worker</p>\n                <a \n                  href=\"/workers/create\"\n                  style={{ \n                    display: 'inline-block',\n                    backgroundColor: '#dc2626', \n                    color: 'white', \n                    padding: '0.5rem 1rem', \n                    borderRadius: '0.25rem',\n                    textDecoration: 'none'\n                  }}\n                >\n                  Create Profile\n                </a>\n              </div>\n            </div>\n            \n            <div style={{ marginTop: '2rem', padding: '1.5rem', backgroundColor: '#f3f4f6', borderRadius: '0.5rem' }}>\n              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>\n                Worker Features\n              </h3>\n              <ul style={{ textAlign: 'left', color: '#6b7280', maxWidth: '600px', margin: '0 auto' }}>\n                <li style={{ marginBottom: '0.5rem' }}>• Professional worker profiles</li>\n                <li style={{ marginBottom: '0.5rem' }}>• Skills and experience showcase</li>\n                <li style={{ marginBottom: '0.5rem' }}>• Direct client connections</li>\n                <li style={{ marginBottom: '0.5rem' }}>• WhatsApp integration</li>\n                <li style={{ marginBottom: '0.5rem' }}>• Document management</li>\n                <li>• Real-time messaging with Barta</li>\n              </ul>\n            </div>\n            \n            <div style={{ marginTop: '2rem', paddingTop: '1.5rem', borderTop: '1px solid #e5e7eb' }}>\n              <p style={{ color: '#9ca3af' }}>\n                <a href=\"/\" style={{ color: '#2563eb', textDecoration: 'none' }}>← Back to Home</a> | \n                <a href=\"/feed\" style={{ color: '#2563eb', textDecoration: 'none', marginLeft: '0.5rem' }}>Feed</a> | \n                <a href=\"/suppliers\" style={{ color: '#2563eb', textDecoration: 'none', marginLeft: '0.5rem' }}>Suppliers</a> | \n                <a href=\"/companies\" style={{ color: '#2563eb', textDecoration: 'none', marginLeft: '0.5rem' }}>Companies</a> | \n                <a href=\"/barta\" style={{ color: '#2563eb', textDecoration: 'none', marginLeft: '0.5rem' }}>Barta</a>\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,OAAO;YAAE,WAAW;YAAS,iBAAiB;QAAU;;0BAE3D,8OAAC;gBAAI,OAAO;oBAAE,iBAAiB;oBAAS,WAAW;oBAA6B,SAAS;gBAAY;0BACnG,cAAA,8OAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAU,QAAQ;wBAAU,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCACzH,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAO;;8CAC/D,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAQ,OAAO;wCAAW,QAAQ;oCAAE;8CAAG;;;;;;8CAGpF,8OAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;oCAAW;8CAAG;;;;;;;;;;;;sCAG3D,8OAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAQ,YAAY;4BAAS;;8CAC/D,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,OAAO;wCACP,gBAAgB;wCAChB,SAAS;wCACT,QAAQ;wCACR,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;8CAID,8OAAC;oCACC,MAAK;oCACL,OAAO;wCACL,iBAAiB;wCACjB,OAAO;wCACP,gBAAgB;wCAChB,SAAS;wCACT,cAAc;wCACd,UAAU;wCACV,YAAY;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAO;0BAC5B,cAAA,8OAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAU,QAAQ;oBAAS;;sCACjD,8OAAC;4BAAG,OAAO;gCAAE,UAAU;gCAAQ,YAAY;gCAAQ,OAAO;gCAAW,WAAW;gCAAU,cAAc;4BAAO;sCAAG;;;;;;sCAIlH,8OAAC;4BAAI,OAAO;gCAAE,iBAAiB;gCAAS,cAAc;gCAAU,WAAW;gCAA6B,SAAS;gCAAQ,WAAW;4BAAS;;8CAC3I,8OAAC;oCAAI,OAAO;wCAAE,UAAU;wCAAQ,cAAc;oCAAO;8CAAG;;;;;;8CACxD,8OAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAO,OAAO;wCAAW,cAAc;oCAAO;8CAAG;;;;;;8CAG9F,8OAAC;oCAAE,OAAO;wCAAE,OAAO;wCAAW,cAAc;oCAAO;8CAAG;;;;;;8CAItD,8OAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,qBAAqB;wCAAwC,KAAK;wCAAU,cAAc;oCAAO;;sDAC9H,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAU,QAAQ;gDAAqB,cAAc;4CAAS;;8DACnF,8OAAC;oDAAG,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,cAAc;oDAAS;8DAAG;;;;;;8DAGlG,8OAAC;oDAAE,OAAO;wDAAE,OAAO;wDAAW,cAAc;oDAAO;8DAAG;;;;;;8DACtD,8OAAC;oDAAO,OAAO;wDACb,iBAAiB;wDACjB,OAAO;wDACP,SAAS;wDACT,QAAQ;wDACR,cAAc;wDACd,QAAQ;oDACV;8DAAG;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAU,QAAQ;gDAAqB,cAAc;4CAAS;;8DACnF,8OAAC;oDAAG,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,cAAc;oDAAS;8DAAG;;;;;;8DAGlG,8OAAC;oDAAE,OAAO;wDAAE,OAAO;wDAAW,cAAc;oDAAO;8DAAG;;;;;;8DACtD,8OAAC;oDAAO,OAAO;wDACb,iBAAiB;wDACjB,OAAO;wDACP,SAAS;wDACT,QAAQ;wDACR,cAAc;wDACd,QAAQ;oDACV;8DAAG;;;;;;;;;;;;sDAKL,8OAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAU,QAAQ;gDAAqB,cAAc;4CAAS;;8DACnF,8OAAC;oDAAG,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,cAAc;oDAAS;8DAAG;;;;;;8DAGlG,8OAAC;oDAAE,OAAO;wDAAE,OAAO;wDAAW,cAAc;oDAAO;8DAAG;;;;;;8DACtD,8OAAC;oDACC,MAAK;oDACL,OAAO;wDACL,SAAS;wDACT,iBAAiB;wDACjB,OAAO;wDACP,SAAS;wDACT,cAAc;wDACd,gBAAgB;oDAClB;8DACD;;;;;;;;;;;;;;;;;;8CAML,8OAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAQ,SAAS;wCAAU,iBAAiB;wCAAW,cAAc;oCAAS;;sDACrG,8OAAC;4CAAG,OAAO;gDAAE,UAAU;gDAAY,YAAY;gDAAO,OAAO;gDAAW,cAAc;4CAAS;sDAAG;;;;;;sDAGlG,8OAAC;4CAAG,OAAO;gDAAE,WAAW;gDAAQ,OAAO;gDAAW,UAAU;gDAAS,QAAQ;4CAAS;;8DACpF,8OAAC;oDAAG,OAAO;wDAAE,cAAc;oDAAS;8DAAG;;;;;;8DACvC,8OAAC;oDAAG,OAAO;wDAAE,cAAc;oDAAS;8DAAG;;;;;;8DACvC,8OAAC;oDAAG,OAAO;wDAAE,cAAc;oDAAS;8DAAG;;;;;;8DACvC,8OAAC;oDAAG,OAAO;wDAAE,cAAc;oDAAS;8DAAG;;;;;;8DACvC,8OAAC;oDAAG,OAAO;wDAAE,cAAc;oDAAS;8DAAG;;;;;;8DACvC,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;8CAIR,8OAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAQ,YAAY;wCAAU,WAAW;oCAAoB;8CACpF,cAAA,8OAAC;wCAAE,OAAO;4CAAE,OAAO;wCAAU;;0DAC3B,8OAAC;gDAAE,MAAK;gDAAI,OAAO;oDAAE,OAAO;oDAAW,gBAAgB;gDAAO;0DAAG;;;;;;4CAAkB;0DACnF,8OAAC;gDAAE,MAAK;gDAAQ,OAAO;oDAAE,OAAO;oDAAW,gBAAgB;oDAAQ,YAAY;gDAAS;0DAAG;;;;;;4CAAQ;0DACnG,8OAAC;gDAAE,MAAK;gDAAa,OAAO;oDAAE,OAAO;oDAAW,gBAAgB;oDAAQ,YAAY;gDAAS;0DAAG;;;;;;4CAAa;0DAC7G,8OAAC;gDAAE,MAAK;gDAAa,OAAO;oDAAE,OAAO;oDAAW,gBAAgB;oDAAQ,YAAY;gDAAS;0DAAG;;;;;;4CAAa;0DAC7G,8OAAC;gDAAE,MAAK;gDAAS,OAAO;oDAAE,OAAO;oDAAW,gBAAgB;oDAAQ,YAAY;gDAAS;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5G", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,IAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}