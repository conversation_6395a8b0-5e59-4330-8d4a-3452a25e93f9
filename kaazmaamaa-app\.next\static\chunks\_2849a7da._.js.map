{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/companies/profile/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter, useSearchParams } from 'next/navigation'\n\nexport default function CompanyProfilePage() {\n  const { user, userRole, getUserProfile, loading } = useAuth()\n  const router = useRouter()\n  const searchParams = useSearchParams()\n  const [userProfile, setUserProfile] = useState<any>(null)\n  const [isOwnProfile, setIsOwnProfile] = useState(true)\n  const targetUserId = searchParams.get('user')\n\n  // Get user profile data\n  useEffect(() => {\n    if (user && userRole) {\n      const profile = getUserProfile()\n      setUserProfile(profile)\n      \n      // Check if viewing own profile or someone else's\n      if (targetUserId && targetUserId !== user.email) {\n        setIsOwnProfile(false)\n      } else {\n        setIsOwnProfile(true)\n      }\n    }\n  }, [user, userRole, getUserProfile, targetUserId])\n\n  // Redirect to home if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/')\n    }\n  }, [user, loading, router])\n\n  // Show loading if still authenticating\n  if (loading) {\n    return (\n      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f3f4f6' }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>\n          <div style={{ color: '#6b7280' }}>Loading profile...</div>\n        </div>\n      </div>\n    )\n  }\n\n  // Redirect if not authenticated\n  if (!user) {\n    return null\n  }\n\n  // Helper function to get user's display name\n  const getUserDisplayName = () => {\n    if (!userProfile) return 'Company'\n    return userProfile.companyInfo?.companyName || 'Company'\n  }\n\n  // Helper function to get user's initial\n  const getUserInitial = () => {\n    const name = getUserDisplayName()\n    return name.charAt(0).toUpperCase()\n  }\n\n  return (\n    <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>\n      {/* Header */}\n      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#7c3aed', margin: 0 }}>\n              KAAZMAAMAA\n            </h2>\n            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Company Profile</span>\n          </div>\n          \n          <div style={{ display: 'flex', gap: '0.75rem' }}>\n            <button \n              onClick={() => router.push('/feed')}\n              style={{ \n                backgroundColor: '#7c3aed', \n                color: 'white', \n                border: 'none',\n                padding: '0.5rem 1rem', \n                borderRadius: '0.25rem',\n                fontSize: '0.875rem',\n                fontWeight: '500',\n                cursor: 'pointer'\n              }}\n            >\n              Back to Feed\n            </button>\n            \n            {isOwnProfile && (\n              <button \n                onClick={() => {\n                  router.push('/companies/edit')\n                }}\n                style={{ \n                  backgroundColor: '#059669', \n                  color: 'white', \n                  border: 'none',\n                  padding: '0.5rem 1rem', \n                  borderRadius: '0.25rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer'\n                }}\n              >\n                Edit Profile\n              </button>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div style={{ padding: '2rem' }}>\n        <div style={{ maxWidth: '800px', margin: '0 auto' }}>\n          \n          {/* Profile Header */}\n          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', marginBottom: '2rem' }}>\n              <div style={{ \n                backgroundColor: '#7c3aed', \n                borderRadius: '50%', \n                width: '5rem', \n                height: '5rem', \n                display: 'flex', \n                alignItems: 'center', \n                justifyContent: 'center', \n                color: 'white', \n                fontSize: '2rem',\n                fontWeight: 'bold' \n              }}>\n                {getUserInitial()}\n              </div>\n              \n              <div>\n                <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0, marginBottom: '0.5rem' }}>\n                  {getUserDisplayName()}\n                </h1>\n                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', color: '#6b7280', marginBottom: '0.5rem' }}>\n                  <span>🏢</span>\n                  <span>Professional Company</span>\n                </div>\n                {userProfile?.companyInfo?.email && (\n                  <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>\n                    📧 {userProfile.companyInfo.email}\n                  </div>\n                )}\n              </div>\n            </div>\n            \n            {!isOwnProfile && (\n              <div style={{ display: 'flex', gap: '1rem' }}>\n                <button style={{ \n                  backgroundColor: '#7c3aed', \n                  color: 'white', \n                  border: 'none',\n                  padding: '0.75rem 1.5rem', \n                  borderRadius: '0.25rem',\n                  fontSize: '1rem',\n                  fontWeight: '500',\n                  cursor: 'pointer'\n                }}>\n                  Connect\n                </button>\n                <button style={{ \n                  backgroundColor: '#059669', \n                  color: 'white', \n                  border: 'none',\n                  padding: '0.75rem 1.5rem', \n                  borderRadius: '0.25rem',\n                  fontSize: '1rem',\n                  fontWeight: '500',\n                  cursor: 'pointer'\n                }}>\n                  Message\n                </button>\n              </div>\n            )}\n          </div>\n\n          {/* Company Information */}\n          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>\n            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>\n              Company Information\n            </h2>\n            \n            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>\n              {userProfile?.companyInfo?.companyName && (\n                <div>\n                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>\n                    Company Name\n                  </label>\n                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.companyName}</div>\n                </div>\n              )}\n              \n              {userProfile?.companyInfo?.email && (\n                <div>\n                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>\n                    Email\n                  </label>\n                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.email}</div>\n                </div>\n              )}\n              \n              {userProfile?.companyInfo?.phone && (\n                <div>\n                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>\n                    Phone\n                  </label>\n                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.phone}</div>\n                </div>\n              )}\n              \n              {userProfile?.companyInfo?.industry && (\n                <div>\n                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>\n                    Industry\n                  </label>\n                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.industry}</div>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Company Details */}\n          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem' }}>\n            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>\n              Company Details & Services\n            </h2>\n            \n            <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>\n              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🏢</div>\n              <p>Company details and services information will be displayed here.</p>\n              {isOwnProfile && (\n                <button \n                  onClick={() => router.push('/companies/edit')}\n                  style={{ \n                    backgroundColor: '#7c3aed', \n                    color: 'white', \n                    border: 'none',\n                    padding: '0.75rem 1.5rem', \n                    borderRadius: '0.25rem',\n                    fontSize: '1rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    marginTop: '1rem'\n                  }}\n                >\n                  Add Company Details\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC1D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,aAAa,GAAG,CAAC;IAEtC,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,QAAQ,UAAU;gBACpB,MAAM,UAAU;gBAChB,eAAe;gBAEf,iDAAiD;gBACjD,IAAI,gBAAgB,iBAAiB,KAAK,KAAK,EAAE;oBAC/C,gBAAgB;gBAClB,OAAO;oBACL,gBAAgB;gBAClB;YACF;QACF;uCAAG;QAAC;QAAM;QAAU;QAAgB;KAAa;IAEjD,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;uCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,uCAAuC;IACvC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,OAAO;gBAAE,WAAW;gBAAS,SAAS;gBAAQ,YAAY;gBAAU,gBAAgB;gBAAU,iBAAiB;YAAU;sBAC5H,cAAA,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAS;;kCAChC,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,cAAc;wBAAO;kCAAG;;;;;;kCACxD,6LAAC;wBAAI,OAAO;4BAAE,OAAO;wBAAU;kCAAG;;;;;;;;;;;;;;;;;IAI1C;IAEA,gCAAgC;IAChC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,6CAA6C;IAC7C,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,OAAO;QACzB,OAAO,YAAY,WAAW,EAAE,eAAe;IACjD;IAEA,wCAAwC;IACxC,MAAM,iBAAiB;QACrB,MAAM,OAAO;QACb,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW;IACnC;IAEA,qBACE,6LAAC;QAAI,OAAO;YAAE,WAAW;YAAS,iBAAiB;QAAU;;0BAE3D,6LAAC;gBAAI,OAAO;oBAAE,iBAAiB;oBAAS,WAAW;oBAA6B,SAAS;gBAAY;0BACnG,cAAA,6LAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAU,QAAQ;wBAAU,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCACzH,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAO;;8CAC/D,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAQ,OAAO;wCAAW,QAAQ;oCAAE;8CAAG;;;;;;8CAGpF,6LAAC;oCAAK,OAAO;wCAAE,OAAO;wCAAW,UAAU;oCAAW;8CAAG;;;;;;;;;;;;sCAG3D,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;4BAAU;;8CAC5C,6LAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,OAAO;wCACL,iBAAiB;wCACjB,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,cAAc;wCACd,UAAU;wCACV,YAAY;wCACZ,QAAQ;oCACV;8CACD;;;;;;gCAIA,8BACC,6LAAC;oCACC,SAAS;wCACP,OAAO,IAAI,CAAC;oCACd;oCACA,OAAO;wCACL,iBAAiB;wCACjB,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,cAAc;wCACd,UAAU;wCACV,YAAY;wCACZ,QAAQ;oCACV;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,OAAO;oBAAE,SAAS;gBAAO;0BAC5B,cAAA,6LAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAS,QAAQ;oBAAS;;sCAGhD,6LAAC;4BAAI,OAAO;gCAAE,iBAAiB;gCAAS,cAAc;gCAAU,WAAW;gCAA6B,SAAS;gCAAQ,cAAc;4BAAO;;8CAC5I,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,YAAY;wCAAU,KAAK;wCAAQ,cAAc;oCAAO;;sDACrF,6LAAC;4CAAI,OAAO;gDACV,iBAAiB;gDACjB,cAAc;gDACd,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,OAAO;gDACP,UAAU;gDACV,YAAY;4CACd;sDACG;;;;;;sDAGH,6LAAC;;8DACC,6LAAC;oDAAG,OAAO;wDAAE,UAAU;wDAAQ,YAAY;wDAAQ,OAAO;wDAAW,QAAQ;wDAAG,cAAc;oDAAS;8DACpG;;;;;;8DAEH,6LAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,YAAY;wDAAU,KAAK;wDAAU,OAAO;wDAAW,cAAc;oDAAS;;sEAC3G,6LAAC;sEAAK;;;;;;sEACN,6LAAC;sEAAK;;;;;;;;;;;;gDAEP,aAAa,aAAa,uBACzB,6LAAC;oDAAI,OAAO;wDAAE,OAAO;wDAAW,UAAU;oDAAW;;wDAAG;wDAClD,YAAY,WAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;gCAMxC,CAAC,8BACA,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,KAAK;oCAAO;;sDACzC,6LAAC;4CAAO,OAAO;gDACb,iBAAiB;gDACjB,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,QAAQ;4CACV;sDAAG;;;;;;sDAGH,6LAAC;4CAAO,OAAO;gDACb,iBAAiB;gDACjB,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,QAAQ;4CACV;sDAAG;;;;;;;;;;;;;;;;;;sCAQT,6LAAC;4BAAI,OAAO;gCAAE,iBAAiB;gCAAS,cAAc;gCAAU,WAAW;gCAA6B,SAAS;gCAAQ,cAAc;4BAAO;;8CAC5I,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAO,OAAO;wCAAW,cAAc;oCAAS;8CAAG;;;;;;8CAIhG,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,qBAAqB;wCAAwC,KAAK;oCAAS;;wCACvG,aAAa,aAAa,6BACzB,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,SAAS;wDAAS,cAAc;oDAAU;8DAAG;;;;;;8DAGxH,6LAAC;oDAAI,OAAO;wDAAE,OAAO;oDAAU;8DAAI,YAAY,WAAW,CAAC,WAAW;;;;;;;;;;;;wCAIzE,aAAa,aAAa,uBACzB,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,SAAS;wDAAS,cAAc;oDAAU;8DAAG;;;;;;8DAGxH,6LAAC;oDAAI,OAAO;wDAAE,OAAO;oDAAU;8DAAI,YAAY,WAAW,CAAC,KAAK;;;;;;;;;;;;wCAInE,aAAa,aAAa,uBACzB,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,SAAS;wDAAS,cAAc;oDAAU;8DAAG;;;;;;8DAGxH,6LAAC;oDAAI,OAAO;wDAAE,OAAO;oDAAU;8DAAI,YAAY,WAAW,CAAC,KAAK;;;;;;;;;;;;wCAInE,aAAa,aAAa,0BACzB,6LAAC;;8DACC,6LAAC;oDAAM,OAAO;wDAAE,UAAU;wDAAY,YAAY;wDAAO,OAAO;wDAAW,SAAS;wDAAS,cAAc;oDAAU;8DAAG;;;;;;8DAGxH,6LAAC;oDAAI,OAAO;wDAAE,OAAO;oDAAU;8DAAI,YAAY,WAAW,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sCAO3E,6LAAC;4BAAI,OAAO;gCAAE,iBAAiB;gCAAS,cAAc;gCAAU,WAAW;gCAA6B,SAAS;4BAAO;;8CACtH,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAO,OAAO;wCAAW,cAAc;oCAAS;8CAAG;;;;;;8CAIhG,6LAAC;oCAAI,OAAO;wCAAE,WAAW;wCAAU,SAAS;wCAAQ,OAAO;oCAAU;;sDACnE,6LAAC;4CAAI,OAAO;gDAAE,UAAU;gDAAQ,cAAc;4CAAO;sDAAG;;;;;;sDACxD,6LAAC;sDAAE;;;;;;wCACF,8BACC,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,OAAO;gDACL,iBAAiB;gDACjB,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,QAAQ;gDACR,WAAW;4CACb;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAjQwB;;QAC8B,kIAAA,CAAA,UAAO;QAC5C,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAHd", "debugId": null}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}