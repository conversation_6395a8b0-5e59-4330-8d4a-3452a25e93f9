'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Download, Settings, Terminal, Globe, CheckCircle, Code, Package } from 'lucide-react'
import { toast } from 'sonner'

export default function Step2Page() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<string[]>([])

  const markSectionComplete = (sectionId: string) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section completed! ✅`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Step 2: Developer Tools Setup</h1>
              <Badge className="bg-blue-100 text-blue-800">
                Development
              </Badge>
            </div>
            <Button
              onClick={() => {
                toast.success('Proceeding to Step 3...')
                router.push('/invoice/step3')
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Next Step
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview */}
        <Card className="mb-6 bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">🛠️ ZATCA Developer Tools & SDK Setup</h3>
              <p className="text-blue-600 mb-4">Download and integrate compliance testing tools</p>
            </div>
          </CardContent>
        </Card>

        {/* SDK Download */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Download className="h-6 w-6 text-green-600" />
              <span>ZATCA SDK Download</span>
              <Badge className="bg-green-100 text-green-800">Required</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-semibold text-green-800 mb-2">Download Sources:</h4>
                <ul className="space-y-2 text-green-700">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span><strong>ZATCA Developer Portal:</strong> https://developer.zatca.gov.sa</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span><strong>Compliance Toolbox:</strong> CLI and offline validation tools</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span><strong>SDK Libraries:</strong> Java, .NET, Python, NodeJS</span>
                  </li>
                </ul>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2">Installation Commands:</h4>
                <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                  <div># Download ZATCA SDK</div>
                  <div>wget https://developer.zatca.gov.sa/sdk/zatca-sdk.zip</div>
                  <div className="mt-2"># Extract and install</div>
                  <div>unzip zatca-sdk.zip</div>
                  <div>cd zatca-sdk</div>
                  <div>./install.sh</div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('sdk')}
                variant={completedSections.includes('sdk') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('sdk') ? '✅ SDK Downloaded' : 'Mark SDK Download Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* CLI Tools */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Terminal className="h-6 w-6 text-purple-600" />
              <span>CLI Validation Tools</span>
              <Badge className="bg-purple-100 text-purple-800">Testing</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-purple-50 p-4 rounded-lg">
                <h4 className="font-semibold text-purple-800 mb-2">Available CLI Commands:</h4>
                <div className="space-y-3">
                  <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                    <div># Validate XML invoice structure</div>
                    <div>fatoorah validate-invoice &lt;invoice.xml&gt;</div>
                  </div>
                  <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                    <div># Validate QR code format</div>
                    <div>fatoorah validate-qr &lt;invoice.xml&gt;</div>
                  </div>
                  <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                    <div># Generate compliant QR code</div>
                    <div>fatoorah generate-qr -f &lt;invoice.xml&gt;</div>
                  </div>
                  <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                    <div># Test digital signature</div>
                    <div>fatoorah validate-signature &lt;invoice.xml&gt;</div>
                  </div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('cli')}
                variant={completedSections.includes('cli') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('cli') ? '✅ CLI Tools Configured' : 'Mark CLI Setup Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Sandbox Environment */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-6 w-6 text-orange-600" />
              <span>Sandbox Environment</span>
              <Badge className="bg-orange-100 text-orange-800">Testing</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-orange-50 p-4 rounded-lg">
                <h4 className="font-semibold text-orange-800 mb-2">Sandbox Features:</h4>
                <ul className="space-y-2 text-orange-700">
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Simulate EGS onboarding process</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Test API calls (clearance/reporting)</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Mock CSID generation and validation</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                    <span>Error simulation and handling</span>
                  </li>
                </ul>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-800 mb-2">Sandbox Endpoints:</h4>
                <div className="space-y-2 font-mono text-sm">
                  <div><strong>Base URL:</strong> https://sandbox-api.zatca.gov.sa</div>
                  <div><strong>Onboarding:</strong> /production/csids</div>
                  <div><strong>Clearance:</strong> /invoices/clearance/run</div>
                  <div><strong>Reporting:</strong> /invoices/reporting/single</div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('sandbox')}
                variant={completedSections.includes('sandbox') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('sandbox') ? '✅ Sandbox Configured' : 'Mark Sandbox Setup Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Code Integration */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Code className="h-6 w-6 text-indigo-600" />
              <span>SDK Integration Examples</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-indigo-50 p-4 rounded-lg">
                <h4 className="font-semibold text-indigo-800 mb-2">NodeJS Integration:</h4>
                <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                  <div>const &#123; ZATCAClient &#125; = require('@zatca/sdk');</div>
                  <div className="mt-1">const client = new ZATCAClient(&#123;</div>
                  <div>&nbsp;&nbsp;baseURL: 'https://sandbox-api.zatca.gov.sa',</div>
                  <div>&nbsp;&nbsp;certificate: 'path/to/cert.pem',</div>
                  <div>&nbsp;&nbsp;privateKey: 'path/to/key.pem'</div>
                  <div>&#125;);</div>
                </div>
              </div>

              <div className="bg-indigo-50 p-4 rounded-lg">
                <h4 className="font-semibold text-indigo-800 mb-2">Python Integration:</h4>
                <div className="bg-black text-green-400 p-3 rounded font-mono text-sm">
                  <div>from zatca_sdk import ZATCAClient</div>
                  <div className="mt-1">client = ZATCAClient(</div>
                  <div>&nbsp;&nbsp;&nbsp;&nbsp;base_url='https://sandbox-api.zatca.gov.sa',</div>
                  <div>&nbsp;&nbsp;&nbsp;&nbsp;cert_path='cert.pem',</div>
                  <div>&nbsp;&nbsp;&nbsp;&nbsp;key_path='key.pem'</div>
                  <div>)</div>
                </div>
              </div>

              <Button
                onClick={() => markSectionComplete('integration')}
                variant={completedSections.includes('integration') ? 'secondary' : 'outline'}
                className="w-full"
              >
                {completedSections.includes('integration') ? '✅ Integration Examples Reviewed' : 'Mark Integration Complete'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Progress Summary */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">Step 2 Progress</h3>
              <p className="text-blue-600 mb-4">
                {completedSections.length}/4 sections completed
              </p>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => router.push('/invoice/step1')}
                  variant="outline"
                >
                  Previous Step
                </Button>
                <Button
                  onClick={() => {
                    if (completedSections.length === 4) {
                      toast.success('Step 2 completed! Moving to Step 3...')
                      router.push('/invoice/step3')
                    } else {
                      toast.warning('Please complete all sections first')
                    }
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                  disabled={completedSections.length < 4}
                >
                  Continue to Step 3
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
