"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PostsContext */ \"(app-pages-browser)/./src/contexts/PostsContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FeedPage() {\n    _s();\n    const { user, userRole, getUserProfile, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { posts, addPost, likePost, reactToPost, addComment, sharePost, voteInPoll, checkIn, createEvent } = (0,_contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Create Post States\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postText, setPostText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFeeling, setSelectedFeeling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taggedUsers, setTaggedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postPrivacy, setPostPrivacy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('public');\n    // Comments States\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [commentTexts, setCommentTexts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAllComments, setShowAllComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Share States\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shareText, setShareText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Other States\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeStory, setActiveStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLocationPicker, setShowLocationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTagPicker, setShowTagPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (user && userRole) {\n                const profile = getUserProfile();\n                setUserProfile(profile);\n                console.log('User profile loaded:', profile);\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        userRole,\n        getUserProfile\n    ]);\n    // Redirect to home if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/');\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Helper function to get user's display name\n    const getUserDisplayName = ()=>{\n        if (!userProfile) return 'User';\n        if (userRole === 'worker') {\n            var _userProfile_personalInfo;\n            return ((_userProfile_personalInfo = userProfile.personalInfo) === null || _userProfile_personalInfo === void 0 ? void 0 : _userProfile_personalInfo.workerName) || 'Worker';\n        } else if (userRole === 'supplier') {\n            var _userProfile_personalInfo1;\n            return ((_userProfile_personalInfo1 = userProfile.personalInfo) === null || _userProfile_personalInfo1 === void 0 ? void 0 : _userProfile_personalInfo1.supplierName) || 'Supplier';\n        } else if (userRole === 'company') {\n            var _userProfile_companyInfo;\n            return ((_userProfile_companyInfo = userProfile.companyInfo) === null || _userProfile_companyInfo === void 0 ? void 0 : _userProfile_companyInfo.companyName) || 'Company';\n        }\n        return 'User';\n    };\n    // Helper function to get user's avatar/initial\n    const getUserInitial = ()=>{\n        const name = getUserDisplayName();\n        return name.charAt(0).toUpperCase();\n    };\n    // Helper function to get user's role emoji\n    const getUserRoleEmoji = ()=>{\n        switch(userRole){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Helper functions for Facebook-style features\n    const handleLikePost = (postId)=>{\n        likePost(postId);\n    };\n    const handleCommentSubmit = (postId)=>{\n        const commentText = commentTexts[postId];\n        if (!(commentText === null || commentText === void 0 ? void 0 : commentText.trim()) || !user) return;\n        addComment(postId, commentText, {\n            id: user.email,\n            name: getUserDisplayName(),\n            role: userRole || 'worker'\n        });\n        setCommentTexts((prev)=>({\n                ...prev,\n                [postId]: ''\n            }));\n    };\n    const handleSharePost = (postId)=>{\n        if (!shareText.trim()) {\n            sharePost(postId);\n        } else {\n            // Create a new post that shares the original\n            const originalPost = posts.find((p)=>p.id === postId);\n            if (originalPost) {\n                const newPost = {\n                    user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                    user_name: getUserDisplayName(),\n                    user_role: userRole || 'worker',\n                    content: shareText,\n                    post_type: 'text',\n                    visibility: 'public'\n                };\n                addPost(newPost);\n            }\n        }\n        setShowShareModal(null);\n        setShareText('');\n    };\n    const toggleComments = (postId)=>{\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !prev[postId]\n            }));\n    };\n    const formatTimeAgo = (dateString)=>{\n        const now = new Date();\n        const postDate = new Date(dateString);\n        const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d\");\n        const diffInWeeks = Math.floor(diffInDays / 7);\n        return \"\".concat(diffInWeeks, \"w\");\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'worker':\n                return '#2563eb';\n            case 'supplier':\n                return '#059669';\n            case 'company':\n                return '#7c3aed';\n            default:\n                return '#6b7280';\n        }\n    };\n    const getRoleEmoji = (role)=>{\n        switch(role){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Advanced reaction system\n    const reactions = [\n        {\n            type: 'like',\n            emoji: '👍',\n            color: '#1877f2',\n            label: 'Like'\n        },\n        {\n            type: 'love',\n            emoji: '❤️',\n            color: '#e74c3c',\n            label: 'Love'\n        },\n        {\n            type: 'care',\n            emoji: '🤗',\n            color: '#f39c12',\n            label: 'Care'\n        },\n        {\n            type: 'haha',\n            emoji: '😂',\n            color: '#f1c40f',\n            label: 'Haha'\n        },\n        {\n            type: 'wow',\n            emoji: '😮',\n            color: '#f39c12',\n            label: 'Wow'\n        },\n        {\n            type: 'sad',\n            emoji: '😢',\n            color: '#3498db',\n            label: 'Sad'\n        },\n        {\n            type: 'angry',\n            emoji: '😡',\n            color: '#e74c3c',\n            label: 'Angry'\n        }\n    ];\n    const handleReaction = (postId, reactionType)=>{\n        if (!user) return;\n        reactToPost(postId, reactionType, {\n            id: user.email,\n            name: getUserDisplayName()\n        });\n    };\n    const getReactionDisplay = (post)=>{\n        const reactionCounts = post.reaction_counts || {};\n        const totalReactions = Object.values(reactionCounts).reduce((sum, count)=>sum + (count || 0), 0);\n        if (totalReactions === 0) return null;\n        const topReactions = Object.entries(reactionCounts).filter((param)=>{\n            let [_, count] = param;\n            return count > 0;\n        }).sort((param, param1)=>{\n            let [_, a] = param, [__, b] = param1;\n            return b - a;\n        }).slice(0, 3);\n        return {\n            topReactions,\n            totalReactions\n        };\n    };\n    // Show loading if still authenticating\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#f0f2f5'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '2rem',\n                            marginBottom: '1rem'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#65676b'\n                        },\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: '#f0f2f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: '#1877f2',\n                    padding: '0.75rem 1rem',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: 'white',\n                                        margin: 0\n                                    },\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'rgba(255,255,255,0.2)',\n                                        borderRadius: '20px',\n                                        padding: '0.5rem 1rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search KAAZMAAMAA...\",\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            color: 'white',\n                                            outline: 'none',\n                                            fontSize: '0.875rem'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.75rem',\n                                        color: 'white'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                borderRadius: '50%',\n                                                width: '2rem',\n                                                height: '2rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '0.875rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: getUserInitial()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const profilePath = userRole === 'worker' ? '/workers/profile' : userRole === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                router.push(profilePath);\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Go Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                await signOut();\n                                                router.push('/');\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 2fr 1fr',\n                    gap: '1rem',\n                    padding: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Quick Access\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/workers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDD27\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Workers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/suppliers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e8f5e8',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Suppliers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/companies\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#f3e8ff',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFE2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/barta\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#fff3cd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Barta Messenger\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem',\n                                        overflowX: 'auto',\n                                        paddingBottom: '0.5rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: '120px',\n                                                height: '200px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(45deg, #1877f2, #42a5f5)',\n                                                position: 'relative',\n                                                cursor: 'pointer',\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                justifyContent: 'flex-end',\n                                                padding: '1rem',\n                                                color: 'white'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: 'absolute',\n                                                        top: '1rem',\n                                                        left: '1rem',\n                                                        backgroundColor: 'rgba(255,255,255,0.3)',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '0.875rem',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"Create Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            'Ahmed Al-Rashid',\n                                            'Saudi Building Co.',\n                                            'Tech Solutions',\n                                            'Construction Pro'\n                                        ].map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    minWidth: '120px',\n                                                    height: '200px',\n                                                    borderRadius: '12px',\n                                                    background: \"linear-gradient(45deg, \".concat([\n                                                        '#ff6b6b',\n                                                        '#4ecdc4',\n                                                        '#45b7d1',\n                                                        '#96ceb4'\n                                                    ][index], \", \").concat([\n                                                        '#ffa726',\n                                                        '#26a69a',\n                                                        '#42a5f5',\n                                                        '#81c784'\n                                                    ][index], \")\"),\n                                                    position: 'relative',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    justifyContent: 'space-between',\n                                                    padding: '1rem',\n                                                    color: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: 'rgba(255,255,255,0.3)',\n                                                            borderRadius: '50%',\n                                                            width: '2.5rem',\n                                                            height: '2.5rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: '600'\n                                                        },\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.75rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    color: 'white',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: getUserInitial()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"What's on your mind, \".concat(getUserDisplayName(), \"?\"),\n                                                onClick: ()=>setShowCreatePost(true),\n                                                readOnly: true,\n                                                style: {\n                                                    flex: 1,\n                                                    backgroundColor: '#f0f2f5',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '0.75rem 1rem',\n                                                    outline: 'none',\n                                                    fontSize: '1rem',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-around',\n                                            paddingTop: '0.75rem',\n                                            borderTop: '1px solid #e4e6ea'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDCF7 Photo/Video\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDE0A Feeling/Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, this),\n                            posts.map((post)=>{\n                                var _reactions_find, _reactions_find1, _reactions_find2;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '8px',\n                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(post.user_role),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: post.user_name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // Navigate to user's profile based on their role\n                                                                const profilePath = post.user_role === 'worker' ? '/workers/profile' : post.user_role === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                                router.push(\"\".concat(profilePath, \"?user=\").concat(encodeURIComponent(post.user_id)));\n                                                            },\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                padding: 0,\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21',\n                                                                cursor: 'pointer',\n                                                                fontSize: 'inherit',\n                                                                textAlign: 'left'\n                                                            },\n                                                            onMouseEnter: (e)=>e.target.style.textDecoration = 'underline',\n                                                            onMouseLeave: (e)=>e.target.style.textDecoration = 'none',\n                                                            children: post.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                formatTimeAgo(post.created_at),\n                                                                \" • \",\n                                                                getRoleEmoji(post.user_role),\n                                                                \" \",\n                                                                post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: '1rem',\n                                                paddingRight: '1rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#1c1e21',\n                                                    lineHeight: '1.5',\n                                                    margin: 0\n                                                },\n                                                children: post.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this),\n                                        post.media_urls && post.media_urls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f2f5',\n                                                height: '250px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                color: '#65676b',\n                                                fontSize: '3rem'\n                                            },\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '0.75rem 1rem'\n                                            },\n                                            children: [\n                                                (post.likes > 0 || post.comments > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '0.75rem'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem'\n                                                            },\n                                                            children: (()=>{\n                                                                const reactionDisplay = getReactionDisplay(post);\n                                                                if (!reactionDisplay) return null;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        gap: '0.25rem'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            style: {\n                                                                                display: 'flex',\n                                                                                gap: '0.125rem'\n                                                                            },\n                                                                            children: reactionDisplay.topReactions.map((param)=>{\n                                                                                let [reactionType, count] = param;\n                                                                                const reaction = reactions.find((r)=>r.type === reactionType);\n                                                                                if (!reaction) return null;\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    style: {\n                                                                                        backgroundColor: reaction.color,\n                                                                                        borderRadius: '50%',\n                                                                                        width: '1.25rem',\n                                                                                        height: '1.25rem',\n                                                                                        display: 'flex',\n                                                                                        alignItems: 'center',\n                                                                                        justifyContent: 'center',\n                                                                                        fontSize: '0.75rem',\n                                                                                        border: '2px solid white'\n                                                                                    },\n                                                                                    children: reaction.emoji\n                                                                                }, reactionType, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                    lineNumber: 545,\n                                                                                    columnNumber: 37\n                                                                                }, this);\n                                                                            })\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 540,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                color: '#65676b',\n                                                                                fontSize: '0.875rem'\n                                                                            },\n                                                                            children: reactionDisplay.totalReactions\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 564,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 29\n                                                                }, this);\n                                                            })()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '1rem',\n                                                                color: '#65676b',\n                                                                fontSize: '0.875rem'\n                                                            },\n                                                            children: [\n                                                                post.comments > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>toggleComments(post.id),\n                                                                    style: {\n                                                                        background: 'none',\n                                                                        border: 'none',\n                                                                        color: '#65676b',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.875rem'\n                                                                    },\n                                                                    children: [\n                                                                        post.comments,\n                                                                        \" comments\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                (post.shared_count || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        post.shared_count,\n                                                                        \" shares\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 58\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-around',\n                                                        paddingTop: '0.75rem',\n                                                        borderTop: '1px solid #e4e6ea'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: 'relative',\n                                                                flex: 1\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleLikePost(post.id),\n                                                                    onMouseEnter: ()=>setShowEmojiPicker(post.id),\n                                                                    onMouseLeave: ()=>setTimeout(()=>setShowEmojiPicker(false), 300),\n                                                                    style: {\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        gap: '0.5rem',\n                                                                        backgroundColor: 'transparent',\n                                                                        border: 'none',\n                                                                        padding: '0.5rem 1rem',\n                                                                        borderRadius: '6px',\n                                                                        cursor: 'pointer',\n                                                                        color: post.user_reaction ? ((_reactions_find = reactions.find((r)=>r.type === post.user_reaction)) === null || _reactions_find === void 0 ? void 0 : _reactions_find.color) || '#1877f2' : '#65676b',\n                                                                        fontWeight: '500',\n                                                                        width: '100%',\n                                                                        justifyContent: 'center'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            style: {\n                                                                                fontSize: '1.25rem'\n                                                                            },\n                                                                            children: post.user_reaction ? ((_reactions_find1 = reactions.find((r)=>r.type === post.user_reaction)) === null || _reactions_find1 === void 0 ? void 0 : _reactions_find1.emoji) || '👍' : '👍'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 607,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        post.user_reaction ? ((_reactions_find2 = reactions.find((r)=>r.type === post.user_reaction)) === null || _reactions_find2 === void 0 ? void 0 : _reactions_find2.label) || 'Like' : 'Like'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                showEmojiPicker === post.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'absolute',\n                                                                        bottom: '100%',\n                                                                        left: '50%',\n                                                                        transform: 'translateX(-50%)',\n                                                                        backgroundColor: 'white',\n                                                                        borderRadius: '25px',\n                                                                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n                                                                        padding: '0.5rem',\n                                                                        display: 'flex',\n                                                                        gap: '0.25rem',\n                                                                        zIndex: 1000,\n                                                                        marginBottom: '0.5rem'\n                                                                    },\n                                                                    onMouseEnter: ()=>setShowEmojiPicker(post.id),\n                                                                    onMouseLeave: ()=>setShowEmojiPicker(false),\n                                                                    children: reactions.map((reaction)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleReaction(post.id, reaction.type),\n                                                                            style: {\n                                                                                backgroundColor: 'transparent',\n                                                                                border: 'none',\n                                                                                borderRadius: '50%',\n                                                                                width: '2.5rem',\n                                                                                height: '2.5rem',\n                                                                                display: 'flex',\n                                                                                alignItems: 'center',\n                                                                                justifyContent: 'center',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '1.5rem',\n                                                                                transition: 'transform 0.2s'\n                                                                            },\n                                                                            onMouseEnter: (e)=>e.currentTarget.style.transform = 'scale(1.2)',\n                                                                            onMouseLeave: (e)=>e.currentTarget.style.transform = 'scale(1)',\n                                                                            title: reaction.label,\n                                                                            children: reaction.emoji\n                                                                        }, reaction.type, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 634,\n                                                                            columnNumber: 29\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>toggleComments(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Comment\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 660,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowShareModal(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCE4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 700,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Share\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 681,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: '1rem',\n                                                        borderTop: '1px solid #e4e6ea',\n                                                        paddingTop: '1rem'\n                                                    },\n                                                    children: [\n                                                        (post.post_comments || []).slice(0, showAllComments[post.id] ? undefined : 3).map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    gap: '0.75rem',\n                                                                    marginBottom: '1rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundColor: getRoleColor(comment.user_role),\n                                                                            borderRadius: '50%',\n                                                                            width: '2rem',\n                                                                            height: '2rem',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            justifyContent: 'center',\n                                                                            color: 'white',\n                                                                            fontSize: '0.875rem',\n                                                                            fontWeight: 'bold',\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        children: comment.user_name.charAt(0).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 710,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            flex: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#f0f2f5',\n                                                                                    borderRadius: '1rem',\n                                                                                    padding: '0.75rem 1rem'\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            fontWeight: '600',\n                                                                                            fontSize: '0.875rem',\n                                                                                            marginBottom: '0.25rem'\n                                                                                        },\n                                                                                        children: comment.user_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 727,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            fontSize: '0.875rem',\n                                                                                            lineHeight: '1.4'\n                                                                                        },\n                                                                                        children: comment.content\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 730,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 726,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    display: 'flex',\n                                                                                    gap: '1rem',\n                                                                                    marginTop: '0.25rem',\n                                                                                    paddingLeft: '1rem'\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        style: {\n                                                                                            background: 'none',\n                                                                                            border: 'none',\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem',\n                                                                                            cursor: 'pointer',\n                                                                                            fontWeight: '600'\n                                                                                        },\n                                                                                        children: \"Like\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 735,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        style: {\n                                                                                            background: 'none',\n                                                                                            border: 'none',\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem',\n                                                                                            cursor: 'pointer',\n                                                                                            fontWeight: '600'\n                                                                                        },\n                                                                                        children: \"Reply\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 738,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem'\n                                                                                        },\n                                                                                        children: formatTimeAgo(comment.created_at)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 741,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 734,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 725,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, comment.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        (post.post_comments || []).length > 3 && !showAllComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAllComments((prev)=>({\n                                                                        ...prev,\n                                                                        [post.id]: true\n                                                                    })),\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                color: '#65676b',\n                                                                fontSize: '0.875rem',\n                                                                cursor: 'pointer',\n                                                                marginBottom: '1rem'\n                                                            },\n                                                            children: [\n                                                                \"View \",\n                                                                (post.post_comments || []).length - 3,\n                                                                \" more comments\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 751,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '0.75rem',\n                                                                alignItems: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        backgroundColor: getRoleColor(userRole || 'worker'),\n                                                                        borderRadius: '50%',\n                                                                        width: '2rem',\n                                                                        height: '2rem',\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        justifyContent: 'center',\n                                                                        color: 'white',\n                                                                        fontSize: '0.875rem',\n                                                                        fontWeight: 'bold',\n                                                                        flexShrink: 0\n                                                                    },\n                                                                    children: getUserInitial()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        flex: 1,\n                                                                        position: 'relative'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"Write a comment...\",\n                                                                            value: commentTexts[post.id] || '',\n                                                                            onChange: (e)=>setCommentTexts((prev)=>({\n                                                                                        ...prev,\n                                                                                        [post.id]: e.target.value\n                                                                                    })),\n                                                                            onKeyPress: (e)=>{\n                                                                                if (e.key === 'Enter') {\n                                                                                    handleCommentSubmit(post.id);\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                width: '100%',\n                                                                                backgroundColor: '#f0f2f5',\n                                                                                border: 'none',\n                                                                                borderRadius: '1rem',\n                                                                                padding: '0.75rem 1rem',\n                                                                                outline: 'none',\n                                                                                fontSize: '0.875rem'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 777,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        commentTexts[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleCommentSubmit(post.id),\n                                                                            style: {\n                                                                                position: 'absolute',\n                                                                                right: '0.5rem',\n                                                                                top: '50%',\n                                                                                transform: 'translateY(-50%)',\n                                                                                background: 'none',\n                                                                                border: 'none',\n                                                                                color: '#1877f2',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.875rem',\n                                                                                fontWeight: '600'\n                                                                            },\n                                                                            children: \"Post\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 798,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 776,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 760,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this);\n                            }),\n                            posts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\n                                    padding: '3rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 828,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#1c1e21',\n                                            marginBottom: '0.5rem'\n                                        },\n                                        children: \"No posts yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Be the first to share something with your professional network!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 827,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Online Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#7c3aed',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 843,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Modern Tech Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 848,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 841,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#2563eb',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Fatima Al-Zahra\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#059669',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 862,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Khalid Construction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 868,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 861,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 840,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '2rem',\n                                    paddingTop: '1rem',\n                                    borderTop: '1px solid #e4e6ea'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    style: {\n                                        color: '#1877f2',\n                                        textDecoration: 'none',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 872,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 836,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            showCreatePost && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreatePost(false),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 901,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.75rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        color: 'white',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: getUserInitial()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 947,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                color: '#65676b',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                getUserRoleEmoji(),\n                                                \" \",\n                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                \" • \\uD83C\\uDF0D Public\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: postText,\n                                onChange: (e)=>setPostText(e.target.value),\n                                placeholder: \"What's on your mind?\",\n                                style: {\n                                    width: '100%',\n                                    minHeight: '120px',\n                                    border: 'none',\n                                    outline: 'none',\n                                    fontSize: '1.5rem',\n                                    color: '#1c1e21',\n                                    resize: 'none',\n                                    fontFamily: 'inherit'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 956,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 13\n                        }, this),\n                        selectedFeeling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '20px',\n                                    padding: '0.5rem 1rem',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: selectedFeeling\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 984,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedFeeling(''),\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            color: '#65676b'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 975,\n                            columnNumber: 15\n                        }, this),\n                        selectedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'relative',\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '8px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: [\n                                            \"Image Preview (\",\n                                            selectedImages.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 1011,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedImages([]),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '0.5rem',\n                                            right: '0.5rem',\n                                            backgroundColor: 'rgba(0,0,0,0.5)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '2rem',\n                                            height: '2rem',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 1012,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 1003,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 1002,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderTop: '1px solid #e4e6ea'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: \"Add to your post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '0.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImages([\n                                                            'image'\n                                                        ]),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Photo/Video\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1038,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedFeeling('😊 feeling happy'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Feeling/Activity\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1056,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Tag People\",\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1074,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Check In\",\n                                                    children: \"\\uD83D\\uDCCD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1035,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (!postText.trim()) return;\n                                        // Create the post using the posts context\n                                        const newPost = {\n                                            user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                                            user_name: getUserDisplayName(),\n                                            user_role: userRole || 'worker',\n                                            content: postText,\n                                            media_urls: selectedImages.length > 0 ? selectedImages : undefined,\n                                            post_type: selectedImages.length > 0 ? 'image' : 'text',\n                                            visibility: 'public'\n                                        };\n                                        addPost(newPost);\n                                        // Reset form\n                                        setPostText('');\n                                        setSelectedFeeling('');\n                                        setSelectedImages([]);\n                                        setSelectedLocation('');\n                                        setTaggedUsers([]);\n                                        setShowCreatePost(false);\n                                        // Show success message\n                                        alert('Post created successfully!');\n                                    },\n                                    disabled: !postText.trim(),\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                                        color: postText.trim() ? 'white' : '#bcc0c4',\n                                        border: 'none',\n                                        borderRadius: '6px',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '600',\n                                        cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                                    },\n                                    children: \"Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 892,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 880,\n                columnNumber: 9\n            }, this),\n            showShareModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Share Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowShareModal(null),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1193,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 1183,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: 'repeat(2, 1fr)',\n                                        gap: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSharePost(showShareModal),\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCE4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Share Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Send in Message\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1234,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1260,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Copy Link\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1249,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCF1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Share External\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        borderTop: '1px solid #e4e6ea',\n                                        paddingTop: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(userRole || 'worker'),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: getUserInitial()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1283,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21'\n                                                            },\n                                                            children: getUserDisplayName()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 1297,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.875rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                getRoleEmoji(userRole || 'worker'),\n                                                                \" \",\n                                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                                \" • \\uD83C\\uDF0D Public\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 1298,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1282,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: shareText,\n                                            onChange: (e)=>setShareText(e.target.value),\n                                            placeholder: \"Say something about this...\",\n                                            style: {\n                                                width: '100%',\n                                                minHeight: '80px',\n                                                border: '1px solid #e4e6ea',\n                                                borderRadius: '8px',\n                                                padding: '0.75rem',\n                                                outline: 'none',\n                                                fontSize: '1rem',\n                                                resize: 'vertical',\n                                                marginBottom: '1rem'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1304,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSharePost(showShareModal),\n                                            style: {\n                                                width: '100%',\n                                                backgroundColor: '#1877f2',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '6px',\n                                                padding: '0.75rem',\n                                                fontSize: '1rem',\n                                                fontWeight: '600',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Share Post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1281,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 1174,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 1162,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"GkPxFE+r0sNRi9KQtSHIAx0/SUU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});