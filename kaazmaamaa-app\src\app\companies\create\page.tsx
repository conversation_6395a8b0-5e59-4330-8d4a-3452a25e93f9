'use client'

import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useCompanies } from '@/contexts/CompaniesContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Upload, 
  FileText, 
  CheckCircle, 
  User,
  Building,
  FileCheck,
  CreditCard,
  Award,
  Globe,
  Users,
  Briefcase,
  Target,
  Calendar
} from 'lucide-react'
import { toast } from 'sonner'

export default function CreateCompanyProfilePage() {
  const router = useRouter()
  const { user } = useAuth()
  const { createCompanyProfile } = useCompanies()
  
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Company Information
  const [companyInfo, setCompanyInfo] = useState({
    companyName: '',
    industry: '',
    companySize: '',
    foundedYear: '',
    headquarters: '',
    website: '',
    description: '',
    mission: '',
    vision: '',
    values: ''
  })

  // Contact Information
  const [contactInfo, setContactInfo] = useState({
    hrEmail: '',
    hrPhone: '',
    hrWhatsapp: '',
    mainOfficeAddress: '',
    branches: [] as string[]
  })

  // Job Openings & Benefits
  const [jobInfo, setJobInfo] = useState({
    jobOpenings: [] as string[],
    companyBenefits: [] as string[],
    workEnvironment: {
      isRemoteFriendly: false,
      hasFlexibleHours: false,
      providesTraining: false
    }
  })

  // Documents
  const [documents, setDocuments] = useState({
    companyLicense: null as File | null,
    commercialRegistration: null as File | null,
    taxCertificate: null as File | null,
    companyProfile: null as File | null
  })

  const steps = [
    { id: 1, title: 'Company Info', icon: Building },
    { id: 2, title: 'Contact Details', icon: User },
    { id: 3, title: 'Jobs & Benefits', icon: Briefcase },
    { id: 4, title: 'Documents', icon: FileCheck },
    { id: 5, title: 'Review', icon: CheckCircle }
  ]

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      // Create company profile using the context function
      const profileData = {
        userId: user?.email || contactInfo.hrEmail || 'current-user',
        companyInfo,
        contactInfo,
        documents: Object.entries(documents).map(([key, file]) => ({
          name: key,
          type: key as any,
          url: file ? URL.createObjectURL(file) : '',
          uploadedAt: new Date().toISOString()
        })).filter(doc => doc.url),
        jobOpenings: jobInfo.jobOpenings.map((job, index) => ({
          id: `job-${Date.now()}-${index}`,
          title: job,
          department: 'General',
          category: 'General',
          description: `Position for ${job}`,
          requirements: [],
          salaryRange: {
            min: 3000,
            max: 8000,
            currency: 'SAR' as any
          },
          employmentType: 'full_time' as any,
          experienceLevel: 'mid' as any,
          location: contactInfo.mainOfficeAddress,
          isRemote: jobInfo.workEnvironment.isRemoteFriendly,
          benefits: jobInfo.companyBenefits,
          urgency: 'within_month' as any,
          documentsRequired: [],
          createdAt: new Date().toISOString(),
          applicationsCount: 0
        })),
        companyBenefits: jobInfo.companyBenefits,
        workEnvironment: jobInfo.workEnvironment,
        isVerified: false,
        isHiring: jobInfo.jobOpenings.length > 0,
        isLiveStreaming: false,
        rating: 0,
        totalReviews: 0,
        totalEmployees: 0
      }

      // Use the context function to create and save the profile
      createCompanyProfile(profileData)

      console.log('✅ Company profile created and saved:', profileData)
      toast.success('Company profile created successfully! 🎉 Please sign in to continue.')
      router.push('/auth/login')
    } catch (error) {
      console.error('Error creating company profile:', error)
      toast.error('Failed to create company profile')
    } finally {
      setIsSubmitting(false)
    }
  }

  const addJobOpening = () => {
    const newJob = prompt('Enter job position:')
    if (newJob && newJob.trim()) {
      setJobInfo(prev => ({
        ...prev,
        jobOpenings: [...prev.jobOpenings, newJob.trim()]
      }))
    }
  }

  const removeJobOpening = (index: number) => {
    setJobInfo(prev => ({
      ...prev,
      jobOpenings: prev.jobOpenings.filter((_, i) => i !== index)
    }))
  }

  const addBenefit = () => {
    const newBenefit = prompt('Enter company benefit:')
    if (newBenefit && newBenefit.trim()) {
      setJobInfo(prev => ({
        ...prev,
        companyBenefits: [...prev.companyBenefits, newBenefit.trim()]
      }))
    }
  }

  const removeBenefit = (index: number) => {
    setJobInfo(prev => ({
      ...prev,
      companyBenefits: prev.companyBenefits.filter((_, i) => i !== index)
    }))
  }

  const addBranch = () => {
    const newBranch = prompt('Enter branch location:')
    if (newBranch && newBranch.trim()) {
      setContactInfo(prev => ({
        ...prev,
        branches: [...prev.branches, newBranch.trim()]
      }))
    }
  }

  const removeBranch = (index: number) => {
    setContactInfo(prev => ({
      ...prev,
      branches: prev.branches.filter((_, i) => i !== index)
    }))
  }

  const handleFileUpload = (field: keyof typeof documents, file: File | null) => {
    setDocuments(prev => ({
      ...prev,
      [field]: file
    }))
    if (file) {
      toast.success(`${file.name} uploaded successfully`)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/companies')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Companies
              </Button>
              <h1 className="text-2xl font-bold text-purple-600">Create Company Profile</h1>
            </div>
            <Badge className="bg-purple-100 text-purple-800">
              Step {currentStep} of {steps.length}
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Progress Steps */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id
                
                return (
                  <div key={step.id} className="flex items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isCompleted 
                        ? 'bg-purple-600 border-purple-600 text-white' 
                        : isActive 
                        ? 'border-purple-600 text-purple-600' 
                        : 'border-gray-300 text-gray-400'
                    }`}>
                      {isCompleted ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <Icon className="h-5 w-5" />
                      )}
                    </div>
                    <span className={`ml-2 text-sm font-medium ${
                      isActive ? 'text-purple-600' : isCompleted ? 'text-purple-600' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </span>
                    {index < steps.length - 1 && (
                      <div className={`w-12 h-0.5 mx-4 ${
                        isCompleted ? 'bg-purple-600' : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-purple-600">
              {React.createElement(steps[currentStep - 1].icon, { className: "h-5 w-5 mr-2" })}
              {steps[currentStep - 1].title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Step 1: Company Information */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="companyName">Company Name *</Label>
                    <Input
                      id="companyName"
                      value={companyInfo.companyName}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, companyName: e.target.value }))}
                      placeholder="Enter company name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="industry">Industry *</Label>
                    <Input
                      id="industry"
                      value={companyInfo.industry}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, industry: e.target.value }))}
                      placeholder="e.g., Technology, Construction, Healthcare"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="companySize">Company Size</Label>
                    <select
                      id="companySize"
                      value={companyInfo.companySize}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, companySize: e.target.value }))}
                      className="w-full p-2 border border-gray-300 rounded-md"
                    >
                      <option value="">Select size</option>
                      <option value="1-10">1-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-200 employees</option>
                      <option value="201-500">201-500 employees</option>
                      <option value="500+">500+ employees</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="foundedYear">Founded Year</Label>
                    <Input
                      id="foundedYear"
                      type="number"
                      value={companyInfo.foundedYear}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, foundedYear: e.target.value }))}
                      placeholder="e.g., 2010"
                    />
                  </div>
                  <div>
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={companyInfo.website}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, website: e.target.value }))}
                      placeholder="https://www.company.com"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="headquarters">Headquarters *</Label>
                  <Input
                    id="headquarters"
                    value={companyInfo.headquarters}
                    onChange={(e) => setCompanyInfo(prev => ({ ...prev, headquarters: e.target.value }))}
                    placeholder="City, Country"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Company Description *</Label>
                  <Textarea
                    id="description"
                    value={companyInfo.description}
                    onChange={(e) => setCompanyInfo(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your company, what you do, and your main services..."
                    rows={4}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="mission">Mission Statement</Label>
                    <Textarea
                      id="mission"
                      value={companyInfo.mission}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, mission: e.target.value }))}
                      placeholder="Your company's mission..."
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="vision">Vision Statement</Label>
                    <Textarea
                      id="vision"
                      value={companyInfo.vision}
                      onChange={(e) => setCompanyInfo(prev => ({ ...prev, vision: e.target.value }))}
                      placeholder="Your company's vision..."
                      rows={3}
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Contact Information */}
            {currentStep === 2 && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="hrEmail">HR Email *</Label>
                    <Input
                      id="hrEmail"
                      type="email"
                      value={contactInfo.hrEmail}
                      onChange={(e) => setContactInfo(prev => ({ ...prev, hrEmail: e.target.value }))}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="hrPhone">HR Phone *</Label>
                    <Input
                      id="hrPhone"
                      value={contactInfo.hrPhone}
                      onChange={(e) => setContactInfo(prev => ({ ...prev, hrPhone: e.target.value }))}
                      placeholder="+966 50 123 4567"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="hrWhatsapp">HR WhatsApp *</Label>
                  <Input
                    id="hrWhatsapp"
                    value={contactInfo.hrWhatsapp}
                    onChange={(e) => setContactInfo(prev => ({ ...prev, hrWhatsapp: e.target.value }))}
                    placeholder="+966 50 123 4567"
                  />
                </div>

                <div>
                  <Label htmlFor="mainOfficeAddress">Main Office Address *</Label>
                  <Textarea
                    id="mainOfficeAddress"
                    value={contactInfo.mainOfficeAddress}
                    onChange={(e) => setContactInfo(prev => ({ ...prev, mainOfficeAddress: e.target.value }))}
                    placeholder="Full address of main office..."
                    rows={3}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Branch Locations</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addBranch}
                    >
                      <Building className="h-4 w-4 mr-2" />
                      Add Branch
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {contactInfo.branches.map((branch, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input value={branch} readOnly className="flex-1" />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeBranch(index)}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Jobs & Benefits */}
            {currentStep === 3 && (
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Current Job Openings</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addJobOpening}
                    >
                      <Briefcase className="h-4 w-4 mr-2" />
                      Add Job Opening
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {jobInfo.jobOpenings.map((job, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input value={job} readOnly className="flex-1" />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeJobOpening(index)}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label>Company Benefits</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addBenefit}
                    >
                      <Award className="h-4 w-4 mr-2" />
                      Add Benefit
                    </Button>
                  </div>
                  <div className="space-y-2">
                    {jobInfo.companyBenefits.map((benefit, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input value={benefit} readOnly className="flex-1" />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeBenefit(index)}
                        >
                          Remove
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Work Environment</Label>
                  <div className="space-y-3 mt-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="remoteFriendly"
                        checked={jobInfo.workEnvironment.isRemoteFriendly}
                        onChange={(e) => setJobInfo(prev => ({
                          ...prev,
                          workEnvironment: { ...prev.workEnvironment, isRemoteFriendly: e.target.checked }
                        }))}
                      />
                      <Label htmlFor="remoteFriendly">Remote-friendly workplace</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="flexibleHours"
                        checked={jobInfo.workEnvironment.hasFlexibleHours}
                        onChange={(e) => setJobInfo(prev => ({
                          ...prev,
                          workEnvironment: { ...prev.workEnvironment, hasFlexibleHours: e.target.checked }
                        }))}
                      />
                      <Label htmlFor="flexibleHours">Flexible working hours</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="providesTraining"
                        checked={jobInfo.workEnvironment.providesTraining}
                        onChange={(e) => setJobInfo(prev => ({
                          ...prev,
                          workEnvironment: { ...prev.workEnvironment, providesTraining: e.target.checked }
                        }))}
                      />
                      <Label htmlFor="providesTraining">Provides training and development</Label>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Documents */}
            {currentStep === 4 && (
              <div className="space-y-4">
                <p className="text-gray-600 mb-4">
                  Upload your company documents. All documents should be in PDF format.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="companyLicense">Company License</Label>
                    <div className="mt-1">
                      <input
                        type="file"
                        id="companyLicense"
                        accept=".pdf"
                        onChange={(e) => handleFileUpload('companyLicense', e.target.files?.[0] || null)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                      {documents.companyLicense && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ {documents.companyLicense.name}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="commercialRegistration">Commercial Registration</Label>
                    <div className="mt-1">
                      <input
                        type="file"
                        id="commercialRegistration"
                        accept=".pdf"
                        onChange={(e) => handleFileUpload('commercialRegistration', e.target.files?.[0] || null)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                      {documents.commercialRegistration && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ {documents.commercialRegistration.name}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="taxCertificate">Tax Certificate</Label>
                    <div className="mt-1">
                      <input
                        type="file"
                        id="taxCertificate"
                        accept=".pdf"
                        onChange={(e) => handleFileUpload('taxCertificate', e.target.files?.[0] || null)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                      {documents.taxCertificate && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ {documents.taxCertificate.name}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="companyProfile">Company Profile/Brochure</Label>
                    <div className="mt-1">
                      <input
                        type="file"
                        id="companyProfile"
                        accept=".pdf"
                        onChange={(e) => handleFileUpload('companyProfile', e.target.files?.[0] || null)}
                        className="w-full p-2 border border-gray-300 rounded-md"
                      />
                      {documents.companyProfile && (
                        <p className="text-sm text-green-600 mt-1">
                          ✓ {documents.companyProfile.name}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 5: Review */}
            {currentStep === 5 && (
              <div className="space-y-6">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <h3 className="font-semibold text-purple-800 mb-2">Review Your Company Profile</h3>
                  <p className="text-purple-600 text-sm">
                    Please review all information before submitting. You can go back to edit any section.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Company Information</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Name:</strong> {companyInfo.companyName || 'Not provided'}</p>
                      <p><strong>Industry:</strong> {companyInfo.industry || 'Not provided'}</p>
                      <p><strong>Size:</strong> {companyInfo.companySize || 'Not provided'}</p>
                      <p><strong>Founded:</strong> {companyInfo.foundedYear || 'Not provided'}</p>
                      <p><strong>Headquarters:</strong> {companyInfo.headquarters || 'Not provided'}</p>
                      <p><strong>Website:</strong> {companyInfo.website || 'Not provided'}</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Contact Information</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>HR Email:</strong> {contactInfo.hrEmail || 'Not provided'}</p>
                      <p><strong>HR Phone:</strong> {contactInfo.hrPhone || 'Not provided'}</p>
                      <p><strong>HR WhatsApp:</strong> {contactInfo.hrWhatsapp || 'Not provided'}</p>
                      <p><strong>Branches:</strong> {contactInfo.branches.length} locations</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Jobs & Benefits</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Job Openings:</strong> {jobInfo.jobOpenings.length} positions</p>
                      <p><strong>Benefits:</strong> {jobInfo.companyBenefits.length} benefits</p>
                      <p><strong>Remote Work:</strong> {jobInfo.workEnvironment.isRemoteFriendly ? 'Yes' : 'No'}</p>
                      <p><strong>Flexible Hours:</strong> {jobInfo.workEnvironment.hasFlexibleHours ? 'Yes' : 'No'}</p>
                      <p><strong>Training:</strong> {jobInfo.workEnvironment.providesTraining ? 'Yes' : 'No'}</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Documents</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Company License:</strong> {documents.companyLicense ? '✓ Uploaded' : '✗ Not uploaded'}</p>
                      <p><strong>Commercial Registration:</strong> {documents.commercialRegistration ? '✓ Uploaded' : '✗ Not uploaded'}</p>
                      <p><strong>Tax Certificate:</strong> {documents.taxCertificate ? '✓ Uploaded' : '✗ Not uploaded'}</p>
                      <p><strong>Company Profile:</strong> {documents.companyProfile ? '✓ Uploaded' : '✗ Not uploaded'}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-6">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
              >
                Previous
              </Button>
              
              {currentStep === steps.length ? (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {isSubmitting ? 'Creating...' : 'Create Profile'}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  Next
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
