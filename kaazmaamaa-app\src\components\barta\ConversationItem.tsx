'use client'

import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { BartaConversation, BartaUser } from '@/contexts/BartaContext'
import { Users, Building, Wrench, Circle } from 'lucide-react'

interface ConversationItemProps {
  conversation: BartaConversation
  otherParticipant: BartaUser | null
  isActive: boolean
  onClick: () => void
}

export function ConversationItem({ 
  conversation, 
  otherParticipant, 
  isActive, 
  onClick 
}: ConversationItemProps) {
  
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'worker':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'supplier':
        return <Building className="h-4 w-4 text-green-600" />
      case 'company':
        return <Users className="h-4 w-4 text-purple-600" />
      default:
        return <Circle className="h-4 w-4 text-gray-600" />
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'worker':
        return 'bg-blue-100 text-blue-800'
      case 'supplier':
        return 'bg-green-100 text-green-800'
      case 'company':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatConversationTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' })
    }
  }

  const truncateMessage = (message: string, maxLength: number = 50) => {
    if (message.length <= maxLength) return message
    return message.substring(0, maxLength) + '...'
  }

  if (!otherParticipant) return null

  return (
    <div
      className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
        isActive ? 'bg-blue-50 border-l-4 border-l-blue-600' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-center space-x-3">
        {/* Avatar with Online Status */}
        <div className="relative flex-shrink-0">
          <Avatar className="h-12 w-12 bg-gray-100 flex items-center justify-center">
            {getRoleIcon(otherParticipant.role)}
          </Avatar>
          {otherParticipant.isOnline && (
            <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
          )}
        </div>
        
        {/* Conversation Info */}
        <div className="flex-1 min-w-0">
          {/* Header Row */}
          <div className="flex items-center justify-between mb-1">
            <h3 className="font-medium text-sm truncate text-gray-900">
              {conversation.isGroup ? conversation.groupName : otherParticipant.name}
            </h3>
            <span className="text-xs text-gray-500 flex-shrink-0">
              {conversation.lastMessage && formatConversationTime(conversation.lastMessage.timestamp)}
            </span>
          </div>
          
          {/* Last Message Row */}
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-600 truncate">
                {conversation.lastMessage ? (
                  <>
                    {conversation.lastMessage.type === 'emoji' ? (
                      <span className="text-lg">{conversation.lastMessage.content}</span>
                    ) : conversation.lastMessage.type === 'image' ? (
                      <span className="italic">📷 Image</span>
                    ) : conversation.lastMessage.type === 'file' ? (
                      <span className="italic">📎 File</span>
                    ) : (
                      truncateMessage(conversation.lastMessage.content)
                    )}
                  </>
                ) : (
                  <span className="italic text-gray-400">No messages yet</span>
                )}
              </p>
            </div>
            
            {/* Unread Badge */}
            {conversation.unreadCount > 0 && (
              <Badge className="bg-blue-600 text-white text-xs ml-2 flex-shrink-0">
                {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
              </Badge>
            )}
          </div>
          
          {/* Role Badge and Status */}
          <div className="flex items-center justify-between mt-1">
            <Badge variant="secondary" className={`text-xs ${getRoleColor(otherParticipant.role)}`}>
              {otherParticipant.role}
            </Badge>
            
            {/* Online Status Text */}
            <span className={`text-xs ${otherParticipant.isOnline ? 'text-green-600' : 'text-gray-400'}`}>
              {otherParticipant.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
