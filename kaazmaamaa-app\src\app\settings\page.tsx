'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Save, 
  User,
  Bell,
  Shield,
  Eye,
  Trash2,
  Download,
  Upload,
  Settings as SettingsIcon,
  LogOut,
  Moon,
  Sun
} from 'lucide-react'
import { toast } from 'sonner'

export default function SettingsPage() {
  const router = useRouter()
  const { user, userRole, signOut } = useAuth()
  
  const [activeTab, setActiveTab] = useState('profile')
  const [isSaving, setIsSaving] = useState(false)

  // Profile Settings
  const [profileSettings, setProfileSettings] = useState({
    displayName: user?.email?.split('@')[0] || 'User',
    email: user?.email || '',
    bio: '',
    location: '',
    website: '',
    phone: '',
    isProfilePublic: true,
    showEmail: false,
    showPhone: false
  })

  // Notification Settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    jobAlerts: true,
    messageNotifications: true,
    followNotifications: true,
    commentNotifications: true,
    likeNotifications: false,
    shareNotifications: true
  })

  // Privacy Settings
  const [privacySettings, setPrivacySettings] = useState({
    profileVisibility: 'public' as 'public' | 'private' | 'contacts',
    searchable: true,
    showOnlineStatus: true,
    allowMessages: true,
    allowFollows: true,
    dataCollection: true,
    analyticsTracking: false
  })

  // Appearance Settings
  const [appearanceSettings, setAppearanceSettings] = useState({
    theme: 'light' as 'light' | 'dark' | 'system',
    language: 'en' as 'en' | 'ar',
    fontSize: 'medium' as 'small' | 'medium' | 'large',
    compactMode: false
  })

  const handleProfileSettingChange = (field: string, value: any) => {
    setProfileSettings(prev => ({ ...prev, [field]: value }))
  }

  const handleNotificationSettingChange = (field: string, value: boolean) => {
    setNotificationSettings(prev => ({ ...prev, [field]: value }))
  }

  const handlePrivacySettingChange = (field: string, value: any) => {
    setPrivacySettings(prev => ({ ...prev, [field]: value }))
  }

  const handleAppearanceSettingChange = (field: string, value: any) => {
    setAppearanceSettings(prev => ({ ...prev, [field]: value }))
  }

  const handleSaveSettings = async () => {
    setIsSaving(true)
    
    try {
      // In a real app, this would save to backend
      const allSettings = {
        profile: profileSettings,
        notifications: notificationSettings,
        privacy: privacySettings,
        appearance: appearanceSettings,
        updatedAt: new Date().toISOString()
      }
      
      localStorage.setItem('kaazmaamaa-user-settings', JSON.stringify(allSettings))
      toast.success('Settings saved successfully! ✅')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleExportData = () => {
    try {
      const userData = {
        profile: profileSettings,
        settings: {
          notifications: notificationSettings,
          privacy: privacySettings,
          appearance: appearanceSettings
        },
        exportedAt: new Date().toISOString()
      }
      
      const dataStr = JSON.stringify(userData, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `kaazmaamaa-data-${new Date().toISOString().split('T')[0]}.json`
      link.click()
      
      URL.revokeObjectURL(url)
      toast.success('Data exported successfully! 📥')
    } catch (error) {
      toast.error('Failed to export data')
    }
  }

  const handleDeleteAccount = () => {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      // In a real app, this would call backend API
      toast.error('Account deletion is not implemented in demo mode')
    }
  }

  const handleSignOut = () => {
    signOut()
    toast.success('Signed out successfully! 👋')
    router.push('/')
  }

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'appearance', label: 'Appearance', icon: Eye }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/profile/${user?.id}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Profile
              </Button>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <SettingsIcon className="h-6 w-6 mr-2" />
                Settings
              </h1>
            </div>
            <Badge variant="outline" className="text-gray-600 capitalize">
              {userRole}
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Settings</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <nav className="space-y-1">
                  {tabs.map((tab) => {
                    const Icon = tab.icon
                    return (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`w-full flex items-center px-4 py-3 text-left hover:bg-gray-50 transition-colors ${
                          activeTab === tab.id
                            ? 'bg-blue-50 text-blue-600 border-r-2 border-blue-600'
                            : 'text-gray-700'
                        }`}
                      >
                        <Icon className="h-5 w-5 mr-3" />
                        {tab.label}
                      </button>
                    )
                  })}
                  
                  {/* Action Buttons */}
                  <div className="border-t pt-4 space-y-1">
                    <button
                      onClick={handleExportData}
                      className="w-full flex items-center px-4 py-3 text-left text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <Download className="h-5 w-5 mr-3" />
                      Export Data
                    </button>
                    <button
                      onClick={handleSignOut}
                      className="w-full flex items-center px-4 py-3 text-left text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      <LogOut className="h-5 w-5 mr-3" />
                      Sign Out
                    </button>
                    <button
                      onClick={handleDeleteAccount}
                      className="w-full flex items-center px-4 py-3 text-left text-red-600 hover:bg-red-50 transition-colors"
                    >
                      <Trash2 className="h-5 w-5 mr-3" />
                      Delete Account
                    </button>
                  </div>
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  {(() => {
                    const activeTabData = tabs.find(t => t.id === activeTab)
                    const Icon = activeTabData?.icon || SettingsIcon
                    return (
                      <>
                        <Icon className="h-5 w-5 mr-2" />
                        {activeTabData?.label || 'Settings'}
                      </>
                    )
                  })()}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Profile Settings */}
                {activeTab === 'profile' && (
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="displayName">Display Name</Label>
                        <Input
                          id="displayName"
                          value={profileSettings.displayName}
                          onChange={(e) => handleProfileSettingChange('displayName', e.target.value)}
                          placeholder="Your display name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          type="email"
                          value={profileSettings.email}
                          onChange={(e) => handleProfileSettingChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="phone">Phone Number</Label>
                        <Input
                          id="phone"
                          value={profileSettings.phone}
                          onChange={(e) => handleProfileSettingChange('phone', e.target.value)}
                          placeholder="+966 XXX XXX XXX"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={profileSettings.location}
                          onChange={(e) => handleProfileSettingChange('location', e.target.value)}
                          placeholder="City, Country"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="website">Website</Label>
                        <Input
                          id="website"
                          value={profileSettings.website}
                          onChange={(e) => handleProfileSettingChange('website', e.target.value)}
                          placeholder="https://yourwebsite.com"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="bio">Bio</Label>
                      <Textarea
                        id="bio"
                        value={profileSettings.bio}
                        onChange={(e) => handleProfileSettingChange('bio', e.target.value)}
                        placeholder="Tell us about yourself..."
                        rows={4}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Privacy</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Public Profile</Label>
                            <p className="text-sm text-gray-500">Make your profile visible to everyone</p>
                          </div>
                          <Switch
                            checked={profileSettings.isProfilePublic}
                            onCheckedChange={(checked) => handleProfileSettingChange('isProfilePublic', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Show Email</Label>
                            <p className="text-sm text-gray-500">Display email on your profile</p>
                          </div>
                          <Switch
                            checked={profileSettings.showEmail}
                            onCheckedChange={(checked) => handleProfileSettingChange('showEmail', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Show Phone</Label>
                            <p className="text-sm text-gray-500">Display phone number on your profile</p>
                          </div>
                          <Switch
                            checked={profileSettings.showPhone}
                            onCheckedChange={(checked) => handleProfileSettingChange('showPhone', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Notification Settings */}
                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">General Notifications</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Email Notifications</Label>
                            <p className="text-sm text-gray-500">Receive notifications via email</p>
                          </div>
                          <Switch
                            checked={notificationSettings.emailNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('emailNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Push Notifications</Label>
                            <p className="text-sm text-gray-500">Receive browser push notifications</p>
                          </div>
                          <Switch
                            checked={notificationSettings.pushNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('pushNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>SMS Notifications</Label>
                            <p className="text-sm text-gray-500">Receive notifications via SMS</p>
                          </div>
                          <Switch
                            checked={notificationSettings.smsNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('smsNotifications', checked)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Activity Notifications</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Job Alerts</Label>
                            <p className="text-sm text-gray-500">Get notified about new job opportunities</p>
                          </div>
                          <Switch
                            checked={notificationSettings.jobAlerts}
                            onCheckedChange={(checked) => handleNotificationSettingChange('jobAlerts', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Messages</Label>
                            <p className="text-sm text-gray-500">Get notified when you receive messages</p>
                          </div>
                          <Switch
                            checked={notificationSettings.messageNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('messageNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>New Followers</Label>
                            <p className="text-sm text-gray-500">Get notified when someone follows you</p>
                          </div>
                          <Switch
                            checked={notificationSettings.followNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('followNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Comments</Label>
                            <p className="text-sm text-gray-500">Get notified when someone comments on your posts</p>
                          </div>
                          <Switch
                            checked={notificationSettings.commentNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('commentNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Likes</Label>
                            <p className="text-sm text-gray-500">Get notified when someone likes your posts</p>
                          </div>
                          <Switch
                            checked={notificationSettings.likeNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('likeNotifications', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Shares</Label>
                            <p className="text-sm text-gray-500">Get notified when someone shares your posts</p>
                          </div>
                          <Switch
                            checked={notificationSettings.shareNotifications}
                            onCheckedChange={(checked) => handleNotificationSettingChange('shareNotifications', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Privacy Settings */}
                {activeTab === 'privacy' && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Profile Privacy</h3>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label>Profile Visibility</Label>
                          <select
                            value={privacySettings.profileVisibility}
                            onChange={(e) => handlePrivacySettingChange('profileVisibility', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="public">Public - Anyone can see your profile</option>
                            <option value="private">Private - Only you can see your profile</option>
                            <option value="contacts">Contacts - Only your contacts can see your profile</option>
                          </select>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Searchable</Label>
                            <p className="text-sm text-gray-500">Allow others to find you in search</p>
                          </div>
                          <Switch
                            checked={privacySettings.searchable}
                            onCheckedChange={(checked) => handlePrivacySettingChange('searchable', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Show Online Status</Label>
                            <p className="text-sm text-gray-500">Let others see when you're online</p>
                          </div>
                          <Switch
                            checked={privacySettings.showOnlineStatus}
                            onCheckedChange={(checked) => handlePrivacySettingChange('showOnlineStatus', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Allow Messages</Label>
                            <p className="text-sm text-gray-500">Allow others to send you messages</p>
                          </div>
                          <Switch
                            checked={privacySettings.allowMessages}
                            onCheckedChange={(checked) => handlePrivacySettingChange('allowMessages', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Allow Follows</Label>
                            <p className="text-sm text-gray-500">Allow others to follow you</p>
                          </div>
                          <Switch
                            checked={privacySettings.allowFollows}
                            onCheckedChange={(checked) => handlePrivacySettingChange('allowFollows', checked)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Data & Analytics</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Data Collection</Label>
                            <p className="text-sm text-gray-500">Allow us to collect data to improve your experience</p>
                          </div>
                          <Switch
                            checked={privacySettings.dataCollection}
                            onCheckedChange={(checked) => handlePrivacySettingChange('dataCollection', checked)}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Analytics Tracking</Label>
                            <p className="text-sm text-gray-500">Allow analytics tracking for personalized content</p>
                          </div>
                          <Switch
                            checked={privacySettings.analyticsTracking}
                            onCheckedChange={(checked) => handlePrivacySettingChange('analyticsTracking', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Appearance Settings */}
                {activeTab === 'appearance' && (
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Theme</h3>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label>Color Theme</Label>
                          <div className="grid grid-cols-3 gap-3">
                            {[
                              { value: 'light', label: 'Light', icon: Sun },
                              { value: 'dark', label: 'Dark', icon: Moon },
                              { value: 'system', label: 'System', icon: SettingsIcon }
                            ].map((theme) => {
                              const Icon = theme.icon
                              return (
                                <button
                                  key={theme.value}
                                  onClick={() => handleAppearanceSettingChange('theme', theme.value)}
                                  className={`p-3 border rounded-lg flex flex-col items-center space-y-2 transition-colors ${
                                    appearanceSettings.theme === theme.value
                                      ? 'border-blue-500 bg-blue-50 text-blue-600'
                                      : 'border-gray-300 hover:border-gray-400'
                                  }`}
                                >
                                  <Icon className="h-5 w-5" />
                                  <span className="text-sm font-medium">{theme.label}</span>
                                </button>
                              )
                            })}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Display</h3>
                      <div className="space-y-3">
                        <div className="space-y-2">
                          <Label>Language</Label>
                          <select
                            value={appearanceSettings.language}
                            onChange={(e) => handleAppearanceSettingChange('language', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="en">English</option>
                            <option value="ar">العربية (Arabic)</option>
                          </select>
                        </div>
                        
                        <div className="space-y-2">
                          <Label>Font Size</Label>
                          <select
                            value={appearanceSettings.fontSize}
                            onChange={(e) => handleAppearanceSettingChange('fontSize', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          >
                            <option value="small">Small</option>
                            <option value="medium">Medium</option>
                            <option value="large">Large</option>
                          </select>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <Label>Compact Mode</Label>
                            <p className="text-sm text-gray-500">Use a more compact layout</p>
                          </div>
                          <Switch
                            checked={appearanceSettings.compactMode}
                            onCheckedChange={(checked) => handleAppearanceSettingChange('compactMode', checked)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Save Button */}
                <div className="flex justify-end mt-8 pt-6 border-t">
                  <Button
                    onClick={handleSaveSettings}
                    disabled={isSaving}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Saving...' : 'Save Settings'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
