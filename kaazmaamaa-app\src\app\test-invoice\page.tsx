'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { downloadInvoicePDF, printInvoice, type InvoiceData } from '@/utils/pdfGenerator'
import { generateZATCAQRCode, generateZATCATimestamp, formatCurrencyForZATCA, type ZATCAQRData } from '@/utils/zatcaQR'
import { toast } from 'sonner'

export default function TestInvoicePage() {
  const [isGenerating, setIsGenerating] = useState(false)

  const generateQRCode = (totalAmount: number, totalVAT: number) => {
    const qrData: ZATCAQRData = {
      sellerName: 'KAAZMAAMAA Technology Solutions',
      vatNumber: '30**********003',
      timestamp: generateZATCATimestamp(),
      invoiceTotal: formatCurrencyForZATCA(totalAmount),
      vatTotal: formatCurrencyForZATCA(totalVAT)
    }

    try {
      return generateZATCAQRCode(qrData)
    } catch (error) {
      console.error('QR Code generation error:', error)
      return ''
    }
  }

  const testPDFWithDeduction = () => {
    setIsGenerating(true)
    
    const subtotal = 1000
    const deductionAmount = 200
    const subtotalAfterDeduction = subtotal - deductionAmount
    const totalVAT = subtotalAfterDeduction * 0.15
    const totalAmount = subtotalAfterDeduction + totalVAT

    const testData: InvoiceData = {
      invoiceNumber: 'TEST-2025-001',
      invoiceDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      currency: 'SAR',
      discount: 0,
      deductionAmount: 200,
      deductionReason: 'Early payment discount - 10% advance payment received from client',
      sellerName: 'KAAZMAAMAA Technology Solutions',
      sellerAddress: '123 Business District, King Fahd Road\nRiyadh 12345, Saudi Arabia\nPhone: +966-11-123-4567',
      sellerVAT: '30**********003',
      clientName: 'ABC Construction Company',
      clientAddress: '456 Industrial Area, Prince Sultan Road\nJeddah 21455, Saudi Arabia\nPhone: +966-12-987-6543',
      clientVAT: '301**********09',
      items: [
        {
          id: '1',
          description: 'Professional Software Development Services - Custom Invoice System',
          quantity: 10,
          rate: 100,
          vatPercent: 15,
          amount: 1000
        }
      ],
      notes: 'Thank you for your business! We appreciate your partnership and look forward to continued collaboration.',
      terms: 'Payment due within 30 days. Late payments may incur additional charges. All prices are inclusive of applicable taxes.',
      bankName: 'Saudi National Bank',
      accountName: 'KAAZMAAMAA Technology Solutions',
      accountNumber: '**********',
      iban: 'SA********************12',
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(totalAmount, totalVAT),
      logoPreview: undefined
    }

    try {
      downloadInvoicePDF(testData)
      toast.success('Test PDF with deduction generated successfully! 📄')
    } catch (error) {
      console.error('PDF generation error:', error)
      toast.error('Failed to generate test PDF')
    } finally {
      setIsGenerating(false)
    }
  }

  const testPrintWithDeduction = () => {
    setIsGenerating(true)
    
    const subtotal = 1500
    const deductionAmount = 300
    const subtotalAfterDeduction = subtotal - deductionAmount
    const totalVAT = subtotalAfterDeduction * 0.15
    const totalAmount = subtotalAfterDeduction + totalVAT

    const testData: InvoiceData = {
      invoiceNumber: 'PRINT-TEST-2025-001',
      invoiceDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      currency: 'SAR',
      discount: 5,
      deductionAmount: 300,
      deductionReason: 'Material cost adjustment - Client provided materials worth SAR 300',
      sellerName: 'KAAZMAAMAA Technology Solutions',
      sellerAddress: '123 Business District, King Fahd Road\nRiyadh 12345, Saudi Arabia',
      sellerVAT: '30**********003',
      clientName: 'XYZ Manufacturing Ltd',
      clientAddress: '789 Factory Zone, Eastern Province\nDammam 31411, Saudi Arabia',
      clientVAT: '301555666777888',
      items: [
        {
          id: '1',
          description: 'Custom ERP System Development',
          quantity: 5,
          rate: 200,
          vatPercent: 15,
          amount: 1000
        },
        {
          id: '2',
          description: 'Database Migration Services',
          quantity: 2,
          rate: 250,
          vatPercent: 15,
          amount: 500
        }
      ],
      notes: 'This is a test print with deduction amount and reason clearly displayed.',
      terms: 'Payment terms: Net 30 days. Deduction applied as agreed.',
      bankName: 'Al Rajhi Bank',
      accountName: 'KAAZMAAMAA Technology Solutions',
      accountNumber: '**********',
      iban: 'SA********************98',
      subtotal,
      subtotalAfterDeduction,
      totalVAT,
      totalAmount,
      qrCode: generateQRCode(totalAmount, totalVAT),
      logoPreview: undefined
    }

    try {
      printInvoice(testData)
      toast.success('Test print with deduction opened successfully! 🖨️')
    } catch (error) {
      console.error('Print error:', error)
      toast.error('Failed to open test print')
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-2xl text-purple-600">
              🧪 Invoice PDF Test Page
            </CardTitle>
            <p className="text-center text-gray-600">
              Test the PDF generation and print functionality with deduction amounts and reasons
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-green-200">
                <CardHeader>
                  <CardTitle className="text-green-600">📄 PDF Generation Test</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Test PDF generation with:
                    <br />• Deduction Amount: SAR 200
                    <br />• Deduction Reason: Early payment discount
                    <br />• ZATCA QR Code
                    <br />• Professional styling
                  </p>
                  <Button
                    onClick={testPDFWithDeduction}
                    disabled={isGenerating}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {isGenerating ? 'Generating...' : 'Generate Test PDF'}
                  </Button>
                </CardContent>
              </Card>

              <Card className="border-purple-200">
                <CardHeader>
                  <CardTitle className="text-purple-600">🖨️ Print Test</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    Test print functionality with:
                    <br />• Deduction Amount: SAR 300
                    <br />• Deduction Reason: Material cost adjustment
                    <br />• Multiple items
                    <br />• Discount + Deduction
                  </p>
                  <Button
                    onClick={testPrintWithDeduction}
                    disabled={isGenerating}
                    variant="outline"
                    className="w-full border-purple-300 text-purple-600 hover:bg-purple-50"
                  >
                    {isGenerating ? 'Opening...' : 'Test Print Dialog'}
                  </Button>
                </CardContent>
              </Card>
            </div>

            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">✅ What to Look For:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Deduction amount clearly displayed in Invoice Details section</li>
                <li>• Deduction reason prominently shown with proper styling</li>
                <li>• Deduction amount and reason in the summary section</li>
                <li>• Important deduction notice in the Notes section</li>
                <li>• Correct calculation: Subtotal - Deduction - Discount, then apply 15% VAT</li>
                <li>• ZATCA QR code generated and displayed</li>
                <li>• Professional HD quality styling</li>
              </ul>
            </div>

            <div className="mt-4 text-center">
              <Button
                onClick={() => window.location.href = '/invoice/create'}
                variant="outline"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                🔙 Back to Invoice Creator
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
