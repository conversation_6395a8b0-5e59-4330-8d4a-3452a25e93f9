'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'

export interface CompanyDocument {
  id: string
  name: string
  type: 'licence' | 'cr' | 'tax_certificate' | 'establishment_card' | 'insurance' | 'contract' | 'other'
  url: string
  uploadedAt: string
}

export interface JobOpening {
  id: string
  title: string
  department: string
  category: string
  description: string
  requirements: string[]
  salaryRange: {
    min: number
    max: number
    currency: 'SAR' | 'USD' | 'EUR'
  }
  employmentType: 'full_time' | 'part_time' | 'contract' | 'internship'
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive'
  location: string
  isRemote: boolean
  benefits: string[]
  applicationDeadline?: string
  urgency: 'immediate' | 'within_week' | 'within_month'
  documentsRequired: string[]
  createdAt: string
  applicationsCount: number
}

export interface CompanyProfile {
  id: string // Must be unique
  userId: string
  companyInfo: {
    companyName: string
    tradeName?: string
    industry: string
    companySize: '1-10' | '11-50' | '51-200' | '201-500' | '501-1000' | '1000+'
    foundedYear: number
    headquarters: string
    website?: string
    description: string
    mission?: string
    vision?: string
  }
  contactInfo: {
    hrManagerName: string
    hrEmail: string
    hrPhone: string
    hrWhatsapp: string
    companyPhone: string
    companyEmail: string
    address: string
    poBox?: string
  }
  legalInfo: {
    crNumber: string // Commercial Registration Number
    taxNumber: string
    licenceNumber: string // Must be unique
    establishmentNumber?: string
    chamberMembership?: string
  }
  documents: CompanyDocument[]
  jobOpenings: JobOpening[]
  videos: {
    id: string
    title: string
    type: 'company_intro' | 'workplace_culture' | 'job_opportunities' | 'employee_testimonials'
    url: string
    thumbnail?: string
    duration?: number
    uploadedAt: string
  }[]
  isLiveStreaming: boolean
  liveStreamUrl?: string
  operatingLocations: string[]
  preferredWorkerCategories: string[]
  companyBenefits: string[]
  workEnvironment: {
    isRemoteFriendly: boolean
    hasFlexibleHours: boolean
    providesTraining: boolean
    hasCareerGrowth: boolean
    workLifeBalance: number // 1-5 rating
  }
  rating: number
  totalReviews: number
  totalEmployees: number
  isVerified: boolean
  isHiring: boolean
  createdAt: string
  updatedAt: string
}

interface CompaniesContextType {
  companies: CompanyProfile[]
  currentCompanyProfile?: CompanyProfile
  createCompanyProfile: (profile: Omit<CompanyProfile, 'id' | 'createdAt' | 'updatedAt'>) => void
  updateCompanyProfile: (id: string, updates: Partial<CompanyProfile>) => void
  addDocument: (companyId: string, document: Omit<CompanyDocument, 'id' | 'uploadedAt'>) => void
  addJobOpening: (companyId: string, jobOpening: Omit<JobOpening, 'id' | 'createdAt' | 'applicationsCount'>) => void
  updateJobOpening: (companyId: string, jobOpeningId: string, updates: Partial<JobOpening>) => void
  deleteJobOpening: (companyId: string, jobOpeningId: string) => void
  startLiveStream: (companyId: string, streamUrl: string) => void
  stopLiveStream: (companyId: string) => void
  getCompaniesByLocation: (location: string) => CompanyProfile[]
  getCompaniesByIndustry: (industry: string) => CompanyProfile[]
  getJobOpeningsByCategory: (category: string) => { company: CompanyProfile, jobOpening: JobOpening }[]
  getHiringCompanies: () => CompanyProfile[]
  validateLicenceUniqueness: (licenceNumber: string, excludeId?: string) => boolean
}

const CompaniesContext = createContext<CompaniesContextType | undefined>(undefined)

// Mock data for demonstration
const mockCompanies: CompanyProfile[] = [
  {
    id: 'company-1',
    userId: 'user-company-1',
    companyInfo: {
      companyName: 'Saudi Tech Solutions',
      tradeName: 'STS',
      industry: 'Information Technology',
      companySize: '201-500',
      foundedYear: 2010,
      headquarters: 'Riyadh, Saudi Arabia',
      website: 'https://sauditechsolutions.com',
      description: 'Leading technology solutions provider in the Kingdom of Saudi Arabia',
      mission: 'To provide innovative technology solutions that drive digital transformation',
      vision: 'To be the leading technology partner in the Middle East'
    },
    contactInfo: {
      hrManagerName: 'Sarah Al-Mahmoud',
      hrEmail: '<EMAIL>',
      hrPhone: '+966112345678',
      hrWhatsapp: '+966501234567',
      companyPhone: '+966112345678',
      companyEmail: '<EMAIL>',
      address: 'King Fahd Road, Olaya District, Riyadh 12345',
      poBox: 'P.O. Box 12345, Riyadh 11564'
    },
    legalInfo: {
      crNumber: 'CR-**********',
      taxNumber: 'TAX-300123456789003',
      licenceNumber: 'LIC-TECH-2024-001',
      establishmentNumber: 'EST-*********',
      chamberMembership: 'CHAMBER-RUH-2024-456'
    },
    documents: [
      {
        id: 'doc-1',
        name: 'Commercial Registration',
        type: 'cr',
        url: '/documents/company-cr.pdf',
        uploadedAt: new Date().toISOString()
      },
      {
        id: 'doc-2',
        name: 'Business License',
        type: 'licence',
        url: '/documents/company-license.pdf',
        uploadedAt: new Date().toISOString()
      }
    ],
    jobOpenings: [
      {
        id: 'job-1',
        title: 'Senior Software Engineer',
        department: 'Engineering',
        category: 'Software Development',
        description: 'We are looking for a senior software engineer to join our growing team',
        requirements: ['5+ years experience', 'React/Node.js', 'AWS knowledge', 'Team leadership'],
        salaryRange: {
          min: 15000,
          max: 25000,
          currency: 'SAR'
        },
        employmentType: 'full_time',
        experienceLevel: 'senior',
        location: 'Riyadh',
        isRemote: false,
        benefits: ['Health Insurance', 'Annual Bonus', 'Training Budget', 'Flexible Hours'],
        urgency: 'immediate',
        documentsRequired: ['CV', 'Portfolio', 'Certificates'],
        createdAt: new Date().toISOString(),
        applicationsCount: 23
      }
    ],
    videos: [
      {
        id: 'video-1',
        title: 'Company Culture Overview',
        type: 'company_intro',
        url: '/videos/company-culture.mp4',
        duration: 240,
        uploadedAt: new Date().toISOString()
      }
    ],
    isLiveStreaming: false,
    operatingLocations: ['Riyadh', 'Jeddah', 'Dammam'],
    preferredWorkerCategories: ['Software Development', 'Data Analysis', 'Project Management'],
    companyBenefits: ['Health Insurance', 'Annual Bonus', 'Training Programs', 'Career Development'],
    workEnvironment: {
      isRemoteFriendly: true,
      hasFlexibleHours: true,
      providesTraining: true,
      hasCareerGrowth: true,
      workLifeBalance: 4
    },
    rating: 4.6,
    totalReviews: 87,
    totalEmployees: 350,
    isVerified: true,
    isHiring: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
]

export function CompaniesProvider({ children }: { children: ReactNode }) {
  const [companies, setCompanies] = useState<CompanyProfile[]>([])
  const [currentCompanyProfile, setCurrentCompanyProfile] = useState<CompanyProfile>()

  useEffect(() => {
    // Load companies from localStorage or use mock data
    const savedCompanies = localStorage.getItem('kaazmaamaa-companies')
    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies))
    } else {
      setCompanies(mockCompanies)
      localStorage.setItem('kaazmaamaa-companies', JSON.stringify(mockCompanies))
    }
  }, [])

  const saveToStorage = useCallback((updatedCompanies: CompanyProfile[]) => {
    localStorage.setItem('kaazmaamaa-companies', JSON.stringify(updatedCompanies))
  }, [])

  const createCompanyProfile = useCallback((profile: Omit<CompanyProfile, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProfile: CompanyProfile = {
      ...profile,
      id: `company-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const updatedCompanies = [...companies, newProfile]
    setCompanies(updatedCompanies)
    saveToStorage(updatedCompanies)
    setCurrentCompanyProfile(newProfile)
  }, [companies, saveToStorage])

  const updateCompanyProfile = useCallback((id: string, updates: Partial<CompanyProfile>) => {
    const updatedCompanies = companies.map(company =>
      company.id === id
        ? { ...company, ...updates, updatedAt: new Date().toISOString() }
        : company
    )
    setCompanies(updatedCompanies)
    saveToStorage(updatedCompanies)
  }, [companies, saveToStorage])

  const addDocument = useCallback((companyId: string, document: Omit<CompanyDocument, 'id' | 'uploadedAt'>) => {
    const newDocument: CompanyDocument = {
      ...document,
      id: Date.now().toString(),
      uploadedAt: new Date().toISOString()
    }

    updateCompanyProfile(companyId, {
      documents: [...(companies.find(c => c.id === companyId)?.documents || []), newDocument]
    })
  }, [companies, updateCompanyProfile])

  const addJobOpening = useCallback((companyId: string, jobOpening: Omit<JobOpening, 'id' | 'createdAt' | 'applicationsCount'>) => {
    const newJobOpening: JobOpening = {
      ...jobOpening,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      applicationsCount: 0
    }

    updateCompanyProfile(companyId, {
      jobOpenings: [...(companies.find(c => c.id === companyId)?.jobOpenings || []), newJobOpening]
    })
  }, [companies, updateCompanyProfile])

  const updateJobOpening = useCallback((companyId: string, jobOpeningId: string, updates: Partial<JobOpening>) => {
    const company = companies.find(c => c.id === companyId)
    if (company) {
      const updatedJobOpenings = company.jobOpenings.map(job =>
        job.id === jobOpeningId ? { ...job, ...updates } : job
      )
      updateCompanyProfile(companyId, { jobOpenings: updatedJobOpenings })
    }
  }, [companies, updateCompanyProfile])

  const deleteJobOpening = useCallback((companyId: string, jobOpeningId: string) => {
    const company = companies.find(c => c.id === companyId)
    if (company) {
      const updatedJobOpenings = company.jobOpenings.filter(job => job.id !== jobOpeningId)
      updateCompanyProfile(companyId, { jobOpenings: updatedJobOpenings })
    }
  }, [companies, updateCompanyProfile])

  const startLiveStream = useCallback((companyId: string, streamUrl: string) => {
    updateCompanyProfile(companyId, {
      isLiveStreaming: true,
      liveStreamUrl: streamUrl
    })
  }, [updateCompanyProfile])

  const stopLiveStream = useCallback((companyId: string) => {
    updateCompanyProfile(companyId, {
      isLiveStreaming: false,
      liveStreamUrl: undefined
    })
  }, [updateCompanyProfile])

  const getCompaniesByLocation = useCallback((location: string) => {
    return companies.filter(company => 
      company.operatingLocations.some(loc => 
        loc.toLowerCase().includes(location.toLowerCase())
      )
    )
  }, [companies])

  const getCompaniesByIndustry = useCallback((industry: string) => {
    return companies.filter(company => 
      company.companyInfo.industry.toLowerCase().includes(industry.toLowerCase())
    )
  }, [companies])

  const getJobOpeningsByCategory = useCallback((category: string) => {
    const results: { company: CompanyProfile, jobOpening: JobOpening }[] = []
    companies.forEach(company => {
      company.jobOpenings.forEach(jobOpening => {
        if (jobOpening.category.toLowerCase().includes(category.toLowerCase())) {
          results.push({ company, jobOpening })
        }
      })
    })
    return results
  }, [companies])

  const getHiringCompanies = useCallback(() => {
    return companies.filter(company => company.isHiring && company.jobOpenings.length > 0)
  }, [companies])

  const validateLicenceUniqueness = useCallback((licenceNumber: string, excludeId?: string) => {
    return !companies.some(company => 
      company.legalInfo.licenceNumber === licenceNumber && company.id !== excludeId
    )
  }, [companies])

  const value = {
    companies,
    currentCompanyProfile,
    createCompanyProfile,
    updateCompanyProfile,
    addDocument,
    addJobOpening,
    updateJobOpening,
    deleteJobOpening,
    startLiveStream,
    stopLiveStream,
    getCompaniesByLocation,
    getCompaniesByIndustry,
    getJobOpeningsByCategory,
    getHiringCompanies,
    validateLicenceUniqueness
  }

  return (
    <CompaniesContext.Provider value={value}>
      {children}
    </CompaniesContext.Provider>
  )
}

export function useCompanies() {
  const context = useContext(CompaniesContext)
  if (context === undefined) {
    throw new Error('useCompanies must be used within a CompaniesProvider')
  }
  return context
}
