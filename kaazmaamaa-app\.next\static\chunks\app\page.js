/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/class-variance-authority/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cva: () => (/* binding */ cva),\n/* harmony export */   cx: () => (/* binding */ cx)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ \nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nconst cx = clsx__WEBPACK_IMPORTED_MODULE_0__.clsx;\nconst cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxjQUFjLGFBQWEsK0NBQStDLGdEQUFnRCxlQUFlLFFBQVEsSUFBSSwwQ0FBMEMseUNBQXlDLFNBQWdCLGdCQUFnQix3Q0FBd0MsSUFBSSxtREFBbUQsU0FBUyxpRUFBZSxJQUFJIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxLQUFaTUFBTUFBIEFVR01FTlRcXGthYXptYWFtYWEtYXBwXFxub2RlX21vZHVsZXNcXGNsc3hcXGRpc3RcXGNsc3gubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIHIoZSl7dmFyIHQsZixuPVwiXCI7aWYoXCJzdHJpbmdcIj09dHlwZW9mIGV8fFwibnVtYmVyXCI9PXR5cGVvZiBlKW4rPWU7ZWxzZSBpZihcIm9iamVjdFwiPT10eXBlb2YgZSlpZihBcnJheS5pc0FycmF5KGUpKXt2YXIgbz1lLmxlbmd0aDtmb3IodD0wO3Q8bzt0KyspZVt0XSYmKGY9cihlW3RdKSkmJihuJiYobis9XCIgXCIpLG4rPWYpfWVsc2UgZm9yKGYgaW4gZSllW2ZdJiYobiYmKG4rPVwiIFwiKSxuKz1mKTtyZXR1cm4gbn1leHBvcnQgZnVuY3Rpb24gY2xzeCgpe2Zvcih2YXIgZSx0LGY9MCxuPVwiXCIsbz1hcmd1bWVudHMubGVuZ3RoO2Y8bztmKyspKGU9YXJndW1lbnRzW2ZdKSYmKHQ9cihlKSkmJihuJiYobis9XCIgXCIpLG4rPXQpO3JldHVybiBufWV4cG9ydCBkZWZhdWx0IGNsc3g7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js":
/*!****************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/Icon.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Icon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaultAttributes.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\");\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst Icon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(_c = (param, ref)=>{\n    let { color = \"currentColor\", size = 24, strokeWidth = 2, absoluteStrokeWidth, className = \"\", children, iconNode, ...rest } = param;\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(\"svg\", {\n        ref,\n        ..._defaultAttributes_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n        className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide\", className),\n        ...!children && !(0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasA11yProp)(rest) && {\n            \"aria-hidden\": \"true\"\n        },\n        ...rest\n    }, [\n        ...iconNode.map((param)=>{\n            let [tag, attrs] = param;\n            return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(tag, attrs);\n        }),\n        ...Array.isArray(children) ? children : [\n            children\n        ]\n    ]);\n});\n_c1 = Icon;\n //# sourceMappingURL=Icon.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Icon$forwardRef\");\n$RefreshReg$(_c1, \"Icon\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/createLucideIcon.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ createLucideIcon)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./shared/src/utils.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\");\n/* harmony import */ var _Icon_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Icon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/Icon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \n\n\nconst createLucideIcon = (iconName, iconNode)=>{\n    const Component = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((param, ref)=>{\n        let { className, ...props } = param;\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_Icon_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            ref,\n            iconNode,\n            className: (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.mergeClasses)(\"lucide-\".concat((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toKebabCase)((0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName))), \"lucide-\".concat(iconName), className),\n            ...props\n        });\n    });\n    Component.displayName = (0,_shared_src_utils_js__WEBPACK_IMPORTED_MODULE_2__.toPascalCase)(iconName);\n    return Component;\n};\n //# sourceMappingURL=createLucideIcon.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vY3JlYXRlTHVjaWRlSWNvbi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBV00sdUJBQW1CLEdBQUMsVUFBa0IsUUFBdUI7SUFDakUsTUFBTSxDQUFZLDJFQUF1QyxRQUEwQjtZQUF6QixFQUFFLENBQVcsV0FBRyxRQUFTOzZCQUNqRixvREFBYSxDQUFDLGdEQUFNO1lBQ2xCO1lBQ0E7WUFDQSxTQUFXLHFFQUNULENBQVUsU0FBbUMsT0FBbkMsa0VBQVksa0VBQWEsRUFBUSxRQUFDLENBQUMsR0FDN0MsUUFBVSxFQUFRLE9BQVIsUUFBUSxHQUNsQjtZQUVGLEdBQUc7UUFBQSxDQUNKOztJQUdPLHdCQUFjLGtFQUFZLENBQUMsUUFBUTtJQUV0QztBQUNUIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhwXFxEZXNrdG9wXFxzcmNcXGNyZWF0ZUx1Y2lkZUljb24udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRWxlbWVudCwgZm9yd2FyZFJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IG1lcmdlQ2xhc3NlcywgdG9LZWJhYkNhc2UsIHRvUGFzY2FsQ2FzZSB9IGZyb20gJ0BsdWNpZGUvc2hhcmVkJztcbmltcG9ydCB7IEljb25Ob2RlLCBMdWNpZGVQcm9wcyB9IGZyb20gJy4vdHlwZXMnO1xuaW1wb3J0IEljb24gZnJvbSAnLi9JY29uJztcblxuLyoqXG4gKiBDcmVhdGUgYSBMdWNpZGUgaWNvbiBjb21wb25lbnRcbiAqIEBwYXJhbSB7c3RyaW5nfSBpY29uTmFtZVxuICogQHBhcmFtIHthcnJheX0gaWNvbk5vZGVcbiAqIEByZXR1cm5zIHtGb3J3YXJkUmVmRXhvdGljQ29tcG9uZW50fSBMdWNpZGVJY29uXG4gKi9cbmNvbnN0IGNyZWF0ZUx1Y2lkZUljb24gPSAoaWNvbk5hbWU6IHN0cmluZywgaWNvbk5vZGU6IEljb25Ob2RlKSA9PiB7XG4gIGNvbnN0IENvbXBvbmVudCA9IGZvcndhcmRSZWY8U1ZHU1ZHRWxlbWVudCwgTHVjaWRlUHJvcHM+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PlxuICAgIGNyZWF0ZUVsZW1lbnQoSWNvbiwge1xuICAgICAgcmVmLFxuICAgICAgaWNvbk5vZGUsXG4gICAgICBjbGFzc05hbWU6IG1lcmdlQ2xhc3NlcyhcbiAgICAgICAgYGx1Y2lkZS0ke3RvS2ViYWJDYXNlKHRvUGFzY2FsQ2FzZShpY29uTmFtZSkpfWAsXG4gICAgICAgIGBsdWNpZGUtJHtpY29uTmFtZX1gLFxuICAgICAgICBjbGFzc05hbWUsXG4gICAgICApLFxuICAgICAgLi4ucHJvcHMsXG4gICAgfSksXG4gICk7XG5cbiAgQ29tcG9uZW50LmRpc3BsYXlOYW1lID0gdG9QYXNjYWxDYXNlKGljb25OYW1lKTtcblxuICByZXR1cm4gQ29tcG9uZW50O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgY3JlYXRlTHVjaWRlSWNvbjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js":
/*!*****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/defaultAttributes.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultAttributes)\n/* harmony export */ });\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ var defaultAttributes = {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: 24,\n    height: 24,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: 2,\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n};\n //# sourceMappingURL=defaultAttributes.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vZGVmYXVsdEF0dHJpYnV0ZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0lBQUEsQ0FBZTtJQUNiLEtBQU87SUFDUCxLQUFPO0lBQ1AsTUFBUTtJQUNSLE9BQVM7SUFDVCxJQUFNO0lBQ04sTUFBUTtJQUNSLFdBQWE7SUFDYixhQUFlO0lBQ2YsY0FBZ0I7QUFDbEIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXHNyY1xcZGVmYXVsdEF0dHJpYnV0ZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1xuICB4bWxuczogJ2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyxcbiAgd2lkdGg6IDI0LFxuICBoZWlnaHQ6IDI0LFxuICB2aWV3Qm94OiAnMCAwIDI0IDI0JyxcbiAgZmlsbDogJ25vbmUnLFxuICBzdHJva2U6ICdjdXJyZW50Q29sb3InLFxuICBzdHJva2VXaWR0aDogMixcbiAgc3Ryb2tlTGluZWNhcDogJ3JvdW5kJyxcbiAgc3Ryb2tlTGluZWpvaW46ICdyb3VuZCcsXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/building.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Building)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"16\",\n            height: \"20\",\n            x: \"4\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"76otgf\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 22v-4h6v4\",\n            key: \"r93iot\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 6h.01\",\n            key: \"1dz90k\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 6h.01\",\n            key: \"1x0f13\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 6h.01\",\n            key: \"1vi96p\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 10h.01\",\n            key: \"1nrarc\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 14h.01\",\n            key: \"1etili\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 10h.01\",\n            key: \"1m94wz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 14h.01\",\n            key: \"1gbofw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 10h.01\",\n            key: \"19clt8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M8 14h.01\",\n            key: \"6423bh\"\n        }\n    ]\n];\nconst Building = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"building\", __iconNode);\n //# sourceMappingURL=building.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/users.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Users)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2\",\n            key: \"1yyitq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 3.128a4 4 0 0 1 0 7.744\",\n            key: \"16gr8j\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M22 21v-2a4 4 0 0 0-3-3.87\",\n            key: \"kshegd\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"9\",\n            cy: \"7\",\n            r: \"4\",\n            key: \"nufk8\"\n        }\n    ]\n];\nconst Users = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"users\", __iconNode);\n //# sourceMappingURL=users.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/wrench.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Wrench)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\",\n            key: \"cbrjhi\"\n        }\n    ]\n];\nconst Wrench = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"wrench\", __iconNode);\n //# sourceMappingURL=wrench.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/shared/src/utils.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hasA11yProp: () => (/* binding */ hasA11yProp),\n/* harmony export */   mergeClasses: () => (/* binding */ mergeClasses),\n/* harmony export */   toCamelCase: () => (/* binding */ toCamelCase),\n/* harmony export */   toKebabCase: () => (/* binding */ toKebabCase),\n/* harmony export */   toPascalCase: () => (/* binding */ toPascalCase)\n/* harmony export */ });\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ const toKebabCase = (string)=>string.replace(/([a-z0-9])([A-Z])/g, \"$1-$2\").toLowerCase();\nconst toCamelCase = (string)=>string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2)=>p2 ? p2.toUpperCase() : p1.toLowerCase());\nconst toPascalCase = (string)=>{\n    const camelCase = toCamelCase(string);\n    return camelCase.charAt(0).toUpperCase() + camelCase.slice(1);\n};\nconst mergeClasses = function() {\n    for(var _len = arguments.length, classes = new Array(_len), _key = 0; _key < _len; _key++){\n        classes[_key] = arguments[_key];\n    }\n    return classes.filter((className, index, array)=>{\n        return Boolean(className) && className.trim() !== \"\" && array.indexOf(className) === index;\n    }).join(\" \").trim();\n};\nconst hasA11yProp = (props)=>{\n    for(const prop in props){\n        if (prop.startsWith(\"aria-\") || prop === \"role\" || prop === \"title\") {\n            return true;\n        }\n    }\n};\n //# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/shared/src/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxocFxcRGVza3RvcFxcS0FBWk1BQU1BQSBBVUdNRU5UXFxrYWF6bWFhbWFhLWFwcFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxhcGlcXG5hdmlnYXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi4vY2xpZW50L2NvbXBvbmVudHMvbmF2aWdhdGlvbic7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW5hdmlnYXRpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CKAAZMAAMAA%20AUGMENT%5C%5Ckaazmaamaa-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CKAAZMAAMAA%20AUGMENT%5C%5Ckaazmaamaa-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDaHAlNUMlNUNEZXNrdG9wJTVDJTVDS0FBWk1BQU1BQSUyMEFVR01FTlQlNUMlNUNrYWF6bWFhbWFhLWFwcCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQW1IIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxocFxcXFxEZXNrdG9wXFxcXEtBQVpNQUFNQUEgQVVHTUVOVFxcXFxrYWF6bWFhbWFhLWFwcFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CKAAZMAAMAA%20AUGMENT%5C%5Ckaazmaamaa-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaHBcXERlc2t0b3BcXEtBQVpNQUFNQUEgQVVHTUVOVFxca2Fhem1hYW1hYS1hcHBcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    classGroups\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  for (const classGroupId in classGroups) {\n    processClassesRecursively(classGroups[classGroupId], classMap, classGroupId, theme);\n  }\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst MODIFIER_SEPARATOR = ':';\nconst MODIFIER_SEPARATOR_LENGTH = MODIFIER_SEPARATOR.length;\nconst createParseClassName = config => {\n  const {\n    prefix,\n    experimentalParseClassName\n  } = config;\n  /**\n   * Parse class name into parts.\n   *\n   * Inspired by `splitAtTopLevelOnly` used in Tailwind CSS\n   * @see https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n   */\n  let parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let parenDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0 && parenDepth === 0) {\n        if (currentCharacter === MODIFIER_SEPARATOR) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + MODIFIER_SEPARATOR_LENGTH;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      } else if (currentCharacter === '(') {\n        parenDepth++;\n      } else if (currentCharacter === ')') {\n        parenDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const baseClassName = stripImportantModifier(baseClassNameWithImportantModifier);\n    const hasImportantModifier = baseClassName !== baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (prefix) {\n    const fullPrefix = prefix + MODIFIER_SEPARATOR;\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => className.startsWith(fullPrefix) ? parseClassNameOriginal(className.substring(fullPrefix.length)) : {\n      isExternal: true,\n      modifiers: [],\n      hasImportantModifier: false,\n      baseClassName: className,\n      maybePostfixModifierPosition: undefined\n    };\n  }\n  if (experimentalParseClassName) {\n    const parseClassNameOriginal = parseClassName;\n    parseClassName = className => experimentalParseClassName({\n      className,\n      parseClassName: parseClassNameOriginal\n    });\n  }\n  return parseClassName;\n};\nconst stripImportantModifier = baseClassName => {\n  if (baseClassName.endsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(0, baseClassName.length - 1);\n  }\n  /**\n   * In Tailwind CSS v3 the important modifier was at the start of the base class name. This is still supported for legacy reasons.\n   * @see https://github.com/dcastil/tailwind-merge/issues/513#issuecomment-2614029864\n   */\n  if (baseClassName.startsWith(IMPORTANT_MODIFIER)) {\n    return baseClassName.substring(1);\n  }\n  return baseClassName;\n};\n\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst createSortModifiers = config => {\n  const orderSensitiveModifiers = Object.fromEntries(config.orderSensitiveModifiers.map(modifier => [modifier, true]));\n  const sortModifiers = modifiers => {\n    if (modifiers.length <= 1) {\n      return modifiers;\n    }\n    const sortedModifiers = [];\n    let unsortedModifiers = [];\n    modifiers.forEach(modifier => {\n      const isPositionSensitive = modifier[0] === '[' || orderSensitiveModifiers[modifier];\n      if (isPositionSensitive) {\n        sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n        unsortedModifiers = [];\n      } else {\n        unsortedModifiers.push(modifier);\n      }\n    });\n    sortedModifiers.push(...unsortedModifiers.sort());\n    return sortedModifiers;\n  };\n  return sortModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  sortModifiers: createSortModifiers(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds,\n    sortModifiers\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      isExternal,\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    if (isExternal) {\n      result = originalClassName + (result.length > 0 ? ' ' + result : result);\n      continue;\n    }\n    let hasPostfixModifier = !!maybePostfixModifierPosition;\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:(\\w[\\w-]*):)?(.+)\\]$/i;\nconst arbitraryVariableRegex = /^\\((?:(\\w[\\w-]*):)?(.+)\\)$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isFraction = value => fractionRegex.test(value);\nconst isNumber = value => !!value && !Number.isNaN(Number(value));\nconst isInteger = value => !!value && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst isAny = () => true;\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst isAnyNonArbitrary = value => !isArbitraryValue(value) && !isArbitraryVariable(value);\nconst isArbitrarySize = value => getIsArbitraryValue(value, isLabelSize, isNever);\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, isLabelLength, isLengthOnly);\nconst isArbitraryNumber = value => getIsArbitraryValue(value, isLabelNumber, isNumber);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, isLabelPosition, isNever);\nconst isArbitraryImage = value => getIsArbitraryValue(value, isLabelImage, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, isLabelShadow, isShadow);\nconst isArbitraryVariable = value => arbitraryVariableRegex.test(value);\nconst isArbitraryVariableLength = value => getIsArbitraryVariable(value, isLabelLength);\nconst isArbitraryVariableFamilyName = value => getIsArbitraryVariable(value, isLabelFamilyName);\nconst isArbitraryVariablePosition = value => getIsArbitraryVariable(value, isLabelPosition);\nconst isArbitraryVariableSize = value => getIsArbitraryVariable(value, isLabelSize);\nconst isArbitraryVariableImage = value => getIsArbitraryVariable(value, isLabelImage);\nconst isArbitraryVariableShadow = value => getIsArbitraryVariable(value, isLabelShadow, true);\n// Helpers\nconst getIsArbitraryValue = (value, testLabel, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst getIsArbitraryVariable = (value, testLabel, shouldMatchNoLabel = false) => {\n  const result = arbitraryVariableRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return testLabel(result[1]);\n    }\n    return shouldMatchNoLabel;\n  }\n  return false;\n};\n// Labels\nconst isLabelPosition = label => label === 'position' || label === 'percentage';\nconst isLabelImage = label => label === 'image' || label === 'url';\nconst isLabelSize = label => label === 'length' || label === 'size' || label === 'bg-size';\nconst isLabelLength = label => label === 'length';\nconst isLabelNumber = label => label === 'number';\nconst isLabelFamilyName = label => label === 'family-name';\nconst isLabelShadow = label => label === 'shadow';\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isAnyNonArbitrary,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isArbitraryVariable,\n  isArbitraryVariableFamilyName,\n  isArbitraryVariableImage,\n  isArbitraryVariableLength,\n  isArbitraryVariablePosition,\n  isArbitraryVariableShadow,\n  isArbitraryVariableSize,\n  isFraction,\n  isInteger,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  /**\n   * Theme getters for theme variable namespaces\n   * @see https://tailwindcss.com/docs/theme#theme-variable-namespaces\n   */\n  /***/\n  const themeColor = fromTheme('color');\n  const themeFont = fromTheme('font');\n  const themeText = fromTheme('text');\n  const themeFontWeight = fromTheme('font-weight');\n  const themeTracking = fromTheme('tracking');\n  const themeLeading = fromTheme('leading');\n  const themeBreakpoint = fromTheme('breakpoint');\n  const themeContainer = fromTheme('container');\n  const themeSpacing = fromTheme('spacing');\n  const themeRadius = fromTheme('radius');\n  const themeShadow = fromTheme('shadow');\n  const themeInsetShadow = fromTheme('inset-shadow');\n  const themeTextShadow = fromTheme('text-shadow');\n  const themeDropShadow = fromTheme('drop-shadow');\n  const themeBlur = fromTheme('blur');\n  const themePerspective = fromTheme('perspective');\n  const themeAspect = fromTheme('aspect');\n  const themeEase = fromTheme('ease');\n  const themeAnimate = fromTheme('animate');\n  /**\n   * Helpers to avoid repeating the same scales\n   *\n   * We use functions that create a new array every time they're called instead of static arrays.\n   * This ensures that users who modify any scale by mutating the array (e.g. with `array.push(element)`) don't accidentally mutate arrays in other parts of the config.\n   */\n  /***/\n  const scaleBreak = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const scalePosition = () => ['center', 'top', 'bottom', 'left', 'right', 'top-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-top', 'top-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-top', 'bottom-right',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'right-bottom', 'bottom-left',\n  // Deprecated since Tailwind CSS v4.1.0, see https://github.com/tailwindlabs/tailwindcss/pull/17378\n  'left-bottom'];\n  const scalePositionWithArbitrary = () => [...scalePosition(), isArbitraryVariable, isArbitraryValue];\n  const scaleOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const scaleOverscroll = () => ['auto', 'contain', 'none'];\n  const scaleUnambiguousSpacing = () => [isArbitraryVariable, isArbitraryValue, themeSpacing];\n  const scaleInset = () => [isFraction, 'full', 'auto', ...scaleUnambiguousSpacing()];\n  const scaleGridTemplateColsRows = () => [isInteger, 'none', 'subgrid', isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartAndEnd = () => ['auto', {\n    span: ['full', isInteger, isArbitraryVariable, isArbitraryValue]\n  }, isInteger, isArbitraryVariable, isArbitraryValue];\n  const scaleGridColRowStartOrEnd = () => [isInteger, 'auto', isArbitraryVariable, isArbitraryValue];\n  const scaleGridAutoColsRows = () => ['auto', 'min', 'max', 'fr', isArbitraryVariable, isArbitraryValue];\n  const scaleAlignPrimaryAxis = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch', 'baseline', 'center-safe', 'end-safe'];\n  const scaleAlignSecondaryAxis = () => ['start', 'end', 'center', 'stretch', 'center-safe', 'end-safe'];\n  const scaleMargin = () => ['auto', ...scaleUnambiguousSpacing()];\n  const scaleSizing = () => [isFraction, 'auto', 'full', 'dvw', 'dvh', 'lvw', 'lvh', 'svw', 'svh', 'min', 'max', 'fit', ...scaleUnambiguousSpacing()];\n  const scaleColor = () => [themeColor, isArbitraryVariable, isArbitraryValue];\n  const scaleBgPosition = () => [...scalePosition(), isArbitraryVariablePosition, isArbitraryPosition, {\n    position: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleBgRepeat = () => ['no-repeat', {\n    repeat: ['', 'x', 'y', 'space', 'round']\n  }];\n  const scaleBgSize = () => ['auto', 'cover', 'contain', isArbitraryVariableSize, isArbitrarySize, {\n    size: [isArbitraryVariable, isArbitraryValue]\n  }];\n  const scaleGradientStopPosition = () => [isPercent, isArbitraryVariableLength, isArbitraryLength];\n  const scaleRadius = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', 'full', themeRadius, isArbitraryVariable, isArbitraryValue];\n  const scaleBorderWidth = () => ['', isNumber, isArbitraryVariableLength, isArbitraryLength];\n  const scaleLineStyle = () => ['solid', 'dashed', 'dotted', 'double'];\n  const scaleBlendMode = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const scaleMaskImagePosition = () => [isNumber, isPercent, isArbitraryVariablePosition, isArbitraryPosition];\n  const scaleBlur = () => [\n  // Deprecated since Tailwind CSS v4.0.0\n  '', 'none', themeBlur, isArbitraryVariable, isArbitraryValue];\n  const scaleRotate = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleScale = () => ['none', isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleSkew = () => [isNumber, isArbitraryVariable, isArbitraryValue];\n  const scaleTranslate = () => [isFraction, 'full', ...scaleUnambiguousSpacing()];\n  return {\n    cacheSize: 500,\n    theme: {\n      animate: ['spin', 'ping', 'pulse', 'bounce'],\n      aspect: ['video'],\n      blur: [isTshirtSize],\n      breakpoint: [isTshirtSize],\n      color: [isAny],\n      container: [isTshirtSize],\n      'drop-shadow': [isTshirtSize],\n      ease: ['in', 'out', 'in-out'],\n      font: [isAnyNonArbitrary],\n      'font-weight': ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'],\n      'inset-shadow': [isTshirtSize],\n      leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose'],\n      perspective: ['dramatic', 'near', 'normal', 'midrange', 'distant', 'none'],\n      radius: [isTshirtSize],\n      shadow: [isTshirtSize],\n      spacing: ['px', isNumber],\n      text: [isTshirtSize],\n      'text-shadow': [isTshirtSize],\n      tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest']\n    },\n    classGroups: {\n      // --------------\n      // --- Layout ---\n      // --------------\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', isFraction, isArbitraryValue, isArbitraryVariable, themeAspect]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       * @deprecated since Tailwind CSS v4.0.0\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isNumber, isArbitraryValue, isArbitraryVariable, themeContainer]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': scaleBreak()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': scaleBreak()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Screen Reader Only\n       * @see https://tailwindcss.com/docs/display#screen-reader-only\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: scalePositionWithArbitrary()\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: scaleOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': scaleOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': scaleOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': scaleOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': scaleOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: scaleInset()\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': scaleInset()\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': scaleInset()\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: scaleInset()\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: scaleInset()\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: scaleInset()\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: scaleInset()\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: scaleInset()\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: scaleInset()\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: [isInteger, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------------\n      // --- Flexbox and Grid ---\n      // ------------------------\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: [isFraction, 'full', 'auto', themeContainer, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['nowrap', 'wrap', 'wrap-reverse']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: [isNumber, isFraction, 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: [isInteger, 'first', 'last', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': scaleGridTemplateColsRows()\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: scaleGridColRowStartAndEnd()\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': scaleGridColRowStartOrEnd()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': scaleGridAutoColsRows()\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': scaleGridAutoColsRows()\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: [...scaleAlignPrimaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': [...scaleAlignSecondaryAxis(), 'normal']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...scaleAlignPrimaryAxis()]\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: [...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', ...scaleAlignSecondaryAxis(), {\n          baseline: ['', 'last']\n        }]\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': scaleAlignPrimaryAxis()\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': [...scaleAlignSecondaryAxis(), 'baseline']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', ...scaleAlignSecondaryAxis()]\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: scaleMargin()\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: scaleMargin()\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: scaleMargin()\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: scaleMargin()\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: scaleMargin()\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: scaleMargin()\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: scaleMargin()\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: scaleMargin()\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: scaleMargin()\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x': [{\n        'space-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y': [{\n        'space-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/margin#adding-space-between-children\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // --------------\n      // --- Sizing ---\n      // --------------\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/width#setting-both-width-and-height\n       */\n      size: [{\n        size: scaleSizing()\n      }],\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: [themeContainer, 'screen', ...scaleSizing()]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [themeContainer, 'screen', /** Deprecated. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [themeContainer, 'screen', 'none', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        'prose', /** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        {\n          screen: [themeBreakpoint]\n        }, ...scaleSizing()]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: ['screen', 'lh', ...scaleSizing()]\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': ['screen', 'lh', 'none', ...scaleSizing()]\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': ['screen', 'lh', ...scaleSizing()]\n      }],\n      // ------------------\n      // --- Typography ---\n      // ------------------\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', themeText, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: [themeFontWeight, isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Font Stretch\n       * @see https://tailwindcss.com/docs/font-stretch\n       */\n      'font-stretch': [{\n        'font-stretch': ['ultra-condensed', 'extra-condensed', 'condensed', 'semi-condensed', 'normal', 'semi-expanded', 'expanded', 'extra-expanded', 'ultra-expanded', isPercent, isArbitraryValue]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isArbitraryVariableFamilyName, isArbitraryValue, themeFont]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: [themeTracking, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': [isNumber, 'none', isArbitraryVariable, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: [/** Deprecated since Tailwind CSS v4.0.0. @see https://github.com/tailwindlabs/tailwindcss.com/issues/2027#issuecomment-2620152757 */\n        themeLeading, ...scaleUnambiguousSpacing()]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['disc', 'decimal', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://v3.tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: scaleColor()\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: scaleColor()\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...scaleLineStyle(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: [isNumber, 'from-font', 'auto', isArbitraryVariable, isArbitraryLength]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: scaleColor()\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': [isNumber, 'auto', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: scaleUnambiguousSpacing()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Overflow Wrap\n       * @see https://tailwindcss.com/docs/overflow-wrap\n       */\n      wrap: [{\n        wrap: ['break-word', 'anywhere', 'normal']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -------------------\n      // --- Backgrounds ---\n      // -------------------\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: scaleBgPosition()\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: scaleBgRepeat()\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: scaleBgSize()\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          linear: [{\n            to: ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n          }, isInteger, isArbitraryVariable, isArbitraryValue],\n          radial: ['', isArbitraryVariable, isArbitraryValue],\n          conic: [isInteger, isArbitraryVariable, isArbitraryValue]\n        }, isArbitraryVariableImage, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: scaleGradientStopPosition()\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: scaleColor()\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: scaleColor()\n      }],\n      // ---------------\n      // --- Borders ---\n      // ---------------\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: scaleRadius()\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': scaleRadius()\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': scaleRadius()\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': scaleRadius()\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': scaleRadius()\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': scaleRadius()\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': scaleRadius()\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': scaleRadius()\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': scaleRadius()\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': scaleRadius()\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': scaleRadius()\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': scaleRadius()\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: scaleBorderWidth()\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': scaleBorderWidth()\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': scaleBorderWidth()\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x': [{\n        'divide-x': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y': [{\n        'divide-y': scaleBorderWidth()\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/border-width#between-children\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/border-style#setting-the-divider-style\n       */\n      'divide-style': [{\n        divide: [...scaleLineStyle(), 'hidden', 'none']\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: scaleColor()\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': scaleColor()\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': scaleColor()\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': scaleColor()\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': scaleColor()\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': scaleColor()\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': scaleColor()\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': scaleColor()\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': scaleColor()\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: scaleColor()\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: [...scaleLineStyle(), 'none', 'hidden']\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: ['', isNumber, isArbitraryVariableLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: scaleColor()\n      }],\n      // ---------------\n      // --- Effects ---\n      // ---------------\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-shadow-color\n       */\n      'shadow-color': [{\n        shadow: scaleColor()\n      }],\n      /**\n       * Inset Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-shadow\n       */\n      'inset-shadow': [{\n        'inset-shadow': ['none', themeInsetShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Inset Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-shadow-color\n       */\n      'inset-shadow-color': [{\n        'inset-shadow': scaleColor()\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-a-ring\n       */\n      'ring-w': [{\n        ring: scaleBorderWidth()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://v3.tailwindcss.com/docs/ring-width#inset-rings\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-ring-color\n       */\n      'ring-color': [{\n        ring: scaleColor()\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://v3.tailwindcss.com/docs/ring-offset-width\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isNumber, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://v3.tailwindcss.com/docs/ring-offset-color\n       * @deprecated since Tailwind CSS v4.0.0\n       * @see https://github.com/tailwindlabs/tailwindcss/blob/v4.0.0/packages/tailwindcss/src/utilities.ts#L4158\n       */\n      'ring-offset-color': [{\n        'ring-offset': scaleColor()\n      }],\n      /**\n       * Inset Ring Width\n       * @see https://tailwindcss.com/docs/box-shadow#adding-an-inset-ring\n       */\n      'inset-ring-w': [{\n        'inset-ring': scaleBorderWidth()\n      }],\n      /**\n       * Inset Ring Color\n       * @see https://tailwindcss.com/docs/box-shadow#setting-the-inset-ring-color\n       */\n      'inset-ring-color': [{\n        'inset-ring': scaleColor()\n      }],\n      /**\n       * Text Shadow\n       * @see https://tailwindcss.com/docs/text-shadow\n       */\n      'text-shadow': [{\n        'text-shadow': ['none', themeTextShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Text Shadow Color\n       * @see https://tailwindcss.com/docs/text-shadow#setting-the-shadow-color\n       */\n      'text-shadow-color': [{\n        'text-shadow': scaleColor()\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...scaleBlendMode(), 'plus-darker', 'plus-lighter']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': scaleBlendMode()\n      }],\n      /**\n       * Mask Clip\n       * @see https://tailwindcss.com/docs/mask-clip\n       */\n      'mask-clip': [{\n        'mask-clip': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }, 'mask-no-clip'],\n      /**\n       * Mask Composite\n       * @see https://tailwindcss.com/docs/mask-composite\n       */\n      'mask-composite': [{\n        mask: ['add', 'subtract', 'intersect', 'exclude']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image-linear-pos': [{\n        'mask-linear': [isNumber]\n      }],\n      'mask-image-linear-from-pos': [{\n        'mask-linear-from': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-to-pos': [{\n        'mask-linear-to': scaleMaskImagePosition()\n      }],\n      'mask-image-linear-from-color': [{\n        'mask-linear-from': scaleColor()\n      }],\n      'mask-image-linear-to-color': [{\n        'mask-linear-to': scaleColor()\n      }],\n      'mask-image-t-from-pos': [{\n        'mask-t-from': scaleMaskImagePosition()\n      }],\n      'mask-image-t-to-pos': [{\n        'mask-t-to': scaleMaskImagePosition()\n      }],\n      'mask-image-t-from-color': [{\n        'mask-t-from': scaleColor()\n      }],\n      'mask-image-t-to-color': [{\n        'mask-t-to': scaleColor()\n      }],\n      'mask-image-r-from-pos': [{\n        'mask-r-from': scaleMaskImagePosition()\n      }],\n      'mask-image-r-to-pos': [{\n        'mask-r-to': scaleMaskImagePosition()\n      }],\n      'mask-image-r-from-color': [{\n        'mask-r-from': scaleColor()\n      }],\n      'mask-image-r-to-color': [{\n        'mask-r-to': scaleColor()\n      }],\n      'mask-image-b-from-pos': [{\n        'mask-b-from': scaleMaskImagePosition()\n      }],\n      'mask-image-b-to-pos': [{\n        'mask-b-to': scaleMaskImagePosition()\n      }],\n      'mask-image-b-from-color': [{\n        'mask-b-from': scaleColor()\n      }],\n      'mask-image-b-to-color': [{\n        'mask-b-to': scaleColor()\n      }],\n      'mask-image-l-from-pos': [{\n        'mask-l-from': scaleMaskImagePosition()\n      }],\n      'mask-image-l-to-pos': [{\n        'mask-l-to': scaleMaskImagePosition()\n      }],\n      'mask-image-l-from-color': [{\n        'mask-l-from': scaleColor()\n      }],\n      'mask-image-l-to-color': [{\n        'mask-l-to': scaleColor()\n      }],\n      'mask-image-x-from-pos': [{\n        'mask-x-from': scaleMaskImagePosition()\n      }],\n      'mask-image-x-to-pos': [{\n        'mask-x-to': scaleMaskImagePosition()\n      }],\n      'mask-image-x-from-color': [{\n        'mask-x-from': scaleColor()\n      }],\n      'mask-image-x-to-color': [{\n        'mask-x-to': scaleColor()\n      }],\n      'mask-image-y-from-pos': [{\n        'mask-y-from': scaleMaskImagePosition()\n      }],\n      'mask-image-y-to-pos': [{\n        'mask-y-to': scaleMaskImagePosition()\n      }],\n      'mask-image-y-from-color': [{\n        'mask-y-from': scaleColor()\n      }],\n      'mask-image-y-to-color': [{\n        'mask-y-to': scaleColor()\n      }],\n      'mask-image-radial': [{\n        'mask-radial': [isArbitraryVariable, isArbitraryValue]\n      }],\n      'mask-image-radial-from-pos': [{\n        'mask-radial-from': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-to-pos': [{\n        'mask-radial-to': scaleMaskImagePosition()\n      }],\n      'mask-image-radial-from-color': [{\n        'mask-radial-from': scaleColor()\n      }],\n      'mask-image-radial-to-color': [{\n        'mask-radial-to': scaleColor()\n      }],\n      'mask-image-radial-shape': [{\n        'mask-radial': ['circle', 'ellipse']\n      }],\n      'mask-image-radial-size': [{\n        'mask-radial': [{\n          closest: ['side', 'corner'],\n          farthest: ['side', 'corner']\n        }]\n      }],\n      'mask-image-radial-pos': [{\n        'mask-radial-at': scalePosition()\n      }],\n      'mask-image-conic-pos': [{\n        'mask-conic': [isNumber]\n      }],\n      'mask-image-conic-from-pos': [{\n        'mask-conic-from': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-to-pos': [{\n        'mask-conic-to': scaleMaskImagePosition()\n      }],\n      'mask-image-conic-from-color': [{\n        'mask-conic-from': scaleColor()\n      }],\n      'mask-image-conic-to-color': [{\n        'mask-conic-to': scaleColor()\n      }],\n      /**\n       * Mask Mode\n       * @see https://tailwindcss.com/docs/mask-mode\n       */\n      'mask-mode': [{\n        mask: ['alpha', 'luminance', 'match']\n      }],\n      /**\n       * Mask Origin\n       * @see https://tailwindcss.com/docs/mask-origin\n       */\n      'mask-origin': [{\n        'mask-origin': ['border', 'padding', 'content', 'fill', 'stroke', 'view']\n      }],\n      /**\n       * Mask Position\n       * @see https://tailwindcss.com/docs/mask-position\n       */\n      'mask-position': [{\n        mask: scaleBgPosition()\n      }],\n      /**\n       * Mask Repeat\n       * @see https://tailwindcss.com/docs/mask-repeat\n       */\n      'mask-repeat': [{\n        mask: scaleBgRepeat()\n      }],\n      /**\n       * Mask Size\n       * @see https://tailwindcss.com/docs/mask-size\n       */\n      'mask-size': [{\n        mask: scaleBgSize()\n      }],\n      /**\n       * Mask Type\n       * @see https://tailwindcss.com/docs/mask-type\n       */\n      'mask-type': [{\n        'mask-type': ['alpha', 'luminance']\n      }],\n      /**\n       * Mask Image\n       * @see https://tailwindcss.com/docs/mask-image\n       */\n      'mask-image': [{\n        mask: ['none', isArbitraryVariable, isArbitraryValue]\n      }],\n      // ---------------\n      // --- Filters ---\n      // ---------------\n      /**\n       * Filter\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: scaleBlur()\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': [\n        // Deprecated since Tailwind CSS v4.0.0\n        '', 'none', themeDropShadow, isArbitraryVariableShadow, isArbitraryShadow]\n      }],\n      /**\n       * Drop Shadow Color\n       * @see https://tailwindcss.com/docs/filter-drop-shadow#setting-the-shadow-color\n       */\n      'drop-shadow-color': [{\n        'drop-shadow': scaleColor()\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Filter\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': [\n        // Deprecated since Tailwind CSS v3.0.0\n        '', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': scaleBlur()\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': ['', isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      // --------------\n      // --- Tables ---\n      // --------------\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // ---------------------------------\n      // --- Transitions and Animation ---\n      // ---------------------------------\n      /**\n       * Transition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['', 'all', 'colors', 'opacity', 'shadow', 'transform', 'none', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Behavior\n       * @see https://tailwindcss.com/docs/transition-behavior\n       */\n      'transition-behavior': [{\n        transition: ['normal', 'discrete']\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: [isNumber, 'initial', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'initial', themeEase, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: [isNumber, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', themeAnimate, isArbitraryVariable, isArbitraryValue]\n      }],\n      // ------------------\n      // --- Transforms ---\n      // ------------------\n      /**\n       * Backface Visibility\n       * @see https://tailwindcss.com/docs/backface-visibility\n       */\n      backface: [{\n        backface: ['hidden', 'visible']\n      }],\n      /**\n       * Perspective\n       * @see https://tailwindcss.com/docs/perspective\n       */\n      perspective: [{\n        perspective: [themePerspective, isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Perspective Origin\n       * @see https://tailwindcss.com/docs/perspective-origin\n       */\n      'perspective-origin': [{\n        'perspective-origin': scalePositionWithArbitrary()\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: scaleRotate()\n      }],\n      /**\n       * Rotate X\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-x': [{\n        'rotate-x': scaleRotate()\n      }],\n      /**\n       * Rotate Y\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-y': [{\n        'rotate-y': scaleRotate()\n      }],\n      /**\n       * Rotate Z\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      'rotate-z': [{\n        'rotate-z': scaleRotate()\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: scaleScale()\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': scaleScale()\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': scaleScale()\n      }],\n      /**\n       * Scale Z\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-z': [{\n        'scale-z': scaleScale()\n      }],\n      /**\n       * Scale 3D\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-3d': ['scale-3d'],\n      /**\n       * Skew\n       * @see https://tailwindcss.com/docs/skew\n       */\n      skew: [{\n        skew: scaleSkew()\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': scaleSkew()\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': scaleSkew()\n      }],\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: [isArbitraryVariable, isArbitraryValue, '', 'none', 'gpu', 'cpu']\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: scalePositionWithArbitrary()\n      }],\n      /**\n       * Transform Style\n       * @see https://tailwindcss.com/docs/transform-style\n       */\n      'transform-style': [{\n        transform: ['3d', 'flat']\n      }],\n      /**\n       * Translate\n       * @see https://tailwindcss.com/docs/translate\n       */\n      translate: [{\n        translate: scaleTranslate()\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': scaleTranslate()\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': scaleTranslate()\n      }],\n      /**\n       * Translate Z\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-z': [{\n        'translate-z': scaleTranslate()\n      }],\n      /**\n       * Translate None\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-none': ['translate-none'],\n      // ---------------------\n      // --- Interactivity ---\n      // ---------------------\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: scaleColor()\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: scaleColor()\n      }],\n      /**\n       * Color Scheme\n       * @see https://tailwindcss.com/docs/color-scheme\n       */\n      'color-scheme': [{\n        scheme: ['normal', 'dark', 'light', 'light-dark', 'only-dark', 'only-light']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryVariable, isArbitraryValue]\n      }],\n      /**\n       * Field Sizing\n       * @see https://tailwindcss.com/docs/field-sizing\n       */\n      'field-sizing': [{\n        'field-sizing': ['fixed', 'content']\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['auto', 'none']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', '', 'y', 'x']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': scaleUnambiguousSpacing()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryVariable, isArbitraryValue]\n      }],\n      // -----------\n      // --- SVG ---\n      // -----------\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: ['none', ...scaleColor()]\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isNumber, isArbitraryVariableLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: ['none', ...scaleColor()]\n      }],\n      // ---------------------\n      // --- Accessibility ---\n      // ---------------------\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-x', 'border-w-y', 'border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-x', 'border-color-y', 'border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      translate: ['translate-x', 'translate-y', 'translate-none'],\n      'translate-none': ['translate', 'translate-x', 'translate-y', 'translate-z'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    },\n    orderSensitiveModifiers: ['*', '**', 'after', 'backdrop', 'before', 'details-content', 'file', 'first-letter', 'first-line', 'marker', 'placeholder', 'selection']\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  overrideConfigProperties(baseConfig.theme, override.theme);\n  overrideConfigProperties(baseConfig.classGroups, override.classGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroups, override.conflictingClassGroups);\n  overrideConfigProperties(baseConfig.conflictingClassGroupModifiers, override.conflictingClassGroupModifiers);\n  overrideProperty(baseConfig, 'orderSensitiveModifiers', override.orderSensitiveModifiers);\n  mergeConfigProperties(baseConfig.theme, extend.theme);\n  mergeConfigProperties(baseConfig.classGroups, extend.classGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroups, extend.conflictingClassGroups);\n  mergeConfigProperties(baseConfig.conflictingClassGroupModifiers, extend.conflictingClassGroupModifiers);\n  mergeArrayProperties(baseConfig, extend, 'orderSensitiveModifiers');\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      mergeArrayProperties(baseObject, mergeObject, key);\n    }\n  }\n};\nconst mergeArrayProperties = (baseObject, mergeObject, key) => {\n  const mergeValue = mergeObject[key];\n  if (mergeValue !== undefined) {\n    baseObject[key] = baseObject[key] ? baseObject[key].concat(mergeValue) : mergeValue;\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Users,Wrench!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { user, userRole, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            if (!loading && user && userRole) {\n                router.push('/feed');\n            }\n        }\n    }[\"Home.useEffect\"], [\n        user,\n        userRole,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    if (user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Redirecting to your feed...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl font-bold text-gray-900 mb-4\",\n                            children: [\n                                \"Welcome to \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600\",\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 24\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-2xl mx-auto\",\n                            children: \"The professional social network connecting Workers, Suppliers, and Companies. Share your expertise, find opportunities, and build meaningful connections.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-blue-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto mb-4 p-3 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"h-8 w-8 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-2xl\",\n                                            children: \"Worker\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: \"Skilled professionals looking for opportunities\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 mb-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Upload your credentials and documents\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Showcase your skills and experience\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Connect with suppliers and companies\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Find job opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            className: \"w-full\",\n                                            onClick: ()=>router.push('/auth/signup?role=worker'),\n                                            children: \"Join as Worker\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-green-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto mb-4 p-3 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-8 w-8 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-2xl\",\n                                            children: \"Supplier\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: \"Agencies providing workforce solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 mb-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Post job requirements and opportunities\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Connect with qualified workers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Manage your workforce network\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Share industry insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            className: \"w-full bg-green-600 hover:bg-green-700\",\n                                            onClick: ()=>router.push('/auth/signup?role=supplier'),\n                                            children: \"Join as Supplier\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-purple-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto mb-4 p-3 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Users_Wrench_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-8 w-8 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-2xl\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                            className: \"text-base\",\n                                            children: \"Businesses seeking workforce solutions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-gray-600 mb-6 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Post project requirements\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Find reliable suppliers and workers\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Build long-term partnerships\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• Share company updates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            className: \"w-full bg-purple-600 hover:bg-purple-700\",\n                                            onClick: ()=>router.push('/auth/signup?role=company'),\n                                            children: \"Join as Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-4\",\n                            children: \"Already have an account?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"lg\",\n                            onClick: ()=>router.push('/auth/login'),\n                            children: \"Sign In\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-12 p-8 bg-white rounded-lg shadow-lg max-w-2xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto mb-4 p-3 bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl\",\n                                        children: \"\\uD83D\\uDCC4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-2\",\n                                    children: \"Invoice Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-6\",\n                                    children: \"Create professional VAT/Tax invoices with ZATCA e-invoicing compliance, QR codes, and PDF generation.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            className: \"bg-orange-600 hover:bg-orange-700 text-white px-8 py-3 text-lg\",\n                            onClick: ()=>router.push('/invoice'),\n                            children: \"Access Invoice System\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-20 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-8\",\n                            children: \"Why Choose KAAZMAAMAA?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Real-time Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Share updates, react to posts, and engage with your professional community in real-time.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Document Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Securely upload and manage your professional documents, certificates, and credentials.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Direct Communication\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Connect directly via WhatsApp integration for seamless business communication.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"3xHTSfgTGal3Ad9auCh24sYIDH0=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            outline: \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button(param) {\n    let { className, variant, size, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_c = Button;\n\nvar _c;\n$RefreshReg$(_c, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardAction: () => (/* binding */ CardAction),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\nfunction Card(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n_c = Card;\nfunction CardHeader(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CardHeader;\nfunction CardTitle(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-title\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"leading-none font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n_c2 = CardTitle;\nfunction CardDescription(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-description\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_c3 = CardDescription;\nfunction CardAction(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"col-start-2 row-span-2 row-start-1 self-start justify-self-end\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, this);\n}\n_c4 = CardAction;\nfunction CardContent(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_c5 = CardContent;\nfunction CardFooter(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"card-footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center px-6 [.border-t]:pt-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n_c6 = CardFooter;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c1, \"CardHeader\");\n$RefreshReg$(_c2, \"CardTitle\");\n$RefreshReg$(_c3, \"CardDescription\");\n$RefreshReg$(_c4, \"CardAction\");\n$RefreshReg$(_c5, \"CardContent\");\n$RefreshReg$(_c6, \"CardFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userRole, setUserRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Start with loading true\n    ;\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load authentication state from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            const loadAuthState = {\n                \"AuthProvider.useEffect.loadAuthState\": ()=>{\n                    try {\n                        const savedUser = localStorage.getItem('kaazmaamaa-user');\n                        const savedRole = localStorage.getItem('kaazmaamaa-user-role');\n                        if (savedUser && savedRole) {\n                            const parsedUser = JSON.parse(savedUser);\n                            // Re-detect the user's role to ensure it's correct (in case profiles were created after login)\n                            const reDetectedRole = detectUserProfileType(parsedUser.email);\n                            console.log('🔄 Re-detecting role for:', parsedUser.email, 'Saved:', savedRole, 'Re-detected:', reDetectedRole);\n                            // Use re-detected role if it's different from saved role\n                            const finalRole = reDetectedRole !== 'worker' ? reDetectedRole : savedRole;\n                            setUser(parsedUser);\n                            setUserRole(finalRole);\n                            // Update localStorage if role changed\n                            if (finalRole !== savedRole) {\n                                console.log('🔄 Updating role in localStorage from', savedRole, 'to', finalRole);\n                                localStorage.setItem('kaazmaamaa-user-role', finalRole);\n                            }\n                            console.log('✅ Loaded user from localStorage:', parsedUser.email, 'Final role:', finalRole);\n                        } else {\n                            console.log('No saved authentication found');\n                        }\n                    } catch (error) {\n                        console.error('Error loading auth state from localStorage:', error);\n                    } finally{\n                        console.log('🔄 AuthContext: Setting loading to false');\n                        setLoading(false);\n                        setIsInitialized(true);\n                        console.log('✅ AuthContext: Initialization complete');\n                    }\n                }\n            }[\"AuthProvider.useEffect.loadAuthState\"];\n            loadAuthState();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signUp = async (email, password, role)=>{\n        // Mock implementation for now\n        console.log('Sign up:', {\n            email,\n            password,\n            role\n        });\n        const mockUser = {\n            id: Date.now().toString(),\n            email\n        };\n        // Save to state\n        setUser(mockUser);\n        setUserRole(role);\n        // Save to localStorage\n        try {\n            localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser));\n            localStorage.setItem('kaazmaamaa-user-role', role);\n            console.log('User authentication saved to localStorage');\n        } catch (error) {\n            console.error('Error saving auth to localStorage:', error);\n        }\n        return {\n            data: {\n                user: mockUser\n            },\n            error: null\n        };\n    };\n    // Function to detect user's profile type by checking all profile contexts\n    const detectUserProfileType = (userEmail)=>{\n        console.log('🔍 Detecting profile type for email:', userEmail);\n        console.log('🔍 All localStorage keys:', Object.keys(localStorage));\n        try {\n            // Check Workers profiles\n            const workersData = localStorage.getItem('kaazmaamaa-workers');\n            console.log('📋 Workers data:', workersData ? 'Found' : 'Not found');\n            if (workersData) {\n                const workers = JSON.parse(workersData);\n                console.log('👷 Checking', workers.length, 'worker profiles');\n                const workerProfile = workers.find((worker)=>{\n                    var _worker_personalInfo, _worker_personalInfo1;\n                    const emailMatch = ((_worker_personalInfo = worker.personalInfo) === null || _worker_personalInfo === void 0 ? void 0 : _worker_personalInfo.email) === userEmail;\n                    const userIdMatch = worker.userId === userEmail;\n                    console.log('👷 Worker check:', (_worker_personalInfo1 = worker.personalInfo) === null || _worker_personalInfo1 === void 0 ? void 0 : _worker_personalInfo1.workerName, 'Email match:', emailMatch, 'UserId match:', userIdMatch);\n                    return emailMatch || userIdMatch;\n                });\n                if (workerProfile) {\n                    var _workerProfile_personalInfo;\n                    console.log('✅ Found worker profile for:', userEmail, (_workerProfile_personalInfo = workerProfile.personalInfo) === null || _workerProfile_personalInfo === void 0 ? void 0 : _workerProfile_personalInfo.workerName);\n                    return 'worker';\n                }\n            }\n            // Check Suppliers profiles\n            const suppliersData = localStorage.getItem('kaazmaamaa-suppliers');\n            console.log('📋 Suppliers data:', suppliersData ? 'Found' : 'Not found');\n            if (suppliersData) {\n                const suppliers = JSON.parse(suppliersData);\n                console.log('🏭 Checking', suppliers.length, 'supplier profiles');\n                const supplierProfile = suppliers.find((supplier)=>{\n                    var _supplier_personalInfo, _supplier_personalInfo1;\n                    const emailMatch = ((_supplier_personalInfo = supplier.personalInfo) === null || _supplier_personalInfo === void 0 ? void 0 : _supplier_personalInfo.email) === userEmail;\n                    const userIdMatch = supplier.userId === userEmail;\n                    console.log('🏭 Supplier check:', (_supplier_personalInfo1 = supplier.personalInfo) === null || _supplier_personalInfo1 === void 0 ? void 0 : _supplier_personalInfo1.supplierName, 'Email match:', emailMatch, 'UserId match:', userIdMatch);\n                    console.log('🏭 Supplier data structure:', {\n                        personalInfo: supplier.personalInfo,\n                        userId: supplier.userId\n                    });\n                    return emailMatch || userIdMatch;\n                });\n                if (supplierProfile) {\n                    var _supplierProfile_personalInfo;\n                    console.log('✅ Found supplier profile for:', userEmail, (_supplierProfile_personalInfo = supplierProfile.personalInfo) === null || _supplierProfile_personalInfo === void 0 ? void 0 : _supplierProfile_personalInfo.supplierName);\n                    return 'supplier';\n                }\n            }\n            // Check Companies profiles\n            const companiesData = localStorage.getItem('kaazmaamaa-companies');\n            console.log('📋 Companies data:', companiesData ? 'Found' : 'Not found');\n            if (companiesData) {\n                const companies = JSON.parse(companiesData);\n                console.log('🏢 Checking', companies.length, 'company profiles');\n                const companyProfile = companies.find((company)=>{\n                    var _company_contactInfo, _company_companyInfo;\n                    const emailMatch = ((_company_contactInfo = company.contactInfo) === null || _company_contactInfo === void 0 ? void 0 : _company_contactInfo.hrEmail) === userEmail;\n                    const userIdMatch = company.userId === userEmail;\n                    console.log('🏢 Company check:', (_company_companyInfo = company.companyInfo) === null || _company_companyInfo === void 0 ? void 0 : _company_companyInfo.companyName, 'Email match:', emailMatch, 'UserId match:', userIdMatch);\n                    return emailMatch || userIdMatch;\n                });\n                if (companyProfile) {\n                    var _companyProfile_companyInfo;\n                    console.log('✅ Found company profile for:', userEmail, (_companyProfile_companyInfo = companyProfile.companyInfo) === null || _companyProfile_companyInfo === void 0 ? void 0 : _companyProfile_companyInfo.companyName);\n                    return 'company';\n                }\n            }\n            console.log('❌ No profile found for:', userEmail, 'defaulting to worker');\n            return 'worker' // Default fallback\n            ;\n        } catch (error) {\n            console.error('💥 Error detecting profile type:', error);\n            return 'worker' // Default fallback\n            ;\n        }\n    };\n    const signIn = async (email, password)=>{\n        // Mock implementation for now\n        console.log('Sign in:', {\n            email,\n            password\n        });\n        // Use email as the consistent user ID\n        const mockUser = {\n            id: email,\n            email\n        };\n        // Detect the user's actual profile type\n        const detectedRole = detectUserProfileType(email);\n        console.log('Detected user role:', detectedRole, 'for email:', email);\n        // Save to state\n        setUser(mockUser);\n        setUserRole(detectedRole);\n        // Save to localStorage\n        try {\n            localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser));\n            localStorage.setItem('kaazmaamaa-user-role', detectedRole);\n            console.log('User authentication saved to localStorage with role:', detectedRole);\n        } catch (error) {\n            console.error('Error saving auth to localStorage:', error);\n        }\n        return {\n            data: {\n                user: mockUser\n            },\n            error: null\n        };\n    };\n    const signOut = async ()=>{\n        // Clear state\n        setUser(null);\n        setUserRole(null);\n        // Clear localStorage\n        try {\n            localStorage.removeItem('kaazmaamaa-user');\n            localStorage.removeItem('kaazmaamaa-user-role');\n            console.log('User authentication cleared from localStorage');\n        } catch (error) {\n            console.error('Error clearing auth from localStorage:', error);\n        }\n    };\n    const getUserProfile = ()=>{\n        if (!user || !userRole) return null;\n        try {\n            if (userRole === 'worker') {\n                const workersData = localStorage.getItem('kaazmaamaa-workers');\n                if (workersData) {\n                    const workers = JSON.parse(workersData);\n                    return workers.find((worker)=>{\n                        var _worker_personalInfo;\n                        return ((_worker_personalInfo = worker.personalInfo) === null || _worker_personalInfo === void 0 ? void 0 : _worker_personalInfo.email) === user.email || worker.userId === user.email;\n                    });\n                }\n            } else if (userRole === 'supplier') {\n                const suppliersData = localStorage.getItem('kaazmaamaa-suppliers');\n                if (suppliersData) {\n                    const suppliers = JSON.parse(suppliersData);\n                    return suppliers.find((supplier)=>{\n                        var _supplier_personalInfo;\n                        return ((_supplier_personalInfo = supplier.personalInfo) === null || _supplier_personalInfo === void 0 ? void 0 : _supplier_personalInfo.email) === user.email || supplier.userId === user.email;\n                    });\n                }\n            } else if (userRole === 'company') {\n                const companiesData = localStorage.getItem('kaazmaamaa-companies');\n                if (companiesData) {\n                    const companies = JSON.parse(companiesData);\n                    return companies.find((company)=>{\n                        var _company_contactInfo;\n                        return ((_company_contactInfo = company.contactInfo) === null || _company_contactInfo === void 0 ? void 0 : _company_contactInfo.hrEmail) === user.email || company.userId === user.email;\n                    });\n                }\n            }\n        } catch (error) {\n            console.error('Error getting user profile:', error);\n        }\n        return null;\n    };\n    const debugProfiles = ()=>{\n        console.log('🐛 DEBUG: All localStorage data:');\n        console.log('Workers:', localStorage.getItem('kaazmaamaa-workers'));\n        console.log('Suppliers:', localStorage.getItem('kaazmaamaa-suppliers'));\n        console.log('Companies:', localStorage.getItem('kaazmaamaa-companies'));\n        try {\n            const workers = JSON.parse(localStorage.getItem('kaazmaamaa-workers') || '[]');\n            const suppliers = JSON.parse(localStorage.getItem('kaazmaamaa-suppliers') || '[]');\n            const companies = JSON.parse(localStorage.getItem('kaazmaamaa-companies') || '[]');\n            console.log('🐛 Parsed Workers:', workers);\n            console.log('🐛 Parsed Suppliers:', suppliers);\n            console.log('🐛 Parsed Companies:', companies);\n        } catch (error) {\n            console.error('🐛 Error parsing localStorage data:', error);\n        }\n    };\n    const refreshUserRole = ()=>{\n        if (user === null || user === void 0 ? void 0 : user.email) {\n            console.log('🔄 Manually refreshing user role for:', user.email);\n            const newRole = detectUserProfileType(user.email);\n            console.log('🔄 New role detected:', newRole);\n            if (newRole !== userRole) {\n                setUserRole(newRole);\n                localStorage.setItem('kaazmaamaa-user-role', newRole);\n                console.log('✅ Role updated from', userRole, 'to', newRole);\n            } else {\n                console.log('✅ Role unchanged:', userRole);\n            }\n        }\n    };\n    const value = {\n        user,\n        userRole,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        getUserProfile,\n        debugProfiles,\n        refreshUserRole\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 290,\n        columnNumber: 10\n    }, this);\n}\n_s(AuthProvider, \"nutbXUp/DxipcvR98n7Ll6y3wI0=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9BdXRoQ29udGV4dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUVzRTtBQXFCdEUsTUFBTUksNEJBQWNKLG9EQUFhQSxDQUE4Qks7QUFFeEQsU0FBU0MsYUFBYSxLQUEyQztRQUEzQyxFQUFFQyxRQUFRLEVBQWlDLEdBQTNDOztJQUMzQixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1AsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDUSxVQUFVQyxZQUFZLEdBQUdULCtDQUFRQSxDQUFrQjtJQUMxRCxNQUFNLENBQUNVLFNBQVNDLFdBQVcsR0FBR1gsK0NBQVFBLENBQUMsTUFBTSwwQkFBMEI7O0lBQ3ZFLE1BQU0sQ0FBQ1ksZUFBZUMsaUJBQWlCLEdBQUdiLCtDQUFRQSxDQUFDO0lBRW5ELHVEQUF1RDtJQUN2REMsZ0RBQVNBO2tDQUFDO1lBQ1IsTUFBTWE7d0RBQWdCO29CQUNwQixJQUFJO3dCQUNGLE1BQU1DLFlBQVlDLGFBQWFDLE9BQU8sQ0FBQzt3QkFDdkMsTUFBTUMsWUFBWUYsYUFBYUMsT0FBTyxDQUFDO3dCQUV2QyxJQUFJRixhQUFhRyxXQUFXOzRCQUMxQixNQUFNQyxhQUFhQyxLQUFLQyxLQUFLLENBQUNOOzRCQUU5QiwrRkFBK0Y7NEJBQy9GLE1BQU1PLGlCQUFpQkMsc0JBQXNCSixXQUFXSyxLQUFLOzRCQUM3REMsUUFBUUMsR0FBRyxDQUFDLDZCQUE2QlAsV0FBV0ssS0FBSyxFQUFFLFVBQVVOLFdBQVcsZ0JBQWdCSTs0QkFFaEcseURBQXlEOzRCQUN6RCxNQUFNSyxZQUFZTCxtQkFBbUIsV0FBV0EsaUJBQWtCSjs0QkFFbEVYLFFBQVFZOzRCQUNSVixZQUFZa0I7NEJBRVosc0NBQXNDOzRCQUN0QyxJQUFJQSxjQUFjVCxXQUFXO2dDQUMzQk8sUUFBUUMsR0FBRyxDQUFDLHlDQUF5Q1IsV0FBVyxNQUFNUztnQ0FDdEVYLGFBQWFZLE9BQU8sQ0FBQyx3QkFBd0JEOzRCQUMvQzs0QkFFQUYsUUFBUUMsR0FBRyxDQUFDLG9DQUFvQ1AsV0FBV0ssS0FBSyxFQUFFLGVBQWVHO3dCQUNuRixPQUFPOzRCQUNMRixRQUFRQyxHQUFHLENBQUM7d0JBQ2Q7b0JBQ0YsRUFBRSxPQUFPRyxPQUFPO3dCQUNkSixRQUFRSSxLQUFLLENBQUMsK0NBQStDQTtvQkFDL0QsU0FBVTt3QkFDUkosUUFBUUMsR0FBRyxDQUFDO3dCQUNaZixXQUFXO3dCQUNYRSxpQkFBaUI7d0JBQ2pCWSxRQUFRQyxHQUFHLENBQUM7b0JBQ2Q7Z0JBQ0Y7O1lBRUFaO1FBQ0Y7aUNBQUcsRUFBRTtJQUVMLE1BQU1nQixTQUFTLE9BQU9OLE9BQWVPLFVBQWtCQztRQUNyRCw4QkFBOEI7UUFDOUJQLFFBQVFDLEdBQUcsQ0FBQyxZQUFZO1lBQUVGO1lBQU9PO1lBQVVDO1FBQUs7UUFDaEQsTUFBTUMsV0FBVztZQUFFQyxJQUFJQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVE7WUFBSWI7UUFBTTtRQUVwRCxnQkFBZ0I7UUFDaEJqQixRQUFRMEI7UUFDUnhCLFlBQVl1QjtRQUVaLHVCQUF1QjtRQUN2QixJQUFJO1lBQ0ZoQixhQUFhWSxPQUFPLENBQUMsbUJBQW1CUixLQUFLa0IsU0FBUyxDQUFDTDtZQUN2RGpCLGFBQWFZLE9BQU8sQ0FBQyx3QkFBd0JJO1lBQzdDUCxRQUFRQyxHQUFHLENBQUM7UUFDZCxFQUFFLE9BQU9HLE9BQU87WUFDZEosUUFBUUksS0FBSyxDQUFDLHNDQUFzQ0E7UUFDdEQ7UUFFQSxPQUFPO1lBQUVVLE1BQU07Z0JBQUVqQyxNQUFNMkI7WUFBUztZQUFHSixPQUFPO1FBQUs7SUFDakQ7SUFFQSwwRUFBMEU7SUFDMUUsTUFBTU4sd0JBQXdCLENBQUNpQjtRQUM3QmYsUUFBUUMsR0FBRyxDQUFDLHdDQUF3Q2M7UUFDcERmLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJlLE9BQU9DLElBQUksQ0FBQzFCO1FBRXJELElBQUk7WUFDRix5QkFBeUI7WUFDekIsTUFBTTJCLGNBQWMzQixhQUFhQyxPQUFPLENBQUM7WUFDekNRLFFBQVFDLEdBQUcsQ0FBQyxvQkFBb0JpQixjQUFjLFVBQVU7WUFDeEQsSUFBSUEsYUFBYTtnQkFDZixNQUFNQyxVQUFVeEIsS0FBS0MsS0FBSyxDQUFDc0I7Z0JBQzNCbEIsUUFBUUMsR0FBRyxDQUFDLGVBQWVrQixRQUFRQyxNQUFNLEVBQUU7Z0JBQzNDLE1BQU1DLGdCQUFnQkYsUUFBUUcsSUFBSSxDQUFDLENBQUNDO3dCQUNmQSxzQkFFYUE7b0JBRmhDLE1BQU1DLGFBQWFELEVBQUFBLHVCQUFBQSxPQUFPRSxZQUFZLGNBQW5CRiwyQ0FBQUEscUJBQXFCeEIsS0FBSyxNQUFLZ0I7b0JBQ2xELE1BQU1XLGNBQWNILE9BQU9JLE1BQU0sS0FBS1o7b0JBQ3RDZixRQUFRQyxHQUFHLENBQUMscUJBQW9Cc0Isd0JBQUFBLE9BQU9FLFlBQVksY0FBbkJGLDRDQUFBQSxzQkFBcUJLLFVBQVUsRUFBRSxnQkFBZ0JKLFlBQVksaUJBQWlCRTtvQkFDOUcsT0FBT0YsY0FBY0U7Z0JBQ3ZCO2dCQUNBLElBQUlMLGVBQWU7d0JBQ3FDQTtvQkFBdERyQixRQUFRQyxHQUFHLENBQUMsK0JBQStCYyxZQUFXTSw4QkFBQUEsY0FBY0ksWUFBWSxjQUExQkosa0RBQUFBLDRCQUE0Qk8sVUFBVTtvQkFDNUYsT0FBTztnQkFDVDtZQUNGO1lBRUEsMkJBQTJCO1lBQzNCLE1BQU1DLGdCQUFnQnRDLGFBQWFDLE9BQU8sQ0FBQztZQUMzQ1EsUUFBUUMsR0FBRyxDQUFDLHNCQUFzQjRCLGdCQUFnQixVQUFVO1lBQzVELElBQUlBLGVBQWU7Z0JBQ2pCLE1BQU1DLFlBQVluQyxLQUFLQyxLQUFLLENBQUNpQztnQkFDN0I3QixRQUFRQyxHQUFHLENBQUMsZUFBZTZCLFVBQVVWLE1BQU0sRUFBRTtnQkFDN0MsTUFBTVcsa0JBQWtCRCxVQUFVUixJQUFJLENBQUMsQ0FBQ1U7d0JBQ25CQSx3QkFFZUE7b0JBRmxDLE1BQU1SLGFBQWFRLEVBQUFBLHlCQUFBQSxTQUFTUCxZQUFZLGNBQXJCTyw2Q0FBQUEsdUJBQXVCakMsS0FBSyxNQUFLZ0I7b0JBQ3BELE1BQU1XLGNBQWNNLFNBQVNMLE1BQU0sS0FBS1o7b0JBQ3hDZixRQUFRQyxHQUFHLENBQUMsdUJBQXNCK0IsMEJBQUFBLFNBQVNQLFlBQVksY0FBckJPLDhDQUFBQSx3QkFBdUJDLFlBQVksRUFBRSxnQkFBZ0JULFlBQVksaUJBQWlCRTtvQkFDcEgxQixRQUFRQyxHQUFHLENBQUMsK0JBQStCO3dCQUFFd0IsY0FBY08sU0FBU1AsWUFBWTt3QkFBRUUsUUFBUUssU0FBU0wsTUFBTTtvQkFBQztvQkFDMUcsT0FBT0gsY0FBY0U7Z0JBQ3ZCO2dCQUNBLElBQUlLLGlCQUFpQjt3QkFDcUNBO29CQUF4RC9CLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNjLFlBQVdnQixnQ0FBQUEsZ0JBQWdCTixZQUFZLGNBQTVCTSxvREFBQUEsOEJBQThCRSxZQUFZO29CQUNsRyxPQUFPO2dCQUNUO1lBQ0Y7WUFFQSwyQkFBMkI7WUFDM0IsTUFBTUMsZ0JBQWdCM0MsYUFBYUMsT0FBTyxDQUFDO1lBQzNDUSxRQUFRQyxHQUFHLENBQUMsc0JBQXNCaUMsZ0JBQWdCLFVBQVU7WUFDNUQsSUFBSUEsZUFBZTtnQkFDakIsTUFBTUMsWUFBWXhDLEtBQUtDLEtBQUssQ0FBQ3NDO2dCQUM3QmxDLFFBQVFDLEdBQUcsQ0FBQyxlQUFla0MsVUFBVWYsTUFBTSxFQUFFO2dCQUM3QyxNQUFNZ0IsaUJBQWlCRCxVQUFVYixJQUFJLENBQUMsQ0FBQ2U7d0JBQ2xCQSxzQkFFY0E7b0JBRmpDLE1BQU1iLGFBQWFhLEVBQUFBLHVCQUFBQSxRQUFRQyxXQUFXLGNBQW5CRCwyQ0FBQUEscUJBQXFCRSxPQUFPLE1BQUt4QjtvQkFDcEQsTUFBTVcsY0FBY1csUUFBUVYsTUFBTSxLQUFLWjtvQkFDdkNmLFFBQVFDLEdBQUcsQ0FBQyxzQkFBcUJvQyx1QkFBQUEsUUFBUUcsV0FBVyxjQUFuQkgsMkNBQUFBLHFCQUFxQkksV0FBVyxFQUFFLGdCQUFnQmpCLFlBQVksaUJBQWlCRTtvQkFDaEgsT0FBT0YsY0FBY0U7Z0JBQ3ZCO2dCQUNBLElBQUlVLGdCQUFnQjt3QkFDcUNBO29CQUF2RHBDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBZ0NjLFlBQVdxQiw4QkFBQUEsZUFBZUksV0FBVyxjQUExQkosa0RBQUFBLDRCQUE0QkssV0FBVztvQkFDOUYsT0FBTztnQkFDVDtZQUNGO1lBRUF6QyxRQUFRQyxHQUFHLENBQUMsMkJBQTJCYyxXQUFXO1lBQ2xELE9BQU8sU0FBUyxtQkFBbUI7O1FBQ3JDLEVBQUUsT0FBT1gsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsb0NBQW9DQTtZQUNsRCxPQUFPLFNBQVMsbUJBQW1COztRQUNyQztJQUNGO0lBRUEsTUFBTXNDLFNBQVMsT0FBTzNDLE9BQWVPO1FBQ25DLDhCQUE4QjtRQUM5Qk4sUUFBUUMsR0FBRyxDQUFDLFlBQVk7WUFBRUY7WUFBT087UUFBUztRQUMxQyxzQ0FBc0M7UUFDdEMsTUFBTUUsV0FBVztZQUFFQyxJQUFJVjtZQUFPQTtRQUFNO1FBRXBDLHdDQUF3QztRQUN4QyxNQUFNNEMsZUFBZTdDLHNCQUFzQkM7UUFDM0NDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBdUIwQyxjQUFjLGNBQWM1QztRQUUvRCxnQkFBZ0I7UUFDaEJqQixRQUFRMEI7UUFDUnhCLFlBQVkyRDtRQUVaLHVCQUF1QjtRQUN2QixJQUFJO1lBQ0ZwRCxhQUFhWSxPQUFPLENBQUMsbUJBQW1CUixLQUFLa0IsU0FBUyxDQUFDTDtZQUN2RGpCLGFBQWFZLE9BQU8sQ0FBQyx3QkFBd0J3QztZQUM3QzNDLFFBQVFDLEdBQUcsQ0FBQyx3REFBd0QwQztRQUN0RSxFQUFFLE9BQU92QyxPQUFPO1lBQ2RKLFFBQVFJLEtBQUssQ0FBQyxzQ0FBc0NBO1FBQ3REO1FBRUEsT0FBTztZQUFFVSxNQUFNO2dCQUFFakMsTUFBTTJCO1lBQVM7WUFBR0osT0FBTztRQUFLO0lBQ2pEO0lBRUEsTUFBTXdDLFVBQVU7UUFDZCxjQUFjO1FBQ2Q5RCxRQUFRO1FBQ1JFLFlBQVk7UUFFWixxQkFBcUI7UUFDckIsSUFBSTtZQUNGTyxhQUFhc0QsVUFBVSxDQUFDO1lBQ3hCdEQsYUFBYXNELFVBQVUsQ0FBQztZQUN4QjdDLFFBQVFDLEdBQUcsQ0FBQztRQUNkLEVBQUUsT0FBT0csT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsMENBQTBDQTtRQUMxRDtJQUNGO0lBRUEsTUFBTTBDLGlCQUFpQjtRQUNyQixJQUFJLENBQUNqRSxRQUFRLENBQUNFLFVBQVUsT0FBTztRQUUvQixJQUFJO1lBQ0YsSUFBSUEsYUFBYSxVQUFVO2dCQUN6QixNQUFNbUMsY0FBYzNCLGFBQWFDLE9BQU8sQ0FBQztnQkFDekMsSUFBSTBCLGFBQWE7b0JBQ2YsTUFBTUMsVUFBVXhCLEtBQUtDLEtBQUssQ0FBQ3NCO29CQUMzQixPQUFPQyxRQUFRRyxJQUFJLENBQUMsQ0FBQ0M7NEJBQ25CQTsrQkFBQUEsRUFBQUEsdUJBQUFBLE9BQU9FLFlBQVksY0FBbkJGLDJDQUFBQSxxQkFBcUJ4QixLQUFLLE1BQUtsQixLQUFLa0IsS0FBSyxJQUFJd0IsT0FBT0ksTUFBTSxLQUFLOUMsS0FBS2tCLEtBQUs7O2dCQUU3RTtZQUNGLE9BQU8sSUFBSWhCLGFBQWEsWUFBWTtnQkFDbEMsTUFBTThDLGdCQUFnQnRDLGFBQWFDLE9BQU8sQ0FBQztnQkFDM0MsSUFBSXFDLGVBQWU7b0JBQ2pCLE1BQU1DLFlBQVluQyxLQUFLQyxLQUFLLENBQUNpQztvQkFDN0IsT0FBT0MsVUFBVVIsSUFBSSxDQUFDLENBQUNVOzRCQUNyQkE7K0JBQUFBLEVBQUFBLHlCQUFBQSxTQUFTUCxZQUFZLGNBQXJCTyw2Q0FBQUEsdUJBQXVCakMsS0FBSyxNQUFLbEIsS0FBS2tCLEtBQUssSUFDM0NpQyxTQUFTTCxNQUFNLEtBQUs5QyxLQUFLa0IsS0FBSzs7Z0JBRWxDO1lBQ0YsT0FBTyxJQUFJaEIsYUFBYSxXQUFXO2dCQUNqQyxNQUFNbUQsZ0JBQWdCM0MsYUFBYUMsT0FBTyxDQUFDO2dCQUMzQyxJQUFJMEMsZUFBZTtvQkFDakIsTUFBTUMsWUFBWXhDLEtBQUtDLEtBQUssQ0FBQ3NDO29CQUM3QixPQUFPQyxVQUFVYixJQUFJLENBQUMsQ0FBQ2U7NEJBQ3JCQTsrQkFBQUEsRUFBQUEsdUJBQUFBLFFBQVFDLFdBQVcsY0FBbkJELDJDQUFBQSxxQkFBcUJFLE9BQU8sTUFBSzFELEtBQUtrQixLQUFLLElBQUlzQyxRQUFRVixNQUFNLEtBQUs5QyxLQUFLa0IsS0FBSzs7Z0JBRWhGO1lBQ0Y7UUFDRixFQUFFLE9BQU9LLE9BQU87WUFDZEosUUFBUUksS0FBSyxDQUFDLCtCQUErQkE7UUFDL0M7UUFFQSxPQUFPO0lBQ1Q7SUFFQSxNQUFNMkMsZ0JBQWdCO1FBQ3BCL0MsUUFBUUMsR0FBRyxDQUFDO1FBQ1pELFFBQVFDLEdBQUcsQ0FBQyxZQUFZVixhQUFhQyxPQUFPLENBQUM7UUFDN0NRLFFBQVFDLEdBQUcsQ0FBQyxjQUFjVixhQUFhQyxPQUFPLENBQUM7UUFDL0NRLFFBQVFDLEdBQUcsQ0FBQyxjQUFjVixhQUFhQyxPQUFPLENBQUM7UUFFL0MsSUFBSTtZQUNGLE1BQU0yQixVQUFVeEIsS0FBS0MsS0FBSyxDQUFDTCxhQUFhQyxPQUFPLENBQUMseUJBQXlCO1lBQ3pFLE1BQU1zQyxZQUFZbkMsS0FBS0MsS0FBSyxDQUFDTCxhQUFhQyxPQUFPLENBQUMsMkJBQTJCO1lBQzdFLE1BQU0yQyxZQUFZeEMsS0FBS0MsS0FBSyxDQUFDTCxhQUFhQyxPQUFPLENBQUMsMkJBQTJCO1lBRTdFUSxRQUFRQyxHQUFHLENBQUMsc0JBQXNCa0I7WUFDbENuQixRQUFRQyxHQUFHLENBQUMsd0JBQXdCNkI7WUFDcEM5QixRQUFRQyxHQUFHLENBQUMsd0JBQXdCa0M7UUFDdEMsRUFBRSxPQUFPL0IsT0FBTztZQUNkSixRQUFRSSxLQUFLLENBQUMsdUNBQXVDQTtRQUN2RDtJQUNGO0lBRUEsTUFBTTRDLGtCQUFrQjtRQUN0QixJQUFJbkUsaUJBQUFBLDJCQUFBQSxLQUFNa0IsS0FBSyxFQUFFO1lBQ2ZDLFFBQVFDLEdBQUcsQ0FBQyx5Q0FBeUNwQixLQUFLa0IsS0FBSztZQUMvRCxNQUFNa0QsVUFBVW5ELHNCQUFzQmpCLEtBQUtrQixLQUFLO1lBQ2hEQyxRQUFRQyxHQUFHLENBQUMseUJBQXlCZ0Q7WUFFckMsSUFBSUEsWUFBWWxFLFVBQVU7Z0JBQ3hCQyxZQUFZaUU7Z0JBQ1oxRCxhQUFhWSxPQUFPLENBQUMsd0JBQXdCOEM7Z0JBQzdDakQsUUFBUUMsR0FBRyxDQUFDLHVCQUF1QmxCLFVBQVUsTUFBTWtFO1lBQ3JELE9BQU87Z0JBQ0xqRCxRQUFRQyxHQUFHLENBQUMscUJBQXFCbEI7WUFDbkM7UUFDRjtJQUNGO0lBRUEsTUFBTW1FLFFBQVE7UUFDWnJFO1FBQ0FFO1FBQ0FFO1FBQ0FvQjtRQUNBcUM7UUFDQUU7UUFDQUU7UUFDQUM7UUFDQUM7SUFDRjtJQUVBLHFCQUFPLDhEQUFDdkUsWUFBWTBFLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQVF0RTs7Ozs7O0FBQzlDO0dBelFnQkQ7S0FBQUE7QUEyUVQsU0FBU3lFOztJQUNkLE1BQU1DLFVBQVUvRSxpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSTRFLFlBQVkzRSxXQUFXO1FBQ3pCLE1BQU0sSUFBSTRFLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUO0lBTmdCRCIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxocFxcRGVza3RvcFxcS0FBWk1BQU1BQSBBVUdNRU5UXFxrYWF6bWFhbWFhLWFwcFxcc3JjXFxjb250ZXh0c1xcQXV0aENvbnRleHQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5cbmV4cG9ydCB0eXBlIFVzZXJSb2xlID0gJ3dvcmtlcicgfCAnc3VwcGxpZXInIHwgJ2NvbXBhbnknXG5cbmludGVyZmFjZSBVc2VyIHtcbiAgaWQ6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG59XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbFxuICB1c2VyUm9sZTogVXNlclJvbGUgfCBudWxsXG4gIGxvYWRpbmc6IGJvb2xlYW5cbiAgc2lnblVwOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgcm9sZTogVXNlclJvbGUpID0+IFByb21pc2U8YW55PlxuICBzaWduSW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiBQcm9taXNlPGFueT5cbiAgc2lnbk91dDogKCkgPT4gUHJvbWlzZTx2b2lkPlxuICBnZXRVc2VyUHJvZmlsZTogKCkgPT4gYW55IHwgbnVsbFxuICBkZWJ1Z1Byb2ZpbGVzOiAoKSA9PiB2b2lkXG4gIHJlZnJlc2hVc2VyUm9sZTogKCkgPT4gdm9pZFxufVxuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpXG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW3VzZXJSb2xlLCBzZXRVc2VyUm9sZV0gPSB1c2VTdGF0ZTxVc2VyUm9sZSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpIC8vIFN0YXJ0IHdpdGggbG9hZGluZyB0cnVlXG4gIGNvbnN0IFtpc0luaXRpYWxpemVkLCBzZXRJc0luaXRpYWxpemVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIC8vIExvYWQgYXV0aGVudGljYXRpb24gc3RhdGUgZnJvbSBsb2NhbFN0b3JhZ2Ugb24gbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBsb2FkQXV0aFN0YXRlID0gKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3Qgc2F2ZWRVc2VyID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtdXNlcicpXG4gICAgICAgIGNvbnN0IHNhdmVkUm9sZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdrYWF6bWFhbWFhLXVzZXItcm9sZScpXG5cbiAgICAgICAgaWYgKHNhdmVkVXNlciAmJiBzYXZlZFJvbGUpIHtcbiAgICAgICAgICBjb25zdCBwYXJzZWRVc2VyID0gSlNPTi5wYXJzZShzYXZlZFVzZXIpXG5cbiAgICAgICAgICAvLyBSZS1kZXRlY3QgdGhlIHVzZXIncyByb2xlIHRvIGVuc3VyZSBpdCdzIGNvcnJlY3QgKGluIGNhc2UgcHJvZmlsZXMgd2VyZSBjcmVhdGVkIGFmdGVyIGxvZ2luKVxuICAgICAgICAgIGNvbnN0IHJlRGV0ZWN0ZWRSb2xlID0gZGV0ZWN0VXNlclByb2ZpbGVUeXBlKHBhcnNlZFVzZXIuZW1haWwpXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgUmUtZGV0ZWN0aW5nIHJvbGUgZm9yOicsIHBhcnNlZFVzZXIuZW1haWwsICdTYXZlZDonLCBzYXZlZFJvbGUsICdSZS1kZXRlY3RlZDonLCByZURldGVjdGVkUm9sZSlcblxuICAgICAgICAgIC8vIFVzZSByZS1kZXRlY3RlZCByb2xlIGlmIGl0J3MgZGlmZmVyZW50IGZyb20gc2F2ZWQgcm9sZVxuICAgICAgICAgIGNvbnN0IGZpbmFsUm9sZSA9IHJlRGV0ZWN0ZWRSb2xlICE9PSAnd29ya2VyJyA/IHJlRGV0ZWN0ZWRSb2xlIDogKHNhdmVkUm9sZSBhcyBVc2VyUm9sZSlcblxuICAgICAgICAgIHNldFVzZXIocGFyc2VkVXNlcilcbiAgICAgICAgICBzZXRVc2VyUm9sZShmaW5hbFJvbGUpXG5cbiAgICAgICAgICAvLyBVcGRhdGUgbG9jYWxTdG9yYWdlIGlmIHJvbGUgY2hhbmdlZFxuICAgICAgICAgIGlmIChmaW5hbFJvbGUgIT09IHNhdmVkUm9sZSkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgVXBkYXRpbmcgcm9sZSBpbiBsb2NhbFN0b3JhZ2UgZnJvbScsIHNhdmVkUm9sZSwgJ3RvJywgZmluYWxSb2xlKVxuICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2thYXptYWFtYWEtdXNlci1yb2xlJywgZmluYWxSb2xlKVxuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgTG9hZGVkIHVzZXIgZnJvbSBsb2NhbFN0b3JhZ2U6JywgcGFyc2VkVXNlci5lbWFpbCwgJ0ZpbmFsIHJvbGU6JywgZmluYWxSb2xlKVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCdObyBzYXZlZCBhdXRoZW50aWNhdGlvbiBmb3VuZCcpXG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgYXV0aCBzdGF0ZSBmcm9tIGxvY2FsU3RvcmFnZTonLCBlcnJvcilcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIEF1dGhDb250ZXh0OiBTZXR0aW5nIGxvYWRpbmcgdG8gZmFsc2UnKVxuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgICBzZXRJc0luaXRpYWxpemVkKHRydWUpXG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUgQXV0aENvbnRleHQ6IEluaXRpYWxpemF0aW9uIGNvbXBsZXRlJylcbiAgICAgIH1cbiAgICB9XG5cbiAgICBsb2FkQXV0aFN0YXRlKClcbiAgfSwgW10pXG5cbiAgY29uc3Qgc2lnblVwID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcsIHJvbGU6IFVzZXJSb2xlKSA9PiB7XG4gICAgLy8gTW9jayBpbXBsZW1lbnRhdGlvbiBmb3Igbm93XG4gICAgY29uc29sZS5sb2coJ1NpZ24gdXA6JywgeyBlbWFpbCwgcGFzc3dvcmQsIHJvbGUgfSlcbiAgICBjb25zdCBtb2NrVXNlciA9IHsgaWQ6IERhdGUubm93KCkudG9TdHJpbmcoKSwgZW1haWwgfVxuXG4gICAgLy8gU2F2ZSB0byBzdGF0ZVxuICAgIHNldFVzZXIobW9ja1VzZXIpXG4gICAgc2V0VXNlclJvbGUocm9sZSlcblxuICAgIC8vIFNhdmUgdG8gbG9jYWxTdG9yYWdlXG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdrYWF6bWFhbWFhLXVzZXInLCBKU09OLnN0cmluZ2lmeShtb2NrVXNlcikpXG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgna2Fhem1hYW1hYS11c2VyLXJvbGUnLCByb2xlKVxuICAgICAgY29uc29sZS5sb2coJ1VzZXIgYXV0aGVudGljYXRpb24gc2F2ZWQgdG8gbG9jYWxTdG9yYWdlJylcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc2F2aW5nIGF1dGggdG8gbG9jYWxTdG9yYWdlOicsIGVycm9yKVxuICAgIH1cblxuICAgIHJldHVybiB7IGRhdGE6IHsgdXNlcjogbW9ja1VzZXIgfSwgZXJyb3I6IG51bGwgfVxuICB9XG5cbiAgLy8gRnVuY3Rpb24gdG8gZGV0ZWN0IHVzZXIncyBwcm9maWxlIHR5cGUgYnkgY2hlY2tpbmcgYWxsIHByb2ZpbGUgY29udGV4dHNcbiAgY29uc3QgZGV0ZWN0VXNlclByb2ZpbGVUeXBlID0gKHVzZXJFbWFpbDogc3RyaW5nKTogVXNlclJvbGUgPT4ge1xuICAgIGNvbnNvbGUubG9nKCfwn5SNIERldGVjdGluZyBwcm9maWxlIHR5cGUgZm9yIGVtYWlsOicsIHVzZXJFbWFpbClcbiAgICBjb25zb2xlLmxvZygn8J+UjSBBbGwgbG9jYWxTdG9yYWdlIGtleXM6JywgT2JqZWN0LmtleXMobG9jYWxTdG9yYWdlKSlcblxuICAgIHRyeSB7XG4gICAgICAvLyBDaGVjayBXb3JrZXJzIHByb2ZpbGVzXG4gICAgICBjb25zdCB3b3JrZXJzRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdrYWF6bWFhbWFhLXdvcmtlcnMnKVxuICAgICAgY29uc29sZS5sb2coJ/Cfk4sgV29ya2VycyBkYXRhOicsIHdvcmtlcnNEYXRhID8gJ0ZvdW5kJyA6ICdOb3QgZm91bmQnKVxuICAgICAgaWYgKHdvcmtlcnNEYXRhKSB7XG4gICAgICAgIGNvbnN0IHdvcmtlcnMgPSBKU09OLnBhcnNlKHdvcmtlcnNEYXRhKVxuICAgICAgICBjb25zb2xlLmxvZygn8J+RtyBDaGVja2luZycsIHdvcmtlcnMubGVuZ3RoLCAnd29ya2VyIHByb2ZpbGVzJylcbiAgICAgICAgY29uc3Qgd29ya2VyUHJvZmlsZSA9IHdvcmtlcnMuZmluZCgod29ya2VyOiBhbnkpID0+IHtcbiAgICAgICAgICBjb25zdCBlbWFpbE1hdGNoID0gd29ya2VyLnBlcnNvbmFsSW5mbz8uZW1haWwgPT09IHVzZXJFbWFpbFxuICAgICAgICAgIGNvbnN0IHVzZXJJZE1hdGNoID0gd29ya2VyLnVzZXJJZCA9PT0gdXNlckVtYWlsXG4gICAgICAgICAgY29uc29sZS5sb2coJ/CfkbcgV29ya2VyIGNoZWNrOicsIHdvcmtlci5wZXJzb25hbEluZm8/Lndvcmtlck5hbWUsICdFbWFpbCBtYXRjaDonLCBlbWFpbE1hdGNoLCAnVXNlcklkIG1hdGNoOicsIHVzZXJJZE1hdGNoKVxuICAgICAgICAgIHJldHVybiBlbWFpbE1hdGNoIHx8IHVzZXJJZE1hdGNoXG4gICAgICAgIH0pXG4gICAgICAgIGlmICh3b3JrZXJQcm9maWxlKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coJ+KchSBGb3VuZCB3b3JrZXIgcHJvZmlsZSBmb3I6JywgdXNlckVtYWlsLCB3b3JrZXJQcm9maWxlLnBlcnNvbmFsSW5mbz8ud29ya2VyTmFtZSlcbiAgICAgICAgICByZXR1cm4gJ3dvcmtlcidcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBTdXBwbGllcnMgcHJvZmlsZXNcbiAgICAgIGNvbnN0IHN1cHBsaWVyc0RhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgna2Fhem1hYW1hYS1zdXBwbGllcnMnKVxuICAgICAgY29uc29sZS5sb2coJ/Cfk4sgU3VwcGxpZXJzIGRhdGE6Jywgc3VwcGxpZXJzRGF0YSA/ICdGb3VuZCcgOiAnTm90IGZvdW5kJylcbiAgICAgIGlmIChzdXBwbGllcnNEYXRhKSB7XG4gICAgICAgIGNvbnN0IHN1cHBsaWVycyA9IEpTT04ucGFyc2Uoc3VwcGxpZXJzRGF0YSlcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfj60gQ2hlY2tpbmcnLCBzdXBwbGllcnMubGVuZ3RoLCAnc3VwcGxpZXIgcHJvZmlsZXMnKVxuICAgICAgICBjb25zdCBzdXBwbGllclByb2ZpbGUgPSBzdXBwbGllcnMuZmluZCgoc3VwcGxpZXI6IGFueSkgPT4ge1xuICAgICAgICAgIGNvbnN0IGVtYWlsTWF0Y2ggPSBzdXBwbGllci5wZXJzb25hbEluZm8/LmVtYWlsID09PSB1c2VyRW1haWxcbiAgICAgICAgICBjb25zdCB1c2VySWRNYXRjaCA9IHN1cHBsaWVyLnVzZXJJZCA9PT0gdXNlckVtYWlsXG4gICAgICAgICAgY29uc29sZS5sb2coJ/Cfj60gU3VwcGxpZXIgY2hlY2s6Jywgc3VwcGxpZXIucGVyc29uYWxJbmZvPy5zdXBwbGllck5hbWUsICdFbWFpbCBtYXRjaDonLCBlbWFpbE1hdGNoLCAnVXNlcklkIG1hdGNoOicsIHVzZXJJZE1hdGNoKVxuICAgICAgICAgIGNvbnNvbGUubG9nKCfwn4+tIFN1cHBsaWVyIGRhdGEgc3RydWN0dXJlOicsIHsgcGVyc29uYWxJbmZvOiBzdXBwbGllci5wZXJzb25hbEluZm8sIHVzZXJJZDogc3VwcGxpZXIudXNlcklkIH0pXG4gICAgICAgICAgcmV0dXJuIGVtYWlsTWF0Y2ggfHwgdXNlcklkTWF0Y2hcbiAgICAgICAgfSlcbiAgICAgICAgaWYgKHN1cHBsaWVyUHJvZmlsZSkge1xuICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgRm91bmQgc3VwcGxpZXIgcHJvZmlsZSBmb3I6JywgdXNlckVtYWlsLCBzdXBwbGllclByb2ZpbGUucGVyc29uYWxJbmZvPy5zdXBwbGllck5hbWUpXG4gICAgICAgICAgcmV0dXJuICdzdXBwbGllcidcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBDaGVjayBDb21wYW5pZXMgcHJvZmlsZXNcbiAgICAgIGNvbnN0IGNvbXBhbmllc0RhdGEgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgna2Fhem1hYW1hYS1jb21wYW5pZXMnKVxuICAgICAgY29uc29sZS5sb2coJ/Cfk4sgQ29tcGFuaWVzIGRhdGE6JywgY29tcGFuaWVzRGF0YSA/ICdGb3VuZCcgOiAnTm90IGZvdW5kJylcbiAgICAgIGlmIChjb21wYW5pZXNEYXRhKSB7XG4gICAgICAgIGNvbnN0IGNvbXBhbmllcyA9IEpTT04ucGFyc2UoY29tcGFuaWVzRGF0YSlcbiAgICAgICAgY29uc29sZS5sb2coJ/Cfj6IgQ2hlY2tpbmcnLCBjb21wYW5pZXMubGVuZ3RoLCAnY29tcGFueSBwcm9maWxlcycpXG4gICAgICAgIGNvbnN0IGNvbXBhbnlQcm9maWxlID0gY29tcGFuaWVzLmZpbmQoKGNvbXBhbnk6IGFueSkgPT4ge1xuICAgICAgICAgIGNvbnN0IGVtYWlsTWF0Y2ggPSBjb21wYW55LmNvbnRhY3RJbmZvPy5ockVtYWlsID09PSB1c2VyRW1haWxcbiAgICAgICAgICBjb25zdCB1c2VySWRNYXRjaCA9IGNvbXBhbnkudXNlcklkID09PSB1c2VyRW1haWxcbiAgICAgICAgICBjb25zb2xlLmxvZygn8J+PoiBDb21wYW55IGNoZWNrOicsIGNvbXBhbnkuY29tcGFueUluZm8/LmNvbXBhbnlOYW1lLCAnRW1haWwgbWF0Y2g6JywgZW1haWxNYXRjaCwgJ1VzZXJJZCBtYXRjaDonLCB1c2VySWRNYXRjaClcbiAgICAgICAgICByZXR1cm4gZW1haWxNYXRjaCB8fCB1c2VySWRNYXRjaFxuICAgICAgICB9KVxuICAgICAgICBpZiAoY29tcGFueVByb2ZpbGUpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygn4pyFIEZvdW5kIGNvbXBhbnkgcHJvZmlsZSBmb3I6JywgdXNlckVtYWlsLCBjb21wYW55UHJvZmlsZS5jb21wYW55SW5mbz8uY29tcGFueU5hbWUpXG4gICAgICAgICAgcmV0dXJuICdjb21wYW55J1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCfinYwgTm8gcHJvZmlsZSBmb3VuZCBmb3I6JywgdXNlckVtYWlsLCAnZGVmYXVsdGluZyB0byB3b3JrZXInKVxuICAgICAgcmV0dXJuICd3b3JrZXInIC8vIERlZmF1bHQgZmFsbGJhY2tcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign8J+SpSBFcnJvciBkZXRlY3RpbmcgcHJvZmlsZSB0eXBlOicsIGVycm9yKVxuICAgICAgcmV0dXJuICd3b3JrZXInIC8vIERlZmF1bHQgZmFsbGJhY2tcbiAgICB9XG4gIH1cblxuICBjb25zdCBzaWduSW4gPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4ge1xuICAgIC8vIE1vY2sgaW1wbGVtZW50YXRpb24gZm9yIG5vd1xuICAgIGNvbnNvbGUubG9nKCdTaWduIGluOicsIHsgZW1haWwsIHBhc3N3b3JkIH0pXG4gICAgLy8gVXNlIGVtYWlsIGFzIHRoZSBjb25zaXN0ZW50IHVzZXIgSURcbiAgICBjb25zdCBtb2NrVXNlciA9IHsgaWQ6IGVtYWlsLCBlbWFpbCB9XG5cbiAgICAvLyBEZXRlY3QgdGhlIHVzZXIncyBhY3R1YWwgcHJvZmlsZSB0eXBlXG4gICAgY29uc3QgZGV0ZWN0ZWRSb2xlID0gZGV0ZWN0VXNlclByb2ZpbGVUeXBlKGVtYWlsKVxuICAgIGNvbnNvbGUubG9nKCdEZXRlY3RlZCB1c2VyIHJvbGU6JywgZGV0ZWN0ZWRSb2xlLCAnZm9yIGVtYWlsOicsIGVtYWlsKVxuXG4gICAgLy8gU2F2ZSB0byBzdGF0ZVxuICAgIHNldFVzZXIobW9ja1VzZXIpXG4gICAgc2V0VXNlclJvbGUoZGV0ZWN0ZWRSb2xlKVxuXG4gICAgLy8gU2F2ZSB0byBsb2NhbFN0b3JhZ2VcbiAgICB0cnkge1xuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ2thYXptYWFtYWEtdXNlcicsIEpTT04uc3RyaW5naWZ5KG1vY2tVc2VyKSlcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdrYWF6bWFhbWFhLXVzZXItcm9sZScsIGRldGVjdGVkUm9sZSlcbiAgICAgIGNvbnNvbGUubG9nKCdVc2VyIGF1dGhlbnRpY2F0aW9uIHNhdmVkIHRvIGxvY2FsU3RvcmFnZSB3aXRoIHJvbGU6JywgZGV0ZWN0ZWRSb2xlKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzYXZpbmcgYXV0aCB0byBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpXG4gICAgfVxuXG4gICAgcmV0dXJuIHsgZGF0YTogeyB1c2VyOiBtb2NrVXNlciB9LCBlcnJvcjogbnVsbCB9XG4gIH1cblxuICBjb25zdCBzaWduT3V0ID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIENsZWFyIHN0YXRlXG4gICAgc2V0VXNlcihudWxsKVxuICAgIHNldFVzZXJSb2xlKG51bGwpXG5cbiAgICAvLyBDbGVhciBsb2NhbFN0b3JhZ2VcbiAgICB0cnkge1xuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2thYXptYWFtYWEtdXNlcicpXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgna2Fhem1hYW1hYS11c2VyLXJvbGUnKVxuICAgICAgY29uc29sZS5sb2coJ1VzZXIgYXV0aGVudGljYXRpb24gY2xlYXJlZCBmcm9tIGxvY2FsU3RvcmFnZScpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNsZWFyaW5nIGF1dGggZnJvbSBsb2NhbFN0b3JhZ2U6JywgZXJyb3IpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0VXNlclByb2ZpbGUgPSAoKSA9PiB7XG4gICAgaWYgKCF1c2VyIHx8ICF1c2VyUm9sZSkgcmV0dXJuIG51bGxcblxuICAgIHRyeSB7XG4gICAgICBpZiAodXNlclJvbGUgPT09ICd3b3JrZXInKSB7XG4gICAgICAgIGNvbnN0IHdvcmtlcnNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtd29ya2VycycpXG4gICAgICAgIGlmICh3b3JrZXJzRGF0YSkge1xuICAgICAgICAgIGNvbnN0IHdvcmtlcnMgPSBKU09OLnBhcnNlKHdvcmtlcnNEYXRhKVxuICAgICAgICAgIHJldHVybiB3b3JrZXJzLmZpbmQoKHdvcmtlcjogYW55KSA9PlxuICAgICAgICAgICAgd29ya2VyLnBlcnNvbmFsSW5mbz8uZW1haWwgPT09IHVzZXIuZW1haWwgfHwgd29ya2VyLnVzZXJJZCA9PT0gdXNlci5lbWFpbFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmICh1c2VyUm9sZSA9PT0gJ3N1cHBsaWVyJykge1xuICAgICAgICBjb25zdCBzdXBwbGllcnNEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtc3VwcGxpZXJzJylcbiAgICAgICAgaWYgKHN1cHBsaWVyc0RhdGEpIHtcbiAgICAgICAgICBjb25zdCBzdXBwbGllcnMgPSBKU09OLnBhcnNlKHN1cHBsaWVyc0RhdGEpXG4gICAgICAgICAgcmV0dXJuIHN1cHBsaWVycy5maW5kKChzdXBwbGllcjogYW55KSA9PlxuICAgICAgICAgICAgc3VwcGxpZXIucGVyc29uYWxJbmZvPy5lbWFpbCA9PT0gdXNlci5lbWFpbCB8fFxuICAgICAgICAgICAgc3VwcGxpZXIudXNlcklkID09PSB1c2VyLmVtYWlsXG4gICAgICAgICAgKVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHVzZXJSb2xlID09PSAnY29tcGFueScpIHtcbiAgICAgICAgY29uc3QgY29tcGFuaWVzRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdrYWF6bWFhbWFhLWNvbXBhbmllcycpXG4gICAgICAgIGlmIChjb21wYW5pZXNEYXRhKSB7XG4gICAgICAgICAgY29uc3QgY29tcGFuaWVzID0gSlNPTi5wYXJzZShjb21wYW5pZXNEYXRhKVxuICAgICAgICAgIHJldHVybiBjb21wYW5pZXMuZmluZCgoY29tcGFueTogYW55KSA9PlxuICAgICAgICAgICAgY29tcGFueS5jb250YWN0SW5mbz8uaHJFbWFpbCA9PT0gdXNlci5lbWFpbCB8fCBjb21wYW55LnVzZXJJZCA9PT0gdXNlci5lbWFpbFxuICAgICAgICAgIClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcilcbiAgICB9XG5cbiAgICByZXR1cm4gbnVsbFxuICB9XG5cbiAgY29uc3QgZGVidWdQcm9maWxlcyA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+QmyBERUJVRzogQWxsIGxvY2FsU3RvcmFnZSBkYXRhOicpXG4gICAgY29uc29sZS5sb2coJ1dvcmtlcnM6JywgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtd29ya2VycycpKVxuICAgIGNvbnNvbGUubG9nKCdTdXBwbGllcnM6JywgbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtc3VwcGxpZXJzJykpXG4gICAgY29uc29sZS5sb2coJ0NvbXBhbmllczonLCBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgna2Fhem1hYW1hYS1jb21wYW5pZXMnKSlcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB3b3JrZXJzID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgna2Fhem1hYW1hYS13b3JrZXJzJykgfHwgJ1tdJylcbiAgICAgIGNvbnN0IHN1cHBsaWVycyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtc3VwcGxpZXJzJykgfHwgJ1tdJylcbiAgICAgIGNvbnN0IGNvbXBhbmllcyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2thYXptYWFtYWEtY29tcGFuaWVzJykgfHwgJ1tdJylcblxuICAgICAgY29uc29sZS5sb2coJ/CfkJsgUGFyc2VkIFdvcmtlcnM6Jywgd29ya2VycylcbiAgICAgIGNvbnNvbGUubG9nKCfwn5CbIFBhcnNlZCBTdXBwbGllcnM6Jywgc3VwcGxpZXJzKVxuICAgICAgY29uc29sZS5sb2coJ/CfkJsgUGFyc2VkIENvbXBhbmllczonLCBjb21wYW5pZXMpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ/CfkJsgRXJyb3IgcGFyc2luZyBsb2NhbFN0b3JhZ2UgZGF0YTonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCByZWZyZXNoVXNlclJvbGUgPSAoKSA9PiB7XG4gICAgaWYgKHVzZXI/LmVtYWlsKSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBNYW51YWxseSByZWZyZXNoaW5nIHVzZXIgcm9sZSBmb3I6JywgdXNlci5lbWFpbClcbiAgICAgIGNvbnN0IG5ld1JvbGUgPSBkZXRlY3RVc2VyUHJvZmlsZVR5cGUodXNlci5lbWFpbClcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SEIE5ldyByb2xlIGRldGVjdGVkOicsIG5ld1JvbGUpXG5cbiAgICAgIGlmIChuZXdSb2xlICE9PSB1c2VyUm9sZSkge1xuICAgICAgICBzZXRVc2VyUm9sZShuZXdSb2xlKVxuICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgna2Fhem1hYW1hYS11c2VyLXJvbGUnLCBuZXdSb2xlKVxuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFJvbGUgdXBkYXRlZCBmcm9tJywgdXNlclJvbGUsICd0bycsIG5ld1JvbGUpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIFJvbGUgdW5jaGFuZ2VkOicsIHVzZXJSb2xlKVxuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHZhbHVlID0ge1xuICAgIHVzZXIsXG4gICAgdXNlclJvbGUsXG4gICAgbG9hZGluZyxcbiAgICBzaWduVXAsXG4gICAgc2lnbkluLFxuICAgIHNpZ25PdXQsXG4gICAgZ2V0VXNlclByb2ZpbGUsXG4gICAgZGVidWdQcm9maWxlcyxcbiAgICByZWZyZXNoVXNlclJvbGUsXG4gIH1cblxuICByZXR1cm4gPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+e2NoaWxkcmVufTwvQXV0aENvbnRleHQuUHJvdmlkZXI+XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VBdXRoKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpXG4gIH1cbiAgcmV0dXJuIGNvbnRleHRcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQXV0aENvbnRleHQiLCJ1bmRlZmluZWQiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwidXNlclJvbGUiLCJzZXRVc2VyUm9sZSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiaXNJbml0aWFsaXplZCIsInNldElzSW5pdGlhbGl6ZWQiLCJsb2FkQXV0aFN0YXRlIiwic2F2ZWRVc2VyIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInNhdmVkUm9sZSIsInBhcnNlZFVzZXIiLCJKU09OIiwicGFyc2UiLCJyZURldGVjdGVkUm9sZSIsImRldGVjdFVzZXJQcm9maWxlVHlwZSIsImVtYWlsIiwiY29uc29sZSIsImxvZyIsImZpbmFsUm9sZSIsInNldEl0ZW0iLCJlcnJvciIsInNpZ25VcCIsInBhc3N3b3JkIiwicm9sZSIsIm1vY2tVc2VyIiwiaWQiLCJEYXRlIiwibm93IiwidG9TdHJpbmciLCJzdHJpbmdpZnkiLCJkYXRhIiwidXNlckVtYWlsIiwiT2JqZWN0Iiwia2V5cyIsIndvcmtlcnNEYXRhIiwid29ya2VycyIsImxlbmd0aCIsIndvcmtlclByb2ZpbGUiLCJmaW5kIiwid29ya2VyIiwiZW1haWxNYXRjaCIsInBlcnNvbmFsSW5mbyIsInVzZXJJZE1hdGNoIiwidXNlcklkIiwid29ya2VyTmFtZSIsInN1cHBsaWVyc0RhdGEiLCJzdXBwbGllcnMiLCJzdXBwbGllclByb2ZpbGUiLCJzdXBwbGllciIsInN1cHBsaWVyTmFtZSIsImNvbXBhbmllc0RhdGEiLCJjb21wYW5pZXMiLCJjb21wYW55UHJvZmlsZSIsImNvbXBhbnkiLCJjb250YWN0SW5mbyIsImhyRW1haWwiLCJjb21wYW55SW5mbyIsImNvbXBhbnlOYW1lIiwic2lnbkluIiwiZGV0ZWN0ZWRSb2xlIiwic2lnbk91dCIsInJlbW92ZUl0ZW0iLCJnZXRVc2VyUHJvZmlsZSIsImRlYnVnUHJvZmlsZXMiLCJyZWZyZXNoVXNlclJvbGUiLCJuZXdSb2xlIiwidmFsdWUiLCJQcm92aWRlciIsInVzZUF1dGgiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0U7SUFBRztRQUFHQyxPQUFILHVCQUF1Qjs7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxocFxcRGVza3RvcFxcS0FBWk1BQU1BQSBBVUdNRU5UXFxrYWF6bWFhbWFhLWFwcFxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chp%5C%5CDesktop%5C%5CKAAZMAAMAA%20AUGMENT%5C%5Ckaazmaamaa-app%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);