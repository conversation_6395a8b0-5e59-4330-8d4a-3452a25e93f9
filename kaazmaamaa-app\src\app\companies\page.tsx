'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useCompanies } from '@/contexts/CompaniesContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Building,
  Search,
  MessageCircle,
  Phone,
  ArrowLeft,
  MapPin,
  Star,
  Plus,
  FileText,
  Video,
  Calendar,
  Briefcase,
  Eye,
  Download,
  Play,
  Radio,
  CheckCircle,
  Clock,
  DollarSign,
  Globe,
  Users,
  Award,
  Factory,
  TrendingUp,
  Target,
  Heart,
  UserCheck
} from 'lucide-react'
import { toast } from 'sonner'

export default function CompaniesPage() {
  const { companies } = useCompanies()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedLocation, setSelectedLocation] = useState('all')
  const [selectedIndustry, setSelectedIndustry] = useState('all')
  const [showHiringOnly, setShowHiringOnly] = useState(false)

  useEffect(() => {
    // Simple loading timer
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  const handleWhatsAppContact = (whatsapp: string, companyName: string) => {
    const message = `Hello ${companyName}, I found your company on KAAZMAAMAA Companies Block and would like to discuss career opportunities.`
    const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  // Filter companies based on search term
  const filteredCompanies = companies.filter(company =>
    company.companyInfo?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.companyInfo?.industry?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.companyInfo?.headquarters?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading Companies...</h2>
          <p className="text-gray-500 mt-2">Please wait while we fetch the companies data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  toast.info('Navigating back to Feed...')
                  router.push('/feed')
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Feed
              </Button>
              <h1 className="text-2xl font-bold text-purple-600">Companies Block</h1>
              <Badge className="bg-purple-100 text-purple-800">
                {filteredCompanies.length} Companies Available
              </Badge>
            </div>
            <Button
              onClick={() => router.push('/companies/create')}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Company Profile
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Quick Stats */}
        <Card className="mb-6 bg-purple-50 border-purple-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-purple-800 mb-2">🏢 Professional Companies Directory</h3>
              <p className="text-purple-600 mb-4">Browse {companies.length} verified companies across different industries</p>
              <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-800">{companies.filter(c => c.isHiring && (c.jobOpenings?.length || 0) > 0).length}</div>
                  <div className="text-sm text-purple-600">Hiring Now</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{companies.filter(c => c.isVerified).length}</div>
                  <div className="text-sm text-blue-600">Verified</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-800">{companies.filter(c => c.isLiveStreaming).length}</div>
                  <div className="text-sm text-green-600">Live</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by company name, industry, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Companies Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCompanies.map((company) => (
            <Card key={company.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <Avatar className="h-16 w-16 bg-purple-100 flex items-center justify-center">
                      <Building className="h-8 w-8 text-purple-600" />
                    </Avatar>
                    {company.isLiveStreaming && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
                        <Radio className="h-3 w-3 mr-1" />
                        LIVE
                      </div>
                    )}
                    {company.isVerified && (
                      <div className="absolute -bottom-1 -right-1 bg-purple-500 text-white rounded-full p-1">
                        <CheckCircle className="h-3 w-3" />
                      </div>
                    )}
                    {company.isHiring && (
                      <div className="absolute -top-1 -left-1 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                        HIRING
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg truncate">{company.companyInfo?.companyName || 'Unknown Company'}</h3>
                    <p className="text-purple-600 font-medium">{company.companyInfo?.industry || 'No Industry'}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className="bg-purple-100 text-purple-800">
                        {company.companyInfo?.companySize || 'Unknown Size'}
                      </Badge>
                      <div className="flex items-center text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="text-sm ml-1">{company.rating || 0}</span>
                        <span className="text-gray-500 text-sm ml-1">({company.totalReviews || 0})</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600 text-sm">
                    <MapPin className="h-4 w-4 mr-2" />
                    {company.companyInfo?.headquarters || 'Location not specified'}
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Users className="h-4 w-4 mr-2" />
                    {company.totalEmployees || 0} employees
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Calendar className="h-4 w-4 mr-2" />
                    Founded: {company.companyInfo?.foundedYear || 'Unknown'}
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Globe className="h-4 w-4 mr-2" />
                    {company.companyInfo?.website || 'No website'}
                  </div>

                  {/* Job Openings */}
                  {company.jobOpenings && company.jobOpenings.length > 0 && (
                    <div className="pt-2 border-t">
                      <p className="text-sm font-medium text-gray-700 mb-2">Open Positions:</p>
                      {company.jobOpenings.slice(0, 2).map((job) => (
                        <div key={job.id} className="bg-gray-50 rounded-lg p-2 mb-2">
                          <p className="font-medium text-sm">{job.title}</p>
                          <p className="text-xs text-gray-600">{job.department}</p>
                          <div className="flex items-center justify-between mt-1">
                            <span className="text-xs text-purple-600">
                              {job.salaryRange.min}-{job.salaryRange.max} {job.salaryRange.currency}
                            </span>
                            <Badge variant="secondary" className="text-xs">
                              {job.urgency.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                      ))}
                      {company.jobOpenings.length > 2 && (
                        <p className="text-xs text-gray-500">+{company.jobOpenings.length - 2} more positions</p>
                      )}
                    </div>
                  )}

                  {/* Company Benefits */}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {(company.companyBenefits || []).slice(0, 3).map((benefit, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {benefit}
                      </Badge>
                    ))}
                    {(company.companyBenefits || []).length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{(company.companyBenefits || []).length - 3} more
                      </Badge>
                    )}
                  </div>

                  {/* Work Environment Indicators */}
                  <div className="flex items-center space-x-3 text-xs text-gray-500 pt-2 border-t">
                    {company.workEnvironment?.isRemoteFriendly && (
                      <div className="flex items-center">
                        <Globe className="h-3 w-3 mr-1" />
                        Remote
                      </div>
                    )}
                    {company.workEnvironment?.hasFlexibleHours && (
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        Flexible
                      </div>
                    )}
                    {company.workEnvironment?.providesTraining && (
                      <div className="flex items-center">
                        <Award className="h-3 w-3 mr-1" />
                        Training
                      </div>
                    )}
                  </div>

                  {/* Media Indicators */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 pt-2 border-t">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {(company.documents || []).length} docs
                    </div>
                    <div className="flex items-center">
                      <Video className="h-4 w-4 mr-1" />
                      {(company.videos || []).length} videos
                    </div>
                    <div className="flex items-center">
                      <Briefcase className="h-4 w-4 mr-1" />
                      {(company.jobOpenings || []).length} jobs
                    </div>
                  </div>

                  <div className="flex gap-2 pt-3">
                    <Button
                      size="sm"
                      onClick={() => router.push(`/companies/${company.id}`)}
                      className="flex-1 bg-purple-600 hover:bg-purple-700"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Profile
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleWhatsAppContact(company.contactInfo?.hrWhatsapp || '', company.companyInfo?.companyName || '')}
                      className="border-purple-300 text-purple-600 hover:bg-purple-50"
                    >
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredCompanies.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Companies Found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search criteria or filters to find companies.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedLocation('all')
                  setSelectedIndustry('all')
                  setShowHiringOnly(false)
                }}
              >
                Clear All Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
