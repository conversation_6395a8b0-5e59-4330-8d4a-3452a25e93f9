'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter, useSearchParams } from 'next/navigation'

export default function CompanyProfilePage() {
  const { user, userRole, getUserProfile, loading } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [userProfile, setUserProfile] = useState<any>(null)
  const [isOwnProfile, setIsOwnProfile] = useState(true)
  const targetUserId = searchParams.get('user')

  // Get user profile data
  useEffect(() => {
    if (user && userRole) {
      const profile = getUserProfile()
      setUserProfile(profile)
      
      // Check if viewing own profile or someone else's
      if (targetUserId && targetUserId !== user.email) {
        setIsOwnProfile(false)
      } else {
        setIsOwnProfile(true)
      }
    }
  }, [user, userRole, getUserProfile, targetUserId])

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
    }
  }, [user, loading, router])

  // Show loading if still authenticating
  if (loading) {
    return (
      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f3f4f6' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>
          <div style={{ color: '#6b7280' }}>Loading profile...</div>
        </div>
      </div>
    )
  }

  // Redirect if not authenticated
  if (!user) {
    return null
  }

  // Helper function to get user's display name
  const getUserDisplayName = () => {
    if (!userProfile) return 'Company'
    return userProfile.companyInfo?.companyName || 'Company'
  }

  // Helper function to get user's initial
  const getUserInitial = () => {
    const name = getUserDisplayName()
    return name.charAt(0).toUpperCase()
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#7c3aed', margin: 0 }}>
              KAAZMAAMAA
            </h2>
            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Company Profile</span>
          </div>
          
          <div style={{ display: 'flex', gap: '0.75rem' }}>
            <button 
              onClick={() => router.push('/feed')}
              style={{ 
                backgroundColor: '#7c3aed', 
                color: 'white', 
                border: 'none',
                padding: '0.5rem 1rem', 
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              Back to Feed
            </button>
            
            {isOwnProfile && (
              <button 
                onClick={() => {
                  router.push('/companies/edit')
                }}
                style={{ 
                  backgroundColor: '#059669', 
                  color: 'white', 
                  border: 'none',
                  padding: '0.5rem 1rem', 
                  borderRadius: '0.25rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Edit Profile
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '2rem' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          
          {/* Profile Header */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', marginBottom: '2rem' }}>
              <div style={{ 
                backgroundColor: '#7c3aed', 
                borderRadius: '50%', 
                width: '5rem', 
                height: '5rem', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                color: 'white', 
                fontSize: '2rem',
                fontWeight: 'bold' 
              }}>
                {getUserInitial()}
              </div>
              
              <div>
                <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0, marginBottom: '0.5rem' }}>
                  {getUserDisplayName()}
                </h1>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', color: '#6b7280', marginBottom: '0.5rem' }}>
                  <span>🏢</span>
                  <span>Professional Company</span>
                </div>
                {userProfile?.companyInfo?.email && (
                  <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    📧 {userProfile.companyInfo.email}
                  </div>
                )}
              </div>
            </div>
            
            {!isOwnProfile && (
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button style={{ 
                  backgroundColor: '#7c3aed', 
                  color: 'white', 
                  border: 'none',
                  padding: '0.75rem 1.5rem', 
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  Connect
                </button>
                <button style={{ 
                  backgroundColor: '#059669', 
                  color: 'white', 
                  border: 'none',
                  padding: '0.75rem 1.5rem', 
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  Message
                </button>
              </div>
            )}
          </div>

          {/* Company Information */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>
              Company Information
            </h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
              {userProfile?.companyInfo?.companyName && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Company Name
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.companyName}</div>
                </div>
              )}
              
              {userProfile?.companyInfo?.email && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Email
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.email}</div>
                </div>
              )}
              
              {userProfile?.companyInfo?.phone && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Phone
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.phone}</div>
                </div>
              )}
              
              {userProfile?.companyInfo?.industry && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Industry
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.companyInfo.industry}</div>
                </div>
              )}
            </div>
          </div>

          {/* Company Details */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>
              Company Details & Services
            </h2>
            
            <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🏢</div>
              <p>Company details and services information will be displayed here.</p>
              {isOwnProfile && (
                <button 
                  onClick={() => router.push('/companies/edit')}
                  style={{ 
                    backgroundColor: '#7c3aed', 
                    color: 'white', 
                    border: 'none',
                    padding: '0.75rem 1.5rem', 
                    borderRadius: '0.25rem',
                    fontSize: '1rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    marginTop: '1rem'
                  }}
                >
                  Add Company Details
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
