'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useWorkers } from '@/contexts/WorkersContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Wrench, Eye, MessageCircle, Star, Search, MapPin, Clock, DollarSign, Plus } from 'lucide-react'
import { toast } from 'sonner'

export default function WorkersPage() {
  const { workers } = useWorkers()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    // Simple loading timer
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  const handleWhatsAppContact = (phone: string | undefined, name: string | undefined) => {
    if (phone) {
      const message = `Hello ${name || 'there'}, I found your profile on KAAZMAAMAA and would like to discuss work opportunities.`
      const whatsappUrl = `https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
      window.open(whatsappUrl, '_blank')
      toast.success(`Opening WhatsApp chat with ${name || 'worker'}`)
    } else {
      toast.error('WhatsApp number not available')
    }
  }

  // Filter workers based on search term
  const filteredWorkers = workers.filter(worker =>
    worker.personalInfo?.workerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.professionalInfo?.specialty?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.professionalInfo?.title?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading Workers...</h2>
          <p className="text-gray-500 mt-2">Please wait while we fetch the workers data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  toast.info('Navigating back to Feed...')
                  router.push('/feed')
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Feed
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Workers Block</h1>
              <Badge className="bg-blue-100 text-blue-800">
                {filteredWorkers.length} Workers Available
              </Badge>
            </div>
            <Button
              onClick={() => router.push('/workers/create')}
              className="bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Worker Profile
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Quick Stats */}
        <Card className="mb-6 bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-blue-800 mb-2">👷 Professional Workers Directory</h3>
              <p className="text-blue-600 mb-4">Browse {workers.length} verified workers across different specialties</p>
              <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{workers.filter(w => w.lifecycle?.status === 'available').length}</div>
                  <div className="text-sm text-blue-600">Available</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-800">{workers.filter(w => w.isVerified).length}</div>
                  <div className="text-sm text-green-600">Verified</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-800">{workers.filter(w => w.isLiveStreaming).length}</div>
                  <div className="text-sm text-purple-600">Live</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by name, specialty, or title..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Workers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWorkers.map((worker) => (
            <Card key={worker.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
                      <Wrench className="h-8 w-8 text-blue-600" />
                    </div>
                    {worker.isVerified && (
                      <div className="absolute -bottom-1 -right-1 bg-blue-500 text-white rounded-full p-1">
                        <Star className="h-3 w-3" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg truncate">
                      {worker.personalInfo?.workerName || 'Unknown Worker'}
                    </h3>
                    <p className="text-blue-600 font-medium">
                      {worker.professionalInfo?.title || 'No Title'}
                    </p>
                    <div className="flex items-center text-yellow-500 mt-1">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="text-sm ml-1">{worker.rating || 0}</span>
                      <span className="text-gray-500 text-sm ml-1">({worker.totalReviews || 0})</span>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600 text-sm">
                    <Wrench className="h-4 w-4 mr-2" />
                    {worker.professionalInfo?.specialty || 'No specialty specified'}
                  </div>
                  
                  <div className="flex items-center text-gray-600 text-sm">
                    <MapPin className="h-4 w-4 mr-2" />
                    {worker.personalInfo?.address || 'Location not specified'}
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Clock className="h-4 w-4 mr-2" />
                    {worker.professionalInfo?.experience || 0} years experience
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <DollarSign className="h-4 w-4 mr-2" />
                    {worker.professionalInfo?.expectedSalaryPerHour || 0} {worker.professionalInfo?.currency || 'SAR'}/hr
                  </div>

                  <div className="flex gap-2 pt-3">
                    <Button
                      size="sm"
                      onClick={() => router.push(`/workers/${worker.id}`)}
                      className="flex-1"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Profile
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleWhatsAppContact(worker.personalInfo?.whatsapp, worker.personalInfo?.workerName)}
                    >
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredWorkers.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Wrench className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {workers.length === 0 ? 'No Workers Available' : 'No Workers Found'}
              </h3>
              <p className="text-gray-600 mb-4">
                {workers.length === 0
                  ? 'There are currently no workers registered in the system.'
                  : 'Try adjusting your search criteria to find workers.'
                }
              </p>
              {workers.length > 0 && (
                <Button
                  variant="outline"
                  onClick={() => setSearchTerm('')}
                >
                  Clear Search
                </Button>
              )}
              {workers.length === 0 && (
                <Button
                  onClick={() => {
                    toast.info('Refreshing workers data...')
                    window.location.reload()
                  }}
                  variant="outline"
                >
                  Refresh Page
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
