'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, ArrowRight, Shield, Key, FileText, CheckCircle, AlertTriangle, Code, Download } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep4() {
  const router = useRouter()
  const [completedSections, setCompletedSections] = useState<number[]>([])

  const markSectionComplete = (sectionId: number) => {
    if (!completedSections.includes(sectionId)) {
      setCompletedSections([...completedSections, sectionId])
      toast.success(`Section ${sectionId} completed! ✅`)
    }
  }

  const signingSteps = [
    {
      id: 1,
      title: "SHA-256 Hash Generation",
      description: "Generate cryptographic hash of invoice data",
      icon: <Key className="h-5 w-5" />,
      details: [
        "Convert invoice XML to canonical form",
        "Apply SHA-256 hashing algorithm",
        "Generate 256-bit hash digest",
        "Ensure data integrity verification"
      ]
    },
    {
      id: 2,
      title: "ECDSA Digital Signature",
      description: "Sign the hash using Elliptic Curve Digital Signature Algorithm",
      icon: <Shield className="h-5 w-5" />,
      details: [
        "Use private key for signing",
        "Apply ECDSA algorithm (secp256k1)",
        "Generate digital signature",
        "Attach signature to invoice"
      ]
    },
    {
      id: 3,
      title: "Certificate Validation",
      description: "Validate signing certificate and chain",
      icon: <CheckCircle className="h-5 w-5" />,
      details: [
        "Verify certificate validity",
        "Check certificate chain",
        "Validate against ZATCA CA",
        "Ensure non-repudiation"
      ]
    },
    {
      id: 4,
      title: "Signature Embedding",
      description: "Embed signature in invoice XML structure",
      icon: <FileText className="h-5 w-5" />,
      details: [
        "Add signature element to XML",
        "Include certificate information",
        "Maintain XML schema compliance",
        "Prepare for transmission"
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-purple-600">Step 4: Invoice Signing Implementation</h1>
              <Badge className="bg-purple-100 text-purple-800">
                Security
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Proceeding to Step 5...')
                  router.push('/invoice/step5')
                }}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Next: QR Code Generation
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-purple-50 border-purple-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <Shield className="h-16 w-16 text-purple-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-purple-800 mb-2">🔐 Invoice Signing Implementation</h3>
              <p className="text-purple-600 mb-4">
                Implement SHA-256 hash generation and ECDSA digital signature for invoice security
              </p>
              <div className="grid grid-cols-2 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-800">{completedSections.length}</div>
                  <div className="text-sm text-purple-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{signingSteps.length - completedSections.length}</div>
                  <div className="text-sm text-blue-600">Remaining</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Concepts */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Key className="h-5 w-5 mr-2 text-purple-600" />
              Key Concepts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">SHA-256 Hashing</h4>
                  <p className="text-blue-600 text-sm">
                    Cryptographic hash function that produces a 256-bit hash value, ensuring data integrity and authenticity.
                  </p>
                </div>
                <div className="p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-800 mb-2">ECDSA Signature</h4>
                  <p className="text-green-600 text-sm">
                    Elliptic Curve Digital Signature Algorithm providing authentication and non-repudiation.
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <h4 className="font-semibold text-yellow-800 mb-2">Certificate Chain</h4>
                  <p className="text-yellow-600 text-sm">
                    Hierarchical structure of certificates ensuring trust and validation through ZATCA CA.
                  </p>
                </div>
                <div className="p-4 bg-red-50 rounded-lg">
                  <h4 className="font-semibold text-red-800 mb-2">Non-Repudiation</h4>
                  <p className="text-red-600 text-sm">
                    Ensures that the signer cannot deny having signed the invoice document.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Implementation Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {signingSteps.map((step) => (
            <Card
              key={step.id}
              className={`transition-all hover:shadow-lg ${
                completedSections.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSections.includes(step.id) ? 'bg-green-100 text-green-600' : 'bg-purple-100 text-purple-600'
                  }`}>
                    {completedSections.includes(step.id) ? <CheckCircle className="h-5 w-5" /> : step.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg">
                        {step.title}
                      </h3>
                      {completedSections.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <ul className="space-y-2 mb-4">
                  {step.details.map((detail, index) => (
                    <li key={index} className="flex items-start space-x-2 text-sm">
                      <div className="w-1.5 h-1.5 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-gray-600">{detail}</span>
                    </li>
                  ))}
                </ul>

                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      toast.info(`Learning about ${step.title}...`)
                    }}
                  >
                    <Code className="h-4 w-4 mr-2" />
                    View Code
                  </Button>

                  {!completedSections.includes(step.id) && (
                    <Button
                      size="sm"
                      onClick={() => markSectionComplete(step.id)}
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Implementation Guide */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="h-5 w-5 mr-2 text-purple-600" />
              Implementation Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">1. Hash Generation Process</h4>
                <code className="text-sm bg-gray-100 p-2 rounded block">
                  hash = SHA256(canonicalXML(invoiceData))
                </code>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">2. Digital Signature Creation</h4>
                <code className="text-sm bg-gray-100 p-2 rounded block">
                  signature = ECDSA.sign(hash, privateKey)
                </code>
              </div>
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-semibold mb-2">3. Signature Verification</h4>
                <code className="text-sm bg-gray-100 p-2 rounded block">
                  isValid = ECDSA.verify(hash, signature, publicKey)
                </code>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security Considerations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-600" />
              Security Considerations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Secure private key storage</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Certificate validation</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Timestamp verification</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Hash algorithm integrity</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Signature format compliance</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                  <span className="text-sm">Audit trail maintenance</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
