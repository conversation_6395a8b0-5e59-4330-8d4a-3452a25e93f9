// PDF Generation Utility for ZATCA Invoices - HD Quality Version
// Enhanced with professional styling and textbox system
import { formatAmountInWords } from './numberToWords'

export interface InvoiceData {
  // Invoice Details
  invoiceNumber: string
  invoiceDate: string
  dueDate: string
  currency: string
  discount: number
  deductionAmount: number // NEW: Deduction amount field
  deductionReason: string // NEW: Deduction reason field

  // Seller Details
  sellerName: string
  sellerAddress: string
  sellerVAT: string
  sellerLogo?: string | null

  // Client Details
  clientName: string
  clientAddress: string
  clientVAT: string
  clientLogo?: string | null

  // Items
  items: Array<{
    id: string
    description: string
    quantity: number
    rate: number
    vatPercent: number
    amount: number
  }>

  // Additional Details
  notes: string
  terms: string
  bankName: string
  accountName: string
  accountNumber: string
  iban: string

  // Calculations
  subtotal: number
  subtotalAfterDeduction: number // NEW: Subtotal after deduction
  totalVAT: number
  totalAmount: number
  qrCode: string

  // Logo
  logoPreview?: string
}

/**
 * Generate HTML template for invoice PDF
 * @param data - Invoice data
 * @returns HTML string for PDF generation
 */
export function generateInvoiceHTML(data: InvoiceData): string {
  const discountAmount = (data.subtotal * data.discount) / 100
  const subtotalAfterDiscount = data.subtotal - discountAmount
  // NEW: Use the passed subtotalAfterDeduction which includes deduction amount logic

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${data.invoiceNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: 'Segoe UI', 'Arial', sans-serif;
          font-size: 11px;
          line-height: 1.5;
          color: #1f2937;
          background: white;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          print-color-adjust: exact;
        }

        .invoice-container {
          max-width: 850px;
          margin: 0 auto;
          padding: 30px;
          background: white;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          border-radius: 12px;
          border: 1px solid #e5e7eb;
          width: 100%;
          box-sizing: border-box;
          overflow-x: hidden;
        }

        .header {
          display: grid;
          grid-template-columns: 1fr auto 1fr;
          align-items: center;
          margin-bottom: 35px;
          padding: 25px;
          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
          border-radius: 12px;
          gap: 25px;
          box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);
        }

        .qr-header {
          text-align: center;
          justify-self: center;
        }

        .qr-code-top {
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .logo-section h1 {
          color: white;
          font-size: 26px;
          font-weight: 700;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .logo-section p {
          color: rgba(255, 255, 255, 0.9);
          font-size: 14px;
          font-weight: 500;
        }

        .invoice-title {
          text-align: right;
        }

        .invoice-title h2 {
          color: white;
          font-size: 32px;
          font-weight: 800;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          letter-spacing: 1px;
        }

        .invoice-number {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          font-weight: 600;
        }

        .details-section {
          display: grid;
          grid-template-columns: 1fr 1fr 1fr;
          gap: 30px;
          margin-bottom: 30px;
        }

        .detail-card {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #A78BFA;
          border: 1px solid #e5e7eb;
          overflow: hidden;
          word-wrap: break-word;
        }

        .detail-card h3 {
          color: #A78BFA;
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 15px;
          text-align: center;
          background: #A78BFA;
          color: white;
          padding: 8px;
          margin: -15px -15px 15px -15px;
          border-radius: 7px 7px 0 0;
        }

        .textbox-container {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
          margin-bottom: 10px;
        }

        .textbox-full {
          grid-column: 1 / -1;
        }

        .textbox {
          border: 1.5px solid #d1d5db;
          border-radius: 6px;
          padding: 10px 12px;
          background: #ffffff;
          font-size: 11px;
          min-height: 24px;
          box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
          font-weight: 500;
          color: #374151;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
        }

        .textbox-label {
          font-size: 9px;
          color: #6b7280;
          font-weight: 600;
          margin-bottom: 4px;
          display: block;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .textbox-large {
          min-height: 50px;
          line-height: 1.4;
          padding: 12px;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
          max-height: 80px;
        }

        .textbox-address {
          min-height: 45px;
          line-height: 1.3;
          word-wrap: break-word;
          word-break: break-word;
          overflow-wrap: break-word;
          white-space: normal;
          overflow: hidden;
          max-height: 75px;
          font-size: 10px;
          padding: 10px;
        }

        .textbox-amount {
          font-weight: 700;
          color: #059669;
          font-size: 12px;
          text-align: right;
        }

        .seller-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: 15px;
        }

        .seller-info {
          flex: 1;
        }

        .seller-logo {
          width: 80px;
          text-align: center;
        }

        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          background: white;
          border: 1px solid #e5e7eb;
        }

        .items-table th {
          background: #A78BFA;
          color: white;
          padding: 12px 8px;
          text-align: left;
          font-weight: bold;
          font-size: 11px;
        }

        .items-table td {
          padding: 10px 8px;
          border-bottom: 1px solid #e5e7eb;
          font-size: 11px;
        }

        .items-table tr:nth-child(even) {
          background: #f9fafb;
        }

        .summary-section {
          display: grid;
          grid-template-columns: 280px 1fr 1fr;
          gap: 20px;
          margin-bottom: 30px;
          max-width: 100%;
          overflow: hidden;
        }

        .notes-section, .bank-section {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 8px;
          border: 1px solid #e5e7eb;
        }

        .summary-card {
          background: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
          color: white;
          padding: 15px;
          border-radius: 8px;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.08);
          max-width: 280px;
          width: 100%;
          box-sizing: border-box;
        }

        .summary-card h4 {
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 15px;
        }

        .summary-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          font-size: 12px;
        }

        .summary-total {
          border-top: 2px solid rgba(255,255,255,0.3);
          padding-top: 10px;
          margin-top: 10px;
          font-size: 16px;
          font-weight: bold;
        }



        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 10px;
          border-top: 1px solid #e5e7eb;
          padding-top: 20px;
        }

        @media print {
          body { margin: 0; }
          .invoice-container { padding: 0; }
          .summary-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .summary-card {
            max-width: 100%;
          }
        }

        @media (max-width: 768px) {
          .summary-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .summary-card {
            max-width: 100%;
          }
          .invoice-container {
            padding: 15px;
          }
          .details-section {
            grid-template-columns: 1fr;
            gap: 15px;
          }
          .textbox-address {
            font-size: 9px;
            max-height: 60px;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Header with QR Code -->
        <div class="header">
          <div class="logo-section">
            <h1>🧾 ZATCA Invoice Generator</h1>
            <p>Create and manage your invoices with ease</p>
          </div>

          <!-- QR Code in Center Top -->
          <div class="qr-header">
            <div class="qr-code-top">
              <img src="https://api.qrserver.com/v1/create-qr-code/?size=130x130&data=${encodeURIComponent(data.qrCode)}"
                   alt="ZATCA QR Code" style="width: 130px; height: 130px; border: 3px solid white; border-radius: 12px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);" />
            </div>
            <p style="font-size: 11px; color: rgba(255, 255, 255, 0.9); margin-top: 8px; text-align: center; font-weight: 600;">ZATCA QR Code</p>
          </div>

          <div class="invoice-title">
            <h2>INVOICE</h2>
            <p class="invoice-number">${data.invoiceNumber}</p>
          </div>
        </div>

        <!-- Details Section -->
        <div class="details-section">
          <div class="detail-card">
            <h3>📋 Invoice Details</h3>
            <div class="textbox-container">
              <div>
                <span class="textbox-label">Invoice Number</span>
                <div class="textbox">${data.invoiceNumber}</div>
              </div>
              <div>
                <span class="textbox-label">Currency</span>
                <div class="textbox">${data.currency}</div>
              </div>
              <div>
                <span class="textbox-label">Invoice Date</span>
                <div class="textbox">${new Date(data.invoiceDate).toLocaleDateString()}</div>
              </div>
              <div>
                <span class="textbox-label">Due Date</span>
                <div class="textbox">${data.dueDate ? new Date(data.dueDate).toLocaleDateString() : 'N/A'}</div>
              </div>
              ${data.discount > 0 ? `
                <div class="textbox-full">
                  <span class="textbox-label">Discount Applied</span>
                  <div class="textbox">${data.discount}% discount applied to subtotal</div>
                </div>
              ` : ''}
              ${data.deductionAmount > 0 ? `
                <div class="textbox-full">
                  <span class="textbox-label">⚠️ Deduction Amount</span>
                  <div class="textbox" style="color: #ea580c; font-weight: 600; background: #fef3f2; border: 1px solid #fecaca;">${data.currency} ${data.deductionAmount.toFixed(2)} deducted before VAT calculation</div>
                </div>
                ${data.deductionReason ? `
                  <div class="textbox-full">
                    <span class="textbox-label">📝 Deduction Reason</span>
                    <div class="textbox" style="color: #374151; font-weight: 500; background: #fffbeb; border: 1px solid #fed7aa; padding: 12px; line-height: 1.4;">${data.deductionReason}</div>
                  </div>
                ` : ''}
              ` : ''}
            </div>
          </div>

          <div class="detail-card">
            <h3>🏢 Billed From (Seller)</h3>
            <div class="seller-container">
              <div class="seller-info">
                <div class="textbox-container">
                  <div class="textbox-full">
                    <span class="textbox-label">Company Name</span>
                    <div class="textbox">${data.sellerName}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">Company Address</span>
                    <div class="textbox textbox-address">${data.sellerAddress.replace(/\n/g, '<br>')}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">VAT Registration Number</span>
                    <div class="textbox">${data.sellerVAT}</div>
                  </div>
                </div>
              </div>
              ${data.sellerLogo ? `
                <div class="seller-logo">
                  <span class="textbox-label">Company Logo</span>
                  <div class="textbox" style="text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;">
                    <img src="${data.sellerLogo}" alt="Company Logo"
                         style="max-width: 70px; max-height: 60px; object-fit: contain;" />
                  </div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="detail-card">
            <h3>👤 Billed To (Client)</h3>
            <div class="seller-container">
              <div class="seller-info">
                <div class="textbox-container">
                  <div class="textbox-full">
                    <span class="textbox-label">Client Name</span>
                    <div class="textbox">${data.clientName}</div>
                  </div>
                  <div class="textbox-full">
                    <span class="textbox-label">Client Address</span>
                    <div class="textbox textbox-address">${data.clientAddress.replace(/\n/g, '<br>')}</div>
                  </div>
                  ${data.clientVAT ? `
                    <div class="textbox-full">
                      <span class="textbox-label">Client VAT Registration Number</span>
                      <div class="textbox">${data.clientVAT}</div>
                    </div>
                  ` : `
                    <div class="textbox-full">
                      <span class="textbox-label">Client VAT Registration Number</span>
                      <div class="textbox" style="color: #9ca3af; font-style: italic;">Not provided</div>
                    </div>
                  `}
                </div>
              </div>
              ${data.clientLogo ? `
                <div class="seller-logo">
                  <span class="textbox-label">Client Logo</span>
                  <div class="textbox" style="text-align: center; padding: 5px; min-height: 70px; display: flex; align-items: center; justify-content: center;">
                    <img src="${data.clientLogo}" alt="Client Logo"
                         style="max-width: 70px; max-height: 60px; object-fit: contain;" />
                  </div>
                </div>
              ` : ''}
            </div>
          </div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 40%">Description</th>
              <th style="width: 10%">Qty</th>
              <th style="width: 15%">Rate</th>
              <th style="width: 10%">VAT %</th>
              <th style="width: 15%">Amount (${data.currency})</th>
            </tr>
          </thead>
          <tbody>
            ${data.items.map(item => `
              <tr>
                <td>${item.description}</td>
                <td>${item.quantity}</td>
                <td>${item.rate.toFixed(2)}</td>
                <td>${item.vatPercent}%</td>
                <td>${item.amount.toFixed(2)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <!-- Summary Section -->
        <div class="summary-section">
          <div class="summary-card">
            <h4 style="color: white; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: rgba(255,255,255,0.25); padding: 8px; margin: -15px -15px 15px -15px; border-radius: 8px 8px 0 0;">💰 Invoice Summary</h4>
            <div class="textbox-container" style="gap: 8px; grid-template-columns: 1fr;">
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Subtotal</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;">${data.currency} ${data.subtotal.toFixed(2)}</div>
              </div>
              ${data.discount > 0 ? `
                <div>
                  <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Discount (${data.discount}%)</span>
                  <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #dc2626; padding: 8px; font-size: 10px;">-${data.currency} ${discountAmount.toFixed(2)}</div>
                </div>
              ` : ''}
              ${data.deductionAmount > 0 ? `
                <div>
                  <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Deduction Amount</span>
                  <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #ea580c; padding: 8px; font-size: 10px;">-${data.currency} ${data.deductionAmount.toFixed(2)}</div>
                </div>
                ${data.deductionReason ? `
                  <div>
                    <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Deduction Reason</span>
                    <div class="textbox" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); color: #6b7280; font-style: italic; padding: 8px; font-size: 9px; line-height: 1.3;">${data.deductionReason}</div>
                  </div>
                ` : ''}
              ` : ''}
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Amount Before VAT</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px; font-weight: 600;">${data.currency} ${data.subtotalAfterDeduction.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Total VAT (15%)</span>
                <div class="textbox textbox-amount" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); padding: 8px; font-size: 10px;">${data.currency} ${data.totalVAT.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Total Amount</span>
                <div class="textbox" style="background: #10b981; border: 2px solid #059669; color: white; font-weight: 700; font-size: 14px; text-align: center; padding: 10px;">${data.currency} ${data.totalAmount.toFixed(2)}</div>
              </div>
              <div>
                <span class="textbox-label" style="color: rgba(255,255,255,0.8); font-size: 8px;">Amount in Words</span>
                <div class="textbox" style="background: rgba(255,255,255,0.95); border: 1px solid rgba(255,255,255,0.3); font-style: italic; color: #374151; text-align: center; font-weight: 600; padding: 8px; font-size: 9px; line-height: 1.3;">${formatAmountInWords(data.totalAmount, data.currency)}</div>
              </div>
            </div>
          </div>

          <div class="bank-section">
            <h4 style="color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;">🏦 Bank Details</h4>
            <div class="textbox-container">
              ${data.accountName ? `
                <div>
                  <span class="textbox-label">Account Name</span>
                  <div class="textbox">${data.accountName}</div>
                </div>
              ` : ''}
              ${data.bankName ? `
                <div>
                  <span class="textbox-label">Bank Name</span>
                  <div class="textbox">${data.bankName}</div>
                </div>
              ` : ''}
              ${data.accountNumber ? `
                <div>
                  <span class="textbox-label">Account Number</span>
                  <div class="textbox">${data.accountNumber}</div>
                </div>
              ` : ''}
              ${data.iban ? `
                <div>
                  <span class="textbox-label">IBAN</span>
                  <div class="textbox">${data.iban}</div>
                </div>
              ` : ''}
              ${!data.accountName && !data.bankName && !data.accountNumber && !data.iban ? `
                <div class="textbox-full">
                  <span class="textbox-label">Payment Information</span>
                  <div class="textbox" style="color: #9ca3af; font-style: italic;">No bank details provided</div>
                </div>
              ` : ''}
            </div>
          </div>

          <div class="notes-section">
            <h4 style="color: #A78BFA; font-size: 14px; font-weight: bold; margin-bottom: 15px; text-align: center; background: #A78BFA; color: white; padding: 8px; margin: 0 0 15px 0; border-radius: 6px;">📝 Notes & Terms</h4>
            <div class="textbox-container" style="grid-template-columns: 1fr;">
              ${data.deductionAmount > 0 && data.deductionReason ? `
                <div class="textbox-full">
                  <span class="textbox-label">⚠️ Important: Deduction Applied</span>
                  <div class="textbox" style="background: #fef3f2; border: 2px solid #fecaca; color: #dc2626; font-weight: 600; padding: 12px; line-height: 1.4;">
                    A deduction of ${data.currency} ${data.deductionAmount.toFixed(2)} has been applied to this invoice.<br>
                    <strong>Reason:</strong> ${data.deductionReason}
                  </div>
                </div>
              ` : ''}
              ${data.notes ? `
                <div class="textbox-full">
                  <span class="textbox-label">Notes</span>
                  <div class="textbox textbox-large">${data.notes}</div>
                </div>
              ` : `
                <div class="textbox-full">
                  <span class="textbox-label">Notes</span>
                  <div class="textbox" style="color: #6b7280; font-style: italic; min-height: 30px; padding: 12px;">Thank you for your business!</div>
                </div>
              `}
              ${data.terms ? `
                <div class="textbox-full">
                  <span class="textbox-label">Terms & Conditions</span>
                  <div class="textbox textbox-large">${data.terms}</div>
                </div>
              ` : `
                <div class="textbox-full">
                  <span class="textbox-label">Terms & Conditions</span>
                  <div class="textbox textbox-large" style="color: #6b7280; font-style: normal; line-height: 1.6; padding: 15px;">
                    • Payment due within 30 days of invoice date<br>
                    • Late payments may incur additional charges<br>
                    • All prices are inclusive of applicable taxes<br>
                    • This invoice is computer generated and valid without signature
                  </div>
                </div>
              `}
            </div>
          </div>
        </div>



        <!-- Footer -->
        <div class="footer">
          <p>This invoice is generated by KAAZMAAMAA ZATCA Invoice System</p>
          <p>Compliant with Saudi Arabia's ZATCA e-invoicing regulations</p>
        </div>
      </div>
    </body>
    </html>
  `
}

/**
 * Generate and download PDF invoice
 * @param data - Invoice data
 * @param filename - PDF filename (optional)
 */
export function downloadInvoicePDF(data: InvoiceData, filename?: string): void {
  const html = generateInvoiceHTML(data)
  const pdfFilename = filename || `invoice-${data.invoiceNumber}.pdf`

  // Create a new window for PDF generation
  const printWindow = window.open('', '_blank')
  if (printWindow) {
    printWindow.document.write(html)
    printWindow.document.close()

    // Wait for content to load, then print
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print()
        // Don't close the window automatically - let user close it
        // printWindow.close()
      }, 500)
    }
  }
}

/**
 * Generate PDF blob for further processing
 * @param data - Invoice data
 * @returns Promise<Blob> - PDF blob
 */
export async function generateInvoicePDFBlob(data: InvoiceData): Promise<Blob> {
  // This is a simplified version
  // In production, use libraries like jsPDF or Puppeteer for proper PDF generation
  const html = generateInvoiceHTML(data)
  const blob = new Blob([html], { type: 'text/html' })
  return blob
}

/**
 * Print invoice directly
 * @param data - Invoice data
 */
export function printInvoice(data: InvoiceData): void {
  const html = generateInvoiceHTML(data)

  // Create a hidden iframe for printing
  const iframe = document.createElement('iframe')
  iframe.style.display = 'none'
  document.body.appendChild(iframe)

  const doc = iframe.contentDocument || iframe.contentWindow?.document
  if (doc) {
    doc.write(html)
    doc.close()

    iframe.onload = () => {
      iframe.contentWindow?.print()
      setTimeout(() => {
        document.body.removeChild(iframe)
      }, 1000)
    }
  }
}

// Export utility functions
export const PDFUtils = {
  generateInvoiceHTML,
  downloadInvoicePDF,
  generateInvoicePDFBlob,
  printInvoice
}
