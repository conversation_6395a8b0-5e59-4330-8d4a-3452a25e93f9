{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect } from 'react'\n\nexport type UserRole = 'worker' | 'supplier' | 'company'\n\ninterface User {\n  id: string\n  email: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  userRole: UserRole | null\n  loading: boolean\n  signUp: (email: string, password: string, role: UserRole) => Promise<any>\n  signIn: (email: string, password: string) => Promise<any>\n  signOut: () => Promise<void>\n  getUserProfile: () => any | null\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [userRole, setUserRole] = useState<UserRole | null>(null)\n  const [loading, setLoading] = useState(true) // Start with loading true\n  const [isInitialized, setIsInitialized] = useState(false)\n\n  // Load authentication state from localStorage on mount\n  useEffect(() => {\n    const loadAuthState = () => {\n      try {\n        const savedUser = localStorage.getItem('kaazmaamaa-user')\n        const savedRole = localStorage.getItem('kaazmaamaa-user-role')\n\n        if (savedUser && savedRole) {\n          const parsedUser = JSON.parse(savedUser)\n          setUser(parsedUser)\n          setUserRole(savedRole as UserRole)\n          console.log('Loaded user from localStorage:', parsedUser.email, savedRole)\n        } else {\n          console.log('No saved authentication found')\n        }\n      } catch (error) {\n        console.error('Error loading auth state from localStorage:', error)\n      } finally {\n        setLoading(false)\n        setIsInitialized(true)\n      }\n    }\n\n    loadAuthState()\n  }, [])\n\n  const signUp = async (email: string, password: string, role: UserRole) => {\n    // Mock implementation for now\n    console.log('Sign up:', { email, password, role })\n    const mockUser = { id: Date.now().toString(), email }\n\n    // Save to state\n    setUser(mockUser)\n    setUserRole(role)\n\n    // Save to localStorage\n    try {\n      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))\n      localStorage.setItem('kaazmaamaa-user-role', role)\n      console.log('User authentication saved to localStorage')\n    } catch (error) {\n      console.error('Error saving auth to localStorage:', error)\n    }\n\n    return { data: { user: mockUser }, error: null }\n  }\n\n  // Function to detect user's profile type by checking all profile contexts\n  const detectUserProfileType = (userEmail: string): UserRole => {\n    console.log('🔍 Detecting profile type for email:', userEmail)\n\n    try {\n      // Check Workers profiles\n      const workersData = localStorage.getItem('kaazmaamaa-workers')\n      console.log('📋 Workers data:', workersData ? 'Found' : 'Not found')\n      if (workersData) {\n        const workers = JSON.parse(workersData)\n        console.log('👷 Checking', workers.length, 'worker profiles')\n        const workerProfile = workers.find((worker: any) => {\n          const emailMatch = worker.personalInfo?.email === userEmail\n          const userIdMatch = worker.userId === userEmail\n          console.log('👷 Worker check:', worker.personalInfo?.workerName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)\n          return emailMatch || userIdMatch\n        })\n        if (workerProfile) {\n          console.log('✅ Found worker profile for:', userEmail, workerProfile.personalInfo?.workerName)\n          return 'worker'\n        }\n      }\n\n      // Check Suppliers profiles\n      const suppliersData = localStorage.getItem('kaazmaamaa-suppliers')\n      console.log('📋 Suppliers data:', suppliersData ? 'Found' : 'Not found')\n      if (suppliersData) {\n        const suppliers = JSON.parse(suppliersData)\n        console.log('🏭 Checking', suppliers.length, 'supplier profiles')\n        const supplierProfile = suppliers.find((supplier: any) => {\n          const emailMatch = supplier.personalInfo?.email === userEmail\n          const userIdMatch = supplier.userId === userEmail\n          console.log('🏭 Supplier check:', supplier.personalInfo?.supplierName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)\n          console.log('🏭 Supplier data structure:', { personalInfo: supplier.personalInfo, userId: supplier.userId })\n          return emailMatch || userIdMatch\n        })\n        if (supplierProfile) {\n          console.log('✅ Found supplier profile for:', userEmail, supplierProfile.personalInfo?.supplierName)\n          return 'supplier'\n        }\n      }\n\n      // Check Companies profiles\n      const companiesData = localStorage.getItem('kaazmaamaa-companies')\n      console.log('📋 Companies data:', companiesData ? 'Found' : 'Not found')\n      if (companiesData) {\n        const companies = JSON.parse(companiesData)\n        console.log('🏢 Checking', companies.length, 'company profiles')\n        const companyProfile = companies.find((company: any) => {\n          const emailMatch = company.contactInfo?.hrEmail === userEmail\n          const userIdMatch = company.userId === userEmail\n          console.log('🏢 Company check:', company.companyInfo?.companyName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)\n          return emailMatch || userIdMatch\n        })\n        if (companyProfile) {\n          console.log('✅ Found company profile for:', userEmail, companyProfile.companyInfo?.companyName)\n          return 'company'\n        }\n      }\n\n      console.log('❌ No profile found for:', userEmail, 'defaulting to worker')\n      return 'worker' // Default fallback\n    } catch (error) {\n      console.error('💥 Error detecting profile type:', error)\n      return 'worker' // Default fallback\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    // Mock implementation for now\n    console.log('Sign in:', { email, password })\n    // Use email as the consistent user ID\n    const mockUser = { id: email, email }\n\n    // Detect the user's actual profile type\n    const detectedRole = detectUserProfileType(email)\n    console.log('Detected user role:', detectedRole, 'for email:', email)\n\n    // Save to state\n    setUser(mockUser)\n    setUserRole(detectedRole)\n\n    // Save to localStorage\n    try {\n      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))\n      localStorage.setItem('kaazmaamaa-user-role', detectedRole)\n      console.log('User authentication saved to localStorage with role:', detectedRole)\n    } catch (error) {\n      console.error('Error saving auth to localStorage:', error)\n    }\n\n    return { data: { user: mockUser }, error: null }\n  }\n\n  const signOut = async () => {\n    // Clear state\n    setUser(null)\n    setUserRole(null)\n\n    // Clear localStorage\n    try {\n      localStorage.removeItem('kaazmaamaa-user')\n      localStorage.removeItem('kaazmaamaa-user-role')\n      console.log('User authentication cleared from localStorage')\n    } catch (error) {\n      console.error('Error clearing auth from localStorage:', error)\n    }\n  }\n\n  const getUserProfile = () => {\n    if (!user || !userRole) return null\n\n    try {\n      if (userRole === 'worker') {\n        const workersData = localStorage.getItem('kaazmaamaa-workers')\n        if (workersData) {\n          const workers = JSON.parse(workersData)\n          return workers.find((worker: any) =>\n            worker.personalInfo?.email === user.email || worker.userId === user.email\n          )\n        }\n      } else if (userRole === 'supplier') {\n        const suppliersData = localStorage.getItem('kaazmaamaa-suppliers')\n        if (suppliersData) {\n          const suppliers = JSON.parse(suppliersData)\n          return suppliers.find((supplier: any) =>\n            supplier.personalInfo?.email === user.email ||\n            supplier.userId === user.email\n          )\n        }\n      } else if (userRole === 'company') {\n        const companiesData = localStorage.getItem('kaazmaamaa-companies')\n        if (companiesData) {\n          const companies = JSON.parse(companiesData)\n          return companies.find((company: any) =>\n            company.contactInfo?.hrEmail === user.email || company.userId === user.email\n          )\n        }\n      }\n    } catch (error) {\n      console.error('Error getting user profile:', error)\n    }\n\n    return null\n  }\n\n  const value = {\n    user,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    getUserProfile,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,0BAA0B;;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,YAAY,aAAa,OAAO,CAAC;gBACvC,MAAM,YAAY,aAAa,OAAO,CAAC;gBAEvC,IAAI,aAAa,WAAW;oBAC1B,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,QAAQ;oBACR,YAAY;oBACZ,QAAQ,GAAG,CAAC,kCAAkC,WAAW,KAAK,EAAE;gBAClE,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;YAC/D,SAAU;gBACR,WAAW;gBACX,iBAAiB;YACnB;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,YAAY;YAAE;YAAO;YAAU;QAAK;QAChD,MAAM,WAAW;YAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;YAAI;QAAM;QAEpD,gBAAgB;QAChB,QAAQ;QACR,YAAY;QAEZ,uBAAuB;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,aAAa,OAAO,CAAC,wBAAwB;YAC7C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO;YAAE,MAAM;gBAAE,MAAM;YAAS;YAAG,OAAO;QAAK;IACjD;IAEA,0EAA0E;IAC1E,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,IAAI;YACF,yBAAyB;YACzB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,QAAQ,GAAG,CAAC,oBAAoB,cAAc,UAAU;YACxD,IAAI,aAAa;gBACf,MAAM,UAAU,KAAK,KAAK,CAAC;gBAC3B,QAAQ,GAAG,CAAC,eAAe,QAAQ,MAAM,EAAE;gBAC3C,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC;oBAClC,MAAM,aAAa,OAAO,YAAY,EAAE,UAAU;oBAClD,MAAM,cAAc,OAAO,MAAM,KAAK;oBACtC,QAAQ,GAAG,CAAC,oBAAoB,OAAO,YAAY,EAAE,YAAY,gBAAgB,YAAY,iBAAiB;oBAC9G,OAAO,cAAc;gBACvB;gBACA,IAAI,eAAe;oBACjB,QAAQ,GAAG,CAAC,+BAA+B,WAAW,cAAc,YAAY,EAAE;oBAClF,OAAO;gBACT;YACF;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,QAAQ,GAAG,CAAC,sBAAsB,gBAAgB,UAAU;YAC5D,IAAI,eAAe;gBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,QAAQ,GAAG,CAAC,eAAe,UAAU,MAAM,EAAE;gBAC7C,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAC;oBACtC,MAAM,aAAa,SAAS,YAAY,EAAE,UAAU;oBACpD,MAAM,cAAc,SAAS,MAAM,KAAK;oBACxC,QAAQ,GAAG,CAAC,sBAAsB,SAAS,YAAY,EAAE,cAAc,gBAAgB,YAAY,iBAAiB;oBACpH,QAAQ,GAAG,CAAC,+BAA+B;wBAAE,cAAc,SAAS,YAAY;wBAAE,QAAQ,SAAS,MAAM;oBAAC;oBAC1G,OAAO,cAAc;gBACvB;gBACA,IAAI,iBAAiB;oBACnB,QAAQ,GAAG,CAAC,iCAAiC,WAAW,gBAAgB,YAAY,EAAE;oBACtF,OAAO;gBACT;YACF;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,QAAQ,GAAG,CAAC,sBAAsB,gBAAgB,UAAU;YAC5D,IAAI,eAAe;gBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,QAAQ,GAAG,CAAC,eAAe,UAAU,MAAM,EAAE;gBAC7C,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAC;oBACrC,MAAM,aAAa,QAAQ,WAAW,EAAE,YAAY;oBACpD,MAAM,cAAc,QAAQ,MAAM,KAAK;oBACvC,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,WAAW,EAAE,aAAa,gBAAgB,YAAY,iBAAiB;oBAChH,OAAO,cAAc;gBACvB;gBACA,IAAI,gBAAgB;oBAClB,QAAQ,GAAG,CAAC,gCAAgC,WAAW,eAAe,WAAW,EAAE;oBACnF,OAAO;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B,WAAW;YAClD,OAAO,SAAS,mBAAmB;;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,SAAS,mBAAmB;;QACrC;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,YAAY;YAAE;YAAO;QAAS;QAC1C,sCAAsC;QACtC,MAAM,WAAW;YAAE,IAAI;YAAO;QAAM;QAEpC,wCAAwC;QACxC,MAAM,eAAe,sBAAsB;QAC3C,QAAQ,GAAG,CAAC,uBAAuB,cAAc,cAAc;QAE/D,gBAAgB;QAChB,QAAQ;QACR,YAAY;QAEZ,uBAAuB;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,aAAa,OAAO,CAAC,wBAAwB;YAC7C,QAAQ,GAAG,CAAC,wDAAwD;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO;YAAE,MAAM;gBAAE,MAAM;YAAS;YAAG,OAAO;QAAK;IACjD;IAEA,MAAM,UAAU;QACd,cAAc;QACd,QAAQ;QACR,YAAY;QAEZ,qBAAqB;QACrB,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,UAAU,OAAO;QAE/B,IAAI;YACF,IAAI,aAAa,UAAU;gBACzB,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,QAAQ,IAAI,CAAC,CAAC,SACnB,OAAO,YAAY,EAAE,UAAU,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,KAAK,KAAK;gBAE7E;YACF,OAAO,IAAI,aAAa,YAAY;gBAClC,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,OAAO,UAAU,IAAI,CAAC,CAAC,WACrB,SAAS,YAAY,EAAE,UAAU,KAAK,KAAK,IAC3C,SAAS,MAAM,KAAK,KAAK,KAAK;gBAElC;YACF,OAAO,IAAI,aAAa,WAAW;gBACjC,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,OAAO,UAAU,IAAI,CAAC,CAAC,UACrB,QAAQ,WAAW,EAAE,YAAY,KAAK,KAAK,IAAI,QAAQ,MAAM,KAAK,KAAK,KAAK;gBAEhF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/PostsContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface Comment {\n  id: string\n  post_id: string\n  user_id: string\n  user_name: string\n  user_role: 'worker' | 'supplier' | 'company'\n  content: string\n  created_at: string\n}\n\nexport interface Post {\n  id: string\n  user_id: string\n  user_name: string\n  user_role: 'worker' | 'supplier' | 'company'\n  content: string\n  media_urls?: string[]\n  post_type: 'text' | 'image' | 'video' | 'live' | 'document' | 'mixed'\n  visibility: 'public' | 'block_only'\n  created_at: string\n  expires_at: string // Posts expire after 1 month\n  likes: number\n  comments: number\n  liked_by_user?: boolean\n  post_comments?: Comment[]\n  shared_count?: number\n}\n\ninterface PostsContextType {\n  posts: Post[]\n  addPost: (post: Omit<Post, 'id' | 'created_at' | 'expires_at' | 'likes' | 'comments'>) => void\n  likePost: (postId: string) => void\n  sharePost: (postId: string) => void\n  deletePost: (postId: string) => void\n  addComment: (postId: string, content: string, user: { id: string, name: string, role: 'worker' | 'supplier' | 'company' }) => void\n  clearAllPosts: () => void\n  resetToInitialPosts: () => void\n  cleanupExpiredPosts: () => void\n  migratePostsToFullNames: () => void\n}\n\nconst PostsContext = createContext<PostsContextType | undefined>(undefined)\n\n// Helper function to calculate expiration date (1 month from creation)\nconst getExpirationDate = (createdAt: string): string => {\n  const created = new Date(createdAt)\n  const expiration = new Date(created)\n  expiration.setMonth(expiration.getMonth() + 1) // Add 1 month\n  return expiration.toISOString()\n}\n\n// Initial mock posts\nconst initialPosts: Post[] = [\n  {\n    id: '1',\n    user_id: 'mock-user-1',\n    user_name: 'Ahmed Al-Rashid',\n    user_role: 'worker',\n    content: 'Looking for construction work in Riyadh. I have 5 years of experience in electrical work. Available immediately! #ElectricalWork #Riyadh #Available',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),\n    likes: 12,\n    comments: 3,\n    shared_count: 2,\n    post_comments: [\n      {\n        id: 'comment-1',\n        post_id: '1',\n        user_id: 'commenter-1',\n        user_name: 'Sarah Construction',\n        user_role: 'company',\n        content: 'We have openings for electrical work. Please send your CV.',\n        created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n      },\n      {\n        id: 'comment-2',\n        post_id: '1',\n        user_id: 'commenter-2',\n        user_name: 'Ali Hassan',\n        user_role: 'worker',\n        content: 'Good luck brother! I also work in electrical.',\n        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n      }\n    ]\n  },\n  {\n    id: '2',\n    user_id: 'mock-user-2',\n    user_name: 'Saudi Construction Co.',\n    user_role: 'company',\n    content: 'We are hiring 20 skilled workers for our new project in Jeddah. Competitive salary and accommodation provided. Contact us for details. #Hiring #Jeddah #Construction',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()),\n    likes: 28,\n    comments: 15\n  },\n  {\n    id: '3',\n    user_id: 'mock-user-3',\n    user_name: 'Gulf Manpower Solutions',\n    user_role: 'supplier',\n    content: 'New batch of certified welders available for immediate deployment. All workers have valid IQAMA and safety certifications. #Welders #Certified #Available',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()),\n    likes: 18,\n    comments: 8\n  },\n  {\n    id: '4',\n    user_id: 'mock-user-4',\n    user_name: 'Mohammed Hassan',\n    user_role: 'worker',\n    content: 'Just completed my safety certification course! Ready for new opportunities in the oil and gas sector. #SafetyCertified #OilAndGas #Ready',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()),\n    likes: 15,\n    comments: 5\n  }\n]\n\nexport function PostsProvider({ children }: { children: ReactNode }) {\n  const [posts, setPosts] = useState<Post[]>([])\n  const [isLoaded, setIsLoaded] = useState(false)\n\n  // Load posts from localStorage on component mount\n  useEffect(() => {\n    const loadPostsFromStorage = () => {\n      try {\n        const savedPosts = localStorage.getItem('kaazmaamaa-posts')\n        if (savedPosts) {\n          const parsedPosts = JSON.parse(savedPosts)\n          console.log('Loaded posts from localStorage:', parsedPosts.length)\n\n          // Filter out expired posts when loading\n          const now = new Date()\n          const activePosts = parsedPosts.filter((post: Post) => {\n            // Handle posts that might not have expires_at field (legacy posts)\n            if (!post.expires_at) {\n              // Add expires_at to legacy posts (1 month from creation)\n              post.expires_at = getExpirationDate(post.created_at)\n            }\n            const expirationDate = new Date(post.expires_at)\n            return expirationDate > now\n          })\n\n          const expiredCount = parsedPosts.length - activePosts.length\n          if (expiredCount > 0) {\n            console.log(`Filtered out ${expiredCount} expired posts on load`)\n            // Save cleaned posts back to localStorage\n            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts))\n          }\n\n          setPosts(activePosts)\n        } else {\n          // If no saved posts, use initial posts\n          console.log('No saved posts found, using initial posts')\n          setPosts(initialPosts)\n          localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts))\n        }\n      } catch (error) {\n        console.error('Error loading posts from localStorage:', error)\n        setPosts(initialPosts)\n      } finally {\n        setIsLoaded(true)\n      }\n    }\n\n    loadPostsFromStorage()\n  }, [])\n\n  // Save posts to localStorage whenever posts change\n  useEffect(() => {\n    if (isLoaded && posts.length > 0) {\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(posts))\n        console.log('Saved posts to localStorage:', posts.length)\n      } catch (error) {\n        console.error('Error saving posts to localStorage:', error)\n      }\n    }\n  }, [posts, isLoaded])\n\n  console.log('PostsProvider initialized with posts:', posts.length)\n\n  const addPost = (newPost: Omit<Post, 'id' | 'created_at' | 'expires_at' | 'likes' | 'comments'>) => {\n    const createdAt = new Date().toISOString()\n    const post: Post = {\n      ...newPost,\n      id: Date.now().toString(),\n      created_at: createdAt,\n      expires_at: getExpirationDate(createdAt), // Posts expire after 1 month\n      likes: 0,\n      comments: 0\n    }\n    console.log('Adding post to context:', post)\n    setPosts(prevPosts => {\n      const updatedPosts = [post, ...prevPosts]\n      console.log('Updated posts array:', updatedPosts)\n\n      // Immediately save to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Post immediately saved to localStorage')\n      } catch (error) {\n        console.error('Error saving post to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const likePost = (postId: string) => {\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.map(post =>\n        post.id === postId\n          ? {\n              ...post,\n              likes: post.liked_by_user ? post.likes - 1 : post.likes + 1,\n              liked_by_user: !post.liked_by_user\n            }\n          : post\n      )\n\n      // Save likes to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Like status saved to localStorage')\n      } catch (error) {\n        console.error('Error saving like to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const sharePost = (postId: string) => {\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.map(post =>\n        post.id === postId\n          ? {\n              ...post,\n              shared_count: (post.shared_count || 0) + 1\n            }\n          : post\n      )\n\n      // Save share count to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Share count saved to localStorage')\n      } catch (error) {\n        console.error('Error saving share to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const addComment = (postId: string, content: string, user: { id: string, name: string, role: 'worker' | 'supplier' | 'company' }) => {\n    const newComment: Comment = {\n      id: Date.now().toString(),\n      post_id: postId,\n      user_id: user.id,\n      user_name: user.name,\n      user_role: user.role,\n      content: content.trim(),\n      created_at: new Date().toISOString()\n    }\n\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.map(post =>\n        post.id === postId\n          ? {\n              ...post,\n              comments: post.comments + 1,\n              post_comments: [...(post.post_comments || []), newComment]\n            }\n          : post\n      )\n\n      // Save comments to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Comment saved to localStorage')\n      } catch (error) {\n        console.error('Error saving comment to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const deletePost = (postId: string) => {\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.filter(post => post.id !== postId)\n\n      // Save deletion to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Post deletion saved to localStorage')\n      } catch (error) {\n        console.error('Error saving deletion to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const clearAllPosts = () => {\n    setPosts([])\n    try {\n      localStorage.removeItem('kaazmaamaa-posts')\n      console.log('All posts cleared from localStorage')\n    } catch (error) {\n      console.error('Error clearing posts from localStorage:', error)\n    }\n  }\n\n  const resetToInitialPosts = () => {\n    setPosts(initialPosts)\n    try {\n      localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts))\n      console.log('Reset to initial posts and saved to localStorage')\n    } catch (error) {\n      console.error('Error resetting posts in localStorage:', error)\n    }\n  }\n\n  // Function to clean up expired posts (older than 1 month)\n  const cleanupExpiredPosts = useCallback(() => {\n    const now = new Date()\n    setPosts(prevPosts => {\n      const activePosts = prevPosts.filter(post => {\n        const expirationDate = new Date(post.expires_at)\n        return expirationDate > now\n      })\n\n      const expiredCount = prevPosts.length - activePosts.length\n      if (expiredCount > 0) {\n        console.log(`Cleaned up ${expiredCount} expired posts`)\n\n        // Save cleaned posts to localStorage\n        try {\n          localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts))\n          console.log('Cleaned posts saved to localStorage')\n        } catch (error) {\n          console.error('Error saving cleaned posts to localStorage:', error)\n        }\n      }\n\n      return activePosts\n    })\n  }, [])\n\n  const migratePostsToFullNames = () => {\n    console.log('Migrating posts to use full names...')\n    // Clear localStorage and reset to initial posts with proper names\n    try {\n      localStorage.removeItem('kaazmaamaa-posts')\n      console.log('Cleared old posts from localStorage')\n    } catch (error) {\n      console.error('Error clearing localStorage:', error)\n    }\n    resetToInitialPosts()\n  }\n\n  const value = {\n    posts,\n    addPost,\n    likePost,\n    sharePost,\n    deletePost,\n    addComment,\n    clearAllPosts,\n    resetToInitialPosts,\n    cleanupExpiredPosts,\n    migratePostsToFullNames\n  }\n\n  return (\n    <PostsContext.Provider value={value}>\n      {children}\n    </PostsContext.Provider>\n  )\n}\n\nexport function usePosts() {\n  const context = useContext(PostsContext)\n  if (context === undefined) {\n    throw new Error('usePosts must be used within a PostsProvider')\n  }\n  return context\n}\n\n// Utility function to format time ago\nexport function formatTimeAgo(dateString: string): string {\n  const now = new Date()\n  const postDate = new Date(dateString)\n  const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))\n\n  if (diffInMinutes < 1) return 'Just now'\n  if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) return `${diffInHours}h ago`\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) return `${diffInDays}d ago`\n\n  return postDate.toLocaleDateString()\n}\n\n// Utility function to format post expiration info\nexport function formatExpirationInfo(expiresAt: string): { text: string, isExpiringSoon: boolean } {\n  const now = new Date()\n  const expirationDate = new Date(expiresAt)\n  const diffInMs = expirationDate.getTime() - now.getTime()\n  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n\n  if (diffInMs <= 0) {\n    return { text: 'Expired', isExpiringSoon: true }\n  }\n\n  if (diffInDays <= 3) {\n    return { text: `Expires in ${diffInDays} day${diffInDays !== 1 ? 's' : ''}`, isExpiringSoon: true }\n  }\n\n  if (diffInDays <= 7) {\n    return { text: `Expires in ${diffInDays} days`, isExpiringSoon: false }\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return { text: `Expires in ${diffInWeeks} week${diffInWeeks !== 1 ? 's' : ''}`, isExpiringSoon: false }\n  }\n\n  return { text: 'Expires in 1 month', isExpiringSoon: false }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AA6CA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAEjE,uEAAuE;AACvE,MAAM,oBAAoB,CAAC;IACzB,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,aAAa,IAAI,KAAK;IAC5B,WAAW,QAAQ,CAAC,WAAW,QAAQ,KAAK,GAAG,cAAc;;IAC7D,OAAO,WAAW,WAAW;AAC/B;AAEA,qBAAqB;AACrB,MAAM,eAAuB;IAC3B;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,IAAI;gBACJ,SAAS;gBACT,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,SAAS;gBACT,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,SAAS;gBACT,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;SACD;IACH;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;IACZ;CACD;AAEM,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,MAAM,cAAc,KAAK,KAAK,CAAC;oBAC/B,QAAQ,GAAG,CAAC,mCAAmC,YAAY,MAAM;oBAEjE,wCAAwC;oBACxC,MAAM,MAAM,IAAI;oBAChB,MAAM,cAAc,YAAY,MAAM,CAAC,CAAC;wBACtC,mEAAmE;wBACnE,IAAI,CAAC,KAAK,UAAU,EAAE;4BACpB,yDAAyD;4BACzD,KAAK,UAAU,GAAG,kBAAkB,KAAK,UAAU;wBACrD;wBACA,MAAM,iBAAiB,IAAI,KAAK,KAAK,UAAU;wBAC/C,OAAO,iBAAiB;oBAC1B;oBAEA,MAAM,eAAe,YAAY,MAAM,GAAG,YAAY,MAAM;oBAC5D,IAAI,eAAe,GAAG;wBACpB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa,sBAAsB,CAAC;wBAChE,0CAA0C;wBAC1C,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;oBAC1D;oBAEA,SAAS;gBACX,OAAO;oBACL,uCAAuC;oBACvC,QAAQ,GAAG,CAAC;oBACZ,SAAS;oBACT,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBAC1D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,SAAS;YACX,SAAU;gBACR,YAAY;YACd;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;YAChC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC,gCAAgC,MAAM,MAAM;YAC1D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;IACF,GAAG;QAAC;QAAO;KAAS;IAEpB,QAAQ,GAAG,CAAC,yCAAyC,MAAM,MAAM;IAEjE,MAAM,UAAU,CAAC;QACf,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,MAAM,OAAa;YACjB,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY;YACZ,YAAY,kBAAkB;YAC9B,OAAO;YACP,UAAU;QACZ;QACA,QAAQ,GAAG,CAAC,2BAA2B;QACvC,SAAS,CAAA;YACP,MAAM,eAAe;gBAAC;mBAAS;aAAU;YACzC,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,mCAAmC;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OACjC,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,OAAO,KAAK,aAAa,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG;oBAC1D,eAAe,CAAC,KAAK,aAAa;gBACpC,IACA;YAGN,6BAA6B;YAC7B,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OACjC,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,cAAc,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI;gBAC3C,IACA;YAGN,mCAAmC;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,aAAa,CAAC,QAAgB,SAAiB;QACnD,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,SAAS,KAAK,EAAE;YAChB,WAAW,KAAK,IAAI;YACpB,WAAW,KAAK,IAAI;YACpB,SAAS,QAAQ,IAAI;YACrB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OACjC,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,GAAG;oBAC1B,eAAe;2BAAK,KAAK,aAAa,IAAI,EAAE;wBAAG;qBAAW;gBAC5D,IACA;YAGN,gCAAgC;YAChC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAE1D,gCAAgC;YAChC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;YAEA,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS,EAAE;QACX,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;IACF;IAEA,MAAM,sBAAsB;QAC1B,SAAS;QACT,IAAI;YACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YACxD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,0DAA0D;IAC1D,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,MAAM,IAAI;QAChB,SAAS,CAAA;YACP,MAAM,cAAc,UAAU,MAAM,CAAC,CAAA;gBACnC,MAAM,iBAAiB,IAAI,KAAK,KAAK,UAAU;gBAC/C,OAAO,iBAAiB;YAC1B;YAEA,MAAM,eAAe,UAAU,MAAM,GAAG,YAAY,MAAM;YAC1D,IAAI,eAAe,GAAG;gBACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,cAAc,CAAC;gBAEtD,qCAAqC;gBACrC,IAAI;oBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;oBACxD,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+CAA+C;gBAC/D;YACF;YAEA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,0BAA0B;QAC9B,QAAQ,GAAG,CAAC;QACZ,kEAAkE;QAClE,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;QACA;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,cAAc,UAAkB;IAC9C,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IAElF,IAAI,gBAAgB,GAAG,OAAO;IAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;IAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;IAElD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,KAAK,CAAC;IAE/C,OAAO,SAAS,kBAAkB;AACpC;AAGO,SAAS,qBAAqB,SAAiB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,iBAAiB,IAAI,KAAK;IAChC,MAAM,WAAW,eAAe,OAAO,KAAK,IAAI,OAAO;IACvD,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE7D,IAAI,YAAY,GAAG;QACjB,OAAO;YAAE,MAAM;YAAW,gBAAgB;QAAK;IACjD;IAEA,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,MAAM,CAAC,WAAW,EAAE,WAAW,IAAI,EAAE,eAAe,IAAI,MAAM,IAAI;YAAE,gBAAgB;QAAK;IACpG;IAEA,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,MAAM,CAAC,WAAW,EAAE,WAAW,KAAK,CAAC;YAAE,gBAAgB;QAAM;IACxE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,MAAM,CAAC,WAAW,EAAE,YAAY,KAAK,EAAE,gBAAgB,IAAI,MAAM,IAAI;YAAE,gBAAgB;QAAM;IACxG;IAEA,OAAO;QAAE,MAAM;QAAsB,gBAAgB;IAAM;AAC7D", "debugId": null}}, {"offset": {"line": 643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/OnlineUsersContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface OnlineUser {\n  id: string\n  name: string\n  email: string\n  role: 'worker' | 'supplier' | 'company'\n  lastSeen: string\n  isOnline: boolean\n  avatar?: string\n}\n\ninterface OnlineUsersContextType {\n  onlineUsers: OnlineUser[]\n  totalOnlineCount: number\n  setUserOnline: (user: { id: string, name: string, email: string, role: 'worker' | 'supplier' | 'company' }) => void\n  setUserOffline: (userId: string) => void\n  isUserOnline: (userId: string) => boolean\n}\n\nconst OnlineUsersContext = createContext<OnlineUsersContextType | undefined>(undefined)\n\n// Mock online users data\nconst mockOnlineUsers: OnlineUser[] = [\n  {\n    id: 'user-1',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'worker',\n    lastSeen: new Date().toISOString(),\n    isOnline: true\n  },\n  {\n    id: 'user-2',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'company',\n    lastSeen: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago\n    isOnline: true\n  },\n  {\n    id: 'user-3',\n    name: 'Gulf Manpower',\n    email: '<EMAIL>',\n    role: 'supplier',\n    lastSeen: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago\n    isOnline: true\n  },\n  {\n    id: 'user-4',\n    name: 'Mohammed Hassan',\n    email: '<EMAIL>',\n    role: 'worker',\n    lastSeen: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago\n    isOnline: true\n  },\n  {\n    id: 'user-5',\n    name: 'Riyadh Builders',\n    email: '<EMAIL>',\n    role: 'company',\n    lastSeen: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago\n    isOnline: true\n  },\n  {\n    id: 'user-6',\n    name: 'Ali Construction',\n    email: '<EMAIL>',\n    role: 'worker',\n    lastSeen: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago\n    isOnline: false\n  }\n]\n\nexport function OnlineUsersProvider({ children }: { children: ReactNode }) {\n  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([])\n\n  useEffect(() => {\n    // Load initial online users\n    setOnlineUsers(mockOnlineUsers)\n\n    // Simulate real-time updates\n    const interval = setInterval(() => {\n      setOnlineUsers(prevUsers => {\n        return prevUsers.map(user => {\n          // Randomly update online status for demo\n          const shouldToggle = Math.random() < 0.1 // 10% chance to toggle\n          if (shouldToggle) {\n            return {\n              ...user,\n              isOnline: !user.isOnline,\n              lastSeen: new Date().toISOString()\n            }\n          }\n          return user\n        })\n      })\n    }, 30000) // Update every 30 seconds\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const setUserOnline = useCallback((user: { id: string, name: string, email: string, role: 'worker' | 'supplier' | 'company' }) => {\n    setOnlineUsers(prevUsers => {\n      const existingUserIndex = prevUsers.findIndex(u => u.id === user.id)\n      const onlineUser: OnlineUser = {\n        id: user.id,\n        name: user.name,\n        email: user.email,\n        role: user.role,\n        lastSeen: new Date().toISOString(),\n        isOnline: true\n      }\n\n      if (existingUserIndex >= 0) {\n        // Update existing user only if status changed\n        const existingUser = prevUsers[existingUserIndex]\n        if (existingUser.isOnline) {\n          // User is already online, no need to update\n          return prevUsers\n        }\n        const updatedUsers = [...prevUsers]\n        updatedUsers[existingUserIndex] = onlineUser\n        return updatedUsers\n      } else {\n        // Add new user\n        return [...prevUsers, onlineUser]\n      }\n    })\n  }, [])\n\n  const setUserOffline = useCallback((userId: string) => {\n    setOnlineUsers(prevUsers =>\n      prevUsers.map(user =>\n        user.id === userId\n          ? { ...user, isOnline: false, lastSeen: new Date().toISOString() }\n          : user\n      )\n    )\n  }, [])\n\n  const isUserOnline = useCallback((userId: string) => {\n    const user = onlineUsers.find(u => u.id === userId)\n    return user?.isOnline || false\n  }, [onlineUsers])\n\n  const totalOnlineCount = onlineUsers.filter(user => user.isOnline).length\n\n  const value = {\n    onlineUsers,\n    totalOnlineCount,\n    setUserOnline,\n    setUserOffline,\n    isUserOnline\n  }\n\n  return (\n    <OnlineUsersContext.Provider value={value}>\n      {children}\n    </OnlineUsersContext.Provider>\n  )\n}\n\nexport function useOnlineUsers() {\n  const context = useContext(OnlineUsersContext)\n  if (context === undefined) {\n    throw new Error('useOnlineUsers must be used within an OnlineUsersProvider')\n  }\n  return context\n}\n\n// Utility function to format last seen time\nexport function formatLastSeen(lastSeenString: string): string {\n  const now = new Date()\n  const lastSeen = new Date(lastSeenString)\n  const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))\n\n  if (diffInMinutes < 1) return 'Just now'\n  if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) return `${diffInHours}h ago`\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) return `${diffInDays}d ago`\n\n  return lastSeen.toLocaleDateString()\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAsBA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsC;AAE7E,yBAAyB;AACzB,MAAM,kBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,OAAO,WAAW;QAChC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;QAC3D,UAAU;IACZ;CACD;AAEM,SAAS,oBAAoB,EAAE,QAAQ,EAA2B;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,eAAe;QAEf,6BAA6B;QAC7B,MAAM,WAAW,YAAY;YAC3B,eAAe,CAAA;gBACb,OAAO,UAAU,GAAG,CAAC,CAAA;oBACnB,yCAAyC;oBACzC,MAAM,eAAe,KAAK,MAAM,KAAK,IAAI,uBAAuB;;oBAChE,IAAI,cAAc;wBAChB,OAAO;4BACL,GAAG,IAAI;4BACP,UAAU,CAAC,KAAK,QAAQ;4BACxB,UAAU,IAAI,OAAO,WAAW;wBAClC;oBACF;oBACA,OAAO;gBACT;YACF;QACF,GAAG,OAAO,0BAA0B;;QAEpC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,eAAe,CAAA;YACb,MAAM,oBAAoB,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;YACnE,MAAM,aAAyB;gBAC7B,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,UAAU,IAAI,OAAO,WAAW;gBAChC,UAAU;YACZ;YAEA,IAAI,qBAAqB,GAAG;gBAC1B,8CAA8C;gBAC9C,MAAM,eAAe,SAAS,CAAC,kBAAkB;gBACjD,IAAI,aAAa,QAAQ,EAAE;oBACzB,4CAA4C;oBAC5C,OAAO;gBACT;gBACA,MAAM,eAAe;uBAAI;iBAAU;gBACnC,YAAY,CAAC,kBAAkB,GAAG;gBAClC,OAAO;YACT,OAAO;gBACL,eAAe;gBACf,OAAO;uBAAI;oBAAW;iBAAW;YACnC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,eAAe,CAAA,YACb,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,UAAU;oBAAO,UAAU,IAAI,OAAO,WAAW;gBAAG,IAC/D;IAGV,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5C,OAAO,MAAM,YAAY;IAC3B,GAAG;QAAC;KAAY;IAEhB,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;IAEzE,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;kBACjC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,eAAe,cAAsB;IACnD,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IAElF,IAAI,gBAAgB,GAAG,OAAO;IAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;IAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;IAElD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,KAAK,CAAC;IAE/C,OAAO,SAAS,kBAAkB;AACpC", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/WorkersContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\nexport interface WorkerDocument {\n  id: string\n  name: string\n  type: 'absher_iqama' | 'muqeem' | 'medical' | 'insurance' | 'chamber' | 'ajeer' | 'experience' | 'fitness' | 'passport' | 'visa' | 'gosi' | 'other'\n  url: string\n  uploadedAt: string\n  isValidated?: boolean\n  fileSize?: number\n  fileName?: string\n}\n\nexport interface WorkerVideo {\n  id: string\n  title: string\n  type: 'introduction' | 'skills' | 'portfolio'\n  url: string\n  thumbnail?: string\n  duration?: number\n  uploadedAt: string\n}\n\nexport interface WorkExperience {\n  id: string\n  company: string\n  position: string\n  startDate: string\n  endDate?: string\n  description: string\n  location: string\n  isCurrentJob: boolean\n}\n\nexport interface WorkerLifecycle {\n  joiningDate?: string\n  vacationStart?: string\n  vacationEnd?: string\n  leavingDate?: string\n  status: 'available' | 'working' | 'on_vacation' | 'left' | 'seeking'\n  currentEmployer?: string\n  notes?: string\n}\n\nexport interface WorkerProfile {\n  id: string // Must be unique\n  userId: string\n  personalInfo: {\n    workerName: string\n    nationality: string\n    kofilName: string\n    kofilPhone: string\n    email: string\n    phone: string\n    whatsapp: string\n    address: string\n    age?: number\n    profileImage?: string\n  }\n  identificationInfo: {\n    iqamaNumber: string\n    passportNumber: string\n    crNumber?: string // Optional for workers\n  }\n  professionalInfo: {\n    workerCategory: string\n    title: string\n    specialty: string\n    experience: number\n    skills: string[]\n    languages: string[]\n    expectedSalaryPerHour: number\n    currency: 'SAR' | 'USD' | 'EUR'\n    salaryPayingDuration: 'daily' | 'weekly' | 'monthly' | 'hourly'\n    availability: 'immediate' | 'within_week' | 'within_month' | 'negotiable'\n  }\n  documents: WorkerDocument[]\n  videos: WorkerVideo[]\n  workExperience: WorkExperience[]\n  lifecycle: WorkerLifecycle\n  preferences: {\n    workType: 'full_time' | 'part_time' | 'contract' | 'freelance'\n    sideLocationInterest: string[]\n    companyNameInterest: string[]\n    preferredLocations?: string[]\n    willingToRelocate: boolean\n    remoteWork: boolean\n    neededDocuments: string[]\n    otherRequirements: string\n  }\n  isLiveStreaming: boolean\n  liveStreamUrl?: string\n  createdAt: string\n  updatedAt: string\n  isVerified: boolean\n  rating: number\n  totalReviews: number\n}\n\ninterface WorkersContextType {\n  workers: WorkerProfile[]\n  currentWorkerProfile?: WorkerProfile\n  createWorkerProfile: (profile: Omit<WorkerProfile, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateWorkerProfile: (id: string, updates: Partial<WorkerProfile>) => void\n  updateWorker: (profile: WorkerProfile) => void\n  addDocument: (workerId: string, document: Omit<WorkerDocument, 'id' | 'uploadedAt'>) => void\n  addVideo: (workerId: string, video: Omit<WorkerVideo, 'id' | 'uploadedAt'>) => void\n  updateLifecycle: (workerId: string, lifecycle: Partial<WorkerLifecycle>) => void\n  startLiveStream: (workerId: string, streamUrl: string) => void\n  stopLiveStream: (workerId: string) => void\n  getWorkersBySpecialty: (specialty: string) => WorkerProfile[]\n  getWorkersByCategory: (category: string) => WorkerProfile[]\n  getAvailableWorkers: () => WorkerProfile[]\n  validateWorkerIdUniqueness: (workerId: string, excludeId?: string) => boolean\n  validateIqamaDocument: (iqamaNumber: string) => Promise<boolean>\n}\n\nconst WorkersContext = createContext<WorkersContextType | undefined>(undefined)\n\n// Mock data for demonstration\nconst mockWorkers: WorkerProfile[] = [\n  {\n    id: 'worker-1', // Must be unique\n    userId: 'user-1',\n    personalInfo: {\n      workerName: 'Ahmed Al-Rashid',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Ahmed Mohammed Al-Rashid',\n      kofilPhone: '+966501234567',\n      email: '<EMAIL>',\n      phone: '+966501234567',\n      whatsapp: '+966501234567',\n      address: 'King Fahd Road, Riyadh 12345, Saudi Arabia'\n    },\n    identificationInfo: {\n      iqamaNumber: '2345678901',\n      passportNumber: 'A12345678',\n      crNumber: 'CR-1010987654'\n    },\n    professionalInfo: {\n      workerCategory: 'Electrical Technician',\n      title: 'Senior Electrical Technician',\n      specialty: 'Electrical Work',\n      experience: 5,\n      skills: ['Electrical Installation', 'Maintenance', 'Troubleshooting', 'Safety Protocols'],\n      languages: ['Arabic', 'English'],\n      expectedSalaryPerHour: 85,\n      currency: 'SAR',\n      salaryPayingDuration: 'weekly',\n      availability: 'immediate'\n    },\n    documents: [\n      {\n        id: 'doc-1',\n        name: 'Iqama Document',\n        type: 'iqama',\n        url: '/documents/iqama.pdf',\n        uploadedAt: new Date().toISOString(),\n        isValidated: true\n      },\n      {\n        id: 'doc-2',\n        name: 'Visa Document',\n        type: 'visa',\n        url: '/documents/visa.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-3',\n        name: 'Passport Copy',\n        type: 'passport',\n        url: '/documents/passport.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-4',\n        name: 'Medical Certificate',\n        type: 'medical',\n        url: '/documents/medical.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-5',\n        name: 'Fitness Certificate',\n        type: 'fitness',\n        url: '/documents/fitness.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-1',\n        title: 'Professional Introduction',\n        type: 'introduction',\n        url: '/videos/intro.mp4',\n        duration: 120,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    workExperience: [\n      {\n        id: 'exp-1',\n        company: 'Saudi Electric Company',\n        position: 'Electrical Technician',\n        startDate: '2020-01-01',\n        endDate: '2023-12-31',\n        description: 'Responsible for electrical installations and maintenance',\n        location: 'Riyadh',\n        isCurrentJob: false\n      }\n    ],\n    lifecycle: {\n      status: 'seeking',\n      notes: 'Looking for new opportunities in electrical field'\n    },\n    preferences: {\n      workType: 'full_time',\n      sideLocationInterest: ['Riyadh', 'Jeddah', 'Dammam'],\n      companyNameInterest: ['Saudi Electric Company', 'ARAMCO', 'SABIC'],\n      willingToRelocate: true,\n      remoteWork: false,\n      neededDocuments: ['Work Permit', 'Safety Certificate', 'Medical Clearance'],\n      otherRequirements: 'Prefer companies with good safety standards and career growth opportunities'\n    },\n    isLiveStreaming: false,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    isVerified: true,\n    rating: 4.8,\n    totalReviews: 15\n  },\n  {\n    id: 'worker-2',\n    userId: 'user-2',\n    personalInfo: {\n      workerName: 'Mohammed Al-Zahrani',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Mohammed Abdullah Al-Zahrani',\n      kofilPhone: '+966502345678',\n      email: '<EMAIL>',\n      phone: '+966502345678',\n      whatsapp: '+966502345678',\n      address: 'Prince Sultan Road, Jeddah 21455, Saudi Arabia'\n    },\n    identificationInfo: {\n      iqamaNumber: '**********',\n      passportNumber: 'B23456789',\n      crNumber: 'CR-**********'\n    },\n    professionalInfo: {\n      workerCategory: 'Plumber',\n      title: 'Senior Plumber',\n      specialty: 'Plumbing & Water Systems',\n      experience: 8,\n      skills: ['Pipe Installation', 'Water System Repair', 'Drainage', 'Emergency Repairs'],\n      languages: ['Arabic', 'English', 'Urdu'],\n      expectedSalaryPerHour: 75,\n      currency: 'SAR',\n      salaryPayingDuration: 'daily',\n      availability: 'within_week'\n    },\n    documents: [\n      {\n        id: 'doc-6',\n        name: 'Iqama Document',\n        type: 'iqama',\n        url: '/documents/iqama2.pdf',\n        uploadedAt: new Date().toISOString(),\n        isValidated: true\n      },\n      {\n        id: 'doc-7',\n        name: 'Plumbing Certificate',\n        type: 'experience',\n        url: '/documents/plumbing-cert.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-2',\n        title: 'Plumbing Skills Demo',\n        type: 'skills',\n        url: '/videos/plumbing-demo.mp4',\n        duration: 180,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    workExperience: [\n      {\n        id: 'exp-2',\n        company: 'Saudi Water Company',\n        position: 'Senior Plumber',\n        startDate: '2018-03-01',\n        endDate: '2024-01-01',\n        description: 'Specialized in water system installations and repairs',\n        location: 'Jeddah',\n        isCurrentJob: false\n      }\n    ],\n    lifecycle: {\n      status: 'available',\n      notes: 'Available for immediate plumbing work'\n    },\n    preferences: {\n      workType: 'contract',\n      sideLocationInterest: ['Jeddah', 'Mecca', 'Taif'],\n      companyNameInterest: ['Saudi Water Company', 'National Water Company'],\n      willingToRelocate: false,\n      remoteWork: false,\n      neededDocuments: ['Work Permit', 'Plumbing License'],\n      otherRequirements: 'Prefer emergency repair contracts'\n    },\n    isLiveStreaming: false,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    isVerified: true,\n    rating: 4.6,\n    totalReviews: 23\n  },\n  {\n    id: 'worker-3',\n    userId: 'user-3',\n    personalInfo: {\n      workerName: 'Khalid Al-Mutairi',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Khalid Saad Al-Mutairi',\n      kofilPhone: '+966503456789',\n      email: '<EMAIL>',\n      phone: '+966503456789',\n      whatsapp: '+966503456789',\n      address: 'King Abdul Aziz Road, Dammam 31411, Saudi Arabia'\n    },\n    identificationInfo: {\n      iqamaNumber: '**********',\n      passportNumber: '*********',\n      crNumber: 'CR-**********'\n    },\n    professionalInfo: {\n      workerCategory: 'Carpenter',\n      title: 'Master Carpenter',\n      specialty: 'Woodworking & Furniture',\n      experience: 12,\n      skills: ['Custom Furniture', 'Cabinet Making', 'Wood Finishing', 'Restoration'],\n      languages: ['Arabic', 'English'],\n      expectedSalaryPerHour: 95,\n      currency: 'SAR',\n      salaryPayingDuration: 'weekly',\n      availability: 'immediate'\n    },\n    documents: [\n      {\n        id: 'doc-8',\n        name: 'Iqama Document',\n        type: 'iqama',\n        url: '/documents/iqama3.pdf',\n        uploadedAt: new Date().toISOString(),\n        isValidated: true\n      },\n      {\n        id: 'doc-9',\n        name: 'Carpentry Master Certificate',\n        type: 'experience',\n        url: '/documents/carpentry-master.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-3',\n        title: 'Custom Furniture Portfolio',\n        type: 'portfolio',\n        url: '/videos/furniture-portfolio.mp4',\n        duration: 300,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    workExperience: [\n      {\n        id: 'exp-3',\n        company: 'Royal Furniture Workshop',\n        position: 'Master Carpenter',\n        startDate: '2015-01-01',\n        description: 'Creating custom furniture for luxury homes and offices',\n        location: 'Dammam',\n        isCurrentJob: true\n      }\n    ],\n    lifecycle: {\n      status: 'seeking',\n      notes: 'Looking for high-end furniture projects'\n    },\n    preferences: {\n      workType: 'freelance',\n      sideLocationInterest: ['Dammam', 'Khobar', 'Dhahran'],\n      companyNameInterest: ['Royal Furniture', 'Elite Woodworks'],\n      willingToRelocate: true,\n      remoteWork: false,\n      neededDocuments: ['Work Permit', 'Carpentry License', 'Portfolio'],\n      otherRequirements: 'Prefer luxury furniture projects'\n    },\n    isLiveStreaming: true,\n    liveStreamUrl: 'https://stream.example.com/khalid-live',\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    isVerified: true,\n    rating: 4.9,\n    totalReviews: 31\n  }\n]\n\nexport function WorkersProvider({ children }: { children: ReactNode }) {\n  const [workers, setWorkers] = useState<WorkerProfile[]>([])\n  const [currentWorkerProfile, setCurrentWorkerProfile] = useState<WorkerProfile>()\n\n  useEffect(() => {\n    try {\n      // Load workers from localStorage or use mock data\n      const savedWorkers = localStorage.getItem('kaazmaamaa-workers')\n      if (savedWorkers) {\n        const parsedWorkers = JSON.parse(savedWorkers)\n        // Validate the data structure\n        if (Array.isArray(parsedWorkers) && parsedWorkers.length > 0) {\n          setWorkers(parsedWorkers)\n        } else {\n          // Use mock data if saved data is invalid\n          setWorkers(mockWorkers)\n          localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))\n        }\n      } else {\n        // Use mock data for first time\n        setWorkers(mockWorkers)\n        localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))\n      }\n    } catch (error) {\n      console.error('Error loading workers:', error)\n      // Fallback to mock data if there's an error\n      setWorkers(mockWorkers)\n      localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))\n    }\n  }, [])\n\n  const saveToStorage = (updatedWorkers: WorkerProfile[]) => {\n    localStorage.setItem('kaazmaamaa-workers', JSON.stringify(updatedWorkers))\n  }\n\n  const createWorkerProfile = (profile: Omit<WorkerProfile, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProfile: WorkerProfile = {\n      ...profile,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    const updatedWorkers = [...workers, newProfile]\n    setWorkers(updatedWorkers)\n    saveToStorage(updatedWorkers)\n    setCurrentWorkerProfile(newProfile)\n  }\n\n  const updateWorkerProfile = (id: string, updates: Partial<WorkerProfile>) => {\n    const updatedWorkers = workers.map(worker =>\n      worker.id === id\n        ? { ...worker, ...updates, updatedAt: new Date().toISOString() }\n        : worker\n    )\n    setWorkers(updatedWorkers)\n    saveToStorage(updatedWorkers)\n  }\n\n  const updateWorker = (profile: WorkerProfile) => {\n    const existingWorkerIndex = workers.findIndex(w => w.userId === profile.userId)\n\n    if (existingWorkerIndex >= 0) {\n      // Update existing worker\n      const updatedWorkers = [...workers]\n      updatedWorkers[existingWorkerIndex] = {\n        ...profile,\n        updatedAt: new Date().toISOString()\n      }\n      setWorkers(updatedWorkers)\n      saveToStorage(updatedWorkers)\n    } else {\n      // Create new worker if doesn't exist\n      const newProfile: WorkerProfile = {\n        ...profile,\n        id: profile.id || Date.now().toString(),\n        createdAt: profile.createdAt || new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }\n      const updatedWorkers = [...workers, newProfile]\n      setWorkers(updatedWorkers)\n      saveToStorage(updatedWorkers)\n    }\n  }\n\n  const addDocument = (workerId: string, document: Omit<WorkerDocument, 'id' | 'uploadedAt'>) => {\n    const newDocument: WorkerDocument = {\n      ...document,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateWorkerProfile(workerId, {\n      documents: [...(workers.find(w => w.id === workerId)?.documents || []), newDocument]\n    })\n  }\n\n  const addVideo = (workerId: string, video: Omit<WorkerVideo, 'id' | 'uploadedAt'>) => {\n    const newVideo: WorkerVideo = {\n      ...video,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateWorkerProfile(workerId, {\n      videos: [...(workers.find(w => w.id === workerId)?.videos || []), newVideo]\n    })\n  }\n\n  const updateLifecycle = (workerId: string, lifecycle: Partial<WorkerLifecycle>) => {\n    const worker = workers.find(w => w.id === workerId)\n    if (worker) {\n      updateWorkerProfile(workerId, {\n        lifecycle: { ...worker.lifecycle, ...lifecycle }\n      })\n    }\n  }\n\n  const startLiveStream = (workerId: string, streamUrl: string) => {\n    updateWorkerProfile(workerId, {\n      isLiveStreaming: true,\n      liveStreamUrl: streamUrl\n    })\n  }\n\n  const stopLiveStream = (workerId: string) => {\n    updateWorkerProfile(workerId, {\n      isLiveStreaming: false,\n      liveStreamUrl: undefined\n    })\n  }\n\n  const getWorkersBySpecialty = (specialty: string) => {\n    return workers.filter(worker =>\n      worker.professionalInfo.specialty.toLowerCase().includes(specialty.toLowerCase())\n    )\n  }\n\n  const getAvailableWorkers = () => {\n    return workers.filter(worker =>\n      worker.lifecycle.status === 'available' || worker.lifecycle.status === 'seeking'\n    )\n  }\n\n  const getWorkersByCategory = (category: string) => {\n    return workers.filter(worker =>\n      worker.professionalInfo.workerCategory.toLowerCase().includes(category.toLowerCase())\n    )\n  }\n\n  const validateWorkerIdUniqueness = (workerId: string, excludeId?: string) => {\n    return !workers.some(worker =>\n      worker.id === workerId && worker.id !== excludeId\n    )\n  }\n\n  const validateIqamaDocument = async (iqamaNumber: string): Promise<boolean> => {\n    // Mock validation - in real app, this would call government API\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        // Simple validation: check if it's 10 digits\n        const isValid = /^\\d{10}$/.test(iqamaNumber)\n        resolve(isValid)\n      }, 1000)\n    })\n  }\n\n  const value = {\n    workers,\n    currentWorkerProfile,\n    createWorkerProfile,\n    updateWorkerProfile,\n    updateWorker,\n    addDocument,\n    addVideo,\n    updateLifecycle,\n    startLiveStream,\n    stopLiveStream,\n    getWorkersBySpecialty,\n    getWorkersByCategory,\n    getAvailableWorkers,\n    validateWorkerIdUniqueness,\n    validateIqamaDocument\n  }\n\n  return (\n    <WorkersContext.Provider value={value}>\n      {children}\n    </WorkersContext.Provider>\n  )\n}\n\nexport function useWorkers() {\n  const context = useContext(WorkersContext)\n  if (context === undefined) {\n    throw new Error('useWorkers must be used within a WorkersProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuHA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAErE,8BAA8B;AAC9B,MAAM,cAA+B;IACnC;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,YAAY;YACZ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,oBAAoB;YAClB,aAAa;YACb,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,gBAAgB;YAChB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;gBAAC;gBAA2B;gBAAe;gBAAmB;aAAmB;YACzF,WAAW;gBAAC;gBAAU;aAAU;YAChC,uBAAuB;YACvB,UAAU;YACV,sBAAsB;YACtB,cAAc;QAChB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;SACD;QACD,WAAW;YACT,QAAQ;YACR,OAAO;QACT;QACA,aAAa;YACX,UAAU;YACV,sBAAsB;gBAAC;gBAAU;gBAAU;aAAS;YACpD,qBAAqB;gBAAC;gBAA0B;gBAAU;aAAQ;YAClE,mBAAmB;YACnB,YAAY;YACZ,iBAAiB;gBAAC;gBAAe;gBAAsB;aAAoB;YAC3E,mBAAmB;QACrB;QACA,iBAAiB;QACjB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,QAAQ;QACR,cAAc;IAChB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,YAAY;YACZ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,oBAAoB;YAClB,aAAa;YACb,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,gBAAgB;YAChB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;gBAAC;gBAAqB;gBAAuB;gBAAY;aAAoB;YACrF,WAAW;gBAAC;gBAAU;gBAAW;aAAO;YACxC,uBAAuB;YACvB,UAAU;YACV,sBAAsB;YACtB,cAAc;QAChB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;SACD;QACD,WAAW;YACT,QAAQ;YACR,OAAO;QACT;QACA,aAAa;YACX,UAAU;YACV,sBAAsB;gBAAC;gBAAU;gBAAS;aAAO;YACjD,qBAAqB;gBAAC;gBAAuB;aAAyB;YACtE,mBAAmB;YACnB,YAAY;YACZ,iBAAiB;gBAAC;gBAAe;aAAmB;YACpD,mBAAmB;QACrB;QACA,iBAAiB;QACjB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,QAAQ;QACR,cAAc;IAChB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,YAAY;YACZ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,oBAAoB;YAClB,aAAa;YACb,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,gBAAgB;YAChB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;gBAAC;gBAAoB;gBAAkB;gBAAkB;aAAc;YAC/E,WAAW;gBAAC;gBAAU;aAAU;YAChC,uBAAuB;YACvB,UAAU;YACV,sBAAsB;YACtB,cAAc;QAChB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;SACD;QACD,WAAW;YACT,QAAQ;YACR,OAAO;QACT;QACA,aAAa;YACX,UAAU;YACV,sBAAsB;gBAAC;gBAAU;gBAAU;aAAU;YACrD,qBAAqB;gBAAC;gBAAmB;aAAkB;YAC3D,mBAAmB;YACnB,YAAY;YACZ,iBAAiB;gBAAC;gBAAe;gBAAqB;aAAY;YAClE,mBAAmB;QACrB;QACA,iBAAiB;QACjB,eAAe;QACf,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,QAAQ;QACR,cAAc;IAChB;CACD;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,kDAAkD;YAClD,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC;gBACjC,8BAA8B;gBAC9B,IAAI,MAAM,OAAO,CAAC,kBAAkB,cAAc,MAAM,GAAG,GAAG;oBAC5D,WAAW;gBACb,OAAO;oBACL,yCAAyC;oBACzC,WAAW;oBACX,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;gBAC5D;YACF,OAAO;gBACL,+BAA+B;gBAC/B,WAAW;gBACX,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,4CAA4C;YAC5C,WAAW;YACX,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;QAC5D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;IAC5D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,aAA4B;YAChC,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,iBAAiB;eAAI;YAAS;SAAW;QAC/C,WAAW;QACX,cAAc;QACd,wBAAwB;IAC1B;IAEA,MAAM,sBAAsB,CAAC,IAAY;QACvC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SACjC,OAAO,EAAE,KAAK,KACV;gBAAE,GAAG,MAAM;gBAAE,GAAG,OAAO;gBAAE,WAAW,IAAI,OAAO,WAAW;YAAG,IAC7D;QAEN,WAAW;QACX,cAAc;IAChB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,sBAAsB,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QAE9E,IAAI,uBAAuB,GAAG;YAC5B,yBAAyB;YACzB,MAAM,iBAAiB;mBAAI;aAAQ;YACnC,cAAc,CAAC,oBAAoB,GAAG;gBACpC,GAAG,OAAO;gBACV,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,WAAW;YACX,cAAc;QAChB,OAAO;YACL,qCAAqC;YACrC,MAAM,aAA4B;gBAChC,GAAG,OAAO;gBACV,IAAI,QAAQ,EAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACrC,WAAW,QAAQ,SAAS,IAAI,IAAI,OAAO,WAAW;gBACtD,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,MAAM,iBAAiB;mBAAI;gBAAS;aAAW;YAC/C,WAAW;YACX,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAC,UAAkB;QACrC,MAAM,cAA8B;YAClC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,oBAAoB,UAAU;YAC5B,WAAW;mBAAK,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,aAAa,EAAE;gBAAG;aAAY;QACtF;IACF;IAEA,MAAM,WAAW,CAAC,UAAkB;QAClC,MAAM,WAAwB;YAC5B,GAAG,KAAK;YACR,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,oBAAoB,UAAU;YAC5B,QAAQ;mBAAK,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,UAAU,EAAE;gBAAG;aAAS;QAC7E;IACF;IAEA,MAAM,kBAAkB,CAAC,UAAkB;QACzC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,oBAAoB,UAAU;gBAC5B,WAAW;oBAAE,GAAG,OAAO,SAAS;oBAAE,GAAG,SAAS;gBAAC;YACjD;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC,UAAkB;QACzC,oBAAoB,UAAU;YAC5B,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB,UAAU;YAC5B,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,gBAAgB,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,WAAW;IAElF;IAEA,MAAM,sBAAsB;QAC1B,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,SAAS,CAAC,MAAM,KAAK,eAAe,OAAO,SAAS,CAAC,MAAM,KAAK;IAE3E;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,gBAAgB,CAAC,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAEtF;IAEA,MAAM,6BAA6B,CAAC,UAAkB;QACpD,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAA,SACnB,OAAO,EAAE,KAAK,YAAY,OAAO,EAAE,KAAK;IAE5C;IAEA,MAAM,wBAAwB,OAAO;QACnC,gEAAgE;QAChE,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,6CAA6C;gBAC7C,MAAM,UAAU,WAAW,IAAI,CAAC;gBAChC,QAAQ;YACV,GAAG;QACL;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/SuppliersContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\nexport interface SupplierDocument {\n  id: string\n  name: string\n  type: 'licence' | 'cr' | 'contract' | 'other'\n  url: string\n  uploadedAt: string\n}\n\nexport interface JobDemand {\n  id: string\n  category: string\n  position: string\n  description: string\n  requirements: string[]\n  salaryPerHour: number\n  currency: 'SAR' | 'USD' | 'EUR'\n  paymentDuration: 'daily' | 'weekly' | 'monthly' | 'project_based'\n  location: string\n  urgency: 'immediate' | 'within_week' | 'within_month'\n  documentsRequired: string[]\n  createdAt: string\n}\n\nexport interface SupplierProfile {\n  id: string // Must be unique\n  userId: string\n  personalInfo: {\n    supplierName: string\n    nationality: string\n    kofilName: string\n    kofilPhone: string\n    profileImage?: string\n    address: string\n    phone: string\n    whatsapp: string\n    email: string\n  }\n  businessInfo: {\n    companyName: string\n    crNumber: string // Commercial Registration Number\n    licenceNumber: string // Must be unique\n    businessType: string\n    establishedYear: number\n    employeeCount: string\n  }\n  documents: SupplierDocument[]\n  jobDemands: JobDemand[]\n  videos: {\n    id: string\n    title: string\n    type: 'company_intro' | 'job_requirements' | 'workplace_tour'\n    url: string\n    thumbnail?: string\n    duration?: number\n    uploadedAt: string\n  }[]\n  isLiveStreaming: boolean\n  liveStreamUrl?: string\n  operatingLocations: string[]\n  preferredWorkerCategories: string[]\n  rating: number\n  totalReviews: number\n  isVerified: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface SuppliersContextType {\n  suppliers: SupplierProfile[]\n  currentSupplierProfile?: SupplierProfile\n  createSupplierProfile: (profile: Omit<SupplierProfile, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateSupplierProfile: (id: string, updates: Partial<SupplierProfile>) => void\n  addDocument: (supplierId: string, document: Omit<SupplierDocument, 'id' | 'uploadedAt'>) => void\n  addJobDemand: (supplierId: string, jobDemand: Omit<JobDemand, 'id' | 'createdAt'>) => void\n  updateJobDemand: (supplierId: string, jobDemandId: string, updates: Partial<JobDemand>) => void\n  deleteJobDemand: (supplierId: string, jobDemandId: string) => void\n  startLiveStream: (supplierId: string, streamUrl: string) => void\n  stopLiveStream: (supplierId: string) => void\n  getSuppliersByLocation: (location: string) => SupplierProfile[]\n  getJobDemandsByCategory: (category: string) => { supplier: SupplierProfile, jobDemand: JobDemand }[]\n  validateLicenceUniqueness: (licenceNumber: string, excludeId?: string) => boolean\n}\n\nconst SuppliersContext = createContext<SuppliersContextType | undefined>(undefined)\n\n// Mock data for demonstration\nconst mockSuppliers: SupplierProfile[] = [\n  {\n    id: 'supplier-1',\n    userId: 'user-supplier-1',\n    personalInfo: {\n      supplierName: 'Ahmed Construction Supplier',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Ahmed Al-Rashid',\n      kofilPhone: '+966501234567',\n      address: 'King Fahd Road, Riyadh 12345',\n      phone: '+966501234567',\n      whatsapp: '+966501234567',\n      email: '<EMAIL>'\n    },\n    businessInfo: {\n      companyName: 'Al-Rashid Construction Supply Co.',\n      crNumber: 'CR-1010123456',\n      licenceNumber: 'LIC-2024-001',\n      businessType: 'Construction Supply',\n      establishedYear: 2015,\n      employeeCount: '50-100'\n    },\n    documents: [\n      {\n        id: 'doc-1',\n        name: 'Business Licence',\n        type: 'licence',\n        url: '/documents/business-licence.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-2',\n        name: 'Commercial Registration',\n        type: 'cr',\n        url: '/documents/cr-certificate.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    jobDemands: [\n      {\n        id: 'job-1',\n        category: 'Electrical Work',\n        position: 'Senior Electrician',\n        description: 'Looking for experienced electricians for large construction project',\n        requirements: ['5+ years experience', 'Electrical license', 'Safety certification'],\n        salaryPerHour: 85,\n        currency: 'SAR',\n        paymentDuration: 'weekly',\n        location: 'Riyadh',\n        urgency: 'immediate',\n        documentsRequired: ['Iqama', 'Electrical License', 'Medical Certificate'],\n        createdAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-1',\n        title: 'Company Introduction',\n        type: 'company_intro',\n        url: '/videos/company-intro.mp4',\n        duration: 180,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    isLiveStreaming: false,\n    operatingLocations: ['Riyadh', 'Jeddah', 'Dammam'],\n    preferredWorkerCategories: ['Electrical Work', 'Plumbing', 'Construction'],\n    rating: 4.7,\n    totalReviews: 23,\n    isVerified: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }\n]\n\nexport function SuppliersProvider({ children }: { children: ReactNode }) {\n  const [suppliers, setSuppliers] = useState<SupplierProfile[]>([])\n  const [currentSupplierProfile, setCurrentSupplierProfile] = useState<SupplierProfile>()\n\n  useEffect(() => {\n    // Load suppliers from localStorage or use mock data\n    const savedSuppliers = localStorage.getItem('kaazmaamaa-suppliers')\n    if (savedSuppliers) {\n      setSuppliers(JSON.parse(savedSuppliers))\n    } else {\n      setSuppliers(mockSuppliers)\n      localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(mockSuppliers))\n    }\n  }, [])\n\n  const saveToStorage = (updatedSuppliers: SupplierProfile[]) => {\n    localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(updatedSuppliers))\n  }\n\n  const createSupplierProfile = (profile: Omit<SupplierProfile, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProfile: SupplierProfile = {\n      ...profile,\n      id: `supplier-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    const updatedSuppliers = [...suppliers, newProfile]\n    setSuppliers(updatedSuppliers)\n    saveToStorage(updatedSuppliers)\n    setCurrentSupplierProfile(newProfile)\n  }\n\n  const updateSupplierProfile = (id: string, updates: Partial<SupplierProfile>) => {\n    const updatedSuppliers = suppliers.map(supplier =>\n      supplier.id === id\n        ? { ...supplier, ...updates, updatedAt: new Date().toISOString() }\n        : supplier\n    )\n    setSuppliers(updatedSuppliers)\n    saveToStorage(updatedSuppliers)\n  }\n\n  const addDocument = (supplierId: string, document: Omit<SupplierDocument, 'id' | 'uploadedAt'>) => {\n    const newDocument: SupplierDocument = {\n      ...document,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateSupplierProfile(supplierId, {\n      documents: [...(suppliers.find(s => s.id === supplierId)?.documents || []), newDocument]\n    })\n  }\n\n  const addJobDemand = (supplierId: string, jobDemand: Omit<JobDemand, 'id' | 'createdAt'>) => {\n    const newJobDemand: JobDemand = {\n      ...jobDemand,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString()\n    }\n\n    updateSupplierProfile(supplierId, {\n      jobDemands: [...(suppliers.find(s => s.id === supplierId)?.jobDemands || []), newJobDemand]\n    })\n  }\n\n  const updateJobDemand = (supplierId: string, jobDemandId: string, updates: Partial<JobDemand>) => {\n    const supplier = suppliers.find(s => s.id === supplierId)\n    if (supplier) {\n      const updatedJobDemands = supplier.jobDemands.map(job =>\n        job.id === jobDemandId ? { ...job, ...updates } : job\n      )\n      updateSupplierProfile(supplierId, { jobDemands: updatedJobDemands })\n    }\n  }\n\n  const deleteJobDemand = (supplierId: string, jobDemandId: string) => {\n    const supplier = suppliers.find(s => s.id === supplierId)\n    if (supplier) {\n      const updatedJobDemands = supplier.jobDemands.filter(job => job.id !== jobDemandId)\n      updateSupplierProfile(supplierId, { jobDemands: updatedJobDemands })\n    }\n  }\n\n  const startLiveStream = (supplierId: string, streamUrl: string) => {\n    updateSupplierProfile(supplierId, {\n      isLiveStreaming: true,\n      liveStreamUrl: streamUrl\n    })\n  }\n\n  const stopLiveStream = (supplierId: string) => {\n    updateSupplierProfile(supplierId, {\n      isLiveStreaming: false,\n      liveStreamUrl: undefined\n    })\n  }\n\n  const getSuppliersByLocation = (location: string) => {\n    return suppliers.filter(supplier => \n      supplier.operatingLocations.some(loc => \n        loc.toLowerCase().includes(location.toLowerCase())\n      )\n    )\n  }\n\n  const getJobDemandsByCategory = (category: string) => {\n    const results: { supplier: SupplierProfile, jobDemand: JobDemand }[] = []\n    suppliers.forEach(supplier => {\n      supplier.jobDemands.forEach(jobDemand => {\n        if (jobDemand.category.toLowerCase().includes(category.toLowerCase())) {\n          results.push({ supplier, jobDemand })\n        }\n      })\n    })\n    return results\n  }\n\n  const validateLicenceUniqueness = (licenceNumber: string, excludeId?: string) => {\n    return !suppliers.some(supplier => \n      supplier.businessInfo.licenceNumber === licenceNumber && supplier.id !== excludeId\n    )\n  }\n\n  const value = {\n    suppliers,\n    currentSupplierProfile,\n    createSupplierProfile,\n    updateSupplierProfile,\n    addDocument,\n    addJobDemand,\n    updateJobDemand,\n    deleteJobDemand,\n    startLiveStream,\n    stopLiveStream,\n    getSuppliersByLocation,\n    getJobDemandsByCategory,\n    validateLicenceUniqueness\n  }\n\n  return (\n    <SuppliersContext.Provider value={value}>\n      {children}\n    </SuppliersContext.Provider>\n  )\n}\n\nexport function useSuppliers() {\n  const context = useContext(SuppliersContext)\n  if (context === undefined) {\n    throw new Error('useSuppliers must be used within a SuppliersProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuFA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAEzE,8BAA8B;AAC9B,MAAM,gBAAmC;IACvC;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,cAAc;YACd,aAAa;YACb,WAAW;YACX,YAAY;YACZ,SAAS;YACT,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA,cAAc;YACZ,aAAa;YACb,UAAU;YACV,eAAe;YACf,cAAc;YACd,iBAAiB;YACjB,eAAe;QACjB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,YAAY;YACV;gBACE,IAAI;gBACJ,UAAU;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;oBAAC;oBAAuB;oBAAsB;iBAAuB;gBACnF,eAAe;gBACf,UAAU;gBACV,iBAAiB;gBACjB,UAAU;gBACV,SAAS;gBACT,mBAAmB;oBAAC;oBAAS;oBAAsB;iBAAsB;gBACzE,WAAW,IAAI,OAAO,WAAW;YACnC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,iBAAiB;QACjB,oBAAoB;YAAC;YAAU;YAAU;SAAS;QAClD,2BAA2B;YAAC;YAAmB;YAAY;SAAe;QAC1E,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,aAAa,KAAK,KAAK,CAAC;QAC1B,OAAO;YACL,aAAa;YACb,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;QAC9D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;IAC9D;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,aAA8B;YAClC,GAAG,OAAO;YACV,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,mBAAmB;eAAI;YAAW;SAAW;QACnD,aAAa;QACb,cAAc;QACd,0BAA0B;IAC5B;IAEA,MAAM,wBAAwB,CAAC,IAAY;QACzC,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAA,WACrC,SAAS,EAAE,KAAK,KACZ;gBAAE,GAAG,QAAQ;gBAAE,GAAG,OAAO;gBAAE,WAAW,IAAI,OAAO,WAAW;YAAG,IAC/D;QAEN,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,cAAc,CAAC,YAAoB;QACvC,MAAM,cAAgC;YACpC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,sBAAsB,YAAY;YAChC,WAAW;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,aAAa,EAAE;gBAAG;aAAY;QAC1F;IACF;IAEA,MAAM,eAAe,CAAC,YAAoB;QACxC,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,sBAAsB,YAAY;YAChC,YAAY;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,cAAc,EAAE;gBAAG;aAAa;QAC7F;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB,aAAqB;QAChE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,IAAI,UAAU;YACZ,MAAM,oBAAoB,SAAS,UAAU,CAAC,GAAG,CAAC,CAAA,MAChD,IAAI,EAAE,KAAK,cAAc;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEpD,sBAAsB,YAAY;gBAAE,YAAY;YAAkB;QACpE;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB;QAC3C,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,IAAI,UAAU;YACZ,MAAM,oBAAoB,SAAS,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACvE,sBAAsB,YAAY;gBAAE,YAAY;YAAkB;QACpE;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB;QAC3C,sBAAsB,YAAY;YAChC,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,YAAY;YAChC,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAO,UAAU,MAAM,CAAC,CAAA,WACtB,SAAS,kBAAkB,CAAC,IAAI,CAAC,CAAA,MAC/B,IAAI,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAGrD;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,UAAiE,EAAE;QACzE,UAAU,OAAO,CAAC,CAAA;YAChB,SAAS,UAAU,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,UAAU,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,KAAK;oBACrE,QAAQ,IAAI,CAAC;wBAAE;wBAAU;oBAAU;gBACrC;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAM,4BAA4B,CAAC,eAAuB;QACxD,OAAO,CAAC,UAAU,IAAI,CAAC,CAAA,WACrB,SAAS,YAAY,CAAC,aAAa,KAAK,iBAAiB,SAAS,EAAE,KAAK;IAE7E;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/CompaniesContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface CompanyDocument {\n  id: string\n  name: string\n  type: 'licence' | 'cr' | 'tax_certificate' | 'establishment_card' | 'insurance' | 'contract' | 'other'\n  url: string\n  uploadedAt: string\n}\n\nexport interface JobOpening {\n  id: string\n  title: string\n  department: string\n  category: string\n  description: string\n  requirements: string[]\n  salaryRange: {\n    min: number\n    max: number\n    currency: 'SAR' | 'USD' | 'EUR'\n  }\n  employmentType: 'full_time' | 'part_time' | 'contract' | 'internship'\n  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive'\n  location: string\n  isRemote: boolean\n  benefits: string[]\n  applicationDeadline?: string\n  urgency: 'immediate' | 'within_week' | 'within_month'\n  documentsRequired: string[]\n  createdAt: string\n  applicationsCount: number\n}\n\nexport interface CompanyProfile {\n  id: string // Must be unique\n  userId: string\n  companyInfo: {\n    companyName: string\n    tradeName?: string\n    industry: string\n    companySize: '1-10' | '11-50' | '51-200' | '201-500' | '501-1000' | '1000+'\n    foundedYear: number\n    headquarters: string\n    website?: string\n    description: string\n    mission?: string\n    vision?: string\n  }\n  contactInfo: {\n    hrManagerName: string\n    hrEmail: string\n    hrPhone: string\n    hrWhatsapp: string\n    companyPhone: string\n    companyEmail: string\n    address: string\n    poBox?: string\n  }\n  legalInfo: {\n    crNumber: string // Commercial Registration Number\n    taxNumber: string\n    licenceNumber: string // Must be unique\n    establishmentNumber?: string\n    chamberMembership?: string\n  }\n  documents: CompanyDocument[]\n  jobOpenings: JobOpening[]\n  videos: {\n    id: string\n    title: string\n    type: 'company_intro' | 'workplace_culture' | 'job_opportunities' | 'employee_testimonials'\n    url: string\n    thumbnail?: string\n    duration?: number\n    uploadedAt: string\n  }[]\n  isLiveStreaming: boolean\n  liveStreamUrl?: string\n  operatingLocations: string[]\n  preferredWorkerCategories: string[]\n  companyBenefits: string[]\n  workEnvironment: {\n    isRemoteFriendly: boolean\n    hasFlexibleHours: boolean\n    providesTraining: boolean\n    hasCareerGrowth: boolean\n    workLifeBalance: number // 1-5 rating\n  }\n  rating: number\n  totalReviews: number\n  totalEmployees: number\n  isVerified: boolean\n  isHiring: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface CompaniesContextType {\n  companies: CompanyProfile[]\n  currentCompanyProfile?: CompanyProfile\n  createCompanyProfile: (profile: Omit<CompanyProfile, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateCompanyProfile: (id: string, updates: Partial<CompanyProfile>) => void\n  addDocument: (companyId: string, document: Omit<CompanyDocument, 'id' | 'uploadedAt'>) => void\n  addJobOpening: (companyId: string, jobOpening: Omit<JobOpening, 'id' | 'createdAt' | 'applicationsCount'>) => void\n  updateJobOpening: (companyId: string, jobOpeningId: string, updates: Partial<JobOpening>) => void\n  deleteJobOpening: (companyId: string, jobOpeningId: string) => void\n  startLiveStream: (companyId: string, streamUrl: string) => void\n  stopLiveStream: (companyId: string) => void\n  getCompaniesByLocation: (location: string) => CompanyProfile[]\n  getCompaniesByIndustry: (industry: string) => CompanyProfile[]\n  getJobOpeningsByCategory: (category: string) => { company: CompanyProfile, jobOpening: JobOpening }[]\n  getHiringCompanies: () => CompanyProfile[]\n  validateLicenceUniqueness: (licenceNumber: string, excludeId?: string) => boolean\n}\n\nconst CompaniesContext = createContext<CompaniesContextType | undefined>(undefined)\n\n// Mock data for demonstration\nconst mockCompanies: CompanyProfile[] = [\n  {\n    id: 'company-1',\n    userId: 'user-company-1',\n    companyInfo: {\n      companyName: 'Saudi Tech Solutions',\n      tradeName: 'STS',\n      industry: 'Information Technology',\n      companySize: '201-500',\n      foundedYear: 2010,\n      headquarters: 'Riyadh, Saudi Arabia',\n      website: 'https://sauditechsolutions.com',\n      description: 'Leading technology solutions provider in the Kingdom of Saudi Arabia',\n      mission: 'To provide innovative technology solutions that drive digital transformation',\n      vision: 'To be the leading technology partner in the Middle East'\n    },\n    contactInfo: {\n      hrManagerName: 'Sarah Al-Mahmoud',\n      hrEmail: '<EMAIL>',\n      hrPhone: '+966112345678',\n      hrWhatsapp: '+966501234567',\n      companyPhone: '+966112345678',\n      companyEmail: '<EMAIL>',\n      address: 'King Fahd Road, Olaya District, Riyadh 12345',\n      poBox: 'P.O. Box 12345, Riyadh 11564'\n    },\n    legalInfo: {\n      crNumber: 'CR-**********',\n      taxNumber: 'TAX-300123456789003',\n      licenceNumber: 'LIC-TECH-2024-001',\n      establishmentNumber: 'EST-*********',\n      chamberMembership: 'CHAMBER-RUH-2024-456'\n    },\n    documents: [\n      {\n        id: 'doc-1',\n        name: 'Commercial Registration',\n        type: 'cr',\n        url: '/documents/company-cr.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-2',\n        name: 'Business License',\n        type: 'licence',\n        url: '/documents/company-license.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    jobOpenings: [\n      {\n        id: 'job-1',\n        title: 'Senior Software Engineer',\n        department: 'Engineering',\n        category: 'Software Development',\n        description: 'We are looking for a senior software engineer to join our growing team',\n        requirements: ['5+ years experience', 'React/Node.js', 'AWS knowledge', 'Team leadership'],\n        salaryRange: {\n          min: 15000,\n          max: 25000,\n          currency: 'SAR'\n        },\n        employmentType: 'full_time',\n        experienceLevel: 'senior',\n        location: 'Riyadh',\n        isRemote: false,\n        benefits: ['Health Insurance', 'Annual Bonus', 'Training Budget', 'Flexible Hours'],\n        urgency: 'immediate',\n        documentsRequired: ['CV', 'Portfolio', 'Certificates'],\n        createdAt: new Date().toISOString(),\n        applicationsCount: 23\n      }\n    ],\n    videos: [\n      {\n        id: 'video-1',\n        title: 'Company Culture Overview',\n        type: 'company_intro',\n        url: '/videos/company-culture.mp4',\n        duration: 240,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    isLiveStreaming: false,\n    operatingLocations: ['Riyadh', 'Jeddah', 'Dammam'],\n    preferredWorkerCategories: ['Software Development', 'Data Analysis', 'Project Management'],\n    companyBenefits: ['Health Insurance', 'Annual Bonus', 'Training Programs', 'Career Development'],\n    workEnvironment: {\n      isRemoteFriendly: true,\n      hasFlexibleHours: true,\n      providesTraining: true,\n      hasCareerGrowth: true,\n      workLifeBalance: 4\n    },\n    rating: 4.6,\n    totalReviews: 87,\n    totalEmployees: 350,\n    isVerified: true,\n    isHiring: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }\n]\n\nexport function CompaniesProvider({ children }: { children: ReactNode }) {\n  const [companies, setCompanies] = useState<CompanyProfile[]>([])\n  const [currentCompanyProfile, setCurrentCompanyProfile] = useState<CompanyProfile>()\n\n  useEffect(() => {\n    // Load companies from localStorage or use mock data\n    const savedCompanies = localStorage.getItem('kaazmaamaa-companies')\n    if (savedCompanies) {\n      setCompanies(JSON.parse(savedCompanies))\n    } else {\n      setCompanies(mockCompanies)\n      localStorage.setItem('kaazmaamaa-companies', JSON.stringify(mockCompanies))\n    }\n  }, [])\n\n  const saveToStorage = useCallback((updatedCompanies: CompanyProfile[]) => {\n    localStorage.setItem('kaazmaamaa-companies', JSON.stringify(updatedCompanies))\n  }, [])\n\n  const createCompanyProfile = useCallback((profile: Omit<CompanyProfile, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProfile: CompanyProfile = {\n      ...profile,\n      id: `company-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    const updatedCompanies = [...companies, newProfile]\n    setCompanies(updatedCompanies)\n    saveToStorage(updatedCompanies)\n    setCurrentCompanyProfile(newProfile)\n  }, [companies, saveToStorage])\n\n  const updateCompanyProfile = useCallback((id: string, updates: Partial<CompanyProfile>) => {\n    const updatedCompanies = companies.map(company =>\n      company.id === id\n        ? { ...company, ...updates, updatedAt: new Date().toISOString() }\n        : company\n    )\n    setCompanies(updatedCompanies)\n    saveToStorage(updatedCompanies)\n  }, [companies, saveToStorage])\n\n  const addDocument = useCallback((companyId: string, document: Omit<CompanyDocument, 'id' | 'uploadedAt'>) => {\n    const newDocument: CompanyDocument = {\n      ...document,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateCompanyProfile(companyId, {\n      documents: [...(companies.find(c => c.id === companyId)?.documents || []), newDocument]\n    })\n  }, [companies, updateCompanyProfile])\n\n  const addJobOpening = useCallback((companyId: string, jobOpening: Omit<JobOpening, 'id' | 'createdAt' | 'applicationsCount'>) => {\n    const newJobOpening: JobOpening = {\n      ...jobOpening,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString(),\n      applicationsCount: 0\n    }\n\n    updateCompanyProfile(companyId, {\n      jobOpenings: [...(companies.find(c => c.id === companyId)?.jobOpenings || []), newJobOpening]\n    })\n  }, [companies, updateCompanyProfile])\n\n  const updateJobOpening = useCallback((companyId: string, jobOpeningId: string, updates: Partial<JobOpening>) => {\n    const company = companies.find(c => c.id === companyId)\n    if (company) {\n      const updatedJobOpenings = company.jobOpenings.map(job =>\n        job.id === jobOpeningId ? { ...job, ...updates } : job\n      )\n      updateCompanyProfile(companyId, { jobOpenings: updatedJobOpenings })\n    }\n  }, [companies, updateCompanyProfile])\n\n  const deleteJobOpening = useCallback((companyId: string, jobOpeningId: string) => {\n    const company = companies.find(c => c.id === companyId)\n    if (company) {\n      const updatedJobOpenings = company.jobOpenings.filter(job => job.id !== jobOpeningId)\n      updateCompanyProfile(companyId, { jobOpenings: updatedJobOpenings })\n    }\n  }, [companies, updateCompanyProfile])\n\n  const startLiveStream = useCallback((companyId: string, streamUrl: string) => {\n    updateCompanyProfile(companyId, {\n      isLiveStreaming: true,\n      liveStreamUrl: streamUrl\n    })\n  }, [updateCompanyProfile])\n\n  const stopLiveStream = useCallback((companyId: string) => {\n    updateCompanyProfile(companyId, {\n      isLiveStreaming: false,\n      liveStreamUrl: undefined\n    })\n  }, [updateCompanyProfile])\n\n  const getCompaniesByLocation = useCallback((location: string) => {\n    return companies.filter(company => \n      company.operatingLocations.some(loc => \n        loc.toLowerCase().includes(location.toLowerCase())\n      )\n    )\n  }, [companies])\n\n  const getCompaniesByIndustry = useCallback((industry: string) => {\n    return companies.filter(company => \n      company.companyInfo.industry.toLowerCase().includes(industry.toLowerCase())\n    )\n  }, [companies])\n\n  const getJobOpeningsByCategory = useCallback((category: string) => {\n    const results: { company: CompanyProfile, jobOpening: JobOpening }[] = []\n    companies.forEach(company => {\n      company.jobOpenings.forEach(jobOpening => {\n        if (jobOpening.category.toLowerCase().includes(category.toLowerCase())) {\n          results.push({ company, jobOpening })\n        }\n      })\n    })\n    return results\n  }, [companies])\n\n  const getHiringCompanies = useCallback(() => {\n    return companies.filter(company => company.isHiring && company.jobOpenings.length > 0)\n  }, [companies])\n\n  const validateLicenceUniqueness = useCallback((licenceNumber: string, excludeId?: string) => {\n    return !companies.some(company => \n      company.legalInfo.licenceNumber === licenceNumber && company.id !== excludeId\n    )\n  }, [companies])\n\n  const value = {\n    companies,\n    currentCompanyProfile,\n    createCompanyProfile,\n    updateCompanyProfile,\n    addDocument,\n    addJobOpening,\n    updateJobOpening,\n    deleteJobOpening,\n    startLiveStream,\n    stopLiveStream,\n    getCompaniesByLocation,\n    getCompaniesByIndustry,\n    getJobOpeningsByCategory,\n    getHiringCompanies,\n    validateLicenceUniqueness\n  }\n\n  return (\n    <CompaniesContext.Provider value={value}>\n      {children}\n    </CompaniesContext.Provider>\n  )\n}\n\nexport function useCompanies() {\n  const context = useContext(CompaniesContext)\n  if (context === undefined) {\n    throw new Error('useCompanies must be used within a CompaniesProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAsHA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAEzE,8BAA8B;AAC9B,MAAM,gBAAkC;IACtC;QACE,IAAI;QACJ,QAAQ;QACR,aAAa;YACX,aAAa;YACb,WAAW;YACX,UAAU;YACV,aAAa;YACb,aAAa;YACb,cAAc;YACd,SAAS;YACT,aAAa;YACb,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,eAAe;YACf,SAAS;YACT,SAAS;YACT,YAAY;YACZ,cAAc;YACd,cAAc;YACd,SAAS;YACT,OAAO;QACT;QACA,WAAW;YACT,UAAU;YACV,WAAW;YACX,eAAe;YACf,qBAAqB;YACrB,mBAAmB;QACrB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,aAAa;gBACb,cAAc;oBAAC;oBAAuB;oBAAiB;oBAAiB;iBAAkB;gBAC1F,aAAa;oBACX,KAAK;oBACL,KAAK;oBACL,UAAU;gBACZ;gBACA,gBAAgB;gBAChB,iBAAiB;gBACjB,UAAU;gBACV,UAAU;gBACV,UAAU;oBAAC;oBAAoB;oBAAgB;oBAAmB;iBAAiB;gBACnF,SAAS;gBACT,mBAAmB;oBAAC;oBAAM;oBAAa;iBAAe;gBACtD,WAAW,IAAI,OAAO,WAAW;gBACjC,mBAAmB;YACrB;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,iBAAiB;QACjB,oBAAoB;YAAC;YAAU;YAAU;SAAS;QAClD,2BAA2B;YAAC;YAAwB;YAAiB;SAAqB;QAC1F,iBAAiB;YAAC;YAAoB;YAAgB;YAAqB;SAAqB;QAChG,iBAAiB;YACf,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;YACjB,iBAAiB;QACnB;QACA,QAAQ;QACR,cAAc;QACd,gBAAgB;QAChB,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,aAAa,KAAK,KAAK,CAAC;QAC1B,OAAO;YACL,aAAa;YACb,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;QAC9D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;IAC9D,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,aAA6B;YACjC,GAAG,OAAO;YACV,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,mBAAmB;eAAI;YAAW;SAAW;QACnD,aAAa;QACb,cAAc;QACd,yBAAyB;IAC3B,GAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAY;QACpD,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAA,UACrC,QAAQ,EAAE,KAAK,KACX;gBAAE,GAAG,OAAO;gBAAE,GAAG,OAAO;gBAAE,WAAW,IAAI,OAAO,WAAW;YAAG,IAC9D;QAEN,aAAa;QACb,cAAc;IAChB,GAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QAClD,MAAM,cAA+B;YACnC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,qBAAqB,WAAW;YAC9B,WAAW;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,aAAa,EAAE;gBAAG;aAAY;QACzF;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACpD,MAAM,gBAA4B;YAChC,GAAG,UAAU;YACb,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,mBAAmB;QACrB;QAEA,qBAAqB,WAAW;YAC9B,aAAa;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,eAAe,EAAE;gBAAG;aAAc;QAC/F;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB,cAAsB;QAC7E,MAAM,UAAU,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,SAAS;YACX,MAAM,qBAAqB,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAA,MACjD,IAAI,EAAE,KAAK,eAAe;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAErD,qBAAqB,WAAW;gBAAE,aAAa;YAAmB;QACpE;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACvD,MAAM,UAAU,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,SAAS;YACX,MAAM,qBAAqB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACxE,qBAAqB,WAAW;gBAAE,aAAa;YAAmB;QACpE;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACtD,qBAAqB,WAAW;YAC9B,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAqB;IAEzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,qBAAqB,WAAW;YAC9B,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAqB;IAEzB,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,OAAO,UAAU,MAAM,CAAC,CAAA,UACtB,QAAQ,kBAAkB,CAAC,IAAI,CAAC,CAAA,MAC9B,IAAI,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAGrD,GAAG;QAAC;KAAU;IAEd,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,OAAO,UAAU,MAAM,CAAC,CAAA,UACtB,QAAQ,WAAW,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAE5E,GAAG;QAAC;KAAU;IAEd,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5C,MAAM,UAAiE,EAAE;QACzE,UAAU,OAAO,CAAC,CAAA;YAChB,QAAQ,WAAW,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,KAAK;oBACtE,QAAQ,IAAI,CAAC;wBAAE;wBAAS;oBAAW;gBACrC;YACF;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAU;IAEd,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG;IACtF,GAAG;QAAC;KAAU;IAEd,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,eAAuB;QACpE,OAAO,CAAC,UAAU,IAAI,CAAC,CAAA,UACrB,QAAQ,SAAS,CAAC,aAAa,KAAK,iBAAiB,QAAQ,EAAE,KAAK;IAExE,GAAG;QAAC;KAAU;IAEd,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/SocialContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface UserConnection {\n  userId: string\n  userName: string\n  userRole: string\n  connectedAt: string\n  isFollowing: boolean\n  isFollowedBy: boolean\n}\n\nexport interface ProfileInteraction {\n  userId: string\n  profileViews: number\n  lastViewedAt: string\n  totalLikes: number\n  totalComments: number\n  totalShares: number\n}\n\ninterface SocialContextType {\n  connections: UserConnection[]\n  profileInteractions: { [userId: string]: ProfileInteraction }\n  followUser: (userId: string, userName: string, userRole: string) => void\n  unfollowUser: (userId: string) => void\n  isFollowing: (userId: string) => boolean\n  getFollowers: (userId: string) => UserConnection[]\n  getFollowing: (userId: string) => UserConnection[]\n  recordProfileView: (userId: string) => void\n  getProfileStats: (userId: string) => ProfileInteraction\n  updateProfileInteraction: (userId: string, type: 'like' | 'comment' | 'share') => void\n}\n\nconst SocialContext = createContext<SocialContextType | undefined>(undefined)\n\nexport function SocialProvider({ children }: { children: ReactNode }) {\n  const [connections, setConnections] = useState<UserConnection[]>([])\n  const [profileInteractions, setProfileInteractions] = useState<{ [userId: string]: ProfileInteraction }>({})\n\n  useEffect(() => {\n    // Load social data from localStorage\n    const savedConnections = localStorage.getItem('kaazmaamaa-connections')\n    const savedInteractions = localStorage.getItem('kaazmaamaa-profile-interactions')\n    \n    if (savedConnections) {\n      setConnections(JSON.parse(savedConnections))\n    }\n    \n    if (savedInteractions) {\n      setProfileInteractions(JSON.parse(savedInteractions))\n    }\n  }, [])\n\n  const saveConnections = useCallback((updatedConnections: UserConnection[]) => {\n    localStorage.setItem('kaazmaamaa-connections', JSON.stringify(updatedConnections))\n  }, [])\n\n  const saveInteractions = useCallback((updatedInteractions: { [userId: string]: ProfileInteraction }) => {\n    localStorage.setItem('kaazmaamaa-profile-interactions', JSON.stringify(updatedInteractions))\n  }, [])\n\n  const followUser = useCallback((userId: string, userName: string, userRole: string) => {\n    const newConnection: UserConnection = {\n      userId,\n      userName,\n      userRole,\n      connectedAt: new Date().toISOString(),\n      isFollowing: true,\n      isFollowedBy: false\n    }\n\n    const updatedConnections = [...connections.filter(c => c.userId !== userId), newConnection]\n    setConnections(updatedConnections)\n    saveConnections(updatedConnections)\n  }, [connections, saveConnections])\n\n  const unfollowUser = useCallback((userId: string) => {\n    const updatedConnections = connections.filter(c => c.userId !== userId)\n    setConnections(updatedConnections)\n    saveConnections(updatedConnections)\n  }, [connections, saveConnections])\n\n  const isFollowing = useCallback((userId: string) => {\n    return connections.some(c => c.userId === userId && c.isFollowing)\n  }, [connections])\n\n  const getFollowers = useCallback((userId: string) => {\n    return connections.filter(c => c.userId === userId && c.isFollowedBy)\n  }, [connections])\n\n  const getFollowing = useCallback((userId: string) => {\n    return connections.filter(c => c.isFollowing)\n  }, [connections])\n\n  const recordProfileView = useCallback((userId: string) => {\n    setProfileInteractions(prev => {\n      const current = prev[userId] || {\n        userId,\n        profileViews: 0,\n        lastViewedAt: new Date().toISOString(),\n        totalLikes: 0,\n        totalComments: 0,\n        totalShares: 0\n      }\n\n      const updated = {\n        ...prev,\n        [userId]: {\n          ...current,\n          profileViews: current.profileViews + 1,\n          lastViewedAt: new Date().toISOString()\n        }\n      }\n\n      saveInteractions(updated)\n      return updated\n    })\n  }, [saveInteractions])\n\n  const getProfileStats = useCallback((userId: string): ProfileInteraction => {\n    return profileInteractions[userId] || {\n      userId,\n      profileViews: 0,\n      lastViewedAt: new Date().toISOString(),\n      totalLikes: 0,\n      totalComments: 0,\n      totalShares: 0\n    }\n  }, [profileInteractions])\n\n  const updateProfileInteraction = useCallback((userId: string, type: 'like' | 'comment' | 'share') => {\n    setProfileInteractions(prev => {\n      const current = prev[userId] || {\n        userId,\n        profileViews: 0,\n        lastViewedAt: new Date().toISOString(),\n        totalLikes: 0,\n        totalComments: 0,\n        totalShares: 0\n      }\n\n      const updated = {\n        ...prev,\n        [userId]: {\n          ...current,\n          [`total${type.charAt(0).toUpperCase() + type.slice(1)}s`]: \n            current[`total${type.charAt(0).toUpperCase() + type.slice(1)}s` as keyof ProfileInteraction] as number + 1\n        }\n      }\n\n      saveInteractions(updated)\n      return updated\n    })\n  }, [saveInteractions])\n\n  const value = {\n    connections,\n    profileInteractions,\n    followUser,\n    unfollowUser,\n    isFollowing,\n    getFollowers,\n    getFollowing,\n    recordProfileView,\n    getProfileStats,\n    updateProfileInteraction\n  }\n\n  return (\n    <SocialContext.Provider value={value}>\n      {children}\n    </SocialContext.Provider>\n  )\n}\n\nexport function useSocial() {\n  const context = useContext(SocialContext)\n  if (context === undefined) {\n    throw new Error('useSocial must be used within a SocialProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAmCA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C,CAAC;IAE1G,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAE/C,IAAI,kBAAkB;YACpB,eAAe,KAAK,KAAK,CAAC;QAC5B;QAEA,IAAI,mBAAmB;YACrB,uBAAuB,KAAK,KAAK,CAAC;QACpC;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;IAChE,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,aAAa,OAAO,CAAC,mCAAmC,KAAK,SAAS,CAAC;IACzE,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB,UAAkB;QAChE,MAAM,gBAAgC;YACpC;YACA;YACA;YACA,aAAa,IAAI,OAAO,WAAW;YACnC,aAAa;YACb,cAAc;QAChB;QAEA,MAAM,qBAAqB;eAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAAS;SAAc;QAC3F,eAAe;QACf,gBAAgB;IAClB,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,qBAAqB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAChE,eAAe;QACf,gBAAgB;IAClB,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,WAAW;IACnE,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,YAAY;IACtE,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW;IAC9C,GAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,uBAAuB,CAAA;YACrB,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI;gBAC9B;gBACA,cAAc;gBACd,cAAc,IAAI,OAAO,WAAW;gBACpC,YAAY;gBACZ,eAAe;gBACf,aAAa;YACf;YAEA,MAAM,UAAU;gBACd,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,OAAO;oBACV,cAAc,QAAQ,YAAY,GAAG;oBACrC,cAAc,IAAI,OAAO,WAAW;gBACtC;YACF;YAEA,iBAAiB;YACjB,OAAO;QACT;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,OAAO,mBAAmB,CAAC,OAAO,IAAI;YACpC;YACA,cAAc;YACd,cAAc,IAAI,OAAO,WAAW;YACpC,YAAY;YACZ,eAAe;YACf,aAAa;QACf;IACF,GAAG;QAAC;KAAoB;IAExB,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB;QAC5D,uBAAuB,CAAA;YACrB,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI;gBAC9B;gBACA,cAAc;gBACd,cAAc,IAAI,OAAO,WAAW;gBACpC,YAAY;gBACZ,eAAe;gBACf,aAAa;YACf;YAEA,MAAM,UAAU;gBACd,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,OAAO;oBACV,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EACvD,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAA6B,GAAa;gBAC7G;YACF;YAEA,iBAAiB;YACjB,OAAO;QACT;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/PaymentContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface PaymentTransaction {\n  id: string\n  userId: string\n  profileId: string\n  profileType: 'worker' | 'supplier' | 'company'\n  amount: number\n  currency: 'SAR'\n  purpose: 'contact_access' | 'document_download' | 'premium_access'\n  status: 'pending' | 'completed' | 'failed' | 'cancelled'\n  paymentMethod: 'mada' | 'visa' | 'mastercard' | 'stc_pay' | 'apple_pay'\n  transactionId?: string\n  createdAt: string\n  completedAt?: string\n  expiresAt: string\n}\n\nexport interface AccessPermission {\n  userId: string\n  profileId: string\n  profileType: 'worker' | 'supplier' | 'company'\n  hasContactAccess: boolean\n  hasDocumentAccess: boolean\n  purchasedAt: string\n  expiresAt: string\n  transactionId: string\n}\n\ninterface PaymentContextType {\n  transactions: PaymentTransaction[]\n  permissions: AccessPermission[]\n  initiatePayment: (profileId: string, profileType: 'worker' | 'supplier' | 'company', purpose: 'contact_access' | 'document_download' | 'premium_access') => Promise<string>\n  processPayment: (transactionId: string, paymentMethod: string, paymentDetails: any) => Promise<boolean>\n  hasAccess: (profileId: string, accessType: 'contact' | 'documents') => boolean\n  getTransactionStatus: (transactionId: string) => PaymentTransaction | null\n  getUserTransactions: () => PaymentTransaction[]\n  getUserPermissions: () => AccessPermission[]\n}\n\nconst PaymentContext = createContext<PaymentContextType | undefined>(undefined)\n\n// Saudi Arabian Payment Gateway Configuration\nconst PAYMENT_CONFIG: Record<'contact_access' | 'document_download' | 'premium_access', {\n  amount: number\n  currency: 'SAR'\n  validityDays: number\n}> = {\n  contact_access: {\n    amount: 25, // 25 SAR for contact information access\n    currency: 'SAR' as const,\n    validityDays: 30\n  },\n  document_download: {\n    amount: 50, // 50 SAR for document download access\n    currency: 'SAR' as const,\n    validityDays: 90\n  },\n  premium_access: {\n    amount: 75, // 75 SAR for full premium access (contact + documents)\n    currency: 'SAR' as const,\n    validityDays: 90\n  }\n}\n\nexport function PaymentProvider({ children }: { children: ReactNode }) {\n  const [transactions, setTransactions] = useState<PaymentTransaction[]>([])\n  const [permissions, setPermissions] = useState<AccessPermission[]>([])\n  const [currentUserId, setCurrentUserId] = useState<string>('')\n\n  useEffect(() => {\n    // Load payment data from localStorage (client-side only)\n    if (typeof window !== 'undefined') {\n      const savedTransactions = localStorage.getItem('kaazmaamaa-transactions')\n      const savedPermissions = localStorage.getItem('kaazmaamaa-permissions')\n      const userId = localStorage.getItem('kaazmaamaa-current-user') || 'current-user'\n\n      setCurrentUserId(userId)\n\n      if (savedTransactions) {\n        setTransactions(JSON.parse(savedTransactions))\n      }\n\n      if (savedPermissions) {\n        setPermissions(JSON.parse(savedPermissions))\n      }\n    }\n  }, [])\n\n  const saveToStorage = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('kaazmaamaa-transactions', JSON.stringify(transactions))\n      localStorage.setItem('kaazmaamaa-permissions', JSON.stringify(permissions))\n    }\n  }, [transactions, permissions])\n\n  useEffect(() => {\n    saveToStorage()\n  }, [transactions, permissions, saveToStorage])\n\n  const initiatePayment = useCallback(async (\n    profileId: string,\n    profileType: 'worker' | 'supplier' | 'company',\n    purpose: 'contact_access' | 'document_download' | 'premium_access'\n  ): Promise<string> => {\n    try {\n      if (!profileId || !profileType || !purpose) {\n        throw new Error('Missing required payment parameters')\n      }\n\n      const config = PAYMENT_CONFIG[purpose]\n      if (!config) {\n        throw new Error(`Invalid payment purpose: ${purpose}`)\n      }\n\n      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n\n      const transaction: PaymentTransaction = {\n        id: transactionId,\n        userId: currentUserId,\n        profileId,\n        profileType,\n        amount: config.amount,\n        currency: config.currency,\n        purpose,\n        status: 'pending',\n        paymentMethod: 'mada', // Default to MADA\n        createdAt: new Date().toISOString(),\n        expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes expiry\n      }\n\n      setTransactions(prev => [...prev, transaction])\n      return transactionId\n    } catch (error) {\n      console.error('Error initiating payment:', error)\n      throw error\n    }\n  }, [currentUserId])\n\n  const processPayment = useCallback(async (\n    transactionId: string,\n    paymentMethod: string,\n    paymentDetails: any\n  ): Promise<boolean> => {\n    // Simulate Saudi payment gateway processing\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const transaction = transactions.find(t => t.id === transactionId)\n        if (!transaction) {\n          resolve(false)\n          return\n        }\n\n        // Simulate payment success (90% success rate)\n        const isSuccess = Math.random() > 0.1\n\n        if (isSuccess) {\n          // Update transaction status\n          setTransactions(prev => prev.map(t =>\n            t.id === transactionId\n              ? {\n                  ...t,\n                  status: 'completed' as const,\n                  paymentMethod: paymentMethod as any,\n                  completedAt: new Date().toISOString(),\n                  transactionId: `saudi_${Date.now()}`\n                }\n              : t\n          ))\n\n          // Grant access permissions\n          const config = PAYMENT_CONFIG[transaction.purpose]\n          if (!config) {\n            console.error('Invalid payment purpose in processPayment:', transaction.purpose)\n            resolve(false)\n            return\n          }\n\n          const expiryDate = new Date()\n          expiryDate.setDate(expiryDate.getDate() + config.validityDays)\n\n          const permission: AccessPermission = {\n            userId: currentUserId,\n            profileId: transaction.profileId,\n            profileType: transaction.profileType,\n            hasContactAccess: transaction.purpose === 'contact_access' || transaction.purpose === 'premium_access',\n            hasDocumentAccess: transaction.purpose === 'document_download' || transaction.purpose === 'premium_access',\n            purchasedAt: new Date().toISOString(),\n            expiresAt: expiryDate.toISOString(),\n            transactionId\n          }\n\n          setPermissions(prev => {\n            // Remove any existing permission for the same profile\n            const filtered = prev.filter(p => !(p.profileId === transaction.profileId && p.userId === currentUserId))\n            return [...filtered, permission]\n          })\n\n          resolve(true)\n        } else {\n          // Update transaction as failed\n          setTransactions(prev => prev.map(t =>\n            t.id === transactionId\n              ? { ...t, status: 'failed' as const }\n              : t\n          ))\n          resolve(false)\n        }\n      }, 2000) // Simulate 2 second processing time\n    })\n  }, [transactions, currentUserId])\n\n  const hasAccess = useCallback((profileId: string, accessType: 'contact' | 'documents'): boolean => {\n    const permission = permissions.find(p =>\n      p.profileId === profileId &&\n      p.userId === currentUserId &&\n      new Date(p.expiresAt) > new Date()\n    )\n\n    if (!permission) return false\n\n    return accessType === 'contact' ? permission.hasContactAccess : permission.hasDocumentAccess\n  }, [permissions, currentUserId])\n\n  const getTransactionStatus = useCallback((transactionId: string): PaymentTransaction | null => {\n    return transactions.find(t => t.id === transactionId) || null\n  }, [transactions])\n\n  const getUserTransactions = useCallback((): PaymentTransaction[] => {\n    return transactions.filter(t => t.userId === currentUserId)\n  }, [transactions, currentUserId])\n\n  const getUserPermissions = useCallback((): AccessPermission[] => {\n    return permissions.filter(p => p.userId === currentUserId && new Date(p.expiresAt) > new Date())\n  }, [permissions, currentUserId])\n\n  const value = {\n    transactions,\n    permissions,\n    initiatePayment,\n    processPayment,\n    hasAccess,\n    getTransactionStatus,\n    getUserTransactions,\n    getUserPermissions\n  }\n\n  return (\n    <PaymentContext.Provider value={value}>\n      {children}\n    </PaymentContext.Provider>\n  )\n}\n\nexport function usePayment() {\n  const context = useContext(PaymentContext)\n  if (context === undefined) {\n    throw new Error('usePayment must be used within a PaymentProvider')\n  }\n  return context\n}\n\n// Saudi Arabian Payment Methods\nexport const SAUDI_PAYMENT_METHODS = [\n  {\n    id: 'mada',\n    name: 'MADA',\n    icon: '💳',\n    description: 'Saudi national payment system'\n  },\n  {\n    id: 'visa',\n    name: 'Visa',\n    icon: '💳',\n    description: 'Visa credit/debit cards'\n  },\n  {\n    id: 'mastercard',\n    name: 'Mastercard',\n    icon: '💳',\n    description: 'Mastercard credit/debit cards'\n  },\n  {\n    id: 'stc_pay',\n    name: 'STC Pay',\n    icon: '📱',\n    description: 'STC digital wallet'\n  },\n  {\n    id: 'apple_pay',\n    name: 'Apple Pay',\n    icon: '🍎',\n    description: 'Apple Pay digital wallet'\n  }\n]\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AA0CA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAErE,8CAA8C;AAC9C,MAAM,iBAID;IACH,gBAAgB;QACd,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,mBAAmB;QACjB,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,gBAAgB;QACd,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;AACF;AAEO,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,uCAAmC;;QAcnC;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,uCAAmC;;QAGnC;IACF,GAAG;QAAC;QAAc;KAAY;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;QAAa;KAAc;IAE7C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAClC,WACA,aACA;QAEA,IAAI;YACF,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,cAAc,CAAC,QAAQ;YACtC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS;YACvD;YAEA,MAAM,gBAAgB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAEpF,MAAM,cAAkC;gBACtC,IAAI;gBACJ,QAAQ;gBACR;gBACA;gBACA,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB;gBACA,QAAQ;gBACR,eAAe;gBACf,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,oBAAoB;YACrF;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,eACA,eACA;QAEA,4CAA4C;QAC5C,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpD,IAAI,CAAC,aAAa;oBAChB,QAAQ;oBACR;gBACF;gBAEA,8CAA8C;gBAC9C,MAAM,YAAY,KAAK,MAAM,KAAK;gBAElC,IAAI,WAAW;oBACb,4BAA4B;oBAC5B,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,gBACL;gCACE,GAAG,CAAC;gCACJ,QAAQ;gCACR,eAAe;gCACf,aAAa,IAAI,OAAO,WAAW;gCACnC,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;4BACtC,IACA;oBAGN,2BAA2B;oBAC3B,MAAM,SAAS,cAAc,CAAC,YAAY,OAAO,CAAC;oBAClD,IAAI,CAAC,QAAQ;wBACX,QAAQ,KAAK,CAAC,8CAA8C,YAAY,OAAO;wBAC/E,QAAQ;wBACR;oBACF;oBAEA,MAAM,aAAa,IAAI;oBACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK,OAAO,YAAY;oBAE7D,MAAM,aAA+B;wBACnC,QAAQ;wBACR,WAAW,YAAY,SAAS;wBAChC,aAAa,YAAY,WAAW;wBACpC,kBAAkB,YAAY,OAAO,KAAK,oBAAoB,YAAY,OAAO,KAAK;wBACtF,mBAAmB,YAAY,OAAO,KAAK,uBAAuB,YAAY,OAAO,KAAK;wBAC1F,aAAa,IAAI,OAAO,WAAW;wBACnC,WAAW,WAAW,WAAW;wBACjC;oBACF;oBAEA,eAAe,CAAA;wBACb,sDAAsD;wBACtD,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,SAAS,KAAK,YAAY,SAAS,IAAI,EAAE,MAAM,KAAK,aAAa;wBACvG,OAAO;+BAAI;4BAAU;yBAAW;oBAClC;oBAEA,QAAQ;gBACV,OAAO;oBACL,+BAA+B;oBAC/B,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,gBACL;gCAAE,GAAG,CAAC;gCAAE,QAAQ;4BAAkB,IAClC;oBAEN,QAAQ;gBACV;YACF,GAAG,MAAM,oCAAoC;;QAC/C;IACF,GAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QAChD,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAClC,EAAE,SAAS,KAAK,aAChB,EAAE,MAAM,KAAK,iBACb,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI;QAG9B,IAAI,CAAC,YAAY,OAAO;QAExB,OAAO,eAAe,YAAY,WAAW,gBAAgB,GAAG,WAAW,iBAAiB;IAC9F,GAAG;QAAC;QAAa;KAAc;IAE/B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB;IAC3D,GAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAC/C,GAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,iBAAiB,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI;IAC3F,GAAG;QAAC;QAAa;KAAc;IAE/B,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,MAAM,wBAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 2373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var M=(e,i,s,u,m,a,l,h)=>{let d=document.documentElement,w=[\"light\",\"dark\"];function p(n){(Array.isArray(e)?e:[e]).forEach(y=>{let k=y===\"class\",S=k&&a?m.map(f=>a[f]||f):m;k?(d.classList.remove(...S),d.classList.add(a&&a[n]?a[n]:n)):d.setAttribute(y,n)}),R(n)}function R(n){h&&w.includes(n)&&(d.style.colorScheme=n)}function c(){return window.matchMedia(\"(prefers-color-scheme: dark)\").matches?\"dark\":\"light\"}if(u)p(u);else try{let n=localStorage.getItem(i)||s,y=l&&n===\"system\"?c():n;p(y)}catch(n){}};var b=[\"light\",\"dark\"],I=\"(prefers-color-scheme: dark)\",O=typeof window==\"undefined\",x=t.createContext(void 0),U={setTheme:e=>{},themes:[]},z=()=>{var e;return(e=t.useContext(x))!=null?e:U},J=e=>t.useContext(x)?t.createElement(t.Fragment,null,e.children):t.createElement(V,{...e}),N=[\"light\",\"dark\"],V=({forcedTheme:e,disableTransitionOnChange:i=!1,enableSystem:s=!0,enableColorScheme:u=!0,storageKey:m=\"theme\",themes:a=N,defaultTheme:l=s?\"system\":\"light\",attribute:h=\"data-theme\",value:d,children:w,nonce:p,scriptProps:R})=>{let[c,n]=t.useState(()=>H(m,l)),[T,y]=t.useState(()=>c===\"system\"?E():c),k=d?Object.values(d):a,S=t.useCallback(o=>{let r=o;if(!r)return;o===\"system\"&&s&&(r=E());let v=d?d[r]:r,C=i?W(p):null,P=document.documentElement,L=g=>{g===\"class\"?(P.classList.remove(...k),v&&P.classList.add(v)):g.startsWith(\"data-\")&&(v?P.setAttribute(g,v):P.removeAttribute(g))};if(Array.isArray(h)?h.forEach(L):L(h),u){let g=b.includes(l)?l:null,D=b.includes(r)?r:g;P.style.colorScheme=D}C==null||C()},[p]),f=t.useCallback(o=>{let r=typeof o==\"function\"?o(c):o;n(r);try{localStorage.setItem(m,r)}catch(v){}},[c]),A=t.useCallback(o=>{let r=E(o);y(r),c===\"system\"&&s&&!e&&S(\"system\")},[c,e]);t.useEffect(()=>{let o=window.matchMedia(I);return o.addListener(A),A(o),()=>o.removeListener(A)},[A]),t.useEffect(()=>{let o=r=>{r.key===m&&(r.newValue?n(r.newValue):f(l))};return window.addEventListener(\"storage\",o),()=>window.removeEventListener(\"storage\",o)},[f]),t.useEffect(()=>{S(e!=null?e:c)},[e,c]);let Q=t.useMemo(()=>({theme:c,setTheme:f,forcedTheme:e,resolvedTheme:c===\"system\"?T:c,themes:s?[...a,\"system\"]:a,systemTheme:s?T:void 0}),[c,f,e,T,s,a]);return t.createElement(x.Provider,{value:Q},t.createElement(_,{forcedTheme:e,storageKey:m,attribute:h,enableSystem:s,enableColorScheme:u,defaultTheme:l,value:d,themes:a,nonce:p,scriptProps:R}),w)},_=t.memo(({forcedTheme:e,storageKey:i,attribute:s,enableSystem:u,enableColorScheme:m,defaultTheme:a,value:l,themes:h,nonce:d,scriptProps:w})=>{let p=JSON.stringify([s,i,a,e,h,l,u,m]).slice(1,-1);return t.createElement(\"script\",{...w,suppressHydrationWarning:!0,nonce:typeof window==\"undefined\"?d:\"\",dangerouslySetInnerHTML:{__html:`(${M.toString()})(${p})`}})}),H=(e,i)=>{if(O)return;let s;try{s=localStorage.getItem(e)||void 0}catch(u){}return s||i},W=e=>{let i=document.createElement(\"style\");return e&&i.setAttribute(\"nonce\",e),i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(i),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(i)},1)}},E=e=>(e||(e=window.matchMedia(I)),e.matches?\"dark\":\"light\");export{J as ThemeProvider,z as useTheme};\n"], "names": [], "mappings": ";;;;AAAa;AAAb;;AAAqC,IAAI,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,SAAS,eAAe,EAAC,IAAE;QAAC;QAAQ;KAAO;IAAC,SAAS,EAAE,CAAC;QAAE,CAAC,MAAM,OAAO,CAAC,KAAG,IAAE;YAAC;SAAE,EAAE,OAAO,CAAC,CAAA;YAAI,IAAI,IAAE,MAAI,SAAQ,IAAE,KAAG,IAAE,EAAE,GAAG,CAAC,CAAA,IAAG,CAAC,CAAC,EAAE,IAAE,KAAG;YAAE,IAAE,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,EAAE,SAAS,CAAC,GAAG,CAAC,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE,EAAE,YAAY,CAAC,GAAE;QAAE,IAAG,EAAE;IAAE;IAAC,SAAS,EAAE,CAAC;QAAE,KAAG,EAAE,QAAQ,CAAC,MAAI,CAAC,EAAE,KAAK,CAAC,WAAW,GAAC,CAAC;IAAC;IAAC,SAAS;QAAI,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAC,SAAO;IAAO;IAAC,IAAG,GAAE,EAAE;SAAQ,IAAG;QAAC,IAAI,IAAE,aAAa,OAAO,CAAC,MAAI,GAAE,IAAE,KAAG,MAAI,WAAS,MAAI;QAAE,EAAE;IAAE,EAAC,OAAM,GAAE,CAAC;AAAC;AAAE,IAAI,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,gCAA+B,IAAE,OAAO,UAAQ,aAAY,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,KAAK,IAAG,IAAE;IAAC,UAAS,CAAA,KAAI;IAAE,QAAO,EAAE;AAAA,GAAE,IAAE;IAAK,IAAI;IAAE,OAAM,CAAC,IAAE,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,EAAE,KAAG,OAAK,IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAA,GAAA,qMAAA,CAAA,aAAY,AAAD,EAAE,KAAG,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,qMAAA,CAAA,WAAU,EAAC,MAAK,EAAE,QAAQ,IAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,GAAG,CAAC;IAAA,IAAG,IAAE;IAAC;IAAQ;CAAO,EAAC,IAAE,CAAC,EAAC,aAAY,CAAC,EAAC,2BAA0B,IAAE,CAAC,CAAC,EAAC,cAAa,IAAE,CAAC,CAAC,EAAC,mBAAkB,IAAE,CAAC,CAAC,EAAC,YAAW,IAAE,OAAO,EAAC,QAAO,IAAE,CAAC,EAAC,cAAa,IAAE,IAAE,WAAS,OAAO,EAAC,WAAU,IAAE,YAAY,EAAC,OAAM,CAAC,EAAC,UAAS,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAG,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,EAAE,GAAE,KAAI,CAAC,GAAE,EAAE,GAAC,CAAA,GAAA,qMAAA,CAAA,WAAU,AAAD,EAAE,IAAI,MAAI,WAAS,MAAI,IAAG,IAAE,IAAE,OAAO,MAAM,CAAC,KAAG,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE;QAAE,IAAG,CAAC,GAAE;QAAO,MAAI,YAAU,KAAG,CAAC,IAAE,GAAG;QAAE,IAAI,IAAE,IAAE,CAAC,CAAC,EAAE,GAAC,GAAE,IAAE,IAAE,EAAE,KAAG,MAAK,IAAE,SAAS,eAAe,EAAC,IAAE,CAAA;YAAI,MAAI,UAAQ,CAAC,EAAE,SAAS,CAAC,MAAM,IAAI,IAAG,KAAG,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,IAAE,EAAE,UAAU,CAAC,YAAU,CAAC,IAAE,EAAE,YAAY,CAAC,GAAE,KAAG,EAAE,eAAe,CAAC,EAAE;QAAC;QAAE,IAAG,MAAM,OAAO,CAAC,KAAG,EAAE,OAAO,CAAC,KAAG,EAAE,IAAG,GAAE;YAAC,IAAI,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE,MAAK,IAAE,EAAE,QAAQ,CAAC,KAAG,IAAE;YAAE,EAAE,KAAK,CAAC,WAAW,GAAC;QAAC;QAAC,KAAG,QAAM;IAAG,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,OAAO,KAAG,aAAW,EAAE,KAAG;QAAE,EAAE;QAAG,IAAG;YAAC,aAAa,OAAO,CAAC,GAAE;QAAE,EAAC,OAAM,GAAE,CAAC;IAAC,GAAE;QAAC;KAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,cAAa,AAAD,EAAE,CAAA;QAAI,IAAI,IAAE,EAAE;QAAG,EAAE,IAAG,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE;IAAS,GAAE;QAAC;QAAE;KAAE;IAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,OAAO,UAAU,CAAC;QAAG,OAAO,EAAE,WAAW,CAAC,IAAG,EAAE,IAAG,IAAI,EAAE,cAAc,CAAC;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,IAAI,IAAE,CAAA;YAAI,EAAE,GAAG,KAAG,KAAG,CAAC,EAAE,QAAQ,GAAC,EAAE,EAAE,QAAQ,IAAE,EAAE,EAAE;QAAC;QAAE,OAAO,OAAO,gBAAgB,CAAC,WAAU,IAAG,IAAI,OAAO,mBAAmB,CAAC,WAAU;IAAE,GAAE;QAAC;KAAE,GAAE,CAAA,GAAA,qMAAA,CAAA,YAAW,AAAD,EAAE;QAAK,EAAE,KAAG,OAAK,IAAE;IAAE,GAAE;QAAC;QAAE;KAAE;IAAE,IAAI,IAAE,CAAA,GAAA,qMAAA,CAAA,UAAS,AAAD,EAAE,IAAI,CAAC;YAAC,OAAM;YAAE,UAAS;YAAE,aAAY;YAAE,eAAc,MAAI,WAAS,IAAE;YAAE,QAAO,IAAE;mBAAI;gBAAE;aAAS,GAAC;YAAE,aAAY,IAAE,IAAE,KAAK;QAAC,CAAC,GAAE;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE;IAAE,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,EAAE,QAAQ,EAAC;QAAC,OAAM;IAAC,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,GAAE;QAAC,aAAY;QAAE,YAAW;QAAE,WAAU;QAAE,cAAa;QAAE,mBAAkB;QAAE,cAAa;QAAE,OAAM;QAAE,QAAO;QAAE,OAAM;QAAE,aAAY;IAAC,IAAG;AAAE,GAAE,IAAE,CAAA,GAAA,qMAAA,CAAA,OAAM,AAAD,EAAE,CAAC,EAAC,aAAY,CAAC,EAAC,YAAW,CAAC,EAAC,WAAU,CAAC,EAAC,cAAa,CAAC,EAAC,mBAAkB,CAAC,EAAC,cAAa,CAAC,EAAC,OAAM,CAAC,EAAC,QAAO,CAAC,EAAC,OAAM,CAAC,EAAC,aAAY,CAAC,EAAC;IAAI,IAAI,IAAE,KAAK,SAAS,CAAC;QAAC;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;QAAE;KAAE,EAAE,KAAK,CAAC,GAAE,CAAC;IAAG,OAAO,CAAA,GAAA,qMAAA,CAAA,gBAAe,AAAD,EAAE,UAAS;QAAC,GAAG,CAAC;QAAC,0BAAyB,CAAC;QAAE,OAAM,OAAO,UAAQ,cAAY,IAAE;QAAG,yBAAwB;YAAC,QAAO,CAAC,CAAC,EAAE,EAAE,QAAQ,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAAA;IAAC;AAAE,IAAG,IAAE,CAAC,GAAE;IAAK,IAAG,GAAE;IAAO,IAAI;IAAE,IAAG;QAAC,IAAE,aAAa,OAAO,CAAC,MAAI,KAAK;IAAC,EAAC,OAAM,GAAE,CAAC;IAAC,OAAO,KAAG;AAAC,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,SAAS,aAAa,CAAC;IAAS,OAAO,KAAG,EAAE,YAAY,CAAC,SAAQ,IAAG,EAAE,WAAW,CAAC,SAAS,cAAc,CAAC,iLAAgL,SAAS,IAAI,CAAC,WAAW,CAAC,IAAG;QAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI,GAAE,WAAW;YAAK,SAAS,IAAI,CAAC,WAAW,CAAC;QAAE,GAAE;IAAE;AAAC,GAAE,IAAE,CAAA,IAAG,CAAC,KAAG,CAAC,IAAE,OAAO,UAAU,CAAC,EAAE,GAAE,EAAE,OAAO,GAAC,SAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-dom.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].ReactDOM\n"], "names": ["module", "exports", "require", "vendored", "ReactDOM"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/sonner/dist/index.mjs"], "sourcesContent": ["'use client';\nfunction __insertCSS(code) {\n  if (!code || typeof document == 'undefined') return\n  let head = document.head || document.getElementsByTagName('head')[0]\n  let style = document.createElement('style')\n  style.type = 'text/css'\n  head.appendChild(style)\n  ;style.styleSheet ? (style.styleSheet.cssText = code) : style.appendChild(document.createTextNode(code))\n}\n\nimport React from 'react';\nimport ReactDOM from 'react-dom';\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ React.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ React.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ React.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ React.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ React.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ React.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\n\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = React.useState(document.hidden);\n    React.useEffect(()=>{\n        const callback = ()=>{\n            setIsDocumentHidden(document.hidden);\n        };\n        document.addEventListener('visibilitychange', callback);\n        return ()=>window.removeEventListener('visibilitychange', callback);\n    }, []);\n    return isDocumentHidden;\n};\n\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = React.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !React.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}[data-sonner-toaster][data-lifted=true]{transform:translateY(-8px)}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\n\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = React.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = React.useState(null);\n    const [mounted, setMounted] = React.useState(false);\n    const [removed, setRemoved] = React.useState(false);\n    const [swiping, setSwiping] = React.useState(false);\n    const [swipeOut, setSwipeOut] = React.useState(false);\n    const [isSwiped, setIsSwiped] = React.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = React.useState(0);\n    const [initialHeight, setInitialHeight] = React.useState(0);\n    const remainingTime = React.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = React.useRef(null);\n    const toastRef = React.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = React.useMemo(()=>heights.findIndex((height)=>height.toastId === toast.id) || 0, [\n        heights,\n        toast.id\n    ]);\n    const closeButton = React.useMemo(()=>{\n        var _toast_closeButton;\n        return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n    }, [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = React.useMemo(()=>toast.duration || durationFromToaster || TOAST_LIFETIME, [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = React.useRef(0);\n    const offset = React.useRef(0);\n    const lastCloseTimerStartTimeRef = React.useRef(0);\n    const pointerStartRef = React.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = React.useMemo(()=>{\n        return heights.reduce((prev, curr, reducerIndex)=>{\n            // Calculate offset up until current toast\n            if (reducerIndex >= heightIndex) {\n                return prev;\n            }\n            return prev + curr.height;\n        }, 0);\n    }, [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = React.useMemo(()=>heightIndex * gap + toastsHeightBefore, [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    React.useEffect(()=>{\n        remainingTime.current = duration;\n    }, [\n        duration\n    ]);\n    React.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        setMounted(true);\n    }, []);\n    React.useEffect(()=>{\n        const toastNode = toastRef.current;\n        if (toastNode) {\n            const height = toastNode.getBoundingClientRect().height;\n            // Add toast height to heights array after the toast is mounted\n            setInitialHeight(height);\n            setHeights((h)=>[\n                    {\n                        toastId: toast.id,\n                        height,\n                        position: toast.position\n                    },\n                    ...h\n                ]);\n            return ()=>setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        }\n    }, [\n        setHeights,\n        toast.id\n    ]);\n    React.useLayoutEffect(()=>{\n        if (!mounted) return;\n        const toastNode = toastRef.current;\n        const originalHeight = toastNode.style.height;\n        toastNode.style.height = 'auto';\n        const newHeight = toastNode.getBoundingClientRect().height;\n        toastNode.style.height = originalHeight;\n        setInitialHeight(newHeight);\n        setHeights((heights)=>{\n            const alreadyExists = heights.find((height)=>height.toastId === toast.id);\n            if (!alreadyExists) {\n                return [\n                    {\n                        toastId: toast.id,\n                        height: newHeight,\n                        position: toast.position\n                    },\n                    ...heights\n                ];\n            } else {\n                return heights.map((height)=>height.toastId === toast.id ? {\n                        ...height,\n                        height: newHeight\n                    } : height);\n            }\n        });\n    }, [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id\n    ]);\n    const deleteToast = React.useCallback(()=>{\n        // Save the offset for the exit swipe animation\n        setRemoved(true);\n        setOffsetBeforeRemove(offset.current);\n        setHeights((h)=>h.filter((height)=>height.toastId !== toast.id));\n        setTimeout(()=>{\n            removeToast(toast);\n        }, TIME_BEFORE_UNMOUNT);\n    }, [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    React.useEffect(()=>{\n        if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n        let timeoutId;\n        // Pause the timer on each hover\n        const pauseTimer = ()=>{\n            if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                // Get the elapsed time since the timer started\n                const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                remainingTime.current = remainingTime.current - elapsedTime;\n            }\n            lastCloseTimerStartTimeRef.current = new Date().getTime();\n        };\n        const startTimer = ()=>{\n            // setTimeout(, Infinity) behaves as if the delay is 0.\n            // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n            // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n            if (remainingTime.current === Infinity) return;\n            closeTimerStartTimeRef.current = new Date().getTime();\n            // Let the toast know it has started\n            timeoutId = setTimeout(()=>{\n                toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                deleteToast();\n            }, remainingTime.current);\n        };\n        if (expanded || interacting || isDocumentHidden) {\n            pauseTimer();\n        } else {\n            startTimer();\n        }\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    React.useEffect(()=>{\n        if (toast.delete) {\n            deleteToast();\n        }\n    }, [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ React.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ React.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ React.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, // Apply transform using both x and y values\n            _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ React.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ React.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ React.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ React.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (typeof window === 'undefined') return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = React.useState([]);\n    React.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                setTimeout(()=>{\n                    ReactDOM.flushSync(()=>{\n                        setActiveToasts((toasts)=>toasts.filter((t)=>t.id !== toast.id));\n                    });\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                ReactDOM.flushSync(()=>{\n                    setActiveToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ React.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = React.useState([]);\n    const possiblePositions = React.useMemo(()=>{\n        return Array.from(new Set([\n            position\n        ].concat(toasts.filter((toast)=>toast.position).map((toast)=>toast.position))));\n    }, [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = React.useState([]);\n    const [expanded, setExpanded] = React.useState(false);\n    const [interacting, setInteracting] = React.useState(false);\n    const [actualTheme, setActualTheme] = React.useState(theme !== 'system' ? theme : typeof window !== 'undefined' ? window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light' : 'light');\n    const listRef = React.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = React.useRef(null);\n    const isFocusWithinRef = React.useRef(false);\n    const removeToast = React.useCallback((toastToRemove)=>{\n        setToasts((toasts)=>{\n            var _toasts_find;\n            if (!((_toasts_find = toasts.find((toast)=>toast.id === toastToRemove.id)) == null ? void 0 : _toasts_find.delete)) {\n                ToastState.dismiss(toastToRemove.id);\n            }\n            return toasts.filter(({ id })=>id !== toastToRemove.id);\n        });\n    }, []);\n    React.useEffect(()=>{\n        return ToastState.subscribe((toast)=>{\n            if (toast.dismiss) {\n                // Prevent batching of other state updates\n                requestAnimationFrame(()=>{\n                    setToasts((toasts)=>toasts.map((t)=>t.id === toast.id ? {\n                                ...t,\n                                delete: true\n                            } : t));\n                });\n                return;\n            }\n            // Prevent batching, temp solution.\n            setTimeout(()=>{\n                ReactDOM.flushSync(()=>{\n                    setToasts((toasts)=>{\n                        const indexOfExistingToast = toasts.findIndex((t)=>t.id === toast.id);\n                        // Update the toast if it already exists\n                        if (indexOfExistingToast !== -1) {\n                            return [\n                                ...toasts.slice(0, indexOfExistingToast),\n                                {\n                                    ...toasts[indexOfExistingToast],\n                                    ...toast\n                                },\n                                ...toasts.slice(indexOfExistingToast + 1)\n                            ];\n                        }\n                        return [\n                            toast,\n                            ...toasts\n                        ];\n                    });\n                });\n            });\n        });\n    }, [\n        toasts\n    ]);\n    React.useEffect(()=>{\n        if (theme !== 'system') {\n            setActualTheme(theme);\n            return;\n        }\n        if (theme === 'system') {\n            // check if current preference is dark\n            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                // it's currently dark\n                setActualTheme('dark');\n            } else {\n                // it's not dark\n                setActualTheme('light');\n            }\n        }\n        if (typeof window === 'undefined') return;\n        const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n        try {\n            // Chrome & Firefox\n            darkMediaQuery.addEventListener('change', ({ matches })=>{\n                if (matches) {\n                    setActualTheme('dark');\n                } else {\n                    setActualTheme('light');\n                }\n            });\n        } catch (error) {\n            // Safari < 14\n            darkMediaQuery.addListener(({ matches })=>{\n                try {\n                    if (matches) {\n                        setActualTheme('dark');\n                    } else {\n                        setActualTheme('light');\n                    }\n                } catch (e) {\n                    console.error(e);\n                }\n            });\n        }\n    }, [\n        theme\n    ]);\n    React.useEffect(()=>{\n        // Ensure expanded is always false when no toasts are present / only one left\n        if (toasts.length <= 1) {\n            setExpanded(false);\n        }\n    }, [\n        toasts\n    ]);\n    React.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            var _listRef_current;\n            const isHotkeyPressed = hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) {\n                var _listRef_current1;\n                setExpanded(true);\n                (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n            }\n            if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                setExpanded(false);\n            }\n        };\n        document.addEventListener('keydown', handleKeyDown);\n        return ()=>document.removeEventListener('keydown', handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    React.useEffect(()=>{\n        if (listRef.current) {\n            return ()=>{\n                if (lastFocusedElementRef.current) {\n                    lastFocusedElementRef.current.focus({\n                        preventScroll: true\n                    });\n                    lastFocusedElementRef.current = null;\n                    isFocusWithinRef.current = false;\n                }\n            };\n        }\n    }, [\n        listRef.current\n    ]);\n    return(// Remove item from normal navigation flow, only available via hotkey\n    /*#__PURE__*/ React.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ React.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-lifted\": expanded && toasts.length > 1 && !expand,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ React.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    })));\n});\n\nexport { Toaster, toast, useSonner };\n"], "names": [], "mappings": ";;;;;AAUA;AACA;AAXA;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,CAAC,QAAQ,OAAO,YAAY,aAAa;IAC7C,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;IACpE,IAAI,QAAQ,SAAS,aAAa,CAAC;IACnC,MAAM,IAAI,GAAG;IACb,KAAK,WAAW,CAAC;IAChB,MAAM,UAAU,GAAI,MAAM,UAAU,CAAC,OAAO,GAAG,OAAQ,MAAM,WAAW,CAAC,SAAS,cAAc,CAAC;AACpG;;;AAKA,MAAM,WAAW,CAAC;IACd,OAAO;QACH,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC;AAC5B,MAAM,SAAS,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE;IAClC,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC5C,WAAW;YACP;YACA;SACH,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QACvB,gBAAgB;IACpB,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxC,WAAW;IACf,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;YACrD,WAAW;YACX,KAAK,CAAC,YAAY,EAAE,GAAG;QAC3B;AACR;AACA,MAAM,cAAc,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACzD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,cAAc,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACzD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,WAAW,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACtD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,YAAY,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACvD,OAAO;IACP,SAAS;IACT,MAAM;IACN,QAAQ;IACR,OAAO;AACX,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,UAAU;IACV,GAAG;IACH,UAAU;AACd;AACA,MAAM,YAAY,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;IACvD,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,MAAM;IACN,QAAQ;IACR,aAAa;IACb,eAAe;IACf,gBAAgB;AACpB,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IACzC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR,IAAI,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACR;AAEA,MAAM,sBAAsB;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,SAAS,MAAM;IAC9E,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,WAAW;YACb,oBAAoB,SAAS,MAAM;QACvC;QACA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO,IAAI,OAAO,mBAAmB,CAAC,oBAAoB;IAC9D,GAAG,EAAE;IACL,OAAO;AACX;AAEA,IAAI,gBAAgB;AACpB,MAAM;IACF,aAAa;QACT,kEAAkE;QAClE,IAAI,CAAC,SAAS,GAAG,CAAC;YACd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtB,OAAO;gBACH,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;gBACvC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO;YACnC;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;QACtD;QACA,IAAI,CAAC,QAAQ,GAAG,CAAC;YACb,IAAI,CAAC,OAAO,CAAC;YACb,IAAI,CAAC,MAAM,GAAG;mBACP,IAAI,CAAC,MAAM;gBACd;aACH;QACL;QACA,IAAI,CAAC,MAAM,GAAG,CAAC;YACX,IAAI;YACJ,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,GAAG;YAC7B,MAAM,KAAK,OAAO,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM,YAAY,CAAC,CAAC,WAAW,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,SAAS,MAAM,IAAI,IAAI,KAAK,EAAE,GAAG;YAC9I,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACpC,OAAO,MAAM,EAAE,KAAK;YACxB;YACA,MAAM,cAAc,KAAK,WAAW,KAAK,YAAY,OAAO,KAAK,WAAW;YAC5E,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;gBAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;YAChC;YACA,IAAI,eAAe;gBACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC3B,IAAI,MAAM,EAAE,KAAK,IAAI;wBACjB,IAAI,CAAC,OAAO,CAAC;4BACT,GAAG,KAAK;4BACR,GAAG,IAAI;4BACP;4BACA,OAAO;wBACX;wBACA,OAAO;4BACH,GAAG,KAAK;4BACR,GAAG,IAAI;4BACP;4BACA;4BACA,OAAO;wBACX;oBACJ;oBACA,OAAO;gBACX;YACJ,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC;oBACV,OAAO;oBACP,GAAG,IAAI;oBACP;oBACA;gBACJ;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,GAAG,CAAC;YACZ,IAAI,IAAI;gBACJ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;gBACzB,sBAAsB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;4BAChE;4BACA,SAAS;wBACb;YACZ,OAAO;gBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBACjB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,aAAa,WAAW;4BAC1C,IAAI,MAAM,EAAE;4BACZ,SAAS;wBACb;gBACR;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP;YACJ;QACJ;QACA,IAAI,CAAC,KAAK,GAAG,CAAC,SAAS;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP;gBACA,MAAM;YACV;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,IAAI,GAAG,CAAC,SAAS;YAClB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACf,GAAG,IAAI;gBACP,MAAM;gBACN;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,GAAG,CAAC,SAAS;YACrB,IAAI,CAAC,MAAM;gBACP,kBAAkB;gBAClB;YACJ;YACA,IAAI,KAAK;YACT,IAAI,KAAK,OAAO,KAAK,WAAW;gBAC5B,KAAK,IAAI,CAAC,MAAM,CAAC;oBACb,GAAG,IAAI;oBACP;oBACA,MAAM;oBACN,SAAS,KAAK,OAAO;oBACrB,aAAa,OAAO,KAAK,WAAW,KAAK,aAAa,KAAK,WAAW,GAAG;gBAC7E;YACJ;YACA,MAAM,IAAI,QAAQ,OAAO,CAAC,mBAAmB,WAAW,YAAY;YACpE,IAAI,gBAAgB,OAAO;YAC3B,IAAI;YACJ,MAAM,kBAAkB,EAAE,IAAI,CAAC,OAAO;gBAClC,SAAS;oBACL;oBACA;iBACH;gBACD,MAAM,yBAAyB,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC;gBACpD,IAAI,wBAAwB;oBACxB,gBAAgB;oBAChB,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN,SAAS;oBACb;gBACJ,OAAO,IAAI,eAAe,aAAa,CAAC,SAAS,EAAE,EAAE;oBACjD,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,IAAI,KAAK,KAAK;oBAC9H,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE,IAAI,KAAK,WAAW;oBAChJ,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ,OAAO,IAAI,oBAAoB,OAAO;oBAClC,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK;oBAC9F,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,YAAY,KAAK,WAAW;oBAChH,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ,OAAO,IAAI,KAAK,OAAO,KAAK,WAAW;oBACnC,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,OAAO,KAAK,aAAa,MAAM,KAAK,OAAO,CAAC,YAAY,KAAK,OAAO;oBACpG,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,YAAY,KAAK,WAAW;oBAChH,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ;YACJ,GAAG,KAAK,CAAC,OAAO;gBACZ,SAAS;oBACL;oBACA;iBACH;gBACD,IAAI,KAAK,KAAK,KAAK,WAAW;oBAC1B,gBAAgB;oBAChB,MAAM,cAAc,OAAO,KAAK,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,CAAC,SAAS,KAAK,KAAK;oBAC3F,MAAM,cAAc,OAAO,KAAK,WAAW,KAAK,aAAa,MAAM,KAAK,WAAW,CAAC,SAAS,KAAK,WAAW;oBAC7G,MAAM,mBAAmB,OAAO,gBAAgB,YAAY,CAAC,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC;oBAClF,MAAM,gBAAgB,mBAAmB,cAAc;wBACnD,SAAS;oBACb;oBACA,IAAI,CAAC,MAAM,CAAC;wBACR;wBACA,MAAM;wBACN;wBACA,GAAG,aAAa;oBACpB;gBACJ;YACJ,GAAG,OAAO,CAAC;gBACP,IAAI,eAAe;oBACf,uEAAuE;oBACvE,IAAI,CAAC,OAAO,CAAC;oBACb,KAAK;gBACT;gBACA,KAAK,OAAO,IAAI,OAAO,KAAK,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC;YACtD;YACA,MAAM,SAAS,IAAI,IAAI,QAAQ,CAAC,SAAS,SAAS,gBAAgB,IAAI,CAAC,IAAI,MAAM,CAAC,EAAE,KAAK,WAAW,OAAO,MAAM,CAAC,EAAE,IAAI,QAAQ,MAAM,CAAC,EAAE,GAAG,KAAK,CAAC;YAClJ,IAAI,OAAO,OAAO,YAAY,OAAO,OAAO,UAAU;gBAClD,oCAAoC;gBACpC,OAAO;oBACH;gBACJ;YACJ,OAAO;gBACH,OAAO,OAAO,MAAM,CAAC,IAAI;oBACrB;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK;YAChB,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK;YAChD,IAAI,CAAC,MAAM,CAAC;gBACR,KAAK,IAAI;gBACT;gBACA,GAAG,IAAI;YACX;YACA,OAAO;QACX;QACA,IAAI,CAAC,eAAe,GAAG;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE;QACzE;QACA,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,eAAe,GAAG,IAAI;IAC/B;AACJ;AACA,MAAM,aAAa,IAAI;AACvB,kCAAkC;AAClC,MAAM,gBAAgB,CAAC,SAAS;IAC5B,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK;IAChD,WAAW,QAAQ,CAAC;QAChB,OAAO;QACP,GAAG,IAAI;QACP;IACJ;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,QAAQ,OAAO,SAAS,YAAY,QAAQ,QAAQ,OAAO,KAAK,EAAE,KAAK,aAAa,YAAY,QAAQ,OAAO,KAAK,MAAM,KAAK;AAC1I;AACA,MAAM,aAAa;AACnB,MAAM,aAAa,IAAI,WAAW,MAAM;AACxC,MAAM,YAAY,IAAI,WAAW,eAAe;AAChD,uFAAuF;AACvF,MAAM,QAAQ,OAAO,MAAM,CAAC,YAAY;IACpC,SAAS,WAAW,OAAO;IAC3B,MAAM,WAAW,IAAI;IACrB,SAAS,WAAW,OAAO;IAC3B,OAAO,WAAW,KAAK;IACvB,QAAQ,WAAW,MAAM;IACzB,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;IAC3B,SAAS,WAAW,OAAO;AAC/B,GAAG;IACC;IACA;AACJ;AAEA,YAAY;AAEZ,SAAS,SAAS,MAAM;IACpB,OAAO,OAAO,KAAK,KAAK;AAC5B;AAEA,wBAAwB;AACxB,MAAM,wBAAwB;AAC9B,mBAAmB;AACnB,MAAM,kBAAkB;AACxB,0BAA0B;AAC1B,MAAM,yBAAyB;AAC/B,uCAAuC;AACvC,MAAM,iBAAiB;AACvB,sBAAsB;AACtB,MAAM,cAAc;AACpB,6BAA6B;AAC7B,MAAM,MAAM;AACZ,+BAA+B;AAC/B,MAAM,kBAAkB;AACxB,mCAAmC;AACnC,MAAM,sBAAsB;AAC5B,SAAS,GAAG,GAAG,OAAO;IAClB,OAAO,QAAQ,MAAM,CAAC,SAAS,IAAI,CAAC;AACxC;AACA,SAAS,0BAA0B,QAAQ;IACvC,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B,MAAM,aAAa,EAAE;IACrB,IAAI,GAAG;QACH,WAAW,IAAI,CAAC;IACpB;IACA,IAAI,GAAG;QACH,WAAW,IAAI,CAAC;IACpB;IACA,OAAO;AACX;AACA,MAAM,QAAQ,CAAC;IACX,IAAI,mBAAmB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB,oBAAoB;IACnK,MAAM,EAAE,QAAQ,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,iBAAiB,EAAE,aAAa,sBAAsB,EAAE,KAAK,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,YAAY,EAAE,EAAE,uBAAuB,EAAE,EAAE,UAAU,mBAAmB,EAAE,QAAQ,EAAE,GAAG,EAAE,eAAe,EAAE,UAAU,EAAE,KAAK,EAAE,uBAAuB,aAAa,EAAE,GAAG;IAClZ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACjE,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACzD,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC,MAAM,QAAQ,IAAI,uBAAuB;IAC5E,MAAM,gBAAgB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACnC,MAAM,WAAW,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,MAAM,UAAU,UAAU;IAC1B,MAAM,YAAY,QAAQ,KAAK;IAC/B,MAAM,YAAY,MAAM,IAAI;IAC5B,MAAM,cAAc,MAAM,WAAW,KAAK;IAC1C,MAAM,iBAAiB,MAAM,SAAS,IAAI;IAC1C,MAAM,4BAA4B,MAAM,oBAAoB,IAAI;IAChE,8IAA8I;IAC9I,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,QAAQ,SAAS,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE,KAAK,GAAG;QACjG;QACA,MAAM,EAAE;KACX;IACD,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC9B,IAAI;QACJ,OAAO,CAAC,qBAAqB,MAAM,WAAW,KAAK,OAAO,qBAAqB;IACnF,GAAG;QACC,MAAM,WAAW;QACjB;KACH;IACD,MAAM,WAAW,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,MAAM,QAAQ,IAAI,uBAAuB,gBAAgB;QACxF,MAAM,QAAQ;QACd;KACH;IACD,MAAM,yBAAyB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,MAAM,SAAS,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5B,MAAM,6BAA6B,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAChD,MAAM,kBAAkB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACrC,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B,MAAM,qBAAqB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACrC,OAAO,QAAQ,MAAM,CAAC,CAAC,MAAM,MAAM;YAC/B,0CAA0C;YAC1C,IAAI,gBAAgB,aAAa;gBAC7B,OAAO;YACX;YACA,OAAO,OAAO,KAAK,MAAM;QAC7B,GAAG;IACP,GAAG;QACC;QACA;KACH;IACD,MAAM,mBAAmB;IACzB,MAAM,SAAS,MAAM,MAAM,IAAI;IAC/B,MAAM,WAAW,cAAc;IAC/B,OAAO,OAAO,GAAG,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC,IAAI,cAAc,MAAM,oBAAoB;QACvE;QACA;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,cAAc,OAAO,GAAG;IAC5B,GAAG;QACC;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,sDAAsD;QACtD,WAAW;IACf,GAAG,EAAE;IACL,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,YAAY,SAAS,OAAO;QAClC,IAAI,WAAW;YACX,MAAM,SAAS,UAAU,qBAAqB,GAAG,MAAM;YACvD,+DAA+D;YAC/D,iBAAiB;YACjB,WAAW,CAAC,IAAI;oBACR;wBACI,SAAS,MAAM,EAAE;wBACjB;wBACA,UAAU,MAAM,QAAQ;oBAC5B;uBACG;iBACN;YACL,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;QAC7E;IACJ,GAAG;QACC;QACA,MAAM,EAAE;KACX;IACD,qMAAA,CAAA,UAAK,CAAC,eAAe,CAAC;QAClB,IAAI,CAAC,SAAS;QACd,MAAM,YAAY,SAAS,OAAO;QAClC,MAAM,iBAAiB,UAAU,KAAK,CAAC,MAAM;QAC7C,UAAU,KAAK,CAAC,MAAM,GAAG;QACzB,MAAM,YAAY,UAAU,qBAAqB,GAAG,MAAM;QAC1D,UAAU,KAAK,CAAC,MAAM,GAAG;QACzB,iBAAiB;QACjB,WAAW,CAAC;YACR,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;YACxE,IAAI,CAAC,eAAe;gBAChB,OAAO;oBACH;wBACI,SAAS,MAAM,EAAE;wBACjB,QAAQ;wBACR,UAAU,MAAM,QAAQ;oBAC5B;uBACG;iBACN;YACL,OAAO;gBACH,OAAO,QAAQ,GAAG,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE,GAAG;wBACnD,GAAG,MAAM;wBACT,QAAQ;oBACZ,IAAI;YACZ;QACJ;IACJ,GAAG;QACC;QACA,MAAM,KAAK;QACX,MAAM,WAAW;QACjB;QACA,MAAM,EAAE;KACX;IACD,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC;QAClC,+CAA+C;QAC/C,WAAW;QACX,sBAAsB,OAAO,OAAO;QACpC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,SAAS,OAAO,OAAO,KAAK,MAAM,EAAE;QAC9D,WAAW;YACP,YAAY;QAChB,GAAG;IACP,GAAG;QACC;QACA;QACA;QACA;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,MAAM,OAAO,IAAI,cAAc,aAAa,MAAM,QAAQ,KAAK,YAAY,MAAM,IAAI,KAAK,WAAW;QACzG,IAAI;QACJ,gCAAgC;QAChC,MAAM,aAAa;YACf,IAAI,2BAA2B,OAAO,GAAG,uBAAuB,OAAO,EAAE;gBACrE,+CAA+C;gBAC/C,MAAM,cAAc,IAAI,OAAO,OAAO,KAAK,uBAAuB,OAAO;gBACzE,cAAc,OAAO,GAAG,cAAc,OAAO,GAAG;YACpD;YACA,2BAA2B,OAAO,GAAG,IAAI,OAAO,OAAO;QAC3D;QACA,MAAM,aAAa;YACf,uDAAuD;YACvD,wGAAwG;YACxG,mFAAmF;YACnF,IAAI,cAAc,OAAO,KAAK,UAAU;YACxC,uBAAuB,OAAO,GAAG,IAAI,OAAO,OAAO;YACnD,oCAAoC;YACpC,YAAY,WAAW;gBACnB,MAAM,WAAW,IAAI,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,OAAO;gBACnE;YACJ,GAAG,cAAc,OAAO;QAC5B;QACA,IAAI,YAAY,eAAe,kBAAkB;YAC7C;QACJ,OAAO;YACH;QACJ;QACA,OAAO,IAAI,aAAa;IAC5B,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,MAAM,MAAM,EAAE;YACd;QACJ;IACJ,GAAG;QACC;QACA,MAAM,MAAM;KACf;IACD,SAAS;QACL,IAAI;QACJ,IAAI,SAAS,OAAO,KAAK,IAAI,MAAM,OAAO,EAAE;YACxC,IAAI;YACJ,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC5C,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,MAAM,EAAE;gBAC9K,gBAAgB,cAAc;YAClC,GAAG,MAAM,OAAO;QACpB;QACA,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,QAAQ;YAC7C,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,MAAM,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,MAAM;YAC1K,SAAS,cAAc;QAC3B;IACJ;IACA,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS;IACnF,IAAI,mBAAmB;IACvB,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;QAC3C,UAAU;QACV,KAAK;QACL,WAAW,GAAG,WAAW,gBAAgB,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,EAAE,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,UAAU,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,UAAU;QAC7Z,qBAAqB;QACrB,oBAAoB,CAAC,oBAAoB,MAAM,UAAU,KAAK,OAAO,oBAAoB;QACzF,eAAe,CAAC,QAAQ,MAAM,GAAG,IAAI,MAAM,QAAQ,IAAI;QACvD,gBAAgB;QAChB,gBAAgB,QAAQ,MAAM,OAAO;QACrC,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,mBAAmB;QACnB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,oBAAoB;QACpB,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,wBAAwB;QACxB,iBAAiB,QAAQ,YAAY,mBAAmB;QACxD,OAAO;YACH,WAAW;YACX,mBAAmB;YACnB,aAAa,OAAO,MAAM,GAAG;YAC7B,YAAY,GAAG,UAAU,qBAAqB,OAAO,OAAO,CAAC,EAAE,CAAC;YAChE,oBAAoB,kBAAkB,SAAS,GAAG,cAAc,EAAE,CAAC;YACnE,GAAG,KAAK;YACR,GAAG,MAAM,KAAK;QAClB;QACA,WAAW;YACP,WAAW;YACX,kBAAkB;YAClB,gBAAgB,OAAO,GAAG;QAC9B;QACA,eAAe,CAAC;YACZ,IAAI,YAAY,CAAC,aAAa;YAC9B,cAAc,OAAO,GAAG,IAAI;YAC5B,sBAAsB,OAAO,OAAO;YACpC,sGAAsG;YACtG,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,SAAS;YAC9C,IAAI,MAAM,MAAM,CAAC,OAAO,KAAK,UAAU;YACvC,WAAW;YACX,gBAAgB,OAAO,GAAG;gBACtB,GAAG,MAAM,OAAO;gBAChB,GAAG,MAAM,OAAO;YACpB;QACJ;QACA,aAAa;YACT,IAAI,mBAAmB,oBAAoB;YAC3C,IAAI,YAAY,CAAC,aAAa;YAC9B,gBAAgB,OAAO,GAAG;YAC1B,MAAM,eAAe,OAAO,CAAC,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KAAK;YAC1K,MAAM,eAAe,OAAO,CAAC,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,gBAAgB,CAAC,oBAAoB,OAAO,CAAC,MAAM,GAAG,KAAK;YAC5K,MAAM,YAAY,IAAI,OAAO,OAAO,KAAK,CAAC,CAAC,yBAAyB,cAAc,OAAO,KAAK,OAAO,KAAK,IAAI,uBAAuB,OAAO,EAAE;YAC9I,MAAM,cAAc,mBAAmB,MAAM,eAAe;YAC5D,MAAM,WAAW,KAAK,GAAG,CAAC,eAAe;YACzC,IAAI,KAAK,GAAG,CAAC,gBAAgB,mBAAmB,WAAW,MAAM;gBAC7D,sBAAsB,OAAO,OAAO;gBACpC,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;gBAC/D,IAAI,mBAAmB,KAAK;oBACxB,qBAAqB,eAAe,IAAI,UAAU;gBACtD,OAAO;oBACH,qBAAqB,eAAe,IAAI,SAAS;gBACrD;gBACA;gBACA,YAAY;gBACZ;YACJ,OAAO;gBACH,IAAI,oBAAoB;gBACxB,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC;gBACzH,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,CAAC,GAAG,CAAC;YAC7H;YACA,YAAY;YACZ,WAAW;YACX,kBAAkB;QACtB;QACA,eAAe,CAAC;YACZ,IAAI,sBACJ,mBAAmB;YACnB,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,aAAa;YAC9C,MAAM,gBAAgB,CAAC,CAAC,uBAAuB,OAAO,YAAY,EAAE,KAAK,OAAO,KAAK,IAAI,qBAAqB,QAAQ,GAAG,MAAM,IAAI;YACnI,IAAI,eAAe;YACnB,MAAM,SAAS,MAAM,OAAO,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACxD,MAAM,SAAS,MAAM,OAAO,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACxD,IAAI;YACJ,MAAM,kBAAkB,CAAC,yBAAyB,MAAM,eAAe,KAAK,OAAO,yBAAyB,0BAA0B;YACtI,kDAAkD;YAClD,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK,GAAG,CAAC,UAAU,CAAC,GAAG;gBACnE,kBAAkB,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,MAAM;YAClE;YACA,IAAI,cAAc;gBACd,GAAG;gBACH,GAAG;YACP;YACA,MAAM,eAAe,CAAC;gBAClB,MAAM,SAAS,KAAK,GAAG,CAAC,SAAS;gBACjC,OAAO,IAAI,CAAC,MAAM,MAAM;YAC5B;YACA,2CAA2C;YAC3C,IAAI,mBAAmB,KAAK;gBACxB,yBAAyB;gBACzB,IAAI,gBAAgB,QAAQ,CAAC,UAAU,gBAAgB,QAAQ,CAAC,WAAW;oBACvE,IAAI,gBAAgB,QAAQ,CAAC,UAAU,SAAS,KAAK,gBAAgB,QAAQ,CAAC,aAAa,SAAS,GAAG;wBACnG,YAAY,CAAC,GAAG;oBACpB,OAAO;wBACH,2CAA2C;wBAC3C,MAAM,gBAAgB,SAAS,aAAa;wBAC5C,+DAA+D;wBAC/D,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,UAAU,gBAAgB;oBACjF;gBACJ;YACJ,OAAO,IAAI,mBAAmB,KAAK;gBAC/B,2BAA2B;gBAC3B,IAAI,gBAAgB,QAAQ,CAAC,WAAW,gBAAgB,QAAQ,CAAC,UAAU;oBACvE,IAAI,gBAAgB,QAAQ,CAAC,WAAW,SAAS,KAAK,gBAAgB,QAAQ,CAAC,YAAY,SAAS,GAAG;wBACnG,YAAY,CAAC,GAAG;oBACpB,OAAO;wBACH,2CAA2C;wBAC3C,MAAM,gBAAgB,SAAS,aAAa;wBAC5C,+DAA+D;wBAC/D,YAAY,CAAC,GAAG,KAAK,GAAG,CAAC,iBAAiB,KAAK,GAAG,CAAC,UAAU,gBAAgB;oBACjF;gBACJ;YACJ;YACA,IAAI,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC,IAAI,GAAG;gBAC5D,YAAY;YAChB;YACA,CAAC,oBAAoB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK,CAAC,WAAW,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;YACtI,CAAC,qBAAqB,SAAS,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK,CAAC,WAAW,CAAC,oBAAoB,GAAG,YAAY,CAAC,CAAC,EAAE,CAAC;QAC5I;IACJ,GAAG,eAAe,CAAC,MAAM,GAAG,IAAI,cAAc,YAAY,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClG,cAAc;QACd,iBAAiB;QACjB,qBAAqB;QACrB,SAAS,YAAY,CAAC,cAAc,KAAK,IAAI;YACzC;YACA,MAAM,SAAS,IAAI,OAAO,KAAK,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO;QACnE;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,WAAW;IAC1L,GAAG,CAAC,eAAe,SAAS,OAAO,KAAK,IAAI,MAAM,KAAK,KAAK,OAAO,eAAe,aAAa,MAAM,CAAC,aAAa,MAAM,IAAI,IAAI,MAAM,OAAO,KAAK,MAAM,IAAI,KAAK,QAAQ,CAAC,CAAC,SAAS,OAAO,KAAK,IAAI,KAAK,CAAC,UAAU,MAAM,QAAQ,MAAM,IAAI,IAAI,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtR,aAAa;QACb,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,IAAI;IAC5K,GAAG,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,aAAa,CAAC,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,mBAAmB,MAAM,MAAM,IAAI,KAAK,YAAY,OAAO,QAAQ,MAAM,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QAC1L,gBAAgB;QAChB,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,OAAO;IAClL,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACxC,cAAc;QACd,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,KAAK,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,KAAK;IAC9K,GAAG,MAAM,GAAG,GAAG,MAAM,GAAG,GAAG,OAAO,MAAM,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK,MAAM,KAAK,GAAG,MAAM,WAAW,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;QACtJ,oBAAoB;QACpB,WAAW,GAAG,sBAAsB,2BAA2B,cAAc,OAAO,KAAK,IAAI,WAAW,WAAW,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,WAAW;IAC3O,GAAG,OAAO,MAAM,WAAW,KAAK,aAAa,MAAM,WAAW,KAAK,MAAM,WAAW,IAAI,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,SAAS,MAAM,MAAM,IAAI,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClP,eAAe;QACf,eAAe;QACf,OAAO,MAAM,iBAAiB,IAAI;QAClC,SAAS,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG;YAC7B,IAAI,CAAC,aAAa;YAClB,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE;YAChF;QACJ;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,YAAY;IAC5L,GAAG,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,cAAc,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,IAAI,SAAS,MAAM,MAAM,IAAI,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAClL,eAAe;QACf,eAAe;QACf,OAAO,MAAM,iBAAiB,IAAI;QAClC,SAAS,CAAC;YACN,4CAA4C;YAC5C,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG;YAC7B,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,MAAM,EAAE;YAChF,IAAI,MAAM,gBAAgB,EAAE;YAC5B;QACJ;QACA,WAAW,GAAG,cAAc,OAAO,KAAK,IAAI,WAAW,YAAY,EAAE,SAAS,OAAO,KAAK,IAAI,CAAC,qBAAqB,MAAM,UAAU,KAAK,OAAO,KAAK,IAAI,mBAAmB,YAAY;IAC5L,GAAG,MAAM,MAAM,CAAC,KAAK,IAAI;AAC7B;AACA,SAAS;IACL,IAAI,OAAO,WAAW,aAAa,OAAO;IAC1C,IAAI,OAAO,aAAa,aAAa,OAAO,OAAO,oBAAoB;IACvE,MAAM,eAAe,SAAS,eAAe,CAAC,YAAY,CAAC;IAC3D,IAAI,iBAAiB,UAAU,CAAC,cAAc;QAC1C,OAAO,OAAO,gBAAgB,CAAC,SAAS,eAAe,EAAE,SAAS;IACtE;IACA,OAAO;AACX;AACA,SAAS,aAAa,aAAa,EAAE,YAAY;IAC7C,MAAM,SAAS,CAAC;IAChB;QACI;QACA;KACH,CAAC,OAAO,CAAC,CAAC,QAAQ;QACf,MAAM,WAAW,UAAU;QAC3B,MAAM,SAAS,WAAW,oBAAoB;QAC9C,MAAM,eAAe,WAAW,yBAAyB;QACzD,SAAS,UAAU,MAAM;YACrB;gBACI;gBACA;gBACA;gBACA;aACH,CAAC,OAAO,CAAC,CAAC;gBACP,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,WAAW,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;YAC9E;QACJ;QACA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;YAC1D,UAAU;QACd,OAAO,IAAI,OAAO,WAAW,UAAU;YACnC;gBACI;gBACA;gBACA;gBACA;aACH,CAAC,OAAO,CAAC,CAAC;gBACP,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW;oBAC3B,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG;gBACjC,OAAO;oBACH,MAAM,CAAC,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,OAAO,MAAM,CAAC,IAAI,KAAK,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI;gBACnG;YACJ;QACJ,OAAO;YACH,UAAU;QACd;IACJ;IACA,OAAO;AACX;AACA,SAAS;IACL,MAAM,CAAC,cAAc,gBAAgB,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IACzD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC,CAAC;YACzB,IAAI,MAAM,OAAO,EAAE;gBACf,WAAW;oBACP,4MAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;wBACf,gBAAgB,CAAC,SAAS,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;oBAClE;gBACJ;gBACA;YACJ;YACA,mCAAmC;YACnC,WAAW;gBACP,4MAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;oBACf,gBAAgB,CAAC;wBACb,MAAM,uBAAuB,OAAO,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;wBACpE,wCAAwC;wBACxC,IAAI,yBAAyB,CAAC,GAAG;4BAC7B,OAAO;mCACA,OAAO,KAAK,CAAC,GAAG;gCACnB;oCACI,GAAG,MAAM,CAAC,qBAAqB;oCAC/B,GAAG,KAAK;gCACZ;mCACG,OAAO,KAAK,CAAC,uBAAuB;6BAC1C;wBACL;wBACA,OAAO;4BACH;+BACG;yBACN;oBACL;gBACJ;YACJ;QACJ;IACJ,GAAG,EAAE;IACL,OAAO;QACH,QAAQ;IACZ;AACJ;AACA,MAAM,UAAU,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,UAAU,CAAC,SAAS,QAAQ,KAAK,EAAE,GAAG;IACtE,MAAM,EAAE,MAAM,EAAE,WAAW,cAAc,EAAE,SAAS;QAChD;QACA;KACH,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,qBAAqB,EAAE,YAAY,EAAE,MAAM,sBAAsB,EAAE,MAAM,GAAG,EAAE,KAAK,EAAE,qBAAqB,eAAe,EAAE,GAAG;IACrP,MAAM,CAAC,QAAQ,UAAU,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IAC7C,MAAM,oBAAoB,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QACpC,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI;YACtB;SACH,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,QAAQ,MAAM,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,MAAM,QAAQ;IAC/E,GAAG;QACC;QACA;KACH;IACD,MAAM,CAAC,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,UAAU,WAAW,QAAQ,OAAO,WAAW,cAAc,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS,UAAU;IACtN,MAAM,UAAU,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,MAAM,cAAc,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,UAAU;IAC3E,MAAM,wBAAwB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC3C,MAAM,mBAAmB,qMAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACtC,MAAM,cAAc,qMAAA,CAAA,UAAK,CAAC,WAAW,CAAC,CAAC;QACnC,UAAU,CAAC;YACP,IAAI;YACJ,IAAI,CAAC,CAAC,CAAC,eAAe,OAAO,IAAI,CAAC,CAAC,QAAQ,MAAM,EAAE,KAAK,cAAc,EAAE,CAAC,KAAK,OAAO,KAAK,IAAI,aAAa,MAAM,GAAG;gBAChH,WAAW,OAAO,CAAC,cAAc,EAAE;YACvC;YACA,OAAO,OAAO,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,OAAO,cAAc,EAAE;QAC1D;IACJ,GAAG,EAAE;IACL,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,OAAO,WAAW,SAAS,CAAC,CAAC;YACzB,IAAI,MAAM,OAAO,EAAE;gBACf,0CAA0C;gBAC1C,sBAAsB;oBAClB,UAAU,CAAC,SAAS,OAAO,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;gCAC5C,GAAG,CAAC;gCACJ,QAAQ;4BACZ,IAAI;gBAChB;gBACA;YACJ;YACA,mCAAmC;YACnC,WAAW;gBACP,4MAAA,CAAA,UAAQ,CAAC,SAAS,CAAC;oBACf,UAAU,CAAC;wBACP,MAAM,uBAAuB,OAAO,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,KAAK,MAAM,EAAE;wBACpE,wCAAwC;wBACxC,IAAI,yBAAyB,CAAC,GAAG;4BAC7B,OAAO;mCACA,OAAO,KAAK,CAAC,GAAG;gCACnB;oCACI,GAAG,MAAM,CAAC,qBAAqB;oCAC/B,GAAG,KAAK;gCACZ;mCACG,OAAO,KAAK,CAAC,uBAAuB;6BAC1C;wBACL;wBACA,OAAO;4BACH;+BACG;yBACN;oBACL;gBACJ;YACJ;QACJ;IACJ,GAAG;QACC;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,UAAU,UAAU;YACpB,eAAe;YACf;QACJ;QACA,IAAI,UAAU,UAAU;YACpB,sCAAsC;YACtC,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,CAAC,gCAAgC,OAAO,EAAE;gBAChF,sBAAsB;gBACtB,eAAe;YACnB,OAAO;gBACH,gBAAgB;gBAChB,eAAe;YACnB;QACJ;QACA,IAAI,OAAO,WAAW,aAAa;QACnC,MAAM,iBAAiB,OAAO,UAAU,CAAC;QACzC,IAAI;YACA,mBAAmB;YACnB,eAAe,gBAAgB,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE;gBAClD,IAAI,SAAS;oBACT,eAAe;gBACnB,OAAO;oBACH,eAAe;gBACnB;YACJ;QACJ,EAAE,OAAO,OAAO;YACZ,cAAc;YACd,eAAe,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE;gBACnC,IAAI;oBACA,IAAI,SAAS;wBACT,eAAe;oBACnB,OAAO;wBACH,eAAe;oBACnB;gBACJ,EAAE,OAAO,GAAG;oBACR,QAAQ,KAAK,CAAC;gBAClB;YACJ;QACJ;IACJ,GAAG;QACC;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,6EAA6E;QAC7E,IAAI,OAAO,MAAM,IAAI,GAAG;YACpB,YAAY;QAChB;IACJ,GAAG;QACC;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,MAAM,gBAAgB,CAAC;YACnB,IAAI;YACJ,MAAM,kBAAkB,OAAO,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,MAAM,IAAI,KAAK;YACzE,IAAI,iBAAiB;gBACjB,IAAI;gBACJ,YAAY;gBACZ,CAAC,oBAAoB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,KAAK;YACpF;YACA,IAAI,MAAM,IAAI,KAAK,YAAY,CAAC,SAAS,aAAa,KAAK,QAAQ,OAAO,IAAI,CAAC,CAAC,mBAAmB,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI,iBAAiB,QAAQ,CAAC,SAAS,aAAa,CAAC,CAAC,GAAG;gBACxL,YAAY;YAChB;QACJ;QACA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAI,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QACC;KACH;IACD,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACZ,IAAI,QAAQ,OAAO,EAAE;YACjB,OAAO;gBACH,IAAI,sBAAsB,OAAO,EAAE;oBAC/B,sBAAsB,OAAO,CAAC,KAAK,CAAC;wBAChC,eAAe;oBACnB;oBACA,sBAAsB,OAAO,GAAG;oBAChC,iBAAiB,OAAO,GAAG;gBAC/B;YACJ;QACJ;IACJ,GAAG;QACC,QAAQ,OAAO;KAClB;IACD,OACA,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,WAAW;QACzC,KAAK;QACL,cAAc,GAAG,mBAAmB,CAAC,EAAE,aAAa;QACpD,UAAU,CAAC;QACX,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,0BAA0B;IAC9B,GAAG,kBAAkB,GAAG,CAAC,CAAC,UAAU;QAChC,IAAI;QACJ,MAAM,CAAC,GAAG,EAAE,GAAG,SAAS,KAAK,CAAC;QAC9B,IAAI,CAAC,OAAO,MAAM,EAAE,OAAO;QAC3B,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,MAAM;YAC3C,KAAK;YACL,KAAK,QAAQ,SAAS,yBAAyB;YAC/C,UAAU,CAAC;YACX,KAAK;YACL,WAAW;YACX,uBAAuB;YACvB,qBAAqB;YACrB,mBAAmB;YACnB,eAAe,YAAY,OAAO,MAAM,GAAG,KAAK,CAAC;YACjD,mBAAmB;YACnB,OAAO;gBACH,wBAAwB,GAAG,CAAC,CAAC,YAAY,OAAO,CAAC,EAAE,KAAK,OAAO,KAAK,IAAI,UAAU,MAAM,KAAK,EAAE,EAAE,CAAC;gBAClG,WAAW,GAAG,YAAY,EAAE,CAAC;gBAC7B,SAAS,GAAG,IAAI,EAAE,CAAC;gBACnB,GAAG,KAAK;gBACR,GAAG,aAAa,QAAQ,aAAa;YACzC;YACA,QAAQ,CAAC;gBACL,IAAI,iBAAiB,OAAO,IAAI,CAAC,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM,aAAa,GAAG;oBAChF,iBAAiB,OAAO,GAAG;oBAC3B,IAAI,sBAAsB,OAAO,EAAE;wBAC/B,sBAAsB,OAAO,CAAC,KAAK,CAAC;4BAChC,eAAe;wBACnB;wBACA,sBAAsB,OAAO,GAAG;oBACpC;gBACJ;YACJ;YACA,SAAS,CAAC;gBACN,MAAM,mBAAmB,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK;gBACrG,IAAI,kBAAkB;gBACtB,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC3B,iBAAiB,OAAO,GAAG;oBAC3B,sBAAsB,OAAO,GAAG,MAAM,aAAa;gBACvD;YACJ;YACA,cAAc,IAAI,YAAY;YAC9B,aAAa,IAAI,YAAY;YAC7B,cAAc;gBACV,8EAA8E;gBAC9E,IAAI,CAAC,aAAa;oBACd,YAAY;gBAChB;YACJ;YACA,WAAW,IAAI,YAAY;YAC3B,eAAe,CAAC;gBACZ,MAAM,mBAAmB,MAAM,MAAM,YAAY,eAAe,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK;gBACrG,IAAI,kBAAkB;gBACtB,eAAe;YACnB;YACA,aAAa,IAAI,eAAe;QACpC,GAAG,OAAO,MAAM,CAAC,CAAC,QAAQ,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,MAAM,QAAQ,KAAK,UAAU,GAAG,CAAC,CAAC,OAAO;YACjG,IAAI,wBAAwB;YAC5B,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,OAAO;gBAC5C,KAAK,MAAM,EAAE;gBACb,OAAO;gBACP,OAAO;gBACP,OAAO;gBACP,mBAAmB;gBACnB,UAAU,CAAC,yBAAyB,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ,KAAK,OAAO,yBAAyB;gBAC9H,WAAW,gBAAgB,OAAO,KAAK,IAAI,aAAa,SAAS;gBACjE,sBAAsB,gBAAgB,OAAO,KAAK,IAAI,aAAa,oBAAoB;gBACvF,QAAQ;gBACR,eAAe;gBACf,aAAa,CAAC,4BAA4B,gBAAgB,OAAO,KAAK,IAAI,aAAa,WAAW,KAAK,OAAO,4BAA4B;gBAC1I,aAAa;gBACb,UAAU;gBACV,OAAO,gBAAgB,OAAO,KAAK,IAAI,aAAa,KAAK;gBACzD,UAAU,gBAAgB,OAAO,KAAK,IAAI,aAAa,QAAQ;gBAC/D,YAAY,gBAAgB,OAAO,KAAK,IAAI,aAAa,UAAU;gBACnE,mBAAmB,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB;gBACjF,mBAAmB,gBAAgB,OAAO,KAAK,IAAI,aAAa,iBAAiB;gBACjF,sBAAsB,gBAAgB,OAAO,KAAK,IAAI,aAAa,oBAAoB;gBACvF,aAAa;gBACb,QAAQ,OAAO,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ;gBACvD,SAAS,QAAQ,MAAM,CAAC,CAAC,IAAI,EAAE,QAAQ,IAAI,MAAM,QAAQ;gBACzD,YAAY;gBACZ,iBAAiB;gBACjB,KAAK;gBACL,UAAU;gBACV,iBAAiB,MAAM,eAAe;YAC1C;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}]}