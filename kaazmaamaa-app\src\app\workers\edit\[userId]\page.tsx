'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON>er, useParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useWorkers } from '@/contexts/WorkersContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Save, 
  User,
  Briefcase,
  FileText,
  CreditCard,
  Calendar
} from 'lucide-react'
import { toast } from 'sonner'

export default function EditWorkerProfilePage() {
  const router = useRouter()
  const params = useParams()
  const { user } = useAuth()
  const { workers, updateWorker } = useWorkers()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [currentStep, setCurrentStep] = useState(1)

  // Find the worker profile to edit
  const workerProfile = workers.find(w => w.userId === params.userId)

  // Personal Information
  const [personalInfo, setPersonalInfo] = useState({
    workerName: '',
    nationality: '',
    kofilName: '',
    kofilPhone: '',
    email: '',
    phone: '',
    whatsapp: '',
    address: '',
    age: 0,
    profileImage: ''
  })

  // Identification Information
  const [identificationInfo, setIdentificationInfo] = useState({
    iqamaNumber: '',
    passportNumber: '',
    crNumber: ''
  })

  // Professional Information
  const [professionalInfo, setProfessionalInfo] = useState({
    workerCategory: '',
    title: '',
    specialty: '',
    experience: 0,
    skills: [] as string[],
    languages: [] as string[],
    expectedSalaryPerHour: 0,
    currency: 'SAR' as 'SAR' | 'USD' | 'EUR',
    salaryPayingDuration: 'monthly' as 'daily' | 'weekly' | 'monthly' | 'hourly',
    availability: 'immediate' as 'immediate' | 'within_week' | 'within_month' | 'negotiable'
  })

  // Lifecycle Information
  const [lifecycle, setLifecycle] = useState({
    joiningDate: '',
    vacationStart: '',
    vacationEnd: '',
    leavingDate: '',
    status: 'available' as 'available' | 'working' | 'on_vacation' | 'left' | 'seeking',
    currentEmployer: '',
    notes: ''
  })

  // Preferences
  const [preferences, setPreferences] = useState({
    workType: 'full_time' as 'full_time' | 'part_time' | 'contract' | 'freelance',
    sideLocationInterest: [] as string[],
    companyNameInterest: [] as string[],
    preferredLocations: [] as string[],
    willingToRelocate: false,
    remoteWork: false,
    neededDocuments: [] as string[],
    otherRequirements: ''
  })

  useEffect(() => {
    // Check if user is authorized to edit this profile
    if (user?.id !== params.userId) {
      toast.error('You can only edit your own profile')
      router.push('/feed')
      return
    }

    if (workerProfile) {
      // Load existing data
      setPersonalInfo(workerProfile.personalInfo)
      setIdentificationInfo(workerProfile.identificationInfo)
      setProfessionalInfo(workerProfile.professionalInfo)
      setLifecycle(workerProfile.lifecycle)
      setPreferences(workerProfile.preferences)
    } else {
      // If no worker profile exists, initialize with default values
      setPersonalInfo({
        workerName: 'Hossain Belal', // Your actual name
        nationality: '',
        kofilName: '',
        kofilPhone: '',
        email: user?.email || '',
        phone: '',
        whatsapp: '',
        address: '',
        age: 0,
        profileImage: ''
      })
    }
    
    setIsLoading(false)
  }, [user, params.userId, workerProfile, router])

  const handlePersonalInfoChange = (field: string, value: any) => {
    setPersonalInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleIdentificationInfoChange = (field: string, value: string) => {
    setIdentificationInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleProfessionalInfoChange = (field: string, value: any) => {
    setProfessionalInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleLifecycleChange = (field: string, value: any) => {
    setLifecycle(prev => ({ ...prev, [field]: value }))
  }

  const handlePreferencesChange = (field: string, value: any) => {
    setPreferences(prev => ({ ...prev, [field]: value }))
  }

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(personalInfo.workerName && personalInfo.phone && personalInfo.email)
      case 2:
        return !!(professionalInfo.title && professionalInfo.specialty && professionalInfo.workerCategory)
      case 3:
        return !!(lifecycle.joiningDate)
      default:
        return false
    }
  }

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3))
    } else {
      toast.error('Please fill in all required fields before proceeding')
    }
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSave = async () => {
    if (!validateStep(3)) {
      toast.error('Please complete all required sections')
      return
    }

    setIsSaving(true)

    try {
      const updatedProfile = {
        id: workerProfile?.id || Date.now().toString(),
        userId: user?.id || params.userId as string,
        personalInfo,
        identificationInfo,
        professionalInfo,
        documents: workerProfile?.documents || [],
        videos: workerProfile?.videos || [],
        workExperience: workerProfile?.workExperience || [],
        lifecycle,
        preferences,
        isLiveStreaming: workerProfile?.isLiveStreaming || false,
        liveStreamUrl: workerProfile?.liveStreamUrl,
        createdAt: workerProfile?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isVerified: workerProfile?.isVerified || false,
        rating: workerProfile?.rating || 0,
        totalReviews: workerProfile?.totalReviews || 0
      }

      // Update or create profile
      updateWorker(updatedProfile)

      if (workerProfile) {
        toast.success('Profile updated successfully! ✅')
      } else {
        toast.success('Profile created successfully! 🎉')
      }

      // Redirect to profile page
      router.push(`/profile/${params.userId}`)
    } catch (error) {
      console.error('Error saving profile:', error)
      toast.error('Failed to save profile. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1: return 'Personal Information'
      case 2: return 'Professional Details'
      case 3: return 'Work Lifecycle'
      default: return 'Profile Setup'
    }
  }

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1: return User
      case 2: return Briefcase
      case 3: return Calendar
      default: return User
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/profile/${params.userId}`)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Profile
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Edit Worker Profile</h1>
            </div>
            <Badge variant="outline" className="text-blue-600">
              Step {currentStep} of 3
            </Badge>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            {[1, 2, 3].map((step) => {
              const StepIcon = getStepIcon(step)
              const isActive = step === currentStep
              const isCompleted = step < currentStep
              const isValid = validateStep(step)

              return (
                <div key={step} className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${
                      isCompleted
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : isActive
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : isValid
                        ? 'bg-gray-100 border-gray-300 text-gray-600'
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                    }`}
                  >
                    <StepIcon className="h-6 w-6" />
                  </div>
                  <span className={`text-xs mt-2 text-center ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                    {getStepTitle(step)}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        {/* Form Content */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {(() => {
                const StepIcon = getStepIcon(currentStep)
                return <StepIcon className="h-5 w-5 text-blue-600" />
              })()}
              <span>{getStepTitle(currentStep)}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="workerName">Full Name *</Label>
                    <Input
                      id="workerName"
                      value={personalInfo.workerName}
                      onChange={(e) => handlePersonalInfoChange('workerName', e.target.value)}
                      placeholder="Your full name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="nationality">Nationality *</Label>
                    <Input
                      id="nationality"
                      value={personalInfo.nationality}
                      onChange={(e) => handlePersonalInfoChange('nationality', e.target.value)}
                      placeholder="Your nationality"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="age">Age</Label>
                    <Input
                      id="age"
                      type="number"
                      value={personalInfo.age}
                      onChange={(e) => handlePersonalInfoChange('age', parseInt(e.target.value) || 0)}
                      placeholder="Your age"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="kofilName">Kofil Name</Label>
                    <Input
                      id="kofilName"
                      value={personalInfo.kofilName}
                      onChange={(e) => handlePersonalInfoChange('kofilName', e.target.value)}
                      placeholder="Sponsor name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="kofilPhone">Kofil Phone</Label>
                    <Input
                      id="kofilPhone"
                      value={personalInfo.kofilPhone}
                      onChange={(e) => handlePersonalInfoChange('kofilPhone', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={personalInfo.email}
                      onChange={(e) => handlePersonalInfoChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      value={personalInfo.phone}
                      onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="whatsapp">WhatsApp Number</Label>
                    <Input
                      id="whatsapp"
                      value={personalInfo.whatsapp}
                      onChange={(e) => handlePersonalInfoChange('whatsapp', e.target.value)}
                      placeholder="+966 XXX XXX XXX"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={personalInfo.address}
                    onChange={(e) => handlePersonalInfoChange('address', e.target.value)}
                    placeholder="Your complete address"
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Step 2: Professional Information */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="workerCategory">Worker Category *</Label>
                    <Input
                      id="workerCategory"
                      value={professionalInfo.workerCategory}
                      onChange={(e) => handleProfessionalInfoChange('workerCategory', e.target.value)}
                      placeholder="e.g., Electrical Technician"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="title">Job Title *</Label>
                    <Input
                      id="title"
                      value={professionalInfo.title}
                      onChange={(e) => handleProfessionalInfoChange('title', e.target.value)}
                      placeholder="e.g., Senior Electrical Technician"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="specialty">Specialty *</Label>
                    <Input
                      id="specialty"
                      value={professionalInfo.specialty}
                      onChange={(e) => handleProfessionalInfoChange('specialty', e.target.value)}
                      placeholder="e.g., Industrial Electrical Systems"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="experience">Years of Experience</Label>
                    <Input
                      id="experience"
                      type="number"
                      value={professionalInfo.experience}
                      onChange={(e) => handleProfessionalInfoChange('experience', parseInt(e.target.value) || 0)}
                      placeholder="5"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="expectedSalaryPerHour">Expected Salary Per Hour</Label>
                    <Input
                      id="expectedSalaryPerHour"
                      type="number"
                      value={professionalInfo.expectedSalaryPerHour}
                      onChange={(e) => handleProfessionalInfoChange('expectedSalaryPerHour', parseInt(e.target.value) || 0)}
                      placeholder="85"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <select
                      id="currency"
                      value={professionalInfo.currency}
                      onChange={(e) => handleProfessionalInfoChange('currency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="SAR">SAR</option>
                      <option value="USD">USD</option>
                      <option value="EUR">EUR</option>
                    </select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="skills">Skills</Label>
                  <Input
                    id="skills"
                    value={professionalInfo.skills.join(', ')}
                    onChange={(e) => handleProfessionalInfoChange('skills', e.target.value.split(', ').filter(Boolean))}
                    placeholder="e.g., Electrical Installation, Troubleshooting, Safety (comma separated)"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="languages">Languages</Label>
                  <Input
                    id="languages"
                    value={professionalInfo.languages.join(', ')}
                    onChange={(e) => handleProfessionalInfoChange('languages', e.target.value.split(', ').filter(Boolean))}
                    placeholder="e.g., Arabic, English, Urdu (comma separated)"
                  />
                </div>
              </div>
            )}

            {/* Step 3: Work Lifecycle */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="joiningDate">Joining Date *</Label>
                    <Input
                      id="joiningDate"
                      type="date"
                      value={lifecycle.joiningDate}
                      onChange={(e) => handleLifecycleChange('joiningDate', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="status">Current Status</Label>
                    <select
                      id="status"
                      value={lifecycle.status}
                      onChange={(e) => handleLifecycleChange('status', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="available">Available</option>
                      <option value="working">Working</option>
                      <option value="on_vacation">On Vacation</option>
                      <option value="left">Left</option>
                      <option value="seeking">Seeking</option>
                    </select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currentEmployer">Current Employer</Label>
                    <Input
                      id="currentEmployer"
                      value={lifecycle.currentEmployer}
                      onChange={(e) => handleLifecycleChange('currentEmployer', e.target.value)}
                      placeholder="Company name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="leavingDate">Leaving Date (if applicable)</Label>
                    <Input
                      id="leavingDate"
                      type="date"
                      value={lifecycle.leavingDate}
                      onChange={(e) => handleLifecycleChange('leavingDate', e.target.value)}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="notes">Notes</Label>
                  <Textarea
                    id="notes"
                    value={lifecycle.notes}
                    onChange={(e) => handleLifecycleChange('notes', e.target.value)}
                    placeholder="Additional notes about work status"
                    rows={3}
                  />
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
              >
                Previous
              </Button>
              
              {currentStep < 3 ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={!validateStep(currentStep)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleSave}
                  disabled={isSaving || !validateStep(3)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Saving...' : 'Save Profile'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
