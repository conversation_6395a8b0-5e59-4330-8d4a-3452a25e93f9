'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useWorkers } from '@/contexts/WorkersContext'
import { useSuppliers } from '@/contexts/SuppliersContext'
import { useCompanies } from '@/contexts/CompaniesContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Users, 
  Building, 
  Wrench, 
  Search, 
  MessageCircle, 
  Phone,
  ArrowLeft,
  MapPin,
  Star,
  Plus,
  FileText,
  Video,
  Calendar,
  Briefcase,
  Eye,
  Download,
  Play,
  Radio,
  CheckCircle,
  Clock,
  DollarSign,
  Globe,
  Factory,
  List,
  Filter
} from 'lucide-react'
import { toast } from 'sonner'

export default function DirectoryPage() {
  const { user, userRole } = useAuth()
  const { workers } = useWorkers()
  const { suppliers } = useSuppliers()
  const { companies } = useCompanies()
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')

  useEffect(() => {
    if (!user) {
      router.push('/')
    }
  }, [user, router])

  const getRoleIcon = (type: string) => {
    switch (type) {
      case 'worker':
        return <Wrench className="h-8 w-8 text-blue-600" />
      case 'supplier':
        return <Factory className="h-8 w-8 text-green-600" />
      case 'company':
        return <Building className="h-8 w-8 text-purple-600" />
      default:
        return <Users className="h-8 w-8 text-gray-600" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'worker':
        return 'bg-blue-100 text-blue-800'
      case 'supplier':
        return 'bg-green-100 text-green-800'
      case 'company':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  // Combine all profiles into a unified list
  const allProfiles = [
    ...workers.map(worker => ({
      id: worker.id,
      type: 'worker' as const,
      name: worker.personalInfo?.workerName || 'Unknown Worker',
      title: worker.professionalInfo?.title || 'No Title',
      location: worker.personalInfo?.address || 'Location not specified',
      rating: worker.rating || 0,
      totalReviews: worker.totalReviews || 0,
      isVerified: worker.isVerified || false,
      isLiveStreaming: worker.isLiveStreaming || false,
      documentsCount: (worker.documents || []).length,
      videosCount: (worker.videos || []).length,
      whatsapp: worker.personalInfo?.whatsapp,
      profileImage: worker.personalInfo?.profileImage,
      specialty: worker.professionalInfo?.specialty || '',
      experience: worker.professionalInfo?.experience || 0,
      status: worker.lifecycle?.status || 'seeking'
    })),
    ...suppliers.map(supplier => ({
      id: supplier.id,
      type: 'supplier' as const,
      name: supplier.personalInfo?.supplierName || 'Unknown Supplier',
      title: supplier.businessInfo?.companyName || 'No Company',
      location: supplier.personalInfo?.address || 'Location not specified',
      rating: supplier.rating || 0,
      totalReviews: supplier.totalReviews || 0,
      isVerified: supplier.isVerified || false,
      isLiveStreaming: supplier.isLiveStreaming || false,
      documentsCount: (supplier.documents || []).length,
      videosCount: (supplier.videos || []).length,
      whatsapp: supplier.personalInfo?.whatsapp,
      profileImage: null,
      specialty: supplier.businessInfo?.businessType || '',
      experience: 0,
      status: 'active'
    })),
    ...companies.map(company => ({
      id: company.id,
      type: 'company' as const,
      name: company.companyInfo?.companyName || 'Unknown Company',
      title: company.companyInfo?.industry || 'No Industry',
      location: company.companyInfo?.headquarters || 'Location not specified',
      rating: company.rating || 0,
      totalReviews: company.totalReviews || 0,
      isVerified: company.isVerified || false,
      isLiveStreaming: company.isLiveStreaming || false,
      documentsCount: (company.documents || []).length,
      videosCount: (company.videos || []).length,
      whatsapp: company.contactInfo?.hrWhatsapp,
      profileImage: null,
      specialty: company.companyInfo?.industry || '',
      experience: 0,
      status: company.isHiring ? 'hiring' : 'active'
    }))
  ]

  const filteredProfiles = allProfiles.filter(profile => {
    const matchesSearch = profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         profile.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         profile.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         profile.specialty.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || profile.type === selectedType
    return matchesSearch && matchesType
  })

  const handleViewProfile = (profile: any) => {
    const basePath = profile.type === 'worker' ? '/workers' : 
                    profile.type === 'supplier' ? '/suppliers' : '/companies'
    router.push(`${basePath}/${profile.id}`)
  }

  const handleWhatsAppContact = (whatsapp?: string, name?: string) => {
    if (whatsapp) {
      const message = `Hello ${name}, I found your profile on KAAZMAAMAA Directory and would like to connect.`
      const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
      window.open(whatsappUrl, '_blank')
    } else {
      toast.error('WhatsApp number not available')
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => router.push('/feed')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Feed
              </Button>
              <h1 className="text-2xl font-bold text-gray-900">Professional Directory</h1>
              <Badge className="bg-gray-100 text-gray-800">
                {filteredProfiles.length} Profiles
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Wrench className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Workers</p>
                  <p className="text-2xl font-bold text-gray-900">{workers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Factory className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Suppliers</p>
                  <p className="text-2xl font-bold text-gray-900">{suppliers.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Building className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Companies</p>
                  <p className="text-2xl font-bold text-gray-900">{companies.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <Users className="h-6 w-6 text-gray-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-2xl font-bold text-gray-900">{allProfiles.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search by name, title, location, or specialty..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="worker">Workers</option>
                <option value="supplier">Suppliers</option>
                <option value="company">Companies</option>
              </select>

              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedType('all')
                }}
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Profiles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProfiles.map((profile) => (
            <Card key={`${profile.type}-${profile.id}`} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <Avatar className="h-16 w-16 bg-gray-100 flex items-center justify-center">
                      {profile.profileImage ? (
                        <img 
                          src={profile.profileImage} 
                          alt={profile.name}
                          className="w-full h-full object-cover rounded-full"
                        />
                      ) : (
                        getRoleIcon(profile.type)
                      )}
                    </Avatar>
                    {profile.isLiveStreaming && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
                        <Radio className="h-3 w-3 mr-1" />
                        LIVE
                      </div>
                    )}
                    {profile.isVerified && (
                      <div className="absolute -bottom-1 -right-1 bg-blue-500 text-white rounded-full p-1">
                        <CheckCircle className="h-3 w-3" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg truncate">{profile.name}</h3>
                    <p className="text-gray-600 font-medium">{profile.title}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className={getTypeColor(profile.type)}>
                        {profile.type}
                      </Badge>
                      <div className="flex items-center text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="text-sm ml-1">{profile.rating}</span>
                        <span className="text-gray-500 text-sm ml-1">({profile.totalReviews})</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600 text-sm">
                    <MapPin className="h-4 w-4 mr-2" />
                    {profile.location}
                  </div>
                  
                  {profile.type === 'worker' && (
                    <div className="flex items-center text-gray-600 text-sm">
                      <Briefcase className="h-4 w-4 mr-2" />
                      {profile.experience} years experience
                    </div>
                  )}

                  <div className="flex items-center text-gray-600 text-sm">
                    <Globe className="h-4 w-4 mr-2" />
                    {profile.specialty}
                  </div>

                  {/* Media Indicators */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 pt-2 border-t">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {profile.documentsCount} docs
                    </div>
                    <div className="flex items-center">
                      <Video className="h-4 w-4 mr-1" />
                      {profile.videosCount} videos
                    </div>
                  </div>
                  
                  <div className="flex gap-2 pt-3">
                    <Button
                      size="sm"
                      onClick={() => handleViewProfile(profile)}
                      className="flex-1"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Profile
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleWhatsAppContact(profile.whatsapp, profile.name)}
                    >
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredProfiles.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <List className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Profiles Found</h3>
              <p className="text-gray-600 mb-4">
                Try adjusting your search criteria or filters to find profiles.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm('')
                  setSelectedType('all')
                }}
              >
                Clear All Filters
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
