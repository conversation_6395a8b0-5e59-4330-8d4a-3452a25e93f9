"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PostsContext */ \"(app-pages-browser)/./src/contexts/PostsContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FeedPage() {\n    _s();\n    const { user, userRole, getUserProfile, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { posts, addPost, likePost, reactToPost, addComment, sharePost, voteInPoll, checkIn, createEvent } = (0,_contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Create Post States\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postText, setPostText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFeeling, setSelectedFeeling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taggedUsers, setTaggedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postPrivacy, setPostPrivacy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('public');\n    // Comments States\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [commentTexts, setCommentTexts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAllComments, setShowAllComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Share States\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shareText, setShareText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Other States\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeStory, setActiveStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLocationPicker, setShowLocationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTagPicker, setShowTagPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (user && userRole) {\n                const profile = getUserProfile();\n                setUserProfile(profile);\n                console.log('User profile loaded:', profile);\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        userRole,\n        getUserProfile\n    ]);\n    // Redirect to home if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/');\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Helper function to get user's display name\n    const getUserDisplayName = ()=>{\n        if (!userProfile) return 'User';\n        if (userRole === 'worker') {\n            var _userProfile_personalInfo;\n            return ((_userProfile_personalInfo = userProfile.personalInfo) === null || _userProfile_personalInfo === void 0 ? void 0 : _userProfile_personalInfo.workerName) || 'Worker';\n        } else if (userRole === 'supplier') {\n            var _userProfile_personalInfo1;\n            return ((_userProfile_personalInfo1 = userProfile.personalInfo) === null || _userProfile_personalInfo1 === void 0 ? void 0 : _userProfile_personalInfo1.supplierName) || 'Supplier';\n        } else if (userRole === 'company') {\n            var _userProfile_companyInfo;\n            return ((_userProfile_companyInfo = userProfile.companyInfo) === null || _userProfile_companyInfo === void 0 ? void 0 : _userProfile_companyInfo.companyName) || 'Company';\n        }\n        return 'User';\n    };\n    // Helper function to get user's avatar/initial\n    const getUserInitial = ()=>{\n        const name = getUserDisplayName();\n        return name.charAt(0).toUpperCase();\n    };\n    // Helper function to get user's role emoji\n    const getUserRoleEmoji = ()=>{\n        switch(userRole){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Helper functions for Facebook-style features\n    const handleLikePost = (postId)=>{\n        likePost(postId);\n    };\n    const handleCommentSubmit = (postId)=>{\n        const commentText = commentTexts[postId];\n        if (!(commentText === null || commentText === void 0 ? void 0 : commentText.trim()) || !user) return;\n        addComment(postId, commentText, {\n            id: user.email,\n            name: getUserDisplayName(),\n            role: userRole || 'worker'\n        });\n        setCommentTexts((prev)=>({\n                ...prev,\n                [postId]: ''\n            }));\n    };\n    const handleSharePost = (postId)=>{\n        if (!shareText.trim()) {\n            sharePost(postId);\n        } else {\n            // Create a new post that shares the original\n            const originalPost = posts.find((p)=>p.id === postId);\n            if (originalPost) {\n                const newPost = {\n                    user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                    user_name: getUserDisplayName(),\n                    user_role: userRole || 'worker',\n                    content: shareText,\n                    post_type: 'text',\n                    visibility: 'public'\n                };\n                addPost(newPost);\n            }\n        }\n        setShowShareModal(null);\n        setShareText('');\n    };\n    const toggleComments = (postId)=>{\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !prev[postId]\n            }));\n    };\n    const formatTimeAgo = (dateString)=>{\n        const now = new Date();\n        const postDate = new Date(dateString);\n        const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d\");\n        const diffInWeeks = Math.floor(diffInDays / 7);\n        return \"\".concat(diffInWeeks, \"w\");\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'worker':\n                return '#2563eb';\n            case 'supplier':\n                return '#059669';\n            case 'company':\n                return '#7c3aed';\n            default:\n                return '#6b7280';\n        }\n    };\n    const getRoleEmoji = (role)=>{\n        switch(role){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Advanced reaction system\n    const reactions = [\n        {\n            type: 'like',\n            emoji: '👍',\n            color: '#1877f2',\n            label: 'Like'\n        },\n        {\n            type: 'love',\n            emoji: '❤️',\n            color: '#e74c3c',\n            label: 'Love'\n        },\n        {\n            type: 'care',\n            emoji: '🤗',\n            color: '#f39c12',\n            label: 'Care'\n        },\n        {\n            type: 'haha',\n            emoji: '😂',\n            color: '#f1c40f',\n            label: 'Haha'\n        },\n        {\n            type: 'wow',\n            emoji: '😮',\n            color: '#f39c12',\n            label: 'Wow'\n        },\n        {\n            type: 'sad',\n            emoji: '😢',\n            color: '#3498db',\n            label: 'Sad'\n        },\n        {\n            type: 'angry',\n            emoji: '😡',\n            color: '#e74c3c',\n            label: 'Angry'\n        }\n    ];\n    const handleReaction = (postId, reactionType)=>{\n        if (!user) return;\n        reactToPost(postId, reactionType, {\n            id: user.email,\n            name: getUserDisplayName()\n        });\n    };\n    const getReactionDisplay = (post)=>{\n        const reactionCounts = post.reaction_counts || {};\n        const totalReactions = Object.values(reactionCounts).reduce((sum, count)=>sum + (count || 0), 0);\n        if (totalReactions === 0) return null;\n        const topReactions = Object.entries(reactionCounts).filter((param)=>{\n            let [_, count] = param;\n            return count > 0;\n        }).sort((param, param1)=>{\n            let [_, a] = param, [__, b] = param1;\n            return b - a;\n        }).slice(0, 3);\n        return {\n            topReactions,\n            totalReactions\n        };\n    };\n    // Show loading if still authenticating\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#f0f2f5'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '2rem',\n                            marginBottom: '1rem'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#65676b'\n                        },\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 204,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 203,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: '#f0f2f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: '#1877f2',\n                    padding: '0.75rem 1rem',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: 'white',\n                                        margin: 0\n                                    },\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'rgba(255,255,255,0.2)',\n                                        borderRadius: '20px',\n                                        padding: '0.5rem 1rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search KAAZMAAMAA...\",\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            color: 'white',\n                                            outline: 'none',\n                                            fontSize: '0.875rem'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.75rem',\n                                        color: 'white'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                borderRadius: '50%',\n                                                width: '2rem',\n                                                height: '2rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '0.875rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: getUserInitial()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const profilePath = userRole === 'worker' ? '/workers/profile' : userRole === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                router.push(profilePath);\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Go Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                await signOut();\n                                                router.push('/');\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 2fr 1fr',\n                    gap: '1rem',\n                    padding: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Quick Access\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/workers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDD27\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Workers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/suppliers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e8f5e8',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Suppliers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/companies\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#f3e8ff',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFE2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/barta\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#fff3cd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Barta Messenger\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem',\n                                        overflowX: 'auto',\n                                        paddingBottom: '0.5rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: '120px',\n                                                height: '200px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(45deg, #1877f2, #42a5f5)',\n                                                position: 'relative',\n                                                cursor: 'pointer',\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                justifyContent: 'flex-end',\n                                                padding: '1rem',\n                                                color: 'white'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: 'absolute',\n                                                        top: '1rem',\n                                                        left: '1rem',\n                                                        backgroundColor: 'rgba(255,255,255,0.3)',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '0.875rem',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"Create Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            'Ahmed Al-Rashid',\n                                            'Saudi Building Co.',\n                                            'Tech Solutions',\n                                            'Construction Pro'\n                                        ].map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    minWidth: '120px',\n                                                    height: '200px',\n                                                    borderRadius: '12px',\n                                                    background: \"linear-gradient(45deg, \".concat([\n                                                        '#ff6b6b',\n                                                        '#4ecdc4',\n                                                        '#45b7d1',\n                                                        '#96ceb4'\n                                                    ][index], \", \").concat([\n                                                        '#ffa726',\n                                                        '#26a69a',\n                                                        '#42a5f5',\n                                                        '#81c784'\n                                                    ][index], \")\"),\n                                                    position: 'relative',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    justifyContent: 'space-between',\n                                                    padding: '1rem',\n                                                    color: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: 'rgba(255,255,255,0.3)',\n                                                            borderRadius: '50%',\n                                                            width: '2.5rem',\n                                                            height: '2.5rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: '600'\n                                                        },\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.75rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    color: 'white',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: getUserInitial()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"What's on your mind, \".concat(getUserDisplayName(), \"?\"),\n                                                onClick: ()=>setShowCreatePost(true),\n                                                readOnly: true,\n                                                style: {\n                                                    flex: 1,\n                                                    backgroundColor: '#f0f2f5',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '0.75rem 1rem',\n                                                    outline: 'none',\n                                                    fontSize: '1rem',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-around',\n                                            paddingTop: '0.75rem',\n                                            borderTop: '1px solid #e4e6ea'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDCF7 Photo/Video\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDE0A Feeling/Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, this),\n                            posts.map((post)=>{\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '8px',\n                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(post.user_role),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: post.user_name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // Navigate to user's profile based on their role\n                                                                const profilePath = post.user_role === 'worker' ? '/workers/profile' : post.user_role === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                                router.push(\"\".concat(profilePath, \"?user=\").concat(encodeURIComponent(post.user_id)));\n                                                            },\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                padding: 0,\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21',\n                                                                cursor: 'pointer',\n                                                                fontSize: 'inherit',\n                                                                textAlign: 'left'\n                                                            },\n                                                            onMouseEnter: (e)=>e.target.style.textDecoration = 'underline',\n                                                            onMouseLeave: (e)=>e.target.style.textDecoration = 'none',\n                                                            children: post.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                formatTimeAgo(post.created_at),\n                                                                \" • \",\n                                                                getRoleEmoji(post.user_role),\n                                                                \" \",\n                                                                post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: '1rem',\n                                                paddingRight: '1rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#1c1e21',\n                                                    lineHeight: '1.5',\n                                                    margin: 0\n                                                },\n                                                children: post.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 516,\n                                            columnNumber: 17\n                                        }, this),\n                                        post.media_urls && post.media_urls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f2f5',\n                                                height: '250px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                color: '#65676b',\n                                                fontSize: '3rem'\n                                            },\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 524,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '0.75rem 1rem'\n                                            },\n                                            children: [\n                                                (post.likes > 0 || post.comments > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '0.75rem'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem'\n                                                            },\n                                                            children: post.likes > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: '0.25rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            display: 'flex',\n                                                                            gap: '0.125rem'\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#1877f2',\n                                                                                    borderRadius: '50%',\n                                                                                    width: '1.25rem',\n                                                                                    height: '1.25rem',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    justifyContent: 'center',\n                                                                                    fontSize: '0.75rem'\n                                                                                },\n                                                                                children: \"\\uD83D\\uDC4D\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 537,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#e74c3c',\n                                                                                    borderRadius: '50%',\n                                                                                    width: '1.25rem',\n                                                                                    height: '1.25rem',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    justifyContent: 'center',\n                                                                                    fontSize: '0.75rem'\n                                                                                },\n                                                                                children: \"❤️\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 538,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#f39c12',\n                                                                                    borderRadius: '50%',\n                                                                                    width: '1.25rem',\n                                                                                    height: '1.25rem',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    justifyContent: 'center',\n                                                                                    fontSize: '0.75rem'\n                                                                                },\n                                                                                children: \"\\uD83D\\uDE02\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 536,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#65676b',\n                                                                            fontSize: '0.875rem'\n                                                                        },\n                                                                        children: post.likes\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 541,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '1rem',\n                                                                color: '#65676b',\n                                                                fontSize: '0.875rem'\n                                                            },\n                                                            children: [\n                                                                post.comments > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>toggleComments(post.id),\n                                                                    style: {\n                                                                        background: 'none',\n                                                                        border: 'none',\n                                                                        color: '#65676b',\n                                                                        cursor: 'pointer',\n                                                                        fontSize: '0.875rem'\n                                                                    },\n                                                                    children: [\n                                                                        post.comments,\n                                                                        \" comments\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                (post.shared_count || 0) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        post.shared_count,\n                                                                        \" shares\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 58\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-around',\n                                                        paddingTop: '0.75rem',\n                                                        borderTop: '1px solid #e4e6ea'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleLikePost(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: post.liked_by_user ? '#1877f2' : '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDC4D\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 580,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Like\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>toggleComments(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Comment\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowShareModal(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500',\n                                                                flex: 1,\n                                                                justifyContent: 'center'\n                                                            },\n                                                            onMouseEnter: (e)=>e.currentTarget.style.backgroundColor = '#f0f2f5',\n                                                            onMouseLeave: (e)=>e.currentTarget.style.backgroundColor = 'transparent',\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        fontSize: '1.25rem'\n                                                                    },\n                                                                    children: \"\\uD83D\\uDCE4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" Share\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 19\n                                                }, this),\n                                                showComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        marginTop: '1rem',\n                                                        borderTop: '1px solid #e4e6ea',\n                                                        paddingTop: '1rem'\n                                                    },\n                                                    children: [\n                                                        (post.post_comments || []).slice(0, showAllComments[post.id] ? undefined : 3).map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    gap: '0.75rem',\n                                                                    marginBottom: '1rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            backgroundColor: getRoleColor(comment.user_role),\n                                                                            borderRadius: '50%',\n                                                                            width: '2rem',\n                                                                            height: '2rem',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            justifyContent: 'center',\n                                                                            color: 'white',\n                                                                            fontSize: '0.875rem',\n                                                                            fontWeight: 'bold',\n                                                                            flexShrink: 0\n                                                                        },\n                                                                        children: comment.user_name.charAt(0).toUpperCase()\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            flex: 1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    backgroundColor: '#f0f2f5',\n                                                                                    borderRadius: '1rem',\n                                                                                    padding: '0.75rem 1rem'\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            fontWeight: '600',\n                                                                                            fontSize: '0.875rem',\n                                                                                            marginBottom: '0.25rem'\n                                                                                        },\n                                                                                        children: comment.user_name\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 649,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        style: {\n                                                                                            fontSize: '0.875rem',\n                                                                                            lineHeight: '1.4'\n                                                                                        },\n                                                                                        children: comment.content\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 652,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 648,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    display: 'flex',\n                                                                                    gap: '1rem',\n                                                                                    marginTop: '0.25rem',\n                                                                                    paddingLeft: '1rem'\n                                                                                },\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        style: {\n                                                                                            background: 'none',\n                                                                                            border: 'none',\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem',\n                                                                                            cursor: 'pointer',\n                                                                                            fontWeight: '600'\n                                                                                        },\n                                                                                        children: \"Like\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 657,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                        style: {\n                                                                                            background: 'none',\n                                                                                            border: 'none',\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem',\n                                                                                            cursor: 'pointer',\n                                                                                            fontWeight: '600'\n                                                                                        },\n                                                                                        children: \"Reply\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 660,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#65676b',\n                                                                                            fontSize: '0.75rem'\n                                                                                        },\n                                                                                        children: formatTimeAgo(comment.created_at)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                        lineNumber: 663,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                                lineNumber: 656,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                        lineNumber: 647,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, comment.id, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        (post.post_comments || []).length > 3 && !showAllComments[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowAllComments((prev)=>({\n                                                                        ...prev,\n                                                                        [post.id]: true\n                                                                    })),\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                color: '#65676b',\n                                                                fontSize: '0.875rem',\n                                                                cursor: 'pointer',\n                                                                marginBottom: '1rem'\n                                                            },\n                                                            children: [\n                                                                \"View \",\n                                                                (post.post_comments || []).length - 3,\n                                                                \" more comments\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                gap: '0.75rem',\n                                                                alignItems: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        backgroundColor: getRoleColor(userRole || 'worker'),\n                                                                        borderRadius: '50%',\n                                                                        width: '2rem',\n                                                                        height: '2rem',\n                                                                        display: 'flex',\n                                                                        alignItems: 'center',\n                                                                        justifyContent: 'center',\n                                                                        color: 'white',\n                                                                        fontSize: '0.875rem',\n                                                                        fontWeight: 'bold',\n                                                                        flexShrink: 0\n                                                                    },\n                                                                    children: getUserInitial()\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 683,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        flex: 1,\n                                                                        position: 'relative'\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"Write a comment...\",\n                                                                            value: commentTexts[post.id] || '',\n                                                                            onChange: (e)=>setCommentTexts((prev)=>({\n                                                                                        ...prev,\n                                                                                        [post.id]: e.target.value\n                                                                                    })),\n                                                                            onKeyPress: (e)=>{\n                                                                                if (e.key === 'Enter') {\n                                                                                    handleCommentSubmit(post.id);\n                                                                                }\n                                                                            },\n                                                                            style: {\n                                                                                width: '100%',\n                                                                                backgroundColor: '#f0f2f5',\n                                                                                border: 'none',\n                                                                                borderRadius: '1rem',\n                                                                                padding: '0.75rem 1rem',\n                                                                                outline: 'none',\n                                                                                fontSize: '0.875rem'\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 699,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        commentTexts[post.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleCommentSubmit(post.id),\n                                                                            style: {\n                                                                                position: 'absolute',\n                                                                                right: '0.5rem',\n                                                                                top: '50%',\n                                                                                transform: 'translateY(-50%)',\n                                                                                background: 'none',\n                                                                                border: 'none',\n                                                                                color: '#1877f2',\n                                                                                cursor: 'pointer',\n                                                                                fontSize: '0.875rem',\n                                                                                fontWeight: '600'\n                                                                            },\n                                                                            children: \"Post\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                            lineNumber: 720,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                                    lineNumber: 698,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 682,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 530,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this);\n                            }),\n                            posts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\n                                    padding: '3rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 750,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#1c1e21',\n                                            marginBottom: '0.5rem'\n                                        },\n                                        children: \"No posts yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Be the first to share something with your professional network!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 749,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Online Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 759,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#7c3aed',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 765,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Modern Tech Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 770,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#2563eb',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Fatima Al-Zahra\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#059669',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 785,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 784,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Khalid Construction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 762,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '2rem',\n                                    paddingTop: '1rem',\n                                    borderTop: '1px solid #e4e6ea'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    style: {\n                                        color: '#1877f2',\n                                        textDecoration: 'none',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 795,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 758,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this),\n            showCreatePost && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 830,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreatePost(false),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 823,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.75rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        color: 'white',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: getUserInitial()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                color: '#65676b',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                getUserRoleEmoji(),\n                                                \" \",\n                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                \" • \\uD83C\\uDF0D Public\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 870,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 854,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: postText,\n                                onChange: (e)=>setPostText(e.target.value),\n                                placeholder: \"What's on your mind?\",\n                                style: {\n                                    width: '100%',\n                                    minHeight: '120px',\n                                    border: 'none',\n                                    outline: 'none',\n                                    fontSize: '1.5rem',\n                                    color: '#1c1e21',\n                                    resize: 'none',\n                                    fontFamily: 'inherit'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 878,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 877,\n                            columnNumber: 13\n                        }, this),\n                        selectedFeeling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '20px',\n                                    padding: '0.5rem 1rem',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: selectedFeeling\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedFeeling(''),\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            color: '#65676b'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 898,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 897,\n                            columnNumber: 15\n                        }, this),\n                        selectedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'relative',\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '8px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 932,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: [\n                                            \"Image Preview (\",\n                                            selectedImages.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedImages([]),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '0.5rem',\n                                            right: '0.5rem',\n                                            backgroundColor: 'rgba(0,0,0,0.5)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '2rem',\n                                            height: '2rem',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 925,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 924,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderTop: '1px solid #e4e6ea'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: \"Add to your post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '0.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImages([\n                                                            'image'\n                                                        ]),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Photo/Video\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedFeeling('😊 feeling happy'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Feeling/Activity\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 978,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Tag People\",\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 996,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Check In\",\n                                                    children: \"\\uD83D\\uDCCD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1013,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 959,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (!postText.trim()) return;\n                                        // Create the post using the posts context\n                                        const newPost = {\n                                            user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                                            user_name: getUserDisplayName(),\n                                            user_role: userRole || 'worker',\n                                            content: postText,\n                                            media_urls: selectedImages.length > 0 ? selectedImages : undefined,\n                                            post_type: selectedImages.length > 0 ? 'image' : 'text',\n                                            visibility: 'public'\n                                        };\n                                        addPost(newPost);\n                                        // Reset form\n                                        setPostText('');\n                                        setSelectedFeeling('');\n                                        setSelectedImages([]);\n                                        setSelectedLocation('');\n                                        setTaggedUsers([]);\n                                        setShowCreatePost(false);\n                                        // Show success message\n                                        alert('Post created successfully!');\n                                    },\n                                    disabled: !postText.trim(),\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                                        color: postText.trim() ? 'white' : '#bcc0c4',\n                                        border: 'none',\n                                        borderRadius: '6px',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '600',\n                                        cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                                    },\n                                    children: \"Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1034,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 814,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 802,\n                columnNumber: 9\n            }, this),\n            showShareModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Share Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1112,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowShareModal(null),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 1105,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: 'repeat(2, 1fr)',\n                                        gap: '1rem',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSharePost(showShareModal),\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCE4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Share Now\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1167,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Send in Message\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1156,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCCB\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1182,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Copy Link\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                padding: '1rem',\n                                                backgroundColor: '#f0f2f5',\n                                                border: 'none',\n                                                borderRadius: '8px',\n                                                cursor: 'pointer',\n                                                fontSize: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '1.5rem'\n                                                    },\n                                                    children: \"\\uD83D\\uDCF1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Share External\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        borderTop: '1px solid #e4e6ea',\n                                        paddingTop: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(userRole || 'worker'),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: getUserInitial()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21'\n                                                            },\n                                                            children: getUserDisplayName()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 1219,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.875rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                getRoleEmoji(userRole || 'worker'),\n                                                                \" \",\n                                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                                \" • \\uD83C\\uDF0D Public\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 1220,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1204,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: shareText,\n                                            onChange: (e)=>setShareText(e.target.value),\n                                            placeholder: \"Say something about this...\",\n                                            style: {\n                                                width: '100%',\n                                                minHeight: '80px',\n                                                border: '1px solid #e4e6ea',\n                                                borderRadius: '8px',\n                                                padding: '0.75rem',\n                                                outline: 'none',\n                                                fontSize: '1rem',\n                                                resize: 'vertical',\n                                                marginBottom: '1rem'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>handleSharePost(showShareModal),\n                                            style: {\n                                                width: '100%',\n                                                backgroundColor: '#1877f2',\n                                                color: 'white',\n                                                border: 'none',\n                                                borderRadius: '6px',\n                                                padding: '0.75rem',\n                                                fontSize: '1rem',\n                                                fontWeight: '600',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Share Post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 1243,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 1203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 1136,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 1096,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 1084,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"GkPxFE+r0sNRi9KQtSHIAx0/SUU=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});