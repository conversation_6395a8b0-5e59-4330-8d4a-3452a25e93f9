'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useWorkers } from '@/contexts/WorkersContext'
import { usePayment } from '@/contexts/PaymentContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState, use } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  ArrowLeft,
  MapPin,
  Star,
  MessageCircle,
  Phone,
  Download,
  Play,
  Radio,
  CheckCircle,
  Calendar,
  Briefcase,
  DollarSign,
  Globe,
  FileText,
  Video,
  User,
  Award,
  Clock,
  Building,
  Mail,
  Languages,
  Target,
  Lock,
  CreditCard
} from 'lucide-react'
import PaymentGateway from '@/components/PaymentGateway'
import WorkerDocumentDownload from '@/components/WorkerDocumentDownload'
import { toast } from 'sonner'

interface WorkerProfilePageProps {
  params: Promise<{
    id: string
  }>
}

export default function WorkerProfilePage({ params }: WorkerProfilePageProps) {
  const { user } = useAuth()
  const { workers, updateLifecycle } = useWorkers()
  const { hasAccess } = usePayment()
  const router = useRouter()
  const resolvedParams = use(params)
  const [worker, setWorker] = useState(workers.find(w => w.id === resolvedParams.id))
  const [showPaymentGateway, setShowPaymentGateway] = useState(false)
  const [paymentAccessType, setPaymentAccessType] = useState<'contact' | 'documents' | 'premium'>('contact')

  useEffect(() => {
    if (!user) {
      router.push('/')
    }

    const foundWorker = workers.find(w => w.id === resolvedParams.id)
    if (!foundWorker) {
      router.push('/workers')
    } else {
      setWorker(foundWorker)
    }
  }, [user, resolvedParams.id, workers, router])

  const handleWhatsAppContact = () => {
    if (worker?.personalInfo?.whatsapp) {
      const message = `Hello ${worker.personalInfo?.workerName}, I found your profile on KAAZMAAMAA Workers Block and would like to discuss job opportunities.`
      const whatsappUrl = `https://wa.me/${worker.personalInfo.whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
      window.open(whatsappUrl, '_blank')
    } else {
      toast.error('WhatsApp number not available')
    }
  }

  const handleContactAccess = () => {
    if (hasAccess(resolvedParams.id, 'contact')) {
      // User has access, show contact info
      return true
    } else {
      // Show payment gateway
      setPaymentAccessType('contact')
      setShowPaymentGateway(true)
      return false
    }
  }

  const handleDocumentDownload = (doc: any) => {
    if (hasAccess(resolvedParams.id, 'documents')) {
      // User has access, download document
      toast.success(`Downloading ${doc.name}...`)
      // In a real app, this would download the actual file
    } else {
      // Show payment gateway
      setPaymentAccessType('documents')
      setShowPaymentGateway(true)
    }
  }

  const handlePaymentSuccess = () => {
    setShowPaymentGateway(false)
    toast.success('Access granted! You can now view contact information and download documents.')
  }

  const handleDownloadDocument = (doc: any) => {
    // In a real app, this would download the actual file
    toast.success(`Downloading ${doc.name}...`)
  }

  const handlePlayVideo = (video: any) => {
    // In a real app, this would open a video player
    toast.success(`Playing ${video.title}...`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800'
      case 'seeking':
        return 'bg-blue-100 text-blue-800'
      case 'working':
        return 'bg-yellow-100 text-yellow-800'
      case 'on_vacation':
        return 'bg-orange-100 text-orange-800'
      case 'left':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (!worker) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Worker Not Found</h1>
          <Button onClick={() => router.push('/workers')}>
            Back to Workers
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/workers')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Workers
              </Button>
              <h1 className="text-2xl font-bold text-blue-600">Worker Profile</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Profile Header */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6">
              <div className="relative">
                <Avatar className="h-32 w-32 bg-gray-100 flex items-center justify-center">
                  {worker.personalInfo?.profileImage ? (
                    <img
                      src={worker.personalInfo.profileImage}
                      alt={worker.personalInfo?.workerName || 'Worker'}
                      className="w-full h-full object-cover rounded-full"
                    />
                  ) : (
                    <User className="h-16 w-16 text-blue-600" />
                  )}
                </Avatar>
                {worker.isLiveStreaming && (
                  <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                    <Radio className="h-3 w-3 mr-1" />
                    LIVE
                  </div>
                )}
                {worker.isVerified && (
                  <div className="absolute -bottom-2 -right-2 bg-blue-500 text-white rounded-full p-2">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">{worker.personalInfo?.workerName || 'Unknown Worker'}</h1>
                    <p className="text-xl text-blue-600 font-semibold mt-1">{worker.professionalInfo?.title || 'No Title'}</p>
                    <p className="text-gray-600 mt-1">{worker.professionalInfo?.specialty || 'No Specialty'}</p>

                    <div className="flex items-center space-x-4 mt-3">
                      <Badge className={getStatusColor(worker.lifecycle?.status || 'seeking')}>
                        {(worker.lifecycle?.status || 'seeking').replace('_', ' ')}
                      </Badge>
                      <div className="flex items-center text-yellow-500">
                        <Star className="h-5 w-5 fill-current" />
                        <span className="ml-1 font-semibold">{worker.rating || 0}</span>
                        <span className="text-gray-500 ml-1">({worker.totalReviews || 0} reviews)</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="flex items-center text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        {worker.personalInfo?.address || 'Location not specified'}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Briefcase className="h-4 w-4 mr-2" />
                        {worker.professionalInfo?.experience || 0} years experience
                      </div>
                      <div className="flex items-center text-gray-600">
                        <DollarSign className="h-4 w-4 mr-2" />
                        {worker.professionalInfo?.expectedSalaryPerHour || 0} {worker.professionalInfo?.currency || 'SAR'}/hr
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-4 w-4 mr-2" />
                        Available: {(worker.professionalInfo?.availability || 'negotiable').replace('_', ' ')}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2 mt-4 md:mt-0">
                    <Button onClick={handleWhatsAppContact} className="bg-green-600 hover:bg-green-700">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Contact on WhatsApp
                    </Button>
                    {hasAccess(resolvedParams.id, 'contact') ? (
                      <>
                        <Button variant="outline">
                          <Phone className="h-4 w-4 mr-2" />
                          Call: {worker.personalInfo?.phone}
                        </Button>
                        <Button variant="outline">
                          <Mail className="h-4 w-4 mr-2" />
                          Email: {worker.personalInfo?.email}
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          onClick={handleContactAccess}
                          className="border-orange-300 text-orange-600 hover:bg-orange-50"
                        >
                          <Lock className="h-4 w-4 mr-2" />
                          Unlock Contact Info (25 SAR)
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setPaymentAccessType('premium')
                            setShowPaymentGateway(true)
                          }}
                          className="border-purple-300 text-purple-600 hover:bg-purple-50"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Premium Access (75 SAR)
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="experience">Experience</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="videos">Videos</TabsTrigger>
            <TabsTrigger value="lifecycle">Lifecycle</TabsTrigger>
            <TabsTrigger value="preferences">Preferences</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <User className="h-5 w-5 mr-2" />
                    Personal Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Age</label>
                      <p className="text-gray-900">{worker.personalInfo?.age || 'Not specified'} {worker.personalInfo?.age ? 'years' : ''}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Nationality</label>
                      <p className="text-gray-900">{worker.personalInfo?.nationality || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone</label>
                      <p className="text-gray-900">{worker.personalInfo?.phone || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Email</label>
                      <p className="text-gray-900">{worker.personalInfo?.email || 'Not specified'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    Professional Skills
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Skills</label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {(worker.professionalInfo?.skills || []).map((skill, index) => (
                          <Badge key={index} variant="secondary">{skill}</Badge>
                        ))}
                        {(!worker.professionalInfo?.skills || worker.professionalInfo.skills.length === 0) && (
                          <span className="text-gray-500 text-sm">No skills specified</span>
                        )}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Languages</label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {(worker.professionalInfo?.languages || []).map((language, index) => (
                          <Badge key={index} className="bg-blue-100 text-blue-800">{language}</Badge>
                        ))}
                        {(!worker.professionalInfo?.languages || worker.professionalInfo.languages.length === 0) && (
                          <span className="text-gray-500 text-sm">No languages specified</span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="experience">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Briefcase className="h-5 w-5 mr-2" />
                  Work Experience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {(worker.workExperience || []).map((exp) => (
                    <div key={exp.id || Math.random()} className="border-l-2 border-blue-200 pl-4 pb-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{exp.position || 'Position not specified'}</h3>
                          <p className="text-blue-600 font-medium">{exp.company || 'Company not specified'}</p>
                          <p className="text-gray-600 text-sm">{exp.location || 'Location not specified'}</p>
                          <p className="text-gray-500 text-sm mt-1">
                            {exp.startDate ? new Date(exp.startDate).toLocaleDateString() : 'Start date not specified'} - {
                              exp.endDate ? new Date(exp.endDate).toLocaleDateString() : 'Present'
                            }
                          </p>
                        </div>
                        {exp.isCurrentJob && (
                          <Badge className="bg-green-100 text-green-800">Current</Badge>
                        )}
                      </div>
                      <p className="text-gray-700 mt-2">{exp.description || 'No description provided'}</p>
                    </div>
                  ))}
                  {(!worker.workExperience || worker.workExperience.length === 0) && (
                    <div className="text-center py-8">
                      <p className="text-gray-500">No work experience information available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <WorkerDocumentDownload
              worker={worker}
              currentUserRole={user?.role || 'worker'}
              currentUserId={user?.id || 'current-user'}
            />
          </TabsContent>

          <TabsContent value="videos">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Video className="h-5 w-5 mr-2" />
                  Videos ({(worker.videos || []).length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(worker.videos || []).map((video) => (
                    <div key={video.id || Math.random()} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                        <Button
                          variant="ghost"
                          size="lg"
                          onClick={() => handlePlayVideo(video)}
                        >
                          <Play className="h-8 w-8" />
                        </Button>
                      </div>
                      <h4 className="font-medium">{video.title || 'Video'}</h4>
                      <p className="text-sm text-gray-500 capitalize">{(video.type || 'video').replace('_', ' ')}</p>
                      {video.duration && (
                        <p className="text-xs text-gray-400">Duration: {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}</p>
                      )}
                    </div>
                  ))}
                  {(!worker.videos || worker.videos.length === 0) && (
                    <div className="text-center py-8">
                      <p className="text-gray-500">No videos available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="lifecycle">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Work Lifecycle
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Current Status</label>
                      <Badge className={getStatusColor(worker.lifecycle?.status || 'seeking')}>
                        {(worker.lifecycle?.status || 'seeking').replace('_', ' ')}
                      </Badge>
                    </div>
                    {worker.lifecycle?.currentEmployer && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Current Employer</label>
                        <p className="text-gray-900">{worker.lifecycle.currentEmployer}</p>
                      </div>
                    )}
                    {worker.lifecycle?.joiningDate && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Joining Date</label>
                        <p className="text-gray-900">{new Date(worker.lifecycle.joiningDate).toLocaleDateString()}</p>
                      </div>
                    )}
                    {worker.lifecycle?.vacationStart && worker.lifecycle?.vacationEnd && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Vacation Period</label>
                        <p className="text-gray-900">
                          {new Date(worker.lifecycle.vacationStart).toLocaleDateString()} - {new Date(worker.lifecycle.vacationEnd).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                  </div>
                  {worker.lifecycle?.notes && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">Notes</label>
                      <p className="text-gray-900 mt-1">{worker.lifecycle.notes}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Work Preferences
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Work Type</label>
                      <p className="text-gray-900 capitalize">{(worker.preferences?.workType || 'not_specified').replace('_', ' ')}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Remote Work</label>
                      <p className="text-gray-900">{worker.preferences?.remoteWork ? 'Yes' : 'No'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Willing to Relocate</label>
                      <p className="text-gray-900">{worker.preferences?.willingToRelocate ? 'Yes' : 'No'}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-500">Preferred Locations</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {(worker.preferences?.preferredLocations || []).map((location, index) => (
                        <Badge key={index} variant="outline">{location}</Badge>
                      ))}
                      {(!worker.preferences?.preferredLocations || worker.preferences.preferredLocations.length === 0) && (
                        <span className="text-gray-500 text-sm">No preferred locations specified</span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Payment Gateway Modal */}
      {showPaymentGateway && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <PaymentGateway
                profileId={resolvedParams.id}
                profileType="worker"
                profileName={worker?.personalInfo?.workerName || 'Worker'}
                accessType={paymentAccessType}
                onSuccess={handlePaymentSuccess}
                onCancel={() => setShowPaymentGateway(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
