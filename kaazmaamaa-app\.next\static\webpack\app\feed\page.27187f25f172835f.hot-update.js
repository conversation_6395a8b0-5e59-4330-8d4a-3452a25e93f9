"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/contexts/PostsContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/PostsContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostsProvider: () => (/* binding */ PostsProvider),\n/* harmony export */   formatExpirationInfo: () => (/* binding */ formatExpirationInfo),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo),\n/* harmony export */   usePosts: () => (/* binding */ usePosts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PostsProvider,usePosts,formatTimeAgo,formatExpirationInfo auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst PostsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Helper function to calculate expiration date (1 month from creation)\nconst getExpirationDate = (createdAt)=>{\n    const created = new Date(createdAt);\n    const expiration = new Date(created);\n    expiration.setMonth(expiration.getMonth() + 1) // Add 1 month\n    ;\n    return expiration.toISOString();\n};\n// Initial mock posts\nconst initialPosts = [\n    {\n        id: '1',\n        user_id: 'mock-user-1',\n        user_name: 'Ahmed Al-Rashid',\n        user_role: 'worker',\n        content: 'Looking for construction work in Riyadh. I have 5 years of experience in electrical work. Available immediately! #ElectricalWork #Riyadh #Available',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),\n        likes: 12,\n        comments: 3,\n        shared_count: 2,\n        post_comments: [\n            {\n                id: 'comment-1',\n                post_id: '1',\n                user_id: 'commenter-1',\n                user_name: 'Sarah Construction',\n                user_role: 'company',\n                content: 'We have openings for electrical work. Please send your CV.',\n                created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n            },\n            {\n                id: 'comment-2',\n                post_id: '1',\n                user_id: 'commenter-2',\n                user_name: 'Ali Hassan',\n                user_role: 'worker',\n                content: 'Good luck brother! I also work in electrical.',\n                created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n            }\n        ]\n    },\n    {\n        id: '2',\n        user_id: 'mock-user-2',\n        user_name: 'Saudi Construction Co.',\n        user_role: 'company',\n        content: 'We are hiring 20 skilled workers for our new project in Jeddah. Competitive salary and accommodation provided. Contact us for details. #Hiring #Jeddah #Construction',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()),\n        likes: 28,\n        comments: 15\n    },\n    {\n        id: '3',\n        user_id: 'mock-user-3',\n        user_name: 'Gulf Manpower Solutions',\n        user_role: 'supplier',\n        content: 'New batch of certified welders available for immediate deployment. All workers have valid IQAMA and safety certifications. #Welders #Certified #Available',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()),\n        likes: 18,\n        comments: 8\n    },\n    {\n        id: '4',\n        user_id: 'mock-user-4',\n        user_name: 'Mohammed Hassan',\n        user_role: 'worker',\n        content: 'Just completed my safety certification course! Ready for new opportunities in the oil and gas sector. #SafetyCertified #OilAndGas #Ready',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()),\n        likes: 15,\n        comments: 5\n    }\n];\nfunction PostsProvider(param) {\n    let { children } = param;\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load posts from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PostsProvider.useEffect\": ()=>{\n            const loadPostsFromStorage = {\n                \"PostsProvider.useEffect.loadPostsFromStorage\": ()=>{\n                    try {\n                        const savedPosts = localStorage.getItem('kaazmaamaa-posts');\n                        if (savedPosts) {\n                            const parsedPosts = JSON.parse(savedPosts);\n                            console.log('Loaded posts from localStorage:', parsedPosts.length);\n                            // Filter out expired posts when loading\n                            const now = new Date();\n                            const activePosts = parsedPosts.filter({\n                                \"PostsProvider.useEffect.loadPostsFromStorage.activePosts\": (post)=>{\n                                    // Handle posts that might not have expires_at field (legacy posts)\n                                    if (!post.expires_at) {\n                                        // Add expires_at to legacy posts (1 month from creation)\n                                        post.expires_at = getExpirationDate(post.created_at);\n                                    }\n                                    const expirationDate = new Date(post.expires_at);\n                                    return expirationDate > now;\n                                }\n                            }[\"PostsProvider.useEffect.loadPostsFromStorage.activePosts\"]);\n                            const expiredCount = parsedPosts.length - activePosts.length;\n                            if (expiredCount > 0) {\n                                console.log(\"Filtered out \".concat(expiredCount, \" expired posts on load\"));\n                                // Save cleaned posts back to localStorage\n                                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts));\n                            }\n                            setPosts(activePosts);\n                        } else {\n                            // If no saved posts, use initial posts\n                            console.log('No saved posts found, using initial posts');\n                            setPosts(initialPosts);\n                            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts));\n                        }\n                    } catch (error) {\n                        console.error('Error loading posts from localStorage:', error);\n                        setPosts(initialPosts);\n                    } finally{\n                        setIsLoaded(true);\n                    }\n                }\n            }[\"PostsProvider.useEffect.loadPostsFromStorage\"];\n            loadPostsFromStorage();\n        }\n    }[\"PostsProvider.useEffect\"], []);\n    // Save posts to localStorage whenever posts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PostsProvider.useEffect\": ()=>{\n            if (isLoaded && posts.length > 0) {\n                try {\n                    localStorage.setItem('kaazmaamaa-posts', JSON.stringify(posts));\n                    console.log('Saved posts to localStorage:', posts.length);\n                } catch (error) {\n                    console.error('Error saving posts to localStorage:', error);\n                }\n            }\n        }\n    }[\"PostsProvider.useEffect\"], [\n        posts,\n        isLoaded\n    ]);\n    console.log('PostsProvider initialized with posts:', posts.length);\n    const addPost = (newPost)=>{\n        const createdAt = new Date().toISOString();\n        const post = {\n            ...newPost,\n            id: Date.now().toString(),\n            created_at: createdAt,\n            expires_at: getExpirationDate(createdAt),\n            likes: 0,\n            comments: 0\n        };\n        console.log('Adding post to context:', post);\n        setPosts((prevPosts)=>{\n            const updatedPosts = [\n                post,\n                ...prevPosts\n            ];\n            console.log('Updated posts array:', updatedPosts);\n            // Immediately save to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Post immediately saved to localStorage');\n            } catch (error) {\n                console.error('Error saving post to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const likePost = (postId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {\n                    ...post,\n                    likes: post.liked_by_user ? post.likes - 1 : post.likes + 1,\n                    liked_by_user: !post.liked_by_user\n                } : post);\n            // Save likes to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Like status saved to localStorage');\n            } catch (error) {\n                console.error('Error saving like to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const sharePost = (postId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {\n                    ...post,\n                    shared_count: (post.shared_count || 0) + 1\n                } : post);\n            // Save share count to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Share count saved to localStorage');\n            } catch (error) {\n                console.error('Error saving share to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const addComment = (postId, content, user)=>{\n        const newComment = {\n            id: Date.now().toString(),\n            post_id: postId,\n            user_id: user.id,\n            user_name: user.name,\n            user_role: user.role,\n            content: content.trim(),\n            created_at: new Date().toISOString()\n        };\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {\n                    ...post,\n                    comments: post.comments + 1,\n                    post_comments: [\n                        ...post.post_comments || [],\n                        newComment\n                    ]\n                } : post);\n            // Save comments to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Comment saved to localStorage');\n            } catch (error) {\n                console.error('Error saving comment to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const deletePost = (postId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.filter((post)=>post.id !== postId);\n            // Save deletion to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Post deletion saved to localStorage');\n            } catch (error) {\n                console.error('Error saving deletion to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const clearAllPosts = ()=>{\n        setPosts([]);\n        try {\n            localStorage.removeItem('kaazmaamaa-posts');\n            console.log('All posts cleared from localStorage');\n        } catch (error) {\n            console.error('Error clearing posts from localStorage:', error);\n        }\n    };\n    const resetToInitialPosts = ()=>{\n        setPosts(initialPosts);\n        try {\n            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts));\n            console.log('Reset to initial posts and saved to localStorage');\n        } catch (error) {\n            console.error('Error resetting posts in localStorage:', error);\n        }\n    };\n    // Function to clean up expired posts (older than 1 month)\n    const cleanupExpiredPosts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PostsProvider.useCallback[cleanupExpiredPosts]\": ()=>{\n            const now = new Date();\n            setPosts({\n                \"PostsProvider.useCallback[cleanupExpiredPosts]\": (prevPosts)=>{\n                    const activePosts = prevPosts.filter({\n                        \"PostsProvider.useCallback[cleanupExpiredPosts].activePosts\": (post)=>{\n                            const expirationDate = new Date(post.expires_at);\n                            return expirationDate > now;\n                        }\n                    }[\"PostsProvider.useCallback[cleanupExpiredPosts].activePosts\"]);\n                    const expiredCount = prevPosts.length - activePosts.length;\n                    if (expiredCount > 0) {\n                        console.log(\"Cleaned up \".concat(expiredCount, \" expired posts\"));\n                        // Save cleaned posts to localStorage\n                        try {\n                            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts));\n                            console.log('Cleaned posts saved to localStorage');\n                        } catch (error) {\n                            console.error('Error saving cleaned posts to localStorage:', error);\n                        }\n                    }\n                    return activePosts;\n                }\n            }[\"PostsProvider.useCallback[cleanupExpiredPosts]\"]);\n        }\n    }[\"PostsProvider.useCallback[cleanupExpiredPosts]\"], []);\n    const migratePostsToFullNames = ()=>{\n        console.log('Migrating posts to use full names...');\n        // Clear localStorage and reset to initial posts with proper names\n        try {\n            localStorage.removeItem('kaazmaamaa-posts');\n            console.log('Cleared old posts from localStorage');\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n        }\n        resetToInitialPosts();\n    };\n    const reactToPost = (postId, reactionType, user)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>{\n                if (post.id === postId) {\n                    const reactions = post.reactions || [];\n                    const existingReaction = reactions.find((r)=>r.user_id === user.id);\n                    let newReactions = [\n                        ...reactions\n                    ];\n                    let newReactionCounts = {\n                        ...post.reaction_counts\n                    } || {};\n                    // Remove existing reaction if any\n                    if (existingReaction) {\n                        newReactions = newReactions.filter((r)=>r.user_id !== user.id);\n                        newReactionCounts[existingReaction.reaction_type] = Math.max(0, (newReactionCounts[existingReaction.reaction_type] || 0) - 1);\n                    }\n                    // Add new reaction if different from existing\n                    if (!existingReaction || existingReaction.reaction_type !== reactionType) {\n                        newReactions.push({\n                            id: Date.now().toString(),\n                            user_id: user.id,\n                            user_name: user.name,\n                            reaction_type: reactionType,\n                            created_at: new Date().toISOString()\n                        });\n                        newReactionCounts[reactionType] = (newReactionCounts[reactionType] || 0) + 1;\n                    }\n                    return {\n                        ...post,\n                        reactions: newReactions,\n                        reaction_counts: newReactionCounts,\n                        user_reaction: !existingReaction || existingReaction.reaction_type !== reactionType ? reactionType : undefined,\n                        likes: newReactionCounts.like || 0\n                    };\n                }\n                return post;\n            });\n            // Save to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n            } catch (error) {\n                console.error('Error saving reaction to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const voteInPoll = (postId, optionId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>{\n                if (post.id === postId && post.poll) {\n                    const updatedOptions = post.poll.options.map((option)=>option.id === optionId ? {\n                            ...option,\n                            votes: option.votes + 1\n                        } : option);\n                    return {\n                        ...post,\n                        poll: {\n                            ...post.poll,\n                            options: updatedOptions,\n                            total_votes: post.poll.total_votes + 1\n                        }\n                    };\n                }\n                return post;\n            });\n            // Save to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n            } catch (error) {\n                console.error('Error saving poll vote to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const checkIn = (location, user)=>{\n        const newPost = {\n            id: Date.now().toString(),\n            user_id: user.id,\n            user_name: user.name,\n            user_role: user.role,\n            content: \"\\uD83D\\uDCCD Checked in at \".concat(location),\n            post_type: 'check_in',\n            visibility: 'public',\n            created_at: new Date().toISOString(),\n            expires_at: getExpirationDate(new Date().toISOString()),\n            likes: 0,\n            comments: 0,\n            shared_count: 0,\n            location: location,\n            reactions: [],\n            reaction_counts: {}\n        };\n        setPosts((prevPosts)=>{\n            const updatedPosts = [\n                newPost,\n                ...prevPosts\n            ];\n            // Save to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n            } catch (error) {\n                console.error('Error saving check-in to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const createEvent = (event, user)=>{\n        const newEvent = {\n            ...event,\n            id: Date.now().toString(),\n            attendees: 0,\n            interested: 0\n        };\n        const newPost = {\n            id: Date.now().toString(),\n            user_id: user.id,\n            user_name: user.name,\n            user_role: user.role,\n            content: \"\\uD83D\\uDCC5 Created an event: \".concat(event.title),\n            post_type: 'event',\n            visibility: 'public',\n            created_at: new Date().toISOString(),\n            expires_at: getExpirationDate(new Date().toISOString()),\n            likes: 0,\n            comments: 0,\n            shared_count: 0,\n            event: newEvent,\n            reactions: [],\n            reaction_counts: {}\n        };\n        setPosts((prevPosts)=>{\n            const updatedPosts = [\n                newPost,\n                ...prevPosts\n            ];\n            // Save to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n            } catch (error) {\n                console.error('Error saving event to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const value = {\n        posts,\n        addPost,\n        likePost,\n        reactToPost,\n        sharePost,\n        deletePost,\n        addComment,\n        voteInPoll,\n        checkIn,\n        createEvent,\n        clearAllPosts,\n        resetToInitialPosts,\n        cleanupExpiredPosts,\n        migratePostsToFullNames\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\contexts\\\\PostsContext.tsx\",\n        lineNumber: 592,\n        columnNumber: 5\n    }, this);\n}\n_s(PostsProvider, \"grrvA51HETDAPO/ii/ehM7XMTSc=\");\n_c = PostsProvider;\nfunction usePosts() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PostsContext);\n    if (context === undefined) {\n        throw new Error('usePosts must be used within a PostsProvider');\n    }\n    return context;\n}\n_s1(usePosts, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Utility function to format time ago\nfunction formatTimeAgo(dateString) {\n    const now = new Date();\n    const postDate = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m ago\");\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return \"\".concat(diffInDays, \"d ago\");\n    return postDate.toLocaleDateString();\n}\n// Utility function to format post expiration info\nfunction formatExpirationInfo(expiresAt) {\n    const now = new Date();\n    const expirationDate = new Date(expiresAt);\n    const diffInMs = expirationDate.getTime() - now.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n    if (diffInMs <= 0) {\n        return {\n            text: 'Expired',\n            isExpiringSoon: true\n        };\n    }\n    if (diffInDays <= 3) {\n        return {\n            text: \"Expires in \".concat(diffInDays, \" day\").concat(diffInDays !== 1 ? 's' : ''),\n            isExpiringSoon: true\n        };\n    }\n    if (diffInDays <= 7) {\n        return {\n            text: \"Expires in \".concat(diffInDays, \" days\"),\n            isExpiringSoon: false\n        };\n    }\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n        return {\n            text: \"Expires in \".concat(diffInWeeks, \" week\").concat(diffInWeeks !== 1 ? 's' : ''),\n            isExpiringSoon: false\n        };\n    }\n    return {\n        text: 'Expires in 1 month',\n        isExpiringSoon: false\n    };\n}\nvar _c;\n$RefreshReg$(_c, \"PostsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/PostsContext.tsx\n"));

/***/ })

});