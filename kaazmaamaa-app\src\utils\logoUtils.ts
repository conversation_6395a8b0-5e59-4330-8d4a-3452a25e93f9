// Logo Utilities for Invoice System

/**
 * Create a sample logo canvas for testing
 * @param text - Text to display in the logo
 * @param size - Size of the logo (default: 200)
 * @returns Canvas element with the logo
 */
export function createSampleLogo(text: string = 'KAAZMAAMAA', size: number = 200): HTMLCanvasElement {
  const canvas = document.createElement('canvas')
  canvas.width = size
  canvas.height = size
  
  const ctx = canvas.getContext('2d')
  if (!ctx) throw new Error('Could not get canvas context')
  
  // Background
  ctx.fillStyle = '#8B5CF6' // Purple background
  ctx.fillRect(0, 0, size, size)
  
  // Border
  ctx.strokeStyle = '#6D28D9'
  ctx.lineWidth = 4
  ctx.strokeRect(2, 2, size - 4, size - 4)
  
  // Text
  ctx.fillStyle = 'white'
  ctx.font = `bold ${size / 8}px Arial`
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'
  
  // Main text
  ctx.fillText(text, size / 2, size / 2 - size / 16)
  
  // Subtitle
  ctx.font = `${size / 16}px Arial`
  ctx.fillText('Technology Solutions', size / 2, size / 2 + size / 8)
  
  // Icon/Symbol
  ctx.font = `${size / 6}px Arial`
  ctx.fillText('🧾', size / 2, size / 4)
  
  return canvas
}

/**
 * Convert canvas to blob for file upload simulation
 * @param canvas - Canvas element
 * @param quality - Image quality (0-1)
 * @returns Promise<Blob>
 */
export function canvasToBlob(canvas: HTMLCanvasElement, quality: number = 0.9): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob)
      } else {
        reject(new Error('Failed to create blob from canvas'))
      }
    }, 'image/png', quality)
  })
}

/**
 * Create a sample logo file for testing
 * @param filename - Name for the file
 * @param text - Text to display in the logo
 * @returns Promise<File>
 */
export async function createSampleLogoFile(filename: string = 'sample-logo.png', text: string = 'KAAZMAAMAA'): Promise<File> {
  const canvas = createSampleLogo(text)
  const blob = await canvasToBlob(canvas)
  return new File([blob], filename, { type: 'image/png' })
}

/**
 * Validate image file for logo upload
 * @param file - File to validate
 * @returns Validation result
 */
export function validateLogoFile(file: File): { isValid: boolean; error?: string } {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { isValid: false, error: 'Please select a valid image file (PNG, JPG, JPEG)' }
  }
  
  // Check file size (2MB limit)
  if (file.size > 2 * 1024 * 1024) {
    return { isValid: false, error: 'File size must be less than 2MB' }
  }
  
  // Check file extension
  const allowedExtensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp']
  const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
  if (!allowedExtensions.includes(fileExtension)) {
    return { isValid: false, error: 'Allowed file types: PNG, JPG, JPEG, GIF, WebP' }
  }
  
  return { isValid: true }
}

/**
 * Resize image to fit within maximum dimensions
 * @param file - Image file to resize
 * @param maxWidth - Maximum width
 * @param maxHeight - Maximum height
 * @returns Promise<File>
 */
export function resizeImage(file: File, maxWidth: number = 300, maxHeight: number = 200): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      reject(new Error('Could not get canvas context'))
      return
    }
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width
        width = maxWidth
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height
        height = maxHeight
      }
      
      // Set canvas size
      canvas.width = width
      canvas.height = height
      
      // Draw resized image
      ctx.drawImage(img, 0, 0, width, height)
      
      // Convert to blob
      canvas.toBlob((blob) => {
        if (blob) {
          const resizedFile = new File([blob], file.name, { type: file.type })
          resolve(resizedFile)
        } else {
          reject(new Error('Failed to resize image'))
        }
      }, file.type, 0.9)
    }
    
    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Get image dimensions
 * @param file - Image file
 * @returns Promise<{width: number, height: number}>
 */
export function getImageDimensions(file: File): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    
    img.onload = () => {
      resolve({ width: img.width, height: img.height })
    }
    
    img.onerror = () => reject(new Error('Failed to load image'))
    img.src = URL.createObjectURL(file)
  })
}

/**
 * Format file size for display
 * @param bytes - File size in bytes
 * @returns Formatted string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// Export all utilities
export const LogoUtils = {
  createSampleLogo,
  canvasToBlob,
  createSampleLogoFile,
  validateLogoFile,
  resizeImage,
  getImageDimensions,
  formatFileSize
}
