'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, HelpCircle, AlertCircle, CheckCircle, MessageSquare, Book, Search, ExternalLink } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoiceStep10() {
  const router = useRouter()
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null)

  const faqItems = [
    {
      id: 1,
      question: "What should I do if my invoice is rejected by ZATCA?",
      answer: "Check the error response for specific validation issues. Common causes include invalid XML structure, incorrect VAT calculations, or expired certificates. Use the CLI validation tool to identify and fix issues before resubmission.",
      category: "Rejection Issues"
    },
    {
      id: 2,
      question: "How do I handle certificate expiration?",
      answer: "Monitor certificate expiry dates and renew them before expiration. Implement automated alerts for upcoming expirations. Update your system with new certificates and test thoroughly before the old ones expire.",
      category: "Certificates"
    },
    {
      id: 3,
      question: "Why is my QR code not scanning properly?",
      answer: "Ensure the QR code follows the TLV format specification, uses correct Base64 encoding, and contains all required fields. Test with different QR code readers and verify the data structure matches ZATCA requirements.",
      category: "QR Codes"
    },
    {
      id: 4,
      question: "How do I handle VAT group invoices?",
      answer: "Configure the representative member correctly, ensure proper VAT number usage, and handle intra-group transactions appropriately. Refer to Step 7 for detailed VAT group implementation guidance.",
      category: "VAT Groups"
    },
    {
      id: 5,
      question: "What are the API rate limits?",
      answer: "ZATCA has specific rate limits for production APIs. Implement exponential backoff, queue management, and proper error handling. Monitor your API usage and implement caching where appropriate.",
      category: "API Issues"
    },
    {
      id: 6,
      question: "How do I test in the sandbox environment?",
      answer: "Use sandbox credentials, follow the test scenarios in Step 8, and validate all invoice types. Ensure your implementation handles both success and error responses correctly before moving to production.",
      category: "Testing"
    }
  ]

  const troubleshootingSteps = [
    {
      title: "Invoice Rejection",
      steps: [
        "Review the error response details",
        "Check XML schema validation",
        "Verify business rule compliance",
        "Validate digital signatures",
        "Test with CLI validation tool"
      ],
      color: "red"
    },
    {
      title: "API Connection Issues",
      steps: [
        "Check network connectivity",
        "Verify API endpoints",
        "Validate authentication tokens",
        "Review firewall settings",
        "Test with sandbox environment"
      ],
      color: "blue"
    },
    {
      title: "Certificate Problems",
      steps: [
        "Check certificate validity",
        "Verify certificate chain",
        "Validate private key pairing",
        "Test certificate installation",
        "Review certificate permissions"
      ],
      color: "yellow"
    },
    {
      title: "QR Code Issues",
      steps: [
        "Validate TLV structure",
        "Check Base64 encoding",
        "Verify required fields",
        "Test QR code generation",
        "Validate with QR readers"
      ],
      color: "green"
    }
  ]

  const resources = [
    {
      title: "ZATCA Official Documentation",
      description: "Complete technical specifications and guidelines",
      url: "https://zatca.gov.sa/en/E-Invoicing/Pages/default.aspx",
      type: "Official"
    },
    {
      title: "E-Invoicing Developer Portal",
      description: "Developer resources and API documentation",
      url: "https://zatca.gov.sa/en/E-Invoicing/Pages/Developers.aspx",
      type: "Developer"
    },
    {
      title: "ZATCA Support Center",
      description: "Technical support and help desk",
      url: "https://zatca.gov.sa/en/HelpAndSupport/Pages/default.aspx",
      type: "Support"
    },
    {
      title: "Compliance Validation Tools",
      description: "CLI tools and validation utilities",
      url: "https://zatca.gov.sa/en/E-Invoicing/Pages/Tools.aspx",
      type: "Tools"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/invoice')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Invoice
              </Button>
              <h1 className="text-2xl font-bold text-teal-600">Step 10: FAQ & Troubleshooting</h1>
              <Badge className="bg-teal-100 text-teal-800">
                Support
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                onClick={() => {
                  toast.success('Tutorial completed! 🎉')
                  router.push('/invoice')
                }}
                className="bg-teal-600 hover:bg-teal-700"
              >
                Complete Tutorial
                <CheckCircle className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-teal-50 border-teal-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <HelpCircle className="h-16 w-16 text-teal-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-teal-800 mb-2">❓ FAQ & Troubleshooting</h3>
              <p className="text-teal-600 mb-4">
                Common questions, issues, and solutions for ZATCA e-invoicing implementation
              </p>
              <div className="grid grid-cols-3 gap-4 max-w-lg mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-teal-800">{faqItems.length}</div>
                  <div className="text-sm text-teal-600">FAQ Items</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{troubleshootingSteps.length}</div>
                  <div className="text-sm text-blue-600">Troubleshooting</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-800">{resources.length}</div>
                  <div className="text-sm text-green-600">Resources</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* FAQ Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2 text-teal-600" />
              Frequently Asked Questions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {faqItems.map((faq) => (
                <div key={faq.id} className="border border-gray-200 rounded-lg">
                  <button
                    className="w-full p-4 text-left hover:bg-gray-50 flex items-center justify-between"
                    onClick={() => setExpandedFaq(expandedFaq === faq.id ? null : faq.id)}
                  >
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline" className="text-xs">
                        {faq.category}
                      </Badge>
                      <span className="font-medium">{faq.question}</span>
                    </div>
                    <HelpCircle className={`h-4 w-4 transition-transform ${
                      expandedFaq === faq.id ? 'rotate-180' : ''
                    }`} />
                  </button>
                  {expandedFaq === faq.id && (
                    <div className="p-4 pt-0 border-t border-gray-100">
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Troubleshooting Steps */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-teal-600" />
              Troubleshooting Guide
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {troubleshootingSteps.map((guide, index) => (
                <div key={index} className={`p-4 bg-${guide.color}-50 border border-${guide.color}-200 rounded-lg`}>
                  <h4 className={`font-semibold text-${guide.color}-800 mb-3`}>
                    {guide.title}
                  </h4>
                  <ol className="space-y-2">
                    {guide.steps.map((step, stepIndex) => (
                      <li key={stepIndex} className="flex items-start space-x-2 text-sm">
                        <span className={`bg-${guide.color}-200 text-${guide.color}-800 rounded-full w-5 h-5 flex items-center justify-center text-xs font-semibold flex-shrink-0 mt-0.5`}>
                          {stepIndex + 1}
                        </span>
                        <span className="text-gray-700">{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Error Codes Reference */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Search className="h-5 w-5 mr-2 text-teal-600" />
              Common Error Codes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="p-3 bg-red-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="destructive" className="text-xs">400</Badge>
                    <span className="font-semibold text-red-800">Bad Request</span>
                  </div>
                  <p className="text-red-600 text-sm">Invalid request format or missing required fields</p>
                </div>
                
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="secondary" className="text-xs bg-yellow-200">401</Badge>
                    <span className="font-semibold text-yellow-800">Unauthorized</span>
                  </div>
                  <p className="text-yellow-600 text-sm">Invalid authentication credentials or expired token</p>
                </div>

                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="secondary" className="text-xs bg-orange-200">422</Badge>
                    <span className="font-semibold text-orange-800">Validation Error</span>
                  </div>
                  <p className="text-orange-600 text-sm">Invoice data fails business rule validation</p>
                </div>
              </div>

              <div className="space-y-3">
                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="secondary" className="text-xs bg-purple-200">429</Badge>
                    <span className="font-semibold text-purple-800">Rate Limited</span>
                  </div>
                  <p className="text-purple-600 text-sm">API rate limit exceeded, implement backoff strategy</p>
                </div>

                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="secondary" className="text-xs bg-blue-200">500</Badge>
                    <span className="font-semibold text-blue-800">Server Error</span>
                  </div>
                  <p className="text-blue-600 text-sm">ZATCA server error, retry with exponential backoff</p>
                </div>

                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="secondary" className="text-xs bg-gray-200">503</Badge>
                    <span className="font-semibold text-gray-800">Service Unavailable</span>
                  </div>
                  <p className="text-gray-600 text-sm">ZATCA service temporarily unavailable</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Resources */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Book className="h-5 w-5 mr-2 text-teal-600" />
              Additional Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {resources.map((resource, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-semibold text-gray-800">{resource.title}</h4>
                    <Badge variant="outline" className="text-xs">
                      {resource.type}
                    </Badge>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    {resource.description}
                  </p>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => {
                      toast.info(`Opening ${resource.title}...`)
                      // In a real app, this would open the URL
                    }}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Visit Resource
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Completion Message */}
        <Card className="mt-6 bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-green-800 mb-2">🎉 Tutorial Complete!</h3>
              <p className="text-green-600 mb-4">
                You've completed all 10 steps of the ZATCA e-invoicing implementation tutorial.
              </p>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => {
                    toast.success('Returning to invoice dashboard...')
                    router.push('/invoice')
                  }}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Return to Dashboard
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    toast.success('Opening invoice creator...')
                    router.push('/invoice/create')
                  }}
                >
                  Create Invoice
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
