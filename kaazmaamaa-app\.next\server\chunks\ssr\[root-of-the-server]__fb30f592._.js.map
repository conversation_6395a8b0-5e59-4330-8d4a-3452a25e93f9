{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        onChange={props.onChange}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gWACA;QAEF,KAAK;QACL,UAAU,MAAM,QAAQ;QACvB,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/label.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface LabelProps\n  extends React.LabelHTMLAttributes<HTMLLabelElement> {}\n\nconst Label = React.forwardRef<HTMLLabelElement, LabelProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <label\n        ref={ref}\n        className={cn(\n          \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n          className\n        )}\n        {...props}\n      />\n    )\n  }\n)\nLabel.displayName = \"Label\"\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/workers/create/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useWorkers } from '@/contexts/WorkersContext'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  ArrowLeft, \n  Upload, \n  FileText, \n  CheckCircle, \n  AlertCircle,\n  User,\n  Briefcase,\n  FileCheck,\n  Heart,\n  Shield,\n  Building,\n  CreditCard,\n  Award\n} from 'lucide-react'\nimport { toast } from 'sonner'\n\ninterface DocumentUpload {\n  type: string\n  file: File | null\n  uploaded: boolean\n  required: boolean\n  icon: any\n  label: string\n  description: string\n}\n\nexport default function CreateWorkerProfilePage() {\n  const router = useRouter()\n  const { user } = useAuth()\n  const { createWorkerProfile } = useWorkers()\n  \n  const [currentStep, setCurrentStep] = useState(1)\n  const [isSubmitting, setIsSubmitting] = useState(false)\n\n  // Personal Information\n  const [personalInfo, setPersonalInfo] = useState({\n    workerName: '',\n    nationality: '',\n    kofilName: '',\n    kofilPhone: '',\n    email: '',\n    phone: '',\n    whatsapp: '',\n    address: '',\n    age: '',\n    profileImage: ''\n  })\n\n  // Identification Information\n  const [identificationInfo, setIdentificationInfo] = useState({\n    iqamaNumber: '',\n    passportNumber: '',\n    crNumber: ''\n  })\n\n  // Professional Information\n  const [professionalInfo, setProfessionalInfo] = useState({\n    workerCategory: '',\n    title: '',\n    specialty: '',\n    experience: '',\n    skills: '',\n    languages: '',\n    expectedSalaryPerHour: '',\n    currency: 'SAR',\n    salaryPayingDuration: 'monthly',\n    availability: 'immediate'\n  })\n\n  // Document uploads with all required documents\n  const [documents, setDocuments] = useState<DocumentUpload[]>([\n    {\n      type: 'absher_iqama',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: CreditCard,\n      label: 'Absher IQAMA Copy',\n      description: 'Official IQAMA document from Absher platform (PDF only)'\n    },\n    {\n      type: 'muqeem',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: FileText,\n      label: 'Muqeem Copy',\n      description: 'Muqeem residence document (PDF only)'\n    },\n    {\n      type: 'medical',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: Heart,\n      label: 'Medical Copy',\n      description: 'Medical examination certificate (PDF only)'\n    },\n    {\n      type: 'insurance',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: Shield,\n      label: 'Insurance Copy',\n      description: 'Health insurance document (PDF only)'\n    },\n    {\n      type: 'chamber',\n      file: null,\n      uploaded: false,\n      required: false,\n      icon: Building,\n      label: 'Chamber Copy',\n      description: 'Chamber of Commerce certificate (PDF only) - If Available'\n    },\n    {\n      type: 'ajeer',\n      file: null,\n      uploaded: false,\n      required: false,\n      icon: Briefcase,\n      label: 'Ajeer Copy',\n      description: 'Ajeer work permit document (PDF only) - If Available'\n    },\n    {\n      type: 'experience',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: Award,\n      label: 'All Experience Copies',\n      description: 'All work experience certificates (PDF only)'\n    },\n    {\n      type: 'fitness',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: CheckCircle,\n      label: 'Fitness Copy',\n      description: 'Physical fitness certificate (PDF only)'\n    },\n    {\n      type: 'passport',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: CreditCard,\n      label: 'Passport Copy',\n      description: 'Valid passport document (PDF only)'\n    },\n    {\n      type: 'visa',\n      file: null,\n      uploaded: false,\n      required: true,\n      icon: FileCheck,\n      label: 'Visa Copy',\n      description: 'Current visa document (PDF only)'\n    },\n    {\n      type: 'gosi',\n      file: null,\n      uploaded: false,\n      required: false,\n      icon: Shield,\n      label: 'GOSI Copy',\n      description: 'GOSI social insurance document (PDF only) - If Available'\n    }\n  ])\n\n  // Preferences\n  const [preferences, setPreferences] = useState({\n    workType: 'full_time' as 'full_time' | 'part_time' | 'contract' | 'freelance',\n    sideLocationInterest: [] as string[],\n    companyNameInterest: [] as string[],\n    preferredLocations: [] as string[],\n    willingToRelocate: false,\n    remoteWork: false,\n    neededDocuments: [] as string[],\n    otherRequirements: ''\n  })\n\n  const handlePersonalInfoChange = (field: string, value: string) => {\n    setPersonalInfo(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleIdentificationChange = (field: string, value: string) => {\n    setIdentificationInfo(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleProfessionalChange = (field: string, value: string) => {\n    setProfessionalInfo(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleDocumentUpload = (index: number, file: File) => {\n    if (file.type !== 'application/pdf') {\n      toast.error('Only PDF files are allowed')\n      return\n    }\n\n    if (file.size > 10 * 1024 * 1024) { // 10MB limit\n      toast.error('File size must be less than 10MB')\n      return\n    }\n\n    const updatedDocuments = [...documents]\n    updatedDocuments[index] = {\n      ...updatedDocuments[index],\n      file,\n      uploaded: true\n    }\n    setDocuments(updatedDocuments)\n    toast.success(`${updatedDocuments[index].label} uploaded successfully!`)\n  }\n\n  const removeDocument = (index: number) => {\n    const updatedDocuments = [...documents]\n    updatedDocuments[index] = {\n      ...updatedDocuments[index],\n      file: null,\n      uploaded: false\n    }\n    setDocuments(updatedDocuments)\n    toast.info('Document removed')\n  }\n\n  const validateStep = (step: number): boolean => {\n    switch (step) {\n      case 1:\n        return !!(personalInfo.workerName && personalInfo.phone && personalInfo.email)\n      case 2:\n        return !!(identificationInfo.iqamaNumber && identificationInfo.passportNumber)\n      case 3:\n        return !!(professionalInfo.workerCategory && professionalInfo.title)\n      case 4:\n        const requiredDocs = documents.filter(doc => doc.required)\n        return requiredDocs.every(doc => doc.uploaded)\n      case 5:\n        return true\n      default:\n        return false\n    }\n  }\n\n  const nextStep = () => {\n    if (validateStep(currentStep)) {\n      setCurrentStep(prev => Math.min(prev + 1, 5))\n    } else {\n      toast.error('Please fill in all required fields before proceeding')\n    }\n  }\n\n  const prevStep = () => {\n    setCurrentStep(prev => Math.max(prev - 1, 1))\n  }\n\n  const handleSubmit = async () => {\n    if (!validateStep(5)) {\n      toast.error('Please complete all required sections')\n      return\n    }\n\n    setIsSubmitting(true)\n\n    try {\n      // Convert uploaded files to document objects\n      const workerDocuments = documents\n        .filter(doc => doc.uploaded && doc.file)\n        .map(doc => ({\n          name: doc.file!.name,\n          type: doc.type as any,\n          url: URL.createObjectURL(doc.file!), // In real app, upload to cloud storage\n          uploadedAt: new Date().toISOString(),\n          isValidated: false,\n          fileSize: doc.file!.size,\n          fileName: doc.file!.name\n        }))\n\n      const newProfile = {\n        userId: user?.id || 'current-user',\n        personalInfo: {\n          ...personalInfo,\n          age: personalInfo.age ? parseInt(personalInfo.age) : undefined\n        },\n        identificationInfo,\n        professionalInfo,\n        documents: workerDocuments,\n        videos: [],\n        workExperience: [],\n        lifecycle: {\n          status: 'active' as const,\n          joinDate: new Date().toISOString(),\n          vacationDates: [],\n          leaveDate: null,\n          notes: ''\n        },\n        preferences,\n        isLiveStreaming: false,\n        isVerified: false,\n        rating: 0,\n        totalReviews: 0\n      }\n\n      createWorkerProfile(newProfile)\n      toast.success('Worker profile created successfully! 🎉')\n\n      // Redirect to user's own profile page\n      router.push(`/profile/${user?.id || 'current-user'}`)\n    } catch (error) {\n      console.error('Error creating profile:', error)\n      toast.error('Failed to create profile. Please try again.')\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const getStepTitle = (step: number) => {\n    switch (step) {\n      case 1: return 'Personal Information'\n      case 2: return 'Identification Details'\n      case 3: return 'Professional Information'\n      case 4: return 'Document Upload'\n      case 5: return 'Work Preferences'\n      default: return 'Profile Setup'\n    }\n  }\n\n  const getStepIcon = (step: number) => {\n    switch (step) {\n      case 1: return User\n      case 2: return CreditCard\n      case 3: return Briefcase\n      case 4: return FileText\n      case 5: return CheckCircle\n      default: return User\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => router.push('/workers')}\n              >\n                <ArrowLeft className=\"h-4 w-4 mr-2\" />\n                Back to Workers\n              </Button>\n              <h1 className=\"text-2xl font-bold text-blue-600\">Create Worker Profile</h1>\n            </div>\n            <Badge variant=\"outline\" className=\"text-blue-600\">\n              Step {currentStep} of 5\n            </Badge>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"max-w-4xl mx-auto px-4 py-6\">\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            {[1, 2, 3, 4, 5].map((step) => {\n              const StepIcon = getStepIcon(step)\n              const isActive = step === currentStep\n              const isCompleted = step < currentStep\n              const isValid = validateStep(step)\n\n              return (\n                <div key={step} className=\"flex flex-col items-center\">\n                  <div\n                    className={`w-12 h-12 rounded-full flex items-center justify-center border-2 ${\n                      isCompleted\n                        ? 'bg-green-600 border-green-600 text-white'\n                        : isActive\n                        ? 'bg-blue-600 border-blue-600 text-white'\n                        : isValid\n                        ? 'bg-gray-100 border-gray-300 text-gray-600'\n                        : 'bg-gray-100 border-gray-300 text-gray-400'\n                    }`}\n                  >\n                    {isCompleted ? (\n                      <CheckCircle className=\"h-6 w-6\" />\n                    ) : (\n                      <StepIcon className=\"h-6 w-6\" />\n                    )}\n                  </div>\n                  <span className={`text-xs mt-2 text-center ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>\n                    {getStepTitle(step)}\n                  </span>\n                </div>\n              )\n            })}\n          </div>\n        </div>\n\n        {/* Form Content */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              {(() => {\n                const StepIcon = getStepIcon(currentStep)\n                return <StepIcon className=\"h-5 w-5 text-blue-600\" />\n              })()}\n              <span>{getStepTitle(currentStep)}</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            {/* Step 1: Personal Information */}\n            {currentStep === 1 && (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"workerName\">Full Name *</Label>\n                    <Input\n                      id=\"workerName\"\n                      value={personalInfo.workerName}\n                      onChange={(e) => handlePersonalInfoChange('workerName', e.target.value)}\n                      placeholder=\"Enter your full name\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"nationality\">Nationality *</Label>\n                    <Input\n                      id=\"nationality\"\n                      value={personalInfo.nationality}\n                      onChange={(e) => handlePersonalInfoChange('nationality', e.target.value)}\n                      placeholder=\"Your nationality\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"kofilName\">Kofil Name</Label>\n                    <Input\n                      id=\"kofilName\"\n                      value={personalInfo.kofilName}\n                      onChange={(e) => handlePersonalInfoChange('kofilName', e.target.value)}\n                      placeholder=\"Kofil sponsor name\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"kofilPhone\">Kofil Phone</Label>\n                    <Input\n                      id=\"kofilPhone\"\n                      value={personalInfo.kofilPhone}\n                      onChange={(e) => handlePersonalInfoChange('kofilPhone', e.target.value)}\n                      placeholder=\"+966 XXX XXX XXX\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"email\">Email Address *</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={personalInfo.email}\n                      onChange={(e) => handlePersonalInfoChange('email', e.target.value)}\n                      placeholder=\"<EMAIL>\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"phone\">Phone Number *</Label>\n                    <Input\n                      id=\"phone\"\n                      value={personalInfo.phone}\n                      onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}\n                      placeholder=\"+966 XXX XXX XXX\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"whatsapp\">WhatsApp Number</Label>\n                    <Input\n                      id=\"whatsapp\"\n                      value={personalInfo.whatsapp}\n                      onChange={(e) => handlePersonalInfoChange('whatsapp', e.target.value)}\n                      placeholder=\"+966 XXX XXX XXX\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"age\">Age</Label>\n                    <Input\n                      id=\"age\"\n                      type=\"number\"\n                      value={personalInfo.age}\n                      onChange={(e) => handlePersonalInfoChange('age', e.target.value)}\n                      placeholder=\"Your age\"\n                      min=\"18\"\n                      max=\"65\"\n                    />\n                  </div>\n                </div>\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"address\">Address</Label>\n                  <Textarea\n                    id=\"address\"\n                    value={personalInfo.address}\n                    onChange={(e) => handlePersonalInfoChange('address', e.target.value)}\n                    placeholder=\"Your complete address\"\n                    rows={3}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Step 2: Identification Information */}\n            {currentStep === 2 && (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"iqamaNumber\">IQAMA Number *</Label>\n                    <Input\n                      id=\"iqamaNumber\"\n                      value={identificationInfo.iqamaNumber}\n                      onChange={(e) => handleIdentificationChange('iqamaNumber', e.target.value)}\n                      placeholder=\"Enter IQAMA number\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"passportNumber\">Passport Number *</Label>\n                    <Input\n                      id=\"passportNumber\"\n                      value={identificationInfo.passportNumber}\n                      onChange={(e) => handleIdentificationChange('passportNumber', e.target.value)}\n                      placeholder=\"Enter passport number\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"crNumber\">C.R. Number (Optional)</Label>\n                    <Input\n                      id=\"crNumber\"\n                      value={identificationInfo.crNumber}\n                      onChange={(e) => handleIdentificationChange('crNumber', e.target.value)}\n                      placeholder=\"Commercial registration number\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step 3: Professional Information */}\n            {currentStep === 3 && (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"workerCategory\">Worker Category *</Label>\n                    <Input\n                      id=\"workerCategory\"\n                      value={professionalInfo.workerCategory}\n                      onChange={(e) => handleProfessionalChange('workerCategory', e.target.value)}\n                      placeholder=\"e.g., Construction, IT, Healthcare\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"title\">Job Title *</Label>\n                    <Input\n                      id=\"title\"\n                      value={professionalInfo.title}\n                      onChange={(e) => handleProfessionalChange('title', e.target.value)}\n                      placeholder=\"e.g., Senior Developer, Electrician\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"specialty\">Specialty</Label>\n                    <Input\n                      id=\"specialty\"\n                      value={professionalInfo.specialty}\n                      onChange={(e) => handleProfessionalChange('specialty', e.target.value)}\n                      placeholder=\"Your area of specialization\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"experience\">Years of Experience</Label>\n                    <Input\n                      id=\"experience\"\n                      type=\"number\"\n                      value={professionalInfo.experience}\n                      onChange={(e) => handleProfessionalChange('experience', e.target.value)}\n                      placeholder=\"Years of experience\"\n                      min=\"0\"\n                      max=\"50\"\n                    />\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Step 4: Document Upload */}\n            {currentStep === 4 && (\n              <div className=\"space-y-6\">\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6\">\n                  <h3 className=\"font-semibold text-blue-800 mb-2\">📄 Document Upload Requirements</h3>\n                  <ul className=\"text-sm text-blue-700 space-y-1\">\n                    <li>• All documents must be in PDF format only</li>\n                    <li>• Maximum file size: 10MB per document</li>\n                    <li>• Required documents must be uploaded to proceed</li>\n                    <li>• Optional documents can be uploaded later</li>\n                  </ul>\n                </div>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {documents.map((doc, index) => {\n                    const IconComponent = doc.icon\n                    return (\n                      <div key={doc.type} className=\"border rounded-lg p-4 space-y-3\">\n                        <div className=\"flex items-center justify-between\">\n                          <div className=\"flex items-center space-x-2\">\n                            <IconComponent className=\"h-5 w-5 text-blue-600\" />\n                            <span className=\"font-medium\">{doc.label}</span>\n                            {doc.required && <span className=\"text-red-500\">*</span>}\n                          </div>\n                          {doc.uploaded && (\n                            <CheckCircle className=\"h-5 w-5 text-green-600\" />\n                          )}\n                        </div>\n\n                        <p className=\"text-sm text-gray-600\">{doc.description}</p>\n\n                        {!doc.uploaded ? (\n                          <div className=\"relative\">\n                            <input\n                              type=\"file\"\n                              accept=\".pdf\"\n                              onChange={(e) => {\n                                const file = e.target.files?.[0]\n                                if (file) {\n                                  handleDocumentUpload(index, file)\n                                }\n                              }}\n                              className=\"absolute inset-0 w-full h-full opacity-0 cursor-pointer\"\n                            />\n                            <Button\n                              type=\"button\"\n                              variant=\"outline\"\n                              className=\"w-full flex items-center justify-center space-x-2\"\n                            >\n                              <Upload className=\"h-4 w-4\" />\n                              <span>Upload PDF</span>\n                            </Button>\n                          </div>\n                        ) : (\n                          <div className=\"flex items-center justify-between\">\n                            <span className=\"text-sm text-green-600 flex items-center\">\n                              <CheckCircle className=\"h-4 w-4 mr-1\" />\n                              {doc.file?.name}\n                            </span>\n                            <Button\n                              type=\"button\"\n                              variant=\"ghost\"\n                              size=\"sm\"\n                              onClick={() => removeDocument(index)}\n                              className=\"text-red-600 hover:text-red-700\"\n                            >\n                              Remove\n                            </Button>\n                          </div>\n                        )}\n                      </div>\n                    )\n                  })}\n                </div>\n\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n                  <h3 className=\"font-semibold text-yellow-800 mb-2\">💰 Document Access Information</h3>\n                  <p className=\"text-sm text-yellow-700\">\n                    Once you complete your profile, your documents will be available for download by suppliers and companies.\n                    They will need to complete a payment process to access your documents and an auto-generated CV.\n                  </p>\n                </div>\n              </div>\n            )}\n\n            {/* Step 5: Work Preferences */}\n            {currentStep === 5 && (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"workType\">Work Type</Label>\n                    <select\n                      id=\"workType\"\n                      value={preferences.workType}\n                      onChange={(e) => setPreferences(prev => ({ ...prev, workType: e.target.value as any }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"full_time\">Full Time</option>\n                      <option value=\"part_time\">Part Time</option>\n                      <option value=\"contract\">Contract</option>\n                      <option value=\"freelance\">Freelance</option>\n                    </select>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label>\n                      <input\n                        type=\"checkbox\"\n                        checked={preferences.willingToRelocate}\n                        onChange={(e) => setPreferences(prev => ({ ...prev, willingToRelocate: e.target.checked }))}\n                        className=\"mr-2\"\n                      />\n                      Willing to Relocate\n                    </Label>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label>\n                      <input\n                        type=\"checkbox\"\n                        checked={preferences.remoteWork}\n                        onChange={(e) => setPreferences(prev => ({ ...prev, remoteWork: e.target.checked }))}\n                        className=\"mr-2\"\n                      />\n                      Available for Remote Work\n                    </Label>\n                  </div>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"otherRequirements\">Other Requirements</Label>\n                  <Textarea\n                    id=\"otherRequirements\"\n                    value={preferences.otherRequirements}\n                    onChange={(e) => setPreferences(prev => ({ ...prev, otherRequirements: e.target.value }))}\n                    placeholder=\"Any additional requirements or notes\"\n                    rows={3}\n                  />\n                </div>\n              </div>\n            )}\n\n            {/* Navigation Buttons */}\n            <div className=\"flex justify-between mt-8\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={prevStep}\n                disabled={currentStep === 1}\n              >\n                Previous\n              </Button>\n\n              {currentStep < 5 ? (\n                <Button\n                  type=\"button\"\n                  onClick={nextStep}\n                  disabled={!validateStep(currentStep)}\n                >\n                  Next\n                </Button>\n              ) : (\n                <Button\n                  type=\"button\"\n                  onClick={handleSubmit}\n                  disabled={isSubmitting || !validateStep(5)}\n                  className=\"bg-green-600 hover:bg-green-700\"\n                >\n                  {isSubmitting ? 'Creating Profile...' : 'Create Profile'}\n                </Button>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AA3BA;;;;;;;;;;;;;;AAuCe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,uBAAuB;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,YAAY;QACZ,aAAa;QACb,WAAW;QACX,YAAY;QACZ,OAAO;QACP,OAAO;QACP,UAAU;QACV,SAAS;QACT,KAAK;QACL,cAAc;IAChB;IAEA,6BAA6B;IAC7B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,aAAa;QACb,gBAAgB;QAChB,UAAU;IACZ;IAEA,2BAA2B;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,gBAAgB;QAChB,OAAO;QACP,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,WAAW;QACX,uBAAuB;QACvB,UAAU;QACV,sBAAsB;QACtB,cAAc;IAChB;IAEA,+CAA+C;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAC3D;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,8MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,4MAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,kNAAA,CAAA,aAAU;YAChB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,gNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,UAAU;YACV,UAAU;YACV,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,cAAc;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,UAAU;QACV,sBAAsB,EAAE;QACxB,qBAAqB,EAAE;QACvB,oBAAoB,EAAE;QACtB,mBAAmB;QACnB,YAAY;QACZ,iBAAiB,EAAE;QACnB,mBAAmB;IACrB;IAEA,MAAM,2BAA2B,CAAC,OAAe;QAC/C,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACtD;IAEA,MAAM,6BAA6B,CAAC,OAAe;QACjD,sBAAsB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAC5D;IAEA,MAAM,2BAA2B,CAAC,OAAe;QAC/C,oBAAoB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAC1D;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,IAAI,KAAK,IAAI,KAAK,mBAAmB;YACnC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,mBAAmB;eAAI;SAAU;QACvC,gBAAgB,CAAC,MAAM,GAAG;YACxB,GAAG,gBAAgB,CAAC,MAAM;YAC1B;YACA,UAAU;QACZ;QACA,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC;IACzE;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB;eAAI;SAAU;QACvC,gBAAgB,CAAC,MAAM,GAAG;YACxB,GAAG,gBAAgB,CAAC,MAAM;YAC1B,MAAM;YACN,UAAU;QACZ;QACA,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;IACb;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,aAAa,UAAU,IAAI,aAAa,KAAK,IAAI,aAAa,KAAK;YAC/E,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,mBAAmB,WAAW,IAAI,mBAAmB,cAAc;YAC/E,KAAK;gBACH,OAAO,CAAC,CAAC,CAAC,iBAAiB,cAAc,IAAI,iBAAiB,KAAK;YACrE,KAAK;gBACH,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ;gBACzD,OAAO,aAAa,KAAK,CAAC,CAAA,MAAO,IAAI,QAAQ;YAC/C,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,WAAW;QACf,IAAI,aAAa,cAAc;YAC7B,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;QAC5C,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;IAC5C;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,IAAI;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,6CAA6C;YAC7C,MAAM,kBAAkB,UACrB,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,IAAI,IAAI,IAAI,EACtC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACX,MAAM,IAAI,IAAI,CAAE,IAAI;oBACpB,MAAM,IAAI,IAAI;oBACd,KAAK,IAAI,eAAe,CAAC,IAAI,IAAI;oBACjC,YAAY,IAAI,OAAO,WAAW;oBAClC,aAAa;oBACb,UAAU,IAAI,IAAI,CAAE,IAAI;oBACxB,UAAU,IAAI,IAAI,CAAE,IAAI;gBAC1B,CAAC;YAEH,MAAM,aAAa;gBACjB,QAAQ,MAAM,MAAM;gBACpB,cAAc;oBACZ,GAAG,YAAY;oBACf,KAAK,aAAa,GAAG,GAAG,SAAS,aAAa,GAAG,IAAI;gBACvD;gBACA;gBACA;gBACA,WAAW;gBACX,QAAQ,EAAE;gBACV,gBAAgB,EAAE;gBAClB,WAAW;oBACT,QAAQ;oBACR,UAAU,IAAI,OAAO,WAAW;oBAChC,eAAe,EAAE;oBACjB,WAAW;oBACX,OAAO;gBACT;gBACA;gBACA,iBAAiB;gBACjB,YAAY;gBACZ,QAAQ;gBACR,cAAc;YAChB;YAEA,oBAAoB;YACpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,sCAAsC;YACtC,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,MAAM,MAAM,gBAAgB;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAG,OAAO,kMAAA,CAAA,OAAI;YACnB,KAAK;gBAAG,OAAO,kNAAA,CAAA,aAAU;YACzB,KAAK;gBAAG,OAAO,4MAAA,CAAA,YAAS;YACxB,KAAK;gBAAG,OAAO,8MAAA,CAAA,WAAQ;YACvB,KAAK;gBAAG,OAAO,2NAAA,CAAA,cAAW;YAC1B;gBAAS,OAAO,kMAAA,CAAA,OAAI;QACtB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC;;0DAE3B,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAgB;oCAC3C;oCAAY;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC;gCACpB,MAAM,WAAW,YAAY;gCAC7B,MAAM,WAAW,SAAS;gCAC1B,MAAM,cAAc,OAAO;gCAC3B,MAAM,UAAU,aAAa;gCAE7B,qBACE,8OAAC;oCAAe,WAAU;;sDACxB,8OAAC;4CACC,WAAW,CAAC,iEAAiE,EAC3E,cACI,6CACA,WACA,2CACA,UACA,8CACA,6CACJ;sDAED,4BACC,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC;gDAAS,WAAU;;;;;;;;;;;sDAGxB,8OAAC;4CAAK,WAAW,CAAC,yBAAyB,EAAE,WAAW,8BAA8B,iBAAiB;sDACpG,aAAa;;;;;;;mCAnBR;;;;;4BAuBd;;;;;;;;;;;kCAKJ,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;wCAClB,CAAC;4CACA,MAAM,WAAW,YAAY;4CAC7B,qBAAO,8OAAC;gDAAS,WAAU;;;;;;wCAC7B,CAAC;sDACD,8OAAC;sDAAM,aAAa;;;;;;;;;;;;;;;;;0CAGxB,8OAAC,gIAAA,CAAA,cAAW;;oCAET,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAa;;;;;;0EAC5B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,aAAa,UAAU;gEAC9B,UAAU,CAAC,IAAM,yBAAyB,cAAc,EAAE,MAAM,CAAC,KAAK;gEACtE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAc;;;;;;0EAC7B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,aAAa,WAAW;gEAC/B,UAAU,CAAC,IAAM,yBAAyB,eAAe,EAAE,MAAM,CAAC,KAAK;gEACvE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAY;;;;;;0EAC3B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,aAAa,SAAS;gEAC7B,UAAU,CAAC,IAAM,yBAAyB,aAAa,EAAE,MAAM,CAAC,KAAK;gEACrE,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAa;;;;;;0EAC5B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,aAAa,UAAU;gEAC9B,UAAU,CAAC,IAAM,yBAAyB,cAAc,EAAE,MAAM,CAAC,KAAK;gEACtE,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,aAAa,KAAK;gEACzB,UAAU,CAAC,IAAM,yBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK;gEACjE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,aAAa,KAAK;gEACzB,UAAU,CAAC,IAAM,yBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK;gEACjE,aAAY;gEACZ,QAAQ;;;;;;;;;;;;kEAGZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO,aAAa,QAAQ;gEAC5B,UAAU,CAAC,IAAM,yBAAyB,YAAY,EAAE,MAAM,CAAC,KAAK;gEACpE,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAM;;;;;;0EACrB,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,aAAa,GAAG;gEACvB,UAAU,CAAC,IAAM,yBAAyB,OAAO,EAAE,MAAM,CAAC,KAAK;gEAC/D,aAAY;gEACZ,KAAI;gEACJ,KAAI;;;;;;;;;;;;;;;;;;0DAIV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAU;;;;;;kEACzB,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,aAAa,OAAO;wDAC3B,UAAU,CAAC,IAAM,yBAAyB,WAAW,EAAE,MAAM,CAAC,KAAK;wDACnE,aAAY;wDACZ,MAAM;;;;;;;;;;;;;;;;;;oCAOb,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAc;;;;;;sEAC7B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,mBAAmB,WAAW;4DACrC,UAAU,CAAC,IAAM,2BAA2B,eAAe,EAAE,MAAM,CAAC,KAAK;4DACzE,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,mBAAmB,cAAc;4DACxC,UAAU,CAAC,IAAM,2BAA2B,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DAC5E,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAW;;;;;;sEAC1B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,mBAAmB,QAAQ;4DAClC,UAAU,CAAC,IAAM,2BAA2B,YAAY,EAAE,MAAM,CAAC,KAAK;4DACtE,aAAY;;;;;;;;;;;;;;;;;;;;;;;oCAQrB,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAiB;;;;;;sEAChC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,iBAAiB,cAAc;4DACtC,UAAU,CAAC,IAAM,yBAAyB,kBAAkB,EAAE,MAAM,CAAC,KAAK;4DAC1E,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAQ;;;;;;sEACvB,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,iBAAiB,KAAK;4DAC7B,UAAU,CAAC,IAAM,yBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK;4DACjE,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAY;;;;;;sEAC3B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,iBAAiB,SAAS;4DACjC,UAAU,CAAC,IAAM,yBAAyB,aAAa,EAAE,MAAM,CAAC,KAAK;4DACrE,aAAY;;;;;;;;;;;;8DAGhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;sEAAa;;;;;;sEAC5B,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,iBAAiB,UAAU;4DAClC,UAAU,CAAC,IAAM,yBAAyB,cAAc,EAAE,MAAM,CAAC,KAAK;4DACtE,aAAY;4DACZ,KAAI;4DACJ,KAAI;;;;;;;;;;;;;;;;;;;;;;;oCAQb,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAAI,WAAU;0DACZ,UAAU,GAAG,CAAC,CAAC,KAAK;oDACnB,MAAM,gBAAgB,IAAI,IAAI;oDAC9B,qBACE,8OAAC;wDAAmB,WAAU;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAc,WAAU;;;;;;0FACzB,8OAAC;gFAAK,WAAU;0FAAe,IAAI,KAAK;;;;;;4EACvC,IAAI,QAAQ,kBAAI,8OAAC;gFAAK,WAAU;0FAAe;;;;;;;;;;;;oEAEjD,IAAI,QAAQ,kBACX,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;;;;;;;0EAI3B,8OAAC;gEAAE,WAAU;0EAAyB,IAAI,WAAW;;;;;;4DAEpD,CAAC,IAAI,QAAQ,iBACZ,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAK;wEACL,QAAO;wEACP,UAAU,CAAC;4EACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;4EAChC,IAAI,MAAM;gFACR,qBAAqB,OAAO;4EAC9B;wEACF;wEACA,WAAU;;;;;;kFAEZ,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,WAAU;;0FAEV,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,8OAAC;0FAAK;;;;;;;;;;;;;;;;;qFAIV,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,2NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;;4EACtB,IAAI,IAAI,EAAE;;;;;;;kFAEb,8OAAC,kIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS,IAAM,eAAe;wEAC9B,WAAU;kFACX;;;;;;;;;;;;;uDAhDG,IAAI,IAAI;;;;;gDAuDtB;;;;;;0DAGF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;oCAS5C,gBAAgB,mBACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,8OAAC;gEACC,IAAG;gEACH,OAAO,YAAY,QAAQ;gEAC3B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,UAAU,EAAE,MAAM,CAAC,KAAK;wEAAQ,CAAC;gEACrF,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,8OAAC;wEAAO,OAAM;kFAAY;;;;;;;;;;;;;;;;;;kEAG9B,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8EACJ,8OAAC;oEACC,MAAK;oEACL,SAAS,YAAY,iBAAiB;oEACtC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,mBAAmB,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEACzF,WAAU;;;;;;gEACV;;;;;;;;;;;;kEAIN,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8EACJ,8OAAC;oEACC,MAAK;oEACL,SAAS,YAAY,UAAU;oEAC/B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,EAAE,MAAM,CAAC,OAAO;4EAAC,CAAC;oEAClF,WAAU;;;;;;gEACV;;;;;;;;;;;;;;;;;;0DAMR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAoB;;;;;;kEACnC,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO,YAAY,iBAAiB;wDACpC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACvF,aAAY;wDACZ,MAAM;;;;;;;;;;;;;;;;;;kDAOd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU,gBAAgB;0DAC3B;;;;;;4CAIA,cAAc,kBACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU,CAAC,aAAa;0DACzB;;;;;qEAID,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS;gDACT,UAAU,gBAAgB,CAAC,aAAa;gDACxC,WAAU;0DAET,eAAe,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D", "debugId": null}}]}