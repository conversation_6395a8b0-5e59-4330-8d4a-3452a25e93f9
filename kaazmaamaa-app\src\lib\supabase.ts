import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Database Types
export type UserRole = 'worker' | 'supplier' | 'company'

export interface User {
  id: string
  email: string
  role: UserRole
  created_at: string
  updated_at: string
}

export interface Profile {
  id: string
  user_id: string
  name: string
  phone: string
  nationality?: string
  iqama?: string
  visa?: string
  passport?: string
  chamber?: string
  cr_number?: string
  license?: string
  company_info?: string
  work_category?: string
  preferred_locations?: string[]
  salary_range?: string
  whatsapp_link?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface Post {
  id: string
  user_id: string
  content: string
  media_urls?: string[]
  post_type: 'text' | 'image' | 'video' | 'live' | 'document'
  visibility: 'public' | 'block_only'
  created_at: string
  updated_at: string
  // Relations
  user?: User
  profile?: Profile
  reactions?: Reaction[]
  comments?: Comment[]
  _count?: {
    reactions: number
    comments: number
  }
}

export interface Reaction {
  id: string
  user_id: string
  post_id: string
  type: 'like' | 'love' | 'interested' | 'clap'
  created_at: string
  // Relations
  user?: User
  profile?: Profile
}

export interface Comment {
  id: string
  user_id: string
  post_id: string
  content: string
  created_at: string
  updated_at: string
  // Relations
  user?: User
  profile?: Profile
}

export interface UploadedFile {
  id: string
  user_id: string
  filename: string
  file_path: string
  file_type: string
  file_size: number
  created_at: string
}
