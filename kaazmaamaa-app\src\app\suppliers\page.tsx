import React from 'react'

export default function SuppliersPage() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>
      {/* Top Header with Sign In and Register buttons */}
      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#059669', margin: 0 }}>
              KAAZMAAMAA
            </h2>
            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Suppliers</span>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <a 
              href="/auth/login"
              style={{ 
                color: '#059669', 
                textDecoration: 'none',
                padding: '0.5rem 1rem',
                border: '1px solid #059669',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Sign In
            </a>
            
            <a 
              href="/auth/signup"
              style={{ 
                backgroundColor: '#059669', 
                color: 'white', 
                textDecoration: 'none',
                padding: '0.5rem 1rem', 
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Register
            </a>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h1 style={{ fontSize: '3rem', fontWeight: 'bold', color: '#059669', textAlign: 'center', marginBottom: '2rem' }}>
            Suppliers Directory
          </h1>
          
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>🏭</div>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1rem' }}>
              Professional Suppliers Network
            </h2>
            <p style={{ color: '#6b7280', marginBottom: '2rem' }}>
              Connect with reliable suppliers and vendors for your business procurement needs.
            </p>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem', marginBottom: '2rem' }}>
              <div style={{ padding: '1.5rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem' }}>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#059669', marginBottom: '0.5rem' }}>
                  Browse Suppliers
                </h3>
                <p style={{ color: '#6b7280', marginBottom: '1rem' }}>Find reliable vendors</p>
                <button style={{ 
                  backgroundColor: '#059669', 
                  color: 'white', 
                  padding: '0.5rem 1rem', 
                  border: 'none', 
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}>
                  View All Suppliers
                </button>
              </div>
              
              <div style={{ padding: '1.5rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem' }}>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#2563eb', marginBottom: '0.5rem' }}>
                  Find Products
                </h3>
                <p style={{ color: '#6b7280', marginBottom: '1rem' }}>Search by category</p>
                <button style={{ 
                  backgroundColor: '#2563eb', 
                  color: 'white', 
                  padding: '0.5rem 1rem', 
                  border: 'none', 
                  borderRadius: '0.25rem',
                  cursor: 'pointer'
                }}>
                  Browse Products
                </button>
              </div>
              
              <div style={{ padding: '1.5rem', border: '1px solid #e5e7eb', borderRadius: '0.5rem' }}>
                <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#dc2626', marginBottom: '0.5rem' }}>
                  Create Profile
                </h3>
                <p style={{ color: '#6b7280', marginBottom: '1rem' }}>Join as a supplier</p>
                <a 
                  href="/suppliers/create"
                  style={{ 
                    display: 'inline-block',
                    backgroundColor: '#dc2626', 
                    color: 'white', 
                    padding: '0.5rem 1rem', 
                    borderRadius: '0.25rem',
                    textDecoration: 'none'
                  }}
                >
                  Create Profile
                </a>
              </div>
            </div>
            
            <div style={{ marginTop: '2rem', padding: '1.5rem', backgroundColor: '#f3f4f6', borderRadius: '0.5rem' }}>
              <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                Supplier Features
              </h3>
              <ul style={{ textAlign: 'left', color: '#6b7280', maxWidth: '600px', margin: '0 auto' }}>
                <li style={{ marginBottom: '0.5rem' }}>• Professional supplier profiles</li>
                <li style={{ marginBottom: '0.5rem' }}>• Product and service catalogs</li>
                <li style={{ marginBottom: '0.5rem' }}>• Business verification system</li>
                <li style={{ marginBottom: '0.5rem' }}>• WhatsApp integration</li>
                <li style={{ marginBottom: '0.5rem' }}>• Document management</li>
                <li>• Real-time messaging with Barta</li>
              </ul>
            </div>
            
            <div style={{ marginTop: '2rem', paddingTop: '1.5rem', borderTop: '1px solid #e5e7eb' }}>
              <p style={{ color: '#9ca3af' }}>
                <a href="/" style={{ color: '#059669', textDecoration: 'none' }}>← Back to Home</a> | 
                <a href="/feed" style={{ color: '#059669', textDecoration: 'none', marginLeft: '0.5rem' }}>Feed</a> | 
                <a href="/workers" style={{ color: '#059669', textDecoration: 'none', marginLeft: '0.5rem' }}>Workers</a> | 
                <a href="/companies" style={{ color: '#059669', textDecoration: 'none', marginLeft: '0.5rem' }}>Companies</a> | 
                <a href="/barta" style={{ color: '#059669', textDecoration: 'none', marginLeft: '0.5rem' }}>Barta</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
