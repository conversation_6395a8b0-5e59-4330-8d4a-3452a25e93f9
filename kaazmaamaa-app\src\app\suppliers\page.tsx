'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useSuppliers } from '@/contexts/SuppliersContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Building,
  Search,
  MessageCircle,
  Phone,
  ArrowLeft,
  MapPin,
  Star,
  Plus,
  FileText,
  Video,
  Calendar,
  Briefcase,
  Eye,
  Download,
  Play,
  Radio,
  CheckCircle,
  Clock,
  DollarSign,
  Globe,
  Users,
  Award,
  Factory
} from 'lucide-react'
import { toast } from 'sonner'

export default function SuppliersPage() {
  const { suppliers } = useSuppliers()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    // Simple loading timer
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  const handleWhatsAppContact = (whatsapp: string, supplierName: string) => {
    const message = `Hello ${supplierName}, I found your company on KAAZMAAMAA Suppliers Block and would like to discuss job opportunities.`
    const whatsappUrl = `https://wa.me/${whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  // Filter suppliers based on search term (with null check)
  const filteredSuppliers = (suppliers || []).filter(supplier =>
    supplier.personalInfo?.supplierName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.businessInfo?.companyName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.businessInfo?.businessType?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading Suppliers...</h2>
          <p className="text-gray-500 mt-2">Please wait while we fetch the suppliers data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  toast.info('Navigating back to Feed...')
                  router.push('/feed')
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Feed
              </Button>
              <h1 className="text-2xl font-bold text-green-600">Suppliers Block</h1>
              <Badge className="bg-green-100 text-green-800">
                {filteredSuppliers.length} Suppliers Available
              </Badge>
            </div>
            <Button
              onClick={() => router.push('/suppliers/create')}
              className="bg-green-600 hover:bg-green-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Suppliers Profile
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Quick Stats */}
        <Card className="mb-6 bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-green-800 mb-2">🏭 Professional Suppliers Directory</h3>
              <p className="text-green-600 mb-4">Browse {(suppliers || []).length} verified suppliers across different business types</p>
              <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-800">{(suppliers || []).filter(s => s.jobDemands?.length > 0).length}</div>
                  <div className="text-sm text-green-600">Active Jobs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{(suppliers || []).filter(s => s.isVerified).length}</div>
                  <div className="text-sm text-blue-600">Verified</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-800">{(suppliers || []).filter(s => s.isLiveStreaming).length}</div>
                  <div className="text-sm text-purple-600">Live</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Search */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search by supplier name, company, or business type..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Suppliers Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredSuppliers.map((supplier) => (
            <Card key={supplier.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className="relative">
                    <Avatar className="h-16 w-16 bg-green-100 flex items-center justify-center">
                      {supplier.personalInfo.profileImage ? (
                        <img
                          src={supplier.personalInfo.profileImage}
                          alt={supplier.personalInfo.supplierName}
                          className="w-full h-full object-cover rounded-full"
                        />
                      ) : (
                        <Factory className="h-8 w-8 text-green-600" />
                      )}
                    </Avatar>
                    {supplier.isLiveStreaming && (
                      <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full flex items-center">
                        <Radio className="h-3 w-3 mr-1" />
                        LIVE
                      </div>
                    )}
                    {supplier.isVerified && (
                      <div className="absolute -bottom-1 -right-1 bg-green-500 text-white rounded-full p-1">
                        <CheckCircle className="h-3 w-3" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-lg truncate">{supplier.personalInfo?.supplierName || 'Unknown Supplier'}</h3>
                    <p className="text-green-600 font-medium">{supplier.businessInfo?.companyName || 'No Company Name'}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge className="bg-green-100 text-green-800">
                        {supplier.businessInfo?.businessType || 'Business'}
                      </Badge>
                      <div className="flex items-center text-yellow-500">
                        <Star className="h-4 w-4 fill-current" />
                        <span className="text-sm ml-1">{supplier.rating || 0}</span>
                        <span className="text-gray-500 text-sm ml-1">({supplier.totalReviews || 0})</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-gray-600 text-sm">
                    <MapPin className="h-4 w-4 mr-2" />
                    {supplier.personalInfo?.address || 'Address not specified'}
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Building className="h-4 w-4 mr-2" />
                    CR: {supplier.businessInfo?.crNumber || 'Not provided'}
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Award className="h-4 w-4 mr-2" />
                    License: {supplier.businessInfo?.licenceNumber || 'Not provided'}
                  </div>

                  <div className="flex items-center text-gray-600 text-sm">
                    <Users className="h-4 w-4 mr-2" />
                    {supplier.businessInfo?.employeeCount || 'Not specified'} employees
                  </div>

                  {/* Job Demands */}
                  <div className="pt-2 border-t">
                    <p className="text-sm font-medium text-gray-700 mb-2">Active Job Demands:</p>
                    {(supplier.jobDemands || []).slice(0, 2).map((job) => (
                      <div key={job.id} className="bg-gray-50 rounded-lg p-2 mb-2">
                        <p className="font-medium text-sm">{job.position}</p>
                        <p className="text-xs text-gray-600">{job.category}</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-green-600">{job.salaryPerHour} {job.currency}/hr</span>
                          <Badge variant="secondary" className="text-xs">
                            {job.urgency.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>
                    ))}
                    {(supplier.jobDemands || []).length > 2 && (
                      <p className="text-xs text-gray-500">+{(supplier.jobDemands || []).length - 2} more jobs</p>
                    )}
                  </div>

                  {/* Operating Locations */}
                  <div className="flex flex-wrap gap-1 mt-2">
                    {(supplier.operatingLocations || []).slice(0, 3).map((location, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {location}
                      </Badge>
                    ))}
                    {(supplier.operatingLocations || []).length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{(supplier.operatingLocations || []).length - 3} more
                      </Badge>
                    )}
                  </div>

                  {/* Media Indicators */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500 pt-2 border-t">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-1" />
                      {(supplier.documents || []).length} docs
                    </div>
                    <div className="flex items-center">
                      <Video className="h-4 w-4 mr-1" />
                      {(supplier.videos || []).length} videos
                    </div>
                    <div className="flex items-center">
                      <Briefcase className="h-4 w-4 mr-1" />
                      {(supplier.jobDemands || []).length} jobs
                    </div>
                  </div>

                  <div className="flex gap-2 pt-3">
                    <Button
                      size="sm"
                      onClick={() => router.push(`/suppliers/${supplier.id}`)}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Profile
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleWhatsAppContact(supplier.personalInfo.whatsapp, supplier.personalInfo.supplierName)}
                      className="border-green-300 text-green-600 hover:bg-green-50"
                    >
                      <MessageCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredSuppliers.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <Factory className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {(suppliers || []).length === 0 ? 'No Suppliers Available' : 'No Suppliers Found'}
              </h3>
              <p className="text-gray-600 mb-4">
                {(suppliers || []).length === 0
                  ? 'There are currently no suppliers registered in the system.'
                  : 'Try adjusting your search criteria to find suppliers.'
                }
              </p>
              {(suppliers || []).length > 0 && (
                <Button
                  variant="outline"
                  onClick={() => setSearchTerm('')}
                >
                  Clear Search
                </Button>
              )}
              {(suppliers || []).length === 0 && (
                <Button
                  onClick={() => {
                    toast.info('Refreshing suppliers data...')
                    window.location.reload()
                  }}
                  variant="outline"
                >
                  Refresh Page
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
