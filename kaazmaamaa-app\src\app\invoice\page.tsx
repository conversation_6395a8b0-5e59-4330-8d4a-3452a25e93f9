'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, FileText, Settings, CheckCircle, AlertCircle, Clock, Download, Upload, QrCode, Shield, Globe, Users } from 'lucide-react'
import { toast } from 'sonner'

export default function InvoicePage() {
  const { user, userRole, loading } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])

  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
      return
    }

    // Simple loading timer
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [loading, user, router])

  const invoiceSteps = [
    {
      id: 1,
      title: "E-Invoicing Phases Overview",
      description: "Understand ZATCA's two-phase implementation",
      icon: <FileText className="h-6 w-6" />,
      status: "active"
    },
    {
      id: 2,
      title: "Developer Tools Setup",
      description: "Download and integrate ZATCA's SDK",
      icon: <Settings className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 3,
      title: "EGS Onboarding Process",
      description: "OTP generation, CSR submission, CSID issuance",
      icon: <Users className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 4,
      title: "Invoice Signing Implementation",
      description: "SHA-256 hash and ECDSA signature",
      icon: <Shield className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 5,
      title: "QR Code Generation",
      description: "TLV format Base64 encoding",
      icon: <QrCode className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 6,
      title: "Clearance & Reporting APIs",
      description: "B2B Clearance and B2C Reporting flows",
      icon: <Globe className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 7,
      title: "VAT Groups & Edge Cases",
      description: "Handle VAT Group scenarios",
      icon: <Users className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 8,
      title: "Sandbox Testing",
      description: "Test with ZATCA's Sandbox environment",
      icon: <Upload className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 9,
      title: "Compliance Validation",
      description: "CLI validation and QR code checks",
      icon: <CheckCircle className="h-6 w-6" />,
      status: "pending"
    },
    {
      id: 10,
      title: "FAQ & Troubleshooting",
      description: "Handle rejected invoices and edge cases",
      icon: <AlertCircle className="h-6 w-6" />,
      status: "pending"
    }
  ]

  const handleStepClick = (stepId: number) => {
    setCurrentStep(stepId)
    toast.info(`Navigating to Step ${stepId}: ${invoiceSteps[stepId - 1].title}`)
    // Navigate to specific step page
    router.push(`/invoice/step${stepId}`)
  }

  const markStepComplete = (stepId: number) => {
    if (!completedSteps.includes(stepId)) {
      setCompletedSteps([...completedSteps, stepId])
      toast.success(`Step ${stepId} completed! ✅`)
    }
  }

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-700">Loading ZATCA Invoice System...</h2>
          <p className="text-gray-500 mt-2">Initializing VAT/Tax Invoice Software</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-4">Please sign in to access the Invoice system.</p>
          <Button onClick={() => router.push('/')}>Go to Home</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  toast.info('Navigating back to Feed...')
                  router.push('/feed')
                }}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Feed
              </Button>
              <h1 className="text-2xl font-bold text-green-600">ZATCA Invoice System</h1>
              <Badge className="bg-green-100 text-green-800">
                VAT/Tax Compliant
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-blue-600">
                Step {currentStep}/10
              </Badge>
              <Badge className="bg-blue-100 text-blue-800">
                {completedSteps.length} Completed
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Overview Card */}
        <Card className="mb-6 bg-green-50 border-green-200">
          <CardContent className="pt-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-green-800 mb-2">🧾 ZATCA E-Invoicing Compliance System</h3>
              <p className="text-green-600 mb-4">Complete VAT/Tax Invoice Software following ZATCA guidelines</p>

              {/* Create New Invoice Button */}
              <div className="mb-6">
                <Button
                  onClick={() => {
                    toast.success('Opening Invoice Creator...')
                    router.push('/invoice/create')
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 text-lg"
                  size="lg"
                >
                  <FileText className="h-5 w-5 mr-2" />
                  CREATE NEW INVOICE
                </Button>
                <p className="text-sm text-green-600 mt-2">Generate ZATCA-compliant invoices with QR codes</p>
              </div>

              <div className="grid grid-cols-3 gap-4 max-w-md mx-auto">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-800">{completedSteps.length}</div>
                  <div className="text-sm text-green-600">Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-800">{10 - completedSteps.length}</div>
                  <div className="text-sm text-blue-600">Remaining</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-800">{Math.round((completedSteps.length / 10) * 100)}%</div>
                  <div className="text-sm text-purple-600">Progress</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {invoiceSteps.map((step) => (
            <Card
              key={step.id}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                currentStep === step.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              } ${
                completedSteps.includes(step.id) ? 'bg-green-50 border-green-200' : ''
              }`}
              onClick={() => handleStepClick(step.id)}
            >
              <CardHeader>
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    completedSteps.includes(step.id) ? 'bg-green-100 text-green-600' :
                    currentStep === step.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {completedSteps.includes(step.id) ? <CheckCircle className="h-6 w-6" /> : step.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold text-lg truncate">
                        Step {step.id}: {step.title}
                      </h3>
                      {completedSteps.includes(step.id) && (
                        <Badge className="bg-green-100 text-green-800">✓</Badge>
                      )}
                      {currentStep === step.id && !completedSteps.includes(step.id) && (
                        <Badge className="bg-blue-100 text-blue-800">Active</Badge>
                      )}
                    </div>
                    <p className="text-gray-600 text-sm mt-1">
                      {step.description}
                    </p>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="flex justify-between items-center">
                  <Button
                    size="sm"
                    variant={currentStep === step.id ? "default" : "outline"}
                    onClick={(e) => {
                      e.stopPropagation()
                      handleStepClick(step.id)
                    }}
                  >
                    {completedSteps.includes(step.id) ? 'Review' : 'Start'}
                  </Button>

                  {!completedSteps.includes(step.id) && (
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation()
                        markStepComplete(step.id)
                      }}
                    >
                      Mark Complete
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
