'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'

export interface Comment {
  id: string
  post_id: string
  user_id: string
  user_name: string
  user_role: 'worker' | 'supplier' | 'company'
  content: string
  created_at: string
}

export interface Post {
  id: string
  user_id: string
  user_name: string
  user_role: 'worker' | 'supplier' | 'company'
  content: string
  media_urls?: string[]
  post_type: 'text' | 'image' | 'video' | 'live' | 'document' | 'mixed'
  visibility: 'public' | 'block_only'
  created_at: string
  expires_at: string // Posts expire after 1 month
  likes: number
  comments: number
  liked_by_user?: boolean
  post_comments?: Comment[]
  shared_count?: number
}

interface PostsContextType {
  posts: Post[]
  addPost: (post: Omit<Post, 'id' | 'created_at' | 'expires_at' | 'likes' | 'comments'>) => void
  likePost: (postId: string) => void
  sharePost: (postId: string) => void
  deletePost: (postId: string) => void
  addComment: (postId: string, content: string, user: { id: string, name: string, role: 'worker' | 'supplier' | 'company' }) => void
  clearAllPosts: () => void
  resetToInitialPosts: () => void
  cleanupExpiredPosts: () => void
  migratePostsToFullNames: () => void
}

const PostsContext = createContext<PostsContextType | undefined>(undefined)

// Helper function to calculate expiration date (1 month from creation)
const getExpirationDate = (createdAt: string): string => {
  const created = new Date(createdAt)
  const expiration = new Date(created)
  expiration.setMonth(expiration.getMonth() + 1) // Add 1 month
  return expiration.toISOString()
}

// Initial mock posts
const initialPosts: Post[] = [
  {
    id: '1',
    user_id: 'mock-user-1',
    user_name: 'Ahmed Al-Rashid',
    user_role: 'worker',
    content: 'Looking for construction work in Riyadh. I have 5 years of experience in electrical work. Available immediately! #ElectricalWork #Riyadh #Available',
    post_type: 'text',
    visibility: 'public',
    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    expires_at: getExpirationDate(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),
    likes: 12,
    comments: 3,
    shared_count: 2,
    post_comments: [
      {
        id: 'comment-1',
        post_id: '1',
        user_id: 'commenter-1',
        user_name: 'Sarah Construction',
        user_role: 'company',
        content: 'We have openings for electrical work. Please send your CV.',
        created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
      },
      {
        id: 'comment-2',
        post_id: '1',
        user_id: 'commenter-2',
        user_name: 'Ali Hassan',
        user_role: 'worker',
        content: 'Good luck brother! I also work in electrical.',
        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()
      }
    ]
  },
  {
    id: '2',
    user_id: 'mock-user-2',
    user_name: 'Saudi Construction Co.',
    user_role: 'company',
    content: 'We are hiring 20 skilled workers for our new project in Jeddah. Competitive salary and accommodation provided. Contact us for details. #Hiring #Jeddah #Construction',
    post_type: 'text',
    visibility: 'public',
    created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
    expires_at: getExpirationDate(new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()),
    likes: 28,
    comments: 15
  },
  {
    id: '3',
    user_id: 'mock-user-3',
    user_name: 'Gulf Manpower Solutions',
    user_role: 'supplier',
    content: 'New batch of certified welders available for immediate deployment. All workers have valid IQAMA and safety certifications. #Welders #Certified #Available',
    post_type: 'text',
    visibility: 'public',
    created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    expires_at: getExpirationDate(new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()),
    likes: 18,
    comments: 8
  },
  {
    id: '4',
    user_id: 'mock-user-4',
    user_name: 'Mohammed Hassan',
    user_role: 'worker',
    content: 'Just completed my safety certification course! Ready for new opportunities in the oil and gas sector. #SafetyCertified #OilAndGas #Ready',
    post_type: 'text',
    visibility: 'public',
    created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago
    expires_at: getExpirationDate(new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()),
    likes: 15,
    comments: 5
  }
]

export function PostsProvider({ children }: { children: ReactNode }) {
  const [posts, setPosts] = useState<Post[]>([])
  const [isLoaded, setIsLoaded] = useState(false)

  // Load posts from localStorage on component mount
  useEffect(() => {
    const loadPostsFromStorage = () => {
      try {
        const savedPosts = localStorage.getItem('kaazmaamaa-posts')
        if (savedPosts) {
          const parsedPosts = JSON.parse(savedPosts)
          console.log('Loaded posts from localStorage:', parsedPosts.length)

          // Filter out expired posts when loading
          const now = new Date()
          const activePosts = parsedPosts.filter((post: Post) => {
            // Handle posts that might not have expires_at field (legacy posts)
            if (!post.expires_at) {
              // Add expires_at to legacy posts (1 month from creation)
              post.expires_at = getExpirationDate(post.created_at)
            }
            const expirationDate = new Date(post.expires_at)
            return expirationDate > now
          })

          const expiredCount = parsedPosts.length - activePosts.length
          if (expiredCount > 0) {
            console.log(`Filtered out ${expiredCount} expired posts on load`)
            // Save cleaned posts back to localStorage
            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts))
          }

          setPosts(activePosts)
        } else {
          // If no saved posts, use initial posts
          console.log('No saved posts found, using initial posts')
          setPosts(initialPosts)
          localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts))
        }
      } catch (error) {
        console.error('Error loading posts from localStorage:', error)
        setPosts(initialPosts)
      } finally {
        setIsLoaded(true)
      }
    }

    loadPostsFromStorage()
  }, [])

  // Save posts to localStorage whenever posts change
  useEffect(() => {
    if (isLoaded && posts.length > 0) {
      try {
        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(posts))
        console.log('Saved posts to localStorage:', posts.length)
      } catch (error) {
        console.error('Error saving posts to localStorage:', error)
      }
    }
  }, [posts, isLoaded])

  console.log('PostsProvider initialized with posts:', posts.length)

  const addPost = (newPost: Omit<Post, 'id' | 'created_at' | 'expires_at' | 'likes' | 'comments'>) => {
    const createdAt = new Date().toISOString()
    const post: Post = {
      ...newPost,
      id: Date.now().toString(),
      created_at: createdAt,
      expires_at: getExpirationDate(createdAt), // Posts expire after 1 month
      likes: 0,
      comments: 0
    }
    console.log('Adding post to context:', post)
    setPosts(prevPosts => {
      const updatedPosts = [post, ...prevPosts]
      console.log('Updated posts array:', updatedPosts)

      // Immediately save to localStorage
      try {
        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))
        console.log('Post immediately saved to localStorage')
      } catch (error) {
        console.error('Error saving post to localStorage:', error)
      }

      return updatedPosts
    })
  }

  const likePost = (postId: string) => {
    setPosts(prevPosts => {
      const updatedPosts = prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              likes: post.liked_by_user ? post.likes - 1 : post.likes + 1,
              liked_by_user: !post.liked_by_user
            }
          : post
      )

      // Save likes to localStorage
      try {
        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))
        console.log('Like status saved to localStorage')
      } catch (error) {
        console.error('Error saving like to localStorage:', error)
      }

      return updatedPosts
    })
  }

  const sharePost = (postId: string) => {
    setPosts(prevPosts => {
      const updatedPosts = prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              shared_count: (post.shared_count || 0) + 1
            }
          : post
      )

      // Save share count to localStorage
      try {
        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))
        console.log('Share count saved to localStorage')
      } catch (error) {
        console.error('Error saving share to localStorage:', error)
      }

      return updatedPosts
    })
  }

  const addComment = (postId: string, content: string, user: { id: string, name: string, role: 'worker' | 'supplier' | 'company' }) => {
    const newComment: Comment = {
      id: Date.now().toString(),
      post_id: postId,
      user_id: user.id,
      user_name: user.name,
      user_role: user.role,
      content: content.trim(),
      created_at: new Date().toISOString()
    }

    setPosts(prevPosts => {
      const updatedPosts = prevPosts.map(post =>
        post.id === postId
          ? {
              ...post,
              comments: post.comments + 1,
              post_comments: [...(post.post_comments || []), newComment]
            }
          : post
      )

      // Save comments to localStorage
      try {
        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))
        console.log('Comment saved to localStorage')
      } catch (error) {
        console.error('Error saving comment to localStorage:', error)
      }

      return updatedPosts
    })
  }

  const deletePost = (postId: string) => {
    setPosts(prevPosts => {
      const updatedPosts = prevPosts.filter(post => post.id !== postId)

      // Save deletion to localStorage
      try {
        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))
        console.log('Post deletion saved to localStorage')
      } catch (error) {
        console.error('Error saving deletion to localStorage:', error)
      }

      return updatedPosts
    })
  }

  const clearAllPosts = () => {
    setPosts([])
    try {
      localStorage.removeItem('kaazmaamaa-posts')
      console.log('All posts cleared from localStorage')
    } catch (error) {
      console.error('Error clearing posts from localStorage:', error)
    }
  }

  const resetToInitialPosts = () => {
    setPosts(initialPosts)
    try {
      localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts))
      console.log('Reset to initial posts and saved to localStorage')
    } catch (error) {
      console.error('Error resetting posts in localStorage:', error)
    }
  }

  // Function to clean up expired posts (older than 1 month)
  const cleanupExpiredPosts = useCallback(() => {
    const now = new Date()
    setPosts(prevPosts => {
      const activePosts = prevPosts.filter(post => {
        const expirationDate = new Date(post.expires_at)
        return expirationDate > now
      })

      const expiredCount = prevPosts.length - activePosts.length
      if (expiredCount > 0) {
        console.log(`Cleaned up ${expiredCount} expired posts`)

        // Save cleaned posts to localStorage
        try {
          localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts))
          console.log('Cleaned posts saved to localStorage')
        } catch (error) {
          console.error('Error saving cleaned posts to localStorage:', error)
        }
      }

      return activePosts
    })
  }, [])

  const migratePostsToFullNames = () => {
    console.log('Migrating posts to use full names...')
    // Clear localStorage and reset to initial posts with proper names
    try {
      localStorage.removeItem('kaazmaamaa-posts')
      console.log('Cleared old posts from localStorage')
    } catch (error) {
      console.error('Error clearing localStorage:', error)
    }
    resetToInitialPosts()
  }

  const value = {
    posts,
    addPost,
    likePost,
    sharePost,
    deletePost,
    addComment,
    clearAllPosts,
    resetToInitialPosts,
    cleanupExpiredPosts,
    migratePostsToFullNames
  }

  return (
    <PostsContext.Provider value={value}>
      {children}
    </PostsContext.Provider>
  )
}

export function usePosts() {
  const context = useContext(PostsContext)
  if (context === undefined) {
    throw new Error('usePosts must be used within a PostsProvider')
  }
  return context
}

// Utility function to format time ago
export function formatTimeAgo(dateString: string): string {
  const now = new Date()
  const postDate = new Date(dateString)
  const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))

  if (diffInMinutes < 1) return 'Just now'
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`

  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h ago`

  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`

  return postDate.toLocaleDateString()
}

// Utility function to format post expiration info
export function formatExpirationInfo(expiresAt: string): { text: string, isExpiringSoon: boolean } {
  const now = new Date()
  const expirationDate = new Date(expiresAt)
  const diffInMs = expirationDate.getTime() - now.getTime()
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))

  if (diffInMs <= 0) {
    return { text: 'Expired', isExpiringSoon: true }
  }

  if (diffInDays <= 3) {
    return { text: `Expires in ${diffInDays} day${diffInDays !== 1 ? 's' : ''}`, isExpiringSoon: true }
  }

  if (diffInDays <= 7) {
    return { text: `Expires in ${diffInDays} days`, isExpiringSoon: false }
  }

  const diffInWeeks = Math.floor(diffInDays / 7)
  if (diffInWeeks < 4) {
    return { text: `Expires in ${diffInWeeks} week${diffInWeeks !== 1 ? 's' : ''}`, isExpiringSoon: false }
  }

  return { text: 'Expires in 1 month', isExpiringSoon: false }
}
