'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, Building, Wrench } from 'lucide-react'

export default function Home() {
  const { user, userRole, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user && userRole) {
      router.push('/feed')
    }
  }, [user, userRole, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Redirecting to your feed...</h2>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            Welcome to <span className="text-blue-600">KAAZMAAMAA</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            The professional social network connecting Workers, Suppliers, and Companies.
            Share your expertise, find opportunities, and build meaningful connections.
          </p>
        </div>

        {/* Role Selection Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          <Card className="hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-blue-300">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center">
                <Wrench className="h-8 w-8 text-blue-600" />
              </div>
              <CardTitle className="text-2xl">Worker</CardTitle>
              <CardDescription className="text-base">
                Skilled professionals looking for opportunities
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• Upload your credentials and documents</li>
                <li>• Showcase your skills and experience</li>
                <li>• Connect with suppliers and companies</li>
                <li>• Find job opportunities</li>
              </ul>
              <Button
                className="w-full"
                onClick={() => router.push('/auth/signup?role=worker')}
              >
                Join as Worker
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-green-300">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-green-100 rounded-full w-16 h-16 flex items-center justify-center">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl">Supplier</CardTitle>
              <CardDescription className="text-base">
                Agencies providing workforce solutions
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• Post job requirements and opportunities</li>
                <li>• Connect with qualified workers</li>
                <li>• Manage your workforce network</li>
                <li>• Share industry insights</li>
              </ul>
              <Button
                className="w-full bg-green-600 hover:bg-green-700"
                onClick={() => router.push('/auth/signup?role=supplier')}
              >
                Join as Supplier
              </Button>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow cursor-pointer border-2 hover:border-purple-300">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 p-3 bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center">
                <Building className="h-8 w-8 text-purple-600" />
              </div>
              <CardTitle className="text-2xl">Company</CardTitle>
              <CardDescription className="text-base">
                Businesses seeking workforce solutions
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <ul className="text-sm text-gray-600 mb-6 space-y-2">
                <li>• Post project requirements</li>
                <li>• Find reliable suppliers and workers</li>
                <li>• Build long-term partnerships</li>
                <li>• Share company updates</li>
              </ul>
              <Button
                className="w-full bg-purple-600 hover:bg-purple-700"
                onClick={() => router.push('/auth/signup?role=company')}
              >
                Join as Company
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Login Section */}
        <div className="text-center">
          <p className="text-gray-600 mb-4">Already have an account?</p>
          <Button
            variant="outline"
            size="lg"
            onClick={() => router.push('/auth/login')}
          >
            Sign In
          </Button>
        </div>

        {/* Features Section */}
        <div className="mt-20 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">Why Choose KAAZMAAMAA?</h2>
          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div>
              <h3 className="text-xl font-semibold mb-2">Real-time Collaboration</h3>
              <p className="text-gray-600">Share updates, react to posts, and engage with your professional community in real-time.</p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Document Management</h3>
              <p className="text-gray-600">Securely upload and manage your professional documents, certificates, and credentials.</p>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Direct Communication</h3>
              <p className="text-gray-600">Connect directly via WhatsApp integration for seamless business communication.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
