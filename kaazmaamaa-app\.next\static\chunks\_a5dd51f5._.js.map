{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/app/feed/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { usePosts } from '@/contexts/PostsContext'\nimport { useRouter } from 'next/navigation'\n\nexport default function FeedPage() {\n  const { user, userRole, getUserProfile, loading, signOut } = useAuth()\n  const { posts, addPost, likePost } = usePosts()\n  const router = useRouter()\n  const [showCreatePost, setShowCreatePost] = useState(false)\n  const [postText, setPostText] = useState('')\n  const [selectedFeeling, setSelectedFeeling] = useState('')\n  const [selectedImage, setSelectedImage] = useState<string | null>(null)\n  const [userProfile, setUserProfile] = useState<any>(null)\n\n  // Get user profile data\n  useEffect(() => {\n    if (user && userRole) {\n      const profile = getUserProfile()\n      setUserProfile(profile)\n      console.log('User profile loaded:', profile)\n    }\n  }, [user, userRole, getUserProfile])\n\n  // Redirect to home if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/')\n    }\n  }, [user, loading, router])\n\n  // Helper function to get user's display name\n  const getUserDisplayName = () => {\n    if (!userProfile) return 'User'\n\n    if (userRole === 'worker') {\n      return userProfile.personalInfo?.workerName || 'Worker'\n    } else if (userRole === 'supplier') {\n      return userProfile.personalInfo?.supplierName || 'Supplier'\n    } else if (userRole === 'company') {\n      return userProfile.companyInfo?.companyName || 'Company'\n    }\n    return 'User'\n  }\n\n  // Helper function to get user's avatar/initial\n  const getUserInitial = () => {\n    const name = getUserDisplayName()\n    return name.charAt(0).toUpperCase()\n  }\n\n  // Helper function to get user's role emoji\n  const getUserRoleEmoji = () => {\n    switch (userRole) {\n      case 'worker': return '🔧'\n      case 'supplier': return '🏭'\n      case 'company': return '🏢'\n      default: return '👤'\n    }\n  }\n\n  // Show loading if still authenticating\n  if (loading) {\n    return (\n      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f0f2f5' }}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>\n          <div style={{ color: '#65676b' }}>Loading your feed...</div>\n        </div>\n      </div>\n    )\n  }\n\n  // Redirect if not authenticated\n  if (!user) {\n    return null\n  }\n  return (\n    <div style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>\n      {/* Facebook-style Header */}\n      <div style={{ backgroundColor: '#1877f2', padding: '0.75rem 1rem', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>\n        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>\n            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white', margin: 0 }}>\n              KAAZMAAMAA\n            </h1>\n            <div style={{ backgroundColor: 'rgba(255,255,255,0.2)', borderRadius: '20px', padding: '0.5rem 1rem' }}>\n              <input \n                type=\"text\" \n                placeholder=\"Search KAAZMAAMAA...\" \n                style={{ \n                  backgroundColor: 'transparent', \n                  border: 'none', \n                  color: 'white', \n                  outline: 'none',\n                  fontSize: '0.875rem'\n                }}\n              />\n            </div>\n          </div>\n          \n          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', color: 'white' }}>\n              <div style={{\n                backgroundColor: 'rgba(255,255,255,0.2)',\n                borderRadius: '50%',\n                width: '2rem',\n                height: '2rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontSize: '0.875rem',\n                fontWeight: 'bold'\n              }}>\n                {getUserInitial()}\n              </div>\n              <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>\n                {getUserDisplayName()}\n              </span>\n            </div>\n\n            <div style={{ display: 'flex', gap: '0.75rem' }}>\n              <button\n                onClick={() => {\n                  const profilePath = userRole === 'worker' ? '/workers/profile' :\n                                    userRole === 'supplier' ? '/suppliers/profile' :\n                                    '/companies/profile'\n                  router.push(profilePath)\n                }}\n                style={{\n                  backgroundColor: 'rgba(255,255,255,0.2)',\n                  color: 'white',\n                  border: '1px solid rgba(255,255,255,0.3)',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '0.25rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer'\n                }}\n              >\n                Go Profile\n              </button>\n\n              <button\n                onClick={async () => {\n                  await signOut()\n                  router.push('/')\n                }}\n                style={{\n                  backgroundColor: 'rgba(255,255,255,0.2)',\n                  color: 'white',\n                  border: '1px solid rgba(255,255,255,0.3)',\n                  padding: '0.5rem 1rem',\n                  borderRadius: '0.25rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer'\n                }}\n              >\n                Sign Out\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Feed Layout */}\n      <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'grid', gridTemplateColumns: '1fr 2fr 1fr', gap: '1rem', padding: '1rem' }}>\n        \n        {/* Left Sidebar */}\n        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>\n            Quick Access\n          </h3>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n            <a href=\"/workers\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#e3f2fd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                🔧\n              </div>\n              <span style={{ fontWeight: '500' }}>Workers</span>\n            </a>\n            \n            <a href=\"/suppliers\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#e8f5e8', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                🏭\n              </div>\n              <span style={{ fontWeight: '500' }}>Suppliers</span>\n            </a>\n            \n            <a href=\"/companies\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#f3e8ff', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                🏢\n              </div>\n              <span style={{ fontWeight: '500' }}>Companies</span>\n            </a>\n            \n            <a href=\"/barta\" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>\n              <div style={{ backgroundColor: '#fff3cd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                💬\n              </div>\n              <span style={{ fontWeight: '500' }}>Barta Messenger</span>\n            </a>\n          </div>\n        </div>\n\n        {/* Center Feed */}\n        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>\n          \n          {/* Create Post */}\n          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n            <div style={{ display: 'flex', gap: '0.75rem', marginBottom: '1rem' }}>\n              <div style={{\n                backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                borderRadius: '50%',\n                width: '2.5rem',\n                height: '2.5rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: 'bold'\n              }}>\n                {getUserInitial()}\n              </div>\n              <input\n                type=\"text\"\n                placeholder={`What's on your mind, ${getUserDisplayName()}?`}\n                onClick={() => setShowCreatePost(true)}\n                readOnly\n                style={{\n                  flex: 1,\n                  backgroundColor: '#f0f2f5',\n                  border: 'none',\n                  borderRadius: '20px',\n                  padding: '0.75rem 1rem',\n                  outline: 'none',\n                  fontSize: '1rem',\n                  cursor: 'pointer'\n                }}\n              />\n            </div>\n            <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>\n              <button\n                onClick={() => setShowCreatePost(true)}\n                style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}\n              >\n                📷 Photo/Video\n              </button>\n              <button\n                onClick={() => setShowCreatePost(true)}\n                style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}\n              >\n                😊 Feeling/Activity\n              </button>\n            </div>\n          </div>\n\n          {/* Dynamic Posts from Context */}\n          {posts.map((post) => {\n            // Helper function to get role color\n            const getRoleColor = (role: string) => {\n              switch (role) {\n                case 'worker': return '#2563eb'\n                case 'supplier': return '#059669'\n                case 'company': return '#7c3aed'\n                default: return '#6b7280'\n              }\n            }\n\n            // Helper function to get role emoji\n            const getRoleEmoji = (role: string) => {\n              switch (role) {\n                case 'worker': return '🔧'\n                case 'supplier': return '🏭'\n                case 'company': return '🏢'\n                default: return '👤'\n              }\n            }\n\n            // Helper function to format time\n            const getTimeAgo = (dateString: string) => {\n              const now = new Date()\n              const postDate = new Date(dateString)\n              const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))\n\n              if (diffInMinutes < 1) return 'Just now'\n              if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`\n\n              const diffInHours = Math.floor(diffInMinutes / 60)\n              if (diffInHours < 24) return `${diffInHours} hours ago`\n\n              const diffInDays = Math.floor(diffInHours / 24)\n              return `${diffInDays} days ago`\n            }\n\n            return (\n              <div key={post.id} style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n                {/* Post Header */}\n                <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n                  <div style={{\n                    backgroundColor: getRoleColor(post.user_role),\n                    borderRadius: '50%',\n                    width: '2.5rem',\n                    height: '2.5rem',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontWeight: 'bold'\n                  }}>\n                    {post.user_name.charAt(0).toUpperCase()}\n                  </div>\n                  <div>\n                    <button\n                      onClick={() => {\n                        // Navigate to user's profile based on their role\n                        const profilePath = post.user_role === 'worker' ? '/workers/profile' :\n                                          post.user_role === 'supplier' ? '/suppliers/profile' :\n                                          '/companies/profile'\n                        router.push(`${profilePath}?user=${encodeURIComponent(post.user_id)}`)\n                      }}\n                      style={{\n                        background: 'none',\n                        border: 'none',\n                        padding: 0,\n                        fontWeight: '600',\n                        color: '#1c1e21',\n                        cursor: 'pointer',\n                        fontSize: 'inherit',\n                        textAlign: 'left'\n                      }}\n                      onMouseEnter={(e) => (e.target as HTMLElement).style.textDecoration = 'underline'}\n                      onMouseLeave={(e) => (e.target as HTMLElement).style.textDecoration = 'none'}\n                    >\n                      {post.user_name}\n                    </button>\n                    <div style={{ fontSize: '0.8rem', color: '#65676b' }}>\n                      {getTimeAgo(post.created_at)} • {getRoleEmoji(post.user_role)} {post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Post Content */}\n                <div style={{ paddingLeft: '1rem', paddingRight: '1rem', marginBottom: '1rem' }}>\n                  <p style={{ color: '#1c1e21', lineHeight: '1.5', margin: 0 }}>\n                    {post.content}\n                  </p>\n                </div>\n\n                {/* Post Media (if any) */}\n                {post.media_urls && post.media_urls.length > 0 && (\n                  <div style={{ backgroundColor: '#f0f2f5', height: '250px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#65676b', fontSize: '3rem' }}>\n                    📷\n                  </div>\n                )}\n\n                {/* Post Actions */}\n                <div style={{ padding: '0.75rem 1rem' }}>\n                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>\n                    <span style={{ color: '#65676b', fontSize: '0.875rem' }}>\n                      👍 {post.likes} likes • 💬 {post.comments} comments\n                    </span>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>\n                    <button\n                      onClick={() => likePost(post.id)}\n                      style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        backgroundColor: 'transparent',\n                        border: 'none',\n                        padding: '0.5rem 1rem',\n                        borderRadius: '6px',\n                        cursor: 'pointer',\n                        color: post.liked_by_user ? '#1877f2' : '#65676b',\n                        fontWeight: '500'\n                      }}\n                    >\n                      👍 Like\n                    </button>\n                    <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                      💬 Comment\n                    </button>\n                    <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>\n                      📤 Share\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )\n          })}\n\n          {/* No Posts Message */}\n          {posts.length === 0 && (\n            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)', padding: '3rem', textAlign: 'center' }}>\n              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📝</div>\n              <h3 style={{ color: '#1c1e21', marginBottom: '0.5rem' }}>No posts yet</h3>\n              <p style={{ color: '#65676b' }}>Be the first to share something with your professional network!</p>\n            </div>\n          )}\n        </div>\n\n        {/* Right Sidebar */}\n        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>\n            Online Now\n          </h3>\n          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>\n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ position: 'relative' }}>\n                <div style={{ backgroundColor: '#7c3aed', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>\n                  M\n                </div>\n                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>\n              </div>\n              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Modern Tech Solutions</span>\n            </div>\n            \n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ position: 'relative' }}>\n                <div style={{ backgroundColor: '#2563eb', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>\n                  F\n                </div>\n                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>\n              </div>\n              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Fatima Al-Zahra</span>\n            </div>\n            \n            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{ position: 'relative' }}>\n                <div style={{ backgroundColor: '#059669', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>\n                  K\n                </div>\n                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>\n              </div>\n              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Khalid Construction</span>\n            </div>\n          </div>\n          \n          <div style={{ marginTop: '2rem', paddingTop: '1rem', borderTop: '1px solid #e4e6ea' }}>\n            <a href=\"/\" style={{ color: '#1877f2', textDecoration: 'none', fontSize: '0.875rem' }}>← Back to Home</a>\n          </div>\n        </div>\n      </div>\n\n      {/* Create Post Modal */}\n      {showCreatePost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(255, 255, 255, 0.8)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000\n        }}>\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '8px',\n            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n            width: '500px',\n            maxHeight: '90vh',\n            overflow: 'auto'\n          }}>\n            {/* Modal Header */}\n            <div style={{\n              padding: '1rem',\n              borderBottom: '1px solid #e4e6ea',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1c1e21', margin: 0 }}>\n                Create Post\n              </h2>\n              <button\n                onClick={() => setShowCreatePost(false)}\n                style={{\n                  backgroundColor: '#f0f2f5',\n                  border: 'none',\n                  borderRadius: '50%',\n                  width: '2.5rem',\n                  height: '2.5rem',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  fontSize: '1.25rem',\n                  color: '#65676b'\n                }}\n              >\n                ✕\n              </button>\n            </div>\n\n            {/* User Info */}\n            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>\n              <div style={{\n                backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                borderRadius: '50%',\n                width: '2.5rem',\n                height: '2.5rem',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                color: 'white',\n                fontWeight: 'bold'\n              }}>\n                {getUserInitial()}\n              </div>\n              <div>\n                <div style={{ fontWeight: '600', color: '#1c1e21' }}>{getUserDisplayName()}</div>\n                <div style={{ fontSize: '0.875rem', color: '#65676b', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                  {getUserRoleEmoji()} {userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User'} • 🌍 Public\n                </div>\n              </div>\n            </div>\n\n            {/* Post Content */}\n            <div style={{ padding: '0 1rem' }}>\n              <textarea\n                value={postText}\n                onChange={(e) => setPostText(e.target.value)}\n                placeholder=\"What's on your mind?\"\n                style={{\n                  width: '100%',\n                  minHeight: '120px',\n                  border: 'none',\n                  outline: 'none',\n                  fontSize: '1.5rem',\n                  color: '#1c1e21',\n                  resize: 'none',\n                  fontFamily: 'inherit'\n                }}\n              />\n            </div>\n\n            {/* Feeling/Activity */}\n            {selectedFeeling && (\n              <div style={{ padding: '0 1rem', marginBottom: '1rem' }}>\n                <div style={{\n                  backgroundColor: '#f0f2f5',\n                  borderRadius: '20px',\n                  padding: '0.5rem 1rem',\n                  display: 'inline-flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <span>{selectedFeeling}</span>\n                  <button\n                    onClick={() => setSelectedFeeling('')}\n                    style={{\n                      backgroundColor: 'transparent',\n                      border: 'none',\n                      cursor: 'pointer',\n                      color: '#65676b'\n                    }}\n                  >\n                    ✕\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Image Preview */}\n            {selectedImage && (\n              <div style={{ padding: '0 1rem', marginBottom: '1rem' }}>\n                <div style={{\n                  position: 'relative',\n                  backgroundColor: '#f0f2f5',\n                  borderRadius: '8px',\n                  padding: '2rem',\n                  textAlign: 'center'\n                }}>\n                  <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📷</div>\n                  <div style={{ color: '#65676b' }}>Image Preview</div>\n                  <button\n                    onClick={() => setSelectedImage(null)}\n                    style={{\n                      position: 'absolute',\n                      top: '0.5rem',\n                      right: '0.5rem',\n                      backgroundColor: 'rgba(0,0,0,0.5)',\n                      color: 'white',\n                      border: 'none',\n                      borderRadius: '50%',\n                      width: '2rem',\n                      height: '2rem',\n                      cursor: 'pointer'\n                    }}\n                  >\n                    ✕\n                  </button>\n                </div>\n              </div>\n            )}\n\n            {/* Add to Post */}\n            <div style={{ padding: '1rem', borderTop: '1px solid #e4e6ea' }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n                <span style={{ fontWeight: '600', color: '#1c1e21' }}>Add to your post</span>\n                <div style={{ display: 'flex', gap: '0.5rem' }}>\n                  <button\n                    onClick={() => setSelectedImage('image')}\n                    style={{\n                      backgroundColor: 'transparent',\n                      border: 'none',\n                      borderRadius: '50%',\n                      width: '2.5rem',\n                      height: '2.5rem',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.25rem'\n                    }}\n                    title=\"Photo/Video\"\n                  >\n                    📷\n                  </button>\n                  <button\n                    onClick={() => setSelectedFeeling('😊 feeling happy')}\n                    style={{\n                      backgroundColor: 'transparent',\n                      border: 'none',\n                      borderRadius: '50%',\n                      width: '2.5rem',\n                      height: '2.5rem',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.25rem'\n                    }}\n                    title=\"Feeling/Activity\"\n                  >\n                    😊\n                  </button>\n                  <button\n                    style={{\n                      backgroundColor: 'transparent',\n                      border: 'none',\n                      borderRadius: '50%',\n                      width: '2.5rem',\n                      height: '2.5rem',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.25rem'\n                    }}\n                    title=\"Tag People\"\n                  >\n                    👥\n                  </button>\n                  <button\n                    style={{\n                      backgroundColor: 'transparent',\n                      border: 'none',\n                      borderRadius: '50%',\n                      width: '2.5rem',\n                      height: '2.5rem',\n                      cursor: 'pointer',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      fontSize: '1.25rem'\n                    }}\n                    title=\"Check In\"\n                  >\n                    📍\n                  </button>\n                </div>\n              </div>\n\n              {/* Post Button */}\n              <button\n                onClick={() => {\n                  if (!postText.trim()) return\n\n                  // Create the post using the posts context\n                  const newPost = {\n                    user_id: user?.email || '',\n                    user_name: getUserDisplayName(),\n                    user_role: (userRole || 'worker') as 'worker' | 'supplier' | 'company',\n                    content: postText,\n                    media_urls: selectedImage ? [selectedImage] : undefined,\n                    post_type: (selectedImage ? 'image' : 'text') as 'text' | 'image' | 'video' | 'live' | 'document' | 'mixed',\n                    visibility: 'public' as 'public' | 'block_only'\n                  }\n\n                  addPost(newPost)\n\n                  // Reset form\n                  setPostText('')\n                  setSelectedFeeling('')\n                  setSelectedImage(null)\n                  setShowCreatePost(false)\n\n                  // Show success message\n                  alert('Post created successfully!')\n                }}\n                disabled={!postText.trim()}\n                style={{\n                  width: '100%',\n                  backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                  color: postText.trim() ? 'white' : '#bcc0c4',\n                  border: 'none',\n                  borderRadius: '6px',\n                  padding: '0.75rem',\n                  fontSize: '1rem',\n                  fontWeight: '600',\n                  cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                }}\n              >\n                Post\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAC5C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAEpD,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,QAAQ,UAAU;gBACpB,MAAM,UAAU;gBAChB,eAAe;gBACf,QAAQ,GAAG,CAAC,wBAAwB;YACtC;QACF;6BAAG;QAAC;QAAM;QAAU;KAAe;IAEnC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;6BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,6CAA6C;IAC7C,MAAM,qBAAqB;QACzB,IAAI,CAAC,aAAa,OAAO;QAEzB,IAAI,aAAa,UAAU;YACzB,OAAO,YAAY,YAAY,EAAE,cAAc;QACjD,OAAO,IAAI,aAAa,YAAY;YAClC,OAAO,YAAY,YAAY,EAAE,gBAAgB;QACnD,OAAO,IAAI,aAAa,WAAW;YACjC,OAAO,YAAY,WAAW,EAAE,eAAe;QACjD;QACA,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,iBAAiB;QACrB,MAAM,OAAO;QACb,OAAO,KAAK,MAAM,CAAC,GAAG,WAAW;IACnC;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,uCAAuC;IACvC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,OAAO;gBAAE,WAAW;gBAAS,SAAS;gBAAQ,YAAY;gBAAU,gBAAgB;gBAAU,iBAAiB;YAAU;sBAC5H,cAAA,6LAAC;gBAAI,OAAO;oBAAE,WAAW;gBAAS;;kCAChC,6LAAC;wBAAI,OAAO;4BAAE,UAAU;4BAAQ,cAAc;wBAAO;kCAAG;;;;;;kCACxD,6LAAC;wBAAI,OAAO;4BAAE,OAAO;wBAAU;kCAAG;;;;;;;;;;;;;;;;;IAI1C;IAEA,gCAAgC;IAChC,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IACA,qBACE,6LAAC;QAAI,OAAO;YAAE,WAAW;YAAS,iBAAiB;QAAU;;0BAE3D,6LAAC;gBAAI,OAAO;oBAAE,iBAAiB;oBAAW,SAAS;oBAAgB,WAAW;gBAA4B;0BACxG,cAAA,6LAAC;oBAAI,OAAO;wBAAE,UAAU;wBAAU,QAAQ;wBAAU,SAAS;wBAAQ,gBAAgB;wBAAiB,YAAY;oBAAS;;sCACzH,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAO;;8CAC/D,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAU,YAAY;wCAAQ,OAAO;wCAAS,QAAQ;oCAAE;8CAAG;;;;;;8CAGlF,6LAAC;oCAAI,OAAO;wCAAE,iBAAiB;wCAAyB,cAAc;wCAAQ,SAAS;oCAAc;8CACnG,cAAA,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;4CACL,iBAAiB;4CACjB,QAAQ;4CACR,OAAO;4CACP,SAAS;4CACT,UAAU;wCACZ;;;;;;;;;;;;;;;;;sCAKN,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,KAAK;gCAAQ,YAAY;4BAAS;;8CAC/D,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,YAAY;wCAAU,KAAK;wCAAW,OAAO;oCAAQ;;sDAClF,6LAAC;4CAAI,OAAO;gDACV,iBAAiB;gDACjB,cAAc;gDACd,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,YAAY;gDACZ,gBAAgB;gDAChB,UAAU;gDACV,YAAY;4CACd;sDACG;;;;;;sDAEH,6LAAC;4CAAK,OAAO;gDAAE,UAAU;gDAAY,YAAY;4CAAM;sDACpD;;;;;;;;;;;;8CAIL,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,KAAK;oCAAU;;sDAC5C,6LAAC;4CACC,SAAS;gDACP,MAAM,cAAc,aAAa,WAAW,qBAC1B,aAAa,aAAa,uBAC1B;gDAClB,OAAO,IAAI,CAAC;4CACd;4CACA,OAAO;gDACL,iBAAiB;gDACjB,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,QAAQ;4CACV;sDACD;;;;;;sDAID,6LAAC;4CACC,SAAS;gDACP,MAAM;gDACN,OAAO,IAAI,CAAC;4CACd;4CACA,OAAO;gDACL,iBAAiB;gDACjB,OAAO;gDACP,QAAQ;gDACR,SAAS;gDACT,cAAc;gDACd,UAAU;gDACV,YAAY;gDACZ,QAAQ;4CACV;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,OAAO;oBAAE,UAAU;oBAAU,QAAQ;oBAAU,SAAS;oBAAQ,qBAAqB;oBAAe,KAAK;oBAAQ,SAAS;gBAAO;;kCAGpI,6LAAC;wBAAI,OAAO;4BAAE,iBAAiB;4BAAS,cAAc;4BAAO,SAAS;4BAAQ,QAAQ;4BAAe,WAAW;wBAA4B;;0CAC1I,6LAAC;gCAAG,OAAO;oCAAE,UAAU;oCAAY,YAAY;oCAAO,OAAO;oCAAW,cAAc;gCAAO;0CAAG;;;;;;0CAGhG,6LAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAU;;kDACrE,6LAAC;wCAAE,MAAK;wCAAW,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DAClK,6LAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;kDAGtC,6LAAC;wCAAE,MAAK;wCAAa,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DACpK,6LAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;kDAGtC,6LAAC;wCAAE,MAAK;wCAAa,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DACpK,6LAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;kDAGtC,6LAAC;wCAAE,MAAK;wCAAS,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;4CAAW,gBAAgB;4CAAQ,OAAO;4CAAW,SAAS;4CAAU,cAAc;wCAAM;;0DAChK,6LAAC;gDAAI,OAAO;oDAAE,iBAAiB;oDAAW,cAAc;oDAAO,OAAO;oDAAU,QAAQ;oDAAU,SAAS;oDAAQ,YAAY;oDAAU,gBAAgB;gDAAS;0DAAG;;;;;;0DAGrK,6LAAC;gDAAK,OAAO;oDAAE,YAAY;gDAAM;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,OAAO;4BAAE,SAAS;4BAAQ,eAAe;4BAAU,KAAK;wBAAO;;0CAGlE,6LAAC;gCAAI,OAAO;oCAAE,iBAAiB;oCAAS,cAAc;oCAAO,SAAS;oCAAQ,WAAW;gCAA4B;;kDACnH,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,KAAK;4CAAW,cAAc;wCAAO;;0DAClE,6LAAC;gDAAI,OAAO;oDACV,iBAAiB,aAAa,WAAW,YAAY,aAAa,aAAa,YAAY;oDAC3F,cAAc;oDACd,OAAO;oDACP,QAAQ;oDACR,SAAS;oDACT,YAAY;oDACZ,gBAAgB;oDAChB,OAAO;oDACP,YAAY;gDACd;0DACG;;;;;;0DAEH,6LAAC;gDACC,MAAK;gDACL,aAAa,CAAC,qBAAqB,EAAE,qBAAqB,CAAC,CAAC;gDAC5D,SAAS,IAAM,kBAAkB;gDACjC,QAAQ;gDACR,OAAO;oDACL,MAAM;oDACN,iBAAiB;oDACjB,QAAQ;oDACR,cAAc;oDACd,SAAS;oDACT,SAAS;oDACT,UAAU;oDACV,QAAQ;gDACV;;;;;;;;;;;;kDAGJ,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,gBAAgB;4CAAgB,YAAY;4CAAW,WAAW;wCAAoB;;0DACnH,6LAAC;gDACC,SAAS,IAAM,kBAAkB;gDACjC,OAAO;oDAAE,SAAS;oDAAQ,YAAY;oDAAU,KAAK;oDAAU,iBAAiB;oDAAe,QAAQ;oDAAQ,SAAS;oDAAe,cAAc;oDAAO,QAAQ;oDAAW,OAAO;gDAAU;0DACjM;;;;;;0DAGD,6LAAC;gDACC,SAAS,IAAM,kBAAkB;gDACjC,OAAO;oDAAE,SAAS;oDAAQ,YAAY;oDAAU,KAAK;oDAAU,iBAAiB;oDAAe,QAAQ;oDAAQ,SAAS;oDAAe,cAAc;oDAAO,QAAQ;oDAAW,OAAO;gDAAU;0DACjM;;;;;;;;;;;;;;;;;;4BAOJ,MAAM,GAAG,CAAC,CAAC;gCACV,oCAAoC;gCACpC,MAAM,eAAe,CAAC;oCACpB,OAAQ;wCACN,KAAK;4CAAU,OAAO;wCACtB,KAAK;4CAAY,OAAO;wCACxB,KAAK;4CAAW,OAAO;wCACvB;4CAAS,OAAO;oCAClB;gCACF;gCAEA,oCAAoC;gCACpC,MAAM,eAAe,CAAC;oCACpB,OAAQ;wCACN,KAAK;4CAAU,OAAO;wCACtB,KAAK;4CAAY,OAAO;wCACxB,KAAK;4CAAW,OAAO;wCACvB;4CAAS,OAAO;oCAClB;gCACF;gCAEA,iCAAiC;gCACjC,MAAM,aAAa,CAAC;oCAClB,MAAM,MAAM,IAAI;oCAChB,MAAM,WAAW,IAAI,KAAK;oCAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;oCAElF,IAAI,gBAAgB,GAAG,OAAO;oCAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,YAAY,CAAC;oCAE7D,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;oCAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,UAAU,CAAC;oCAEvD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;oCAC5C,OAAO,GAAG,WAAW,SAAS,CAAC;gCACjC;gCAEA,qBACE,6LAAC;oCAAkB,OAAO;wCAAE,iBAAiB;wCAAS,cAAc;wCAAO,WAAW;oCAA4B;;sDAEhH,6LAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,SAAS;gDAAQ,YAAY;gDAAU,KAAK;4CAAU;;8DACnF,6LAAC;oDAAI,OAAO;wDACV,iBAAiB,aAAa,KAAK,SAAS;wDAC5C,cAAc;wDACd,OAAO;wDACP,QAAQ;wDACR,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,OAAO;wDACP,YAAY;oDACd;8DACG,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;8DAEvC,6LAAC;;sEACC,6LAAC;4DACC,SAAS;gEACP,iDAAiD;gEACjD,MAAM,cAAc,KAAK,SAAS,KAAK,WAAW,qBAChC,KAAK,SAAS,KAAK,aAAa,uBAChC;gEAClB,OAAO,IAAI,CAAC,GAAG,YAAY,MAAM,EAAE,mBAAmB,KAAK,OAAO,GAAG;4DACvE;4DACA,OAAO;gEACL,YAAY;gEACZ,QAAQ;gEACR,SAAS;gEACT,YAAY;gEACZ,OAAO;gEACP,QAAQ;gEACR,UAAU;gEACV,WAAW;4DACb;4DACA,cAAc,CAAC,IAAM,AAAC,EAAE,MAAM,CAAiB,KAAK,CAAC,cAAc,GAAG;4DACtE,cAAc,CAAC,IAAM,AAAC,EAAE,MAAM,CAAiB,KAAK,CAAC,cAAc,GAAG;sEAErE,KAAK,SAAS;;;;;;sEAEjB,6LAAC;4DAAI,OAAO;gEAAE,UAAU;gEAAU,OAAO;4DAAU;;gEAChD,WAAW,KAAK,UAAU;gEAAE;gEAAI,aAAa,KAAK,SAAS;gEAAE;gEAAE,KAAK,SAAS,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,SAAS,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;sDAMpI,6LAAC;4CAAI,OAAO;gDAAE,aAAa;gDAAQ,cAAc;gDAAQ,cAAc;4CAAO;sDAC5E,cAAA,6LAAC;gDAAE,OAAO;oDAAE,OAAO;oDAAW,YAAY;oDAAO,QAAQ;gDAAE;0DACxD,KAAK,OAAO;;;;;;;;;;;wCAKhB,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,GAAG,mBAC3C,6LAAC;4CAAI,OAAO;gDAAE,iBAAiB;gDAAW,QAAQ;gDAAS,SAAS;gDAAQ,YAAY;gDAAU,gBAAgB;gDAAU,OAAO;gDAAW,UAAU;4CAAO;sDAAG;;;;;;sDAMpK,6LAAC;4CAAI,OAAO;gDAAE,SAAS;4CAAe;;8DACpC,6LAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,gBAAgB;wDAAiB,YAAY;wDAAU,cAAc;oDAAU;8DAC5G,cAAA,6LAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAW,UAAU;wDAAW;;4DAAG;4DACnD,KAAK,KAAK;4DAAC;4DAAa,KAAK,QAAQ;4DAAC;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,OAAO;wDAAE,SAAS;wDAAQ,gBAAgB;wDAAgB,YAAY;wDAAW,WAAW;oDAAoB;;sEACnH,6LAAC;4DACC,SAAS,IAAM,SAAS,KAAK,EAAE;4DAC/B,OAAO;gEACL,SAAS;gEACT,YAAY;gEACZ,KAAK;gEACL,iBAAiB;gEACjB,QAAQ;gEACR,SAAS;gEACT,cAAc;gEACd,QAAQ;gEACR,OAAO,KAAK,aAAa,GAAG,YAAY;gEACxC,YAAY;4DACd;sEACD;;;;;;sEAGD,6LAAC;4DAAO,OAAO;gEAAE,SAAS;gEAAQ,YAAY;gEAAU,KAAK;gEAAU,iBAAiB;gEAAe,QAAQ;gEAAQ,SAAS;gEAAe,cAAc;gEAAO,QAAQ;gEAAW,OAAO;gEAAW,YAAY;4DAAM;sEAAG;;;;;;sEAG9N,6LAAC;4DAAO,OAAO;gEAAE,SAAS;gEAAQ,YAAY;gEAAU,KAAK;gEAAU,iBAAiB;gEAAe,QAAQ;gEAAQ,SAAS;gEAAe,cAAc;gEAAO,QAAQ;gEAAW,OAAO;gEAAW,YAAY;4DAAM;sEAAG;;;;;;;;;;;;;;;;;;;mCAxF1N,KAAK,EAAE;;;;;4BA+FrB;4BAGC,MAAM,MAAM,KAAK,mBAChB,6LAAC;gCAAI,OAAO;oCAAE,iBAAiB;oCAAS,cAAc;oCAAO,WAAW;oCAA6B,SAAS;oCAAQ,WAAW;gCAAS;;kDACxI,6LAAC;wCAAI,OAAO;4CAAE,UAAU;4CAAQ,cAAc;wCAAO;kDAAG;;;;;;kDACxD,6LAAC;wCAAG,OAAO;4CAAE,OAAO;4CAAW,cAAc;wCAAS;kDAAG;;;;;;kDACzD,6LAAC;wCAAE,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAI,OAAO;4BAAE,iBAAiB;4BAAS,cAAc;4BAAO,SAAS;4BAAQ,QAAQ;4BAAe,WAAW;wBAA4B;;0CAC1I,6LAAC;gCAAG,OAAO;oCAAE,UAAU;oCAAY,YAAY;oCAAO,OAAO;oCAAW,cAAc;gCAAO;0CAAG;;;;;;0CAGhG,6LAAC;gCAAI,OAAO;oCAAE,SAAS;oCAAQ,eAAe;oCAAU,KAAK;gCAAU;;kDACrE,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DAClE,6LAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,6LAAC;wDAAI,OAAO;4DAAE,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAQ,QAAQ;4DAAQ,SAAS;4DAAQ,YAAY;4DAAU,gBAAgB;4DAAU,OAAO;4DAAS,UAAU;4DAAY,YAAY;wDAAO;kEAAG;;;;;;kEAG3N,6LAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAY,QAAQ;4DAAQ,OAAO;4DAAQ,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAW,QAAQ;4DAAW,QAAQ;wDAAkB;;;;;;;;;;;;0DAErL,6LAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAG3D,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DAClE,6LAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,6LAAC;wDAAI,OAAO;4DAAE,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAQ,QAAQ;4DAAQ,SAAS;4DAAQ,YAAY;4DAAU,gBAAgB;4DAAU,OAAO;4DAAS,UAAU;4DAAY,YAAY;wDAAO;kEAAG;;;;;;kEAG3N,6LAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAY,QAAQ;4DAAQ,OAAO;4DAAQ,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAW,QAAQ;4DAAW,QAAQ;wDAAkB;;;;;;;;;;;;0DAErL,6LAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAG3D,6LAAC;wCAAI,OAAO;4CAAE,SAAS;4CAAQ,YAAY;4CAAU,KAAK;wCAAU;;0DAClE,6LAAC;gDAAI,OAAO;oDAAE,UAAU;gDAAW;;kEACjC,6LAAC;wDAAI,OAAO;4DAAE,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAQ,QAAQ;4DAAQ,SAAS;4DAAQ,YAAY;4DAAU,gBAAgB;4DAAU,OAAO;4DAAS,UAAU;4DAAY,YAAY;wDAAO;kEAAG;;;;;;kEAG3N,6LAAC;wDAAI,OAAO;4DAAE,UAAU;4DAAY,QAAQ;4DAAQ,OAAO;4DAAQ,iBAAiB;4DAAW,cAAc;4DAAO,OAAO;4DAAW,QAAQ;4DAAW,QAAQ;wDAAkB;;;;;;;;;;;;0DAErL,6LAAC;gDAAK,OAAO;oDAAE,UAAU;oDAAY,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;0CAI7D,6LAAC;gCAAI,OAAO;oCAAE,WAAW;oCAAQ,YAAY;oCAAQ,WAAW;gCAAoB;0CAClF,cAAA,6LAAC;oCAAE,MAAK;oCAAI,OAAO;wCAAE,OAAO;wCAAW,gBAAgB;wCAAQ,UAAU;oCAAW;8CAAG;;;;;;;;;;;;;;;;;;;;;;;YAM5F,gCACC,6LAAC;gBAAI,OAAO;oBACV,UAAU;oBACV,KAAK;oBACL,MAAM;oBACN,OAAO;oBACP,QAAQ;oBACR,iBAAiB;oBACjB,SAAS;oBACT,YAAY;oBACZ,gBAAgB;oBAChB,QAAQ;gBACV;0BACE,cAAA,6LAAC;oBAAI,OAAO;wBACV,iBAAiB;wBACjB,cAAc;wBACd,WAAW;wBACX,OAAO;wBACP,WAAW;wBACX,UAAU;oBACZ;;sCAEE,6LAAC;4BAAI,OAAO;gCACV,SAAS;gCACT,cAAc;gCACd,SAAS;gCACT,gBAAgB;gCAChB,YAAY;4BACd;;8CACE,6LAAC;oCAAG,OAAO;wCAAE,UAAU;wCAAW,YAAY;wCAAO,OAAO;wCAAW,QAAQ;oCAAE;8CAAG;;;;;;8CAGpF,6LAAC;oCACC,SAAS,IAAM,kBAAkB;oCACjC,OAAO;wCACL,iBAAiB;wCACjB,QAAQ;wCACR,cAAc;wCACd,OAAO;wCACP,QAAQ;wCACR,QAAQ;wCACR,SAAS;wCACT,YAAY;wCACZ,gBAAgB;wCAChB,UAAU;wCACV,OAAO;oCACT;8CACD;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,SAAS;gCAAQ,YAAY;gCAAU,KAAK;4BAAU;;8CACnF,6LAAC;oCAAI,OAAO;wCACV,iBAAiB,aAAa,WAAW,YAAY,aAAa,aAAa,YAAY;wCAC3F,cAAc;wCACd,OAAO;wCACP,QAAQ;wCACR,SAAS;wCACT,YAAY;wCACZ,gBAAgB;wCAChB,OAAO;wCACP,YAAY;oCACd;8CACG;;;;;;8CAEH,6LAAC;;sDACC,6LAAC;4CAAI,OAAO;gDAAE,YAAY;gDAAO,OAAO;4CAAU;sDAAI;;;;;;sDACtD,6LAAC;4CAAI,OAAO;gDAAE,UAAU;gDAAY,OAAO;gDAAW,SAAS;gDAAQ,YAAY;gDAAU,KAAK;4CAAU;;gDACzG;gDAAmB;gDAAE,WAAW,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC,KAAK;gDAAO;;;;;;;;;;;;;;;;;;;sCAMrG,6LAAC;4BAAI,OAAO;gCAAE,SAAS;4BAAS;sCAC9B,cAAA,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,OAAO;oCACL,OAAO;oCACP,WAAW;oCACX,QAAQ;oCACR,SAAS;oCACT,UAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,YAAY;gCACd;;;;;;;;;;;wBAKH,iCACC,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAU,cAAc;4BAAO;sCACpD,cAAA,6LAAC;gCAAI,OAAO;oCACV,iBAAiB;oCACjB,cAAc;oCACd,SAAS;oCACT,SAAS;oCACT,YAAY;oCACZ,KAAK;gCACP;;kDACE,6LAAC;kDAAM;;;;;;kDACP,6LAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,OAAO;4CACL,iBAAiB;4CACjB,QAAQ;4CACR,QAAQ;4CACR,OAAO;wCACT;kDACD;;;;;;;;;;;;;;;;;wBAQN,+BACC,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAU,cAAc;4BAAO;sCACpD,cAAA,6LAAC;gCAAI,OAAO;oCACV,UAAU;oCACV,iBAAiB;oCACjB,cAAc;oCACd,SAAS;oCACT,WAAW;gCACb;;kDACE,6LAAC;wCAAI,OAAO;4CAAE,UAAU;4CAAQ,cAAc;wCAAO;kDAAG;;;;;;kDACxD,6LAAC;wCAAI,OAAO;4CAAE,OAAO;wCAAU;kDAAG;;;;;;kDAClC,6LAAC;wCACC,SAAS,IAAM,iBAAiB;wCAChC,OAAO;4CACL,UAAU;4CACV,KAAK;4CACL,OAAO;4CACP,iBAAiB;4CACjB,OAAO;4CACP,QAAQ;4CACR,cAAc;4CACd,OAAO;4CACP,QAAQ;4CACR,QAAQ;wCACV;kDACD;;;;;;;;;;;;;;;;;sCAQP,6LAAC;4BAAI,OAAO;gCAAE,SAAS;gCAAQ,WAAW;4BAAoB;;8CAC5D,6LAAC;oCAAI,OAAO;wCAAE,SAAS;wCAAQ,gBAAgB;wCAAiB,YAAY;wCAAU,cAAc;oCAAO;;sDACzG,6LAAC;4CAAK,OAAO;gDAAE,YAAY;gDAAO,OAAO;4CAAU;sDAAG;;;;;;sDACtD,6LAAC;4CAAI,OAAO;gDAAE,SAAS;gDAAQ,KAAK;4CAAS;;8DAC3C,6LAAC;oDACC,SAAS,IAAM,iBAAiB;oDAChC,OAAO;wDACL,iBAAiB;wDACjB,QAAQ;wDACR,cAAc;wDACd,OAAO;wDACP,QAAQ;wDACR,QAAQ;wDACR,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,UAAU;oDACZ;oDACA,OAAM;8DACP;;;;;;8DAGD,6LAAC;oDACC,SAAS,IAAM,mBAAmB;oDAClC,OAAO;wDACL,iBAAiB;wDACjB,QAAQ;wDACR,cAAc;wDACd,OAAO;wDACP,QAAQ;wDACR,QAAQ;wDACR,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,UAAU;oDACZ;oDACA,OAAM;8DACP;;;;;;8DAGD,6LAAC;oDACC,OAAO;wDACL,iBAAiB;wDACjB,QAAQ;wDACR,cAAc;wDACd,OAAO;wDACP,QAAQ;wDACR,QAAQ;wDACR,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,UAAU;oDACZ;oDACA,OAAM;8DACP;;;;;;8DAGD,6LAAC;oDACC,OAAO;wDACL,iBAAiB;wDACjB,QAAQ;wDACR,cAAc;wDACd,OAAO;wDACP,QAAQ;wDACR,QAAQ;wDACR,SAAS;wDACT,YAAY;wDACZ,gBAAgB;wDAChB,UAAU;oDACZ;oDACA,OAAM;8DACP;;;;;;;;;;;;;;;;;;8CAOL,6LAAC;oCACC,SAAS;wCACP,IAAI,CAAC,SAAS,IAAI,IAAI;wCAEtB,0CAA0C;wCAC1C,MAAM,UAAU;4CACd,SAAS,MAAM,SAAS;4CACxB,WAAW;4CACX,WAAY,YAAY;4CACxB,SAAS;4CACT,YAAY,gBAAgB;gDAAC;6CAAc,GAAG;4CAC9C,WAAY,gBAAgB,UAAU;4CACtC,YAAY;wCACd;wCAEA,QAAQ;wCAER,aAAa;wCACb,YAAY;wCACZ,mBAAmB;wCACnB,iBAAiB;wCACjB,kBAAkB;wCAElB,uBAAuB;wCACvB,MAAM;oCACR;oCACA,UAAU,CAAC,SAAS,IAAI;oCACxB,OAAO;wCACL,OAAO;wCACP,iBAAiB,SAAS,IAAI,KAAK,YAAY;wCAC/C,OAAO,SAAS,IAAI,KAAK,UAAU;wCACnC,QAAQ;wCACR,cAAc;wCACd,SAAS;wCACT,UAAU;wCACV,YAAY;wCACZ,QAAQ,SAAS,IAAI,KAAK,YAAY;oCACxC;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAltBwB;;QACuC,kIAAA,CAAA,UAAO;QAC/B,mIAAA,CAAA,WAAQ;QAC9B,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}, {"offset": {"line": 1796, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}