{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect } from 'react'\n\nexport type UserRole = 'worker' | 'supplier' | 'company'\n\ninterface User {\n  id: string\n  email: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  userRole: UserRole | null\n  loading: boolean\n  signUp: (email: string, password: string, role: UserRole) => Promise<any>\n  signIn: (email: string, password: string) => Promise<any>\n  signOut: () => Promise<void>\n  getUserProfile: () => any | null\n  debugProfiles: () => void\n  refreshUserRole: () => void\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [userRole, setUserRole] = useState<UserRole | null>(null)\n  const [loading, setLoading] = useState(true) // Start with loading true\n  const [isInitialized, setIsInitialized] = useState(false)\n\n  // Load authentication state from localStorage on mount\n  useEffect(() => {\n    const loadAuthState = () => {\n      try {\n        const savedUser = localStorage.getItem('kaazmaamaa-user')\n        const savedRole = localStorage.getItem('kaazmaamaa-user-role')\n\n        if (savedUser && savedRole) {\n          const parsedUser = JSON.parse(savedUser)\n\n          // Re-detect the user's role to ensure it's correct (in case profiles were created after login)\n          const reDetectedRole = detectUserProfileType(parsedUser.email)\n          console.log('🔄 Re-detecting role for:', parsedUser.email, 'Saved:', savedRole, 'Re-detected:', reDetectedRole)\n\n          // Use re-detected role if it's different from saved role\n          const finalRole = reDetectedRole !== 'worker' ? reDetectedRole : (savedRole as UserRole)\n\n          setUser(parsedUser)\n          setUserRole(finalRole)\n\n          // Update localStorage if role changed\n          if (finalRole !== savedRole) {\n            console.log('🔄 Updating role in localStorage from', savedRole, 'to', finalRole)\n            localStorage.setItem('kaazmaamaa-user-role', finalRole)\n          }\n\n          console.log('✅ Loaded user from localStorage:', parsedUser.email, 'Final role:', finalRole)\n        } else {\n          console.log('No saved authentication found')\n        }\n      } catch (error) {\n        console.error('Error loading auth state from localStorage:', error)\n      } finally {\n        console.log('🔄 AuthContext: Setting loading to false')\n        setLoading(false)\n        setIsInitialized(true)\n        console.log('✅ AuthContext: Initialization complete')\n      }\n    }\n\n    loadAuthState()\n  }, [])\n\n  const signUp = async (email: string, password: string, role: UserRole) => {\n    // Mock implementation for now\n    console.log('Sign up:', { email, password, role })\n    const mockUser = { id: Date.now().toString(), email }\n\n    // Save to state\n    setUser(mockUser)\n    setUserRole(role)\n\n    // Save to localStorage\n    try {\n      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))\n      localStorage.setItem('kaazmaamaa-user-role', role)\n      console.log('User authentication saved to localStorage')\n    } catch (error) {\n      console.error('Error saving auth to localStorage:', error)\n    }\n\n    return { data: { user: mockUser }, error: null }\n  }\n\n  // Function to detect user's profile type by checking all profile contexts\n  const detectUserProfileType = (userEmail: string): UserRole => {\n    console.log('🔍 Detecting profile type for email:', userEmail)\n    console.log('🔍 All localStorage keys:', Object.keys(localStorage))\n\n    try {\n      // Check Workers profiles\n      const workersData = localStorage.getItem('kaazmaamaa-workers')\n      console.log('📋 Workers data:', workersData ? 'Found' : 'Not found')\n      if (workersData) {\n        const workers = JSON.parse(workersData)\n        console.log('👷 Checking', workers.length, 'worker profiles')\n        const workerProfile = workers.find((worker: any) => {\n          const emailMatch = worker.personalInfo?.email === userEmail\n          const userIdMatch = worker.userId === userEmail\n          console.log('👷 Worker check:', worker.personalInfo?.workerName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)\n          return emailMatch || userIdMatch\n        })\n        if (workerProfile) {\n          console.log('✅ Found worker profile for:', userEmail, workerProfile.personalInfo?.workerName)\n          return 'worker'\n        }\n      }\n\n      // Check Suppliers profiles\n      const suppliersData = localStorage.getItem('kaazmaamaa-suppliers')\n      console.log('📋 Suppliers data:', suppliersData ? 'Found' : 'Not found')\n      if (suppliersData) {\n        const suppliers = JSON.parse(suppliersData)\n        console.log('🏭 Checking', suppliers.length, 'supplier profiles')\n        const supplierProfile = suppliers.find((supplier: any) => {\n          const emailMatch = supplier.personalInfo?.email === userEmail\n          const userIdMatch = supplier.userId === userEmail\n          console.log('🏭 Supplier check:', supplier.personalInfo?.supplierName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)\n          console.log('🏭 Supplier data structure:', { personalInfo: supplier.personalInfo, userId: supplier.userId })\n          return emailMatch || userIdMatch\n        })\n        if (supplierProfile) {\n          console.log('✅ Found supplier profile for:', userEmail, supplierProfile.personalInfo?.supplierName)\n          return 'supplier'\n        }\n      }\n\n      // Check Companies profiles\n      const companiesData = localStorage.getItem('kaazmaamaa-companies')\n      console.log('📋 Companies data:', companiesData ? 'Found' : 'Not found')\n      if (companiesData) {\n        const companies = JSON.parse(companiesData)\n        console.log('🏢 Checking', companies.length, 'company profiles')\n        const companyProfile = companies.find((company: any) => {\n          const emailMatch = company.contactInfo?.hrEmail === userEmail\n          const userIdMatch = company.userId === userEmail\n          console.log('🏢 Company check:', company.companyInfo?.companyName, 'Email match:', emailMatch, 'UserId match:', userIdMatch)\n          return emailMatch || userIdMatch\n        })\n        if (companyProfile) {\n          console.log('✅ Found company profile for:', userEmail, companyProfile.companyInfo?.companyName)\n          return 'company'\n        }\n      }\n\n      console.log('❌ No profile found for:', userEmail, 'defaulting to worker')\n      return 'worker' // Default fallback\n    } catch (error) {\n      console.error('💥 Error detecting profile type:', error)\n      return 'worker' // Default fallback\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    // Mock implementation for now\n    console.log('Sign in:', { email, password })\n    // Use email as the consistent user ID\n    const mockUser = { id: email, email }\n\n    // Detect the user's actual profile type\n    const detectedRole = detectUserProfileType(email)\n    console.log('Detected user role:', detectedRole, 'for email:', email)\n\n    // Save to state\n    setUser(mockUser)\n    setUserRole(detectedRole)\n\n    // Save to localStorage\n    try {\n      localStorage.setItem('kaazmaamaa-user', JSON.stringify(mockUser))\n      localStorage.setItem('kaazmaamaa-user-role', detectedRole)\n      console.log('User authentication saved to localStorage with role:', detectedRole)\n    } catch (error) {\n      console.error('Error saving auth to localStorage:', error)\n    }\n\n    return { data: { user: mockUser }, error: null }\n  }\n\n  const signOut = async () => {\n    // Clear state\n    setUser(null)\n    setUserRole(null)\n\n    // Clear localStorage\n    try {\n      localStorage.removeItem('kaazmaamaa-user')\n      localStorage.removeItem('kaazmaamaa-user-role')\n      console.log('User authentication cleared from localStorage')\n    } catch (error) {\n      console.error('Error clearing auth from localStorage:', error)\n    }\n  }\n\n  const getUserProfile = () => {\n    if (!user || !userRole) return null\n\n    try {\n      if (userRole === 'worker') {\n        const workersData = localStorage.getItem('kaazmaamaa-workers')\n        if (workersData) {\n          const workers = JSON.parse(workersData)\n          return workers.find((worker: any) =>\n            worker.personalInfo?.email === user.email || worker.userId === user.email\n          )\n        }\n      } else if (userRole === 'supplier') {\n        const suppliersData = localStorage.getItem('kaazmaamaa-suppliers')\n        if (suppliersData) {\n          const suppliers = JSON.parse(suppliersData)\n          return suppliers.find((supplier: any) =>\n            supplier.personalInfo?.email === user.email ||\n            supplier.userId === user.email\n          )\n        }\n      } else if (userRole === 'company') {\n        const companiesData = localStorage.getItem('kaazmaamaa-companies')\n        if (companiesData) {\n          const companies = JSON.parse(companiesData)\n          return companies.find((company: any) =>\n            company.contactInfo?.hrEmail === user.email || company.userId === user.email\n          )\n        }\n      }\n    } catch (error) {\n      console.error('Error getting user profile:', error)\n    }\n\n    return null\n  }\n\n  const debugProfiles = () => {\n    console.log('🐛 DEBUG: All localStorage data:')\n    console.log('Workers:', localStorage.getItem('kaazmaamaa-workers'))\n    console.log('Suppliers:', localStorage.getItem('kaazmaamaa-suppliers'))\n    console.log('Companies:', localStorage.getItem('kaazmaamaa-companies'))\n\n    try {\n      const workers = JSON.parse(localStorage.getItem('kaazmaamaa-workers') || '[]')\n      const suppliers = JSON.parse(localStorage.getItem('kaazmaamaa-suppliers') || '[]')\n      const companies = JSON.parse(localStorage.getItem('kaazmaamaa-companies') || '[]')\n\n      console.log('🐛 Parsed Workers:', workers)\n      console.log('🐛 Parsed Suppliers:', suppliers)\n      console.log('🐛 Parsed Companies:', companies)\n    } catch (error) {\n      console.error('🐛 Error parsing localStorage data:', error)\n    }\n  }\n\n  const refreshUserRole = () => {\n    if (user?.email) {\n      console.log('🔄 Manually refreshing user role for:', user.email)\n      const newRole = detectUserProfileType(user.email)\n      console.log('🔄 New role detected:', newRole)\n\n      if (newRole !== userRole) {\n        setUserRole(newRole)\n        localStorage.setItem('kaazmaamaa-user-role', newRole)\n        console.log('✅ Role updated from', userRole, 'to', newRole)\n      } else {\n        console.log('✅ Role unchanged:', userRole)\n      }\n    }\n  }\n\n  const value = {\n    user,\n    userRole,\n    loading,\n    signUp,\n    signIn,\n    signOut,\n    getUserProfile,\n    debugProfiles,\n    refreshUserRole,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,0BAA0B;;IACvE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,MAAM,YAAY,aAAa,OAAO,CAAC;gBACvC,MAAM,YAAY,aAAa,OAAO,CAAC;gBAEvC,IAAI,aAAa,WAAW;oBAC1B,MAAM,aAAa,KAAK,KAAK,CAAC;oBAE9B,+FAA+F;oBAC/F,MAAM,iBAAiB,sBAAsB,WAAW,KAAK;oBAC7D,QAAQ,GAAG,CAAC,6BAA6B,WAAW,KAAK,EAAE,UAAU,WAAW,gBAAgB;oBAEhG,yDAAyD;oBACzD,MAAM,YAAY,mBAAmB,WAAW,iBAAkB;oBAElE,QAAQ;oBACR,YAAY;oBAEZ,sCAAsC;oBACtC,IAAI,cAAc,WAAW;wBAC3B,QAAQ,GAAG,CAAC,yCAAyC,WAAW,MAAM;wBACtE,aAAa,OAAO,CAAC,wBAAwB;oBAC/C;oBAEA,QAAQ,GAAG,CAAC,oCAAoC,WAAW,KAAK,EAAE,eAAe;gBACnF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;YAC/D,SAAU;gBACR,QAAQ,GAAG,CAAC;gBACZ,WAAW;gBACX,iBAAiB;gBACjB,QAAQ,GAAG,CAAC;YACd;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,YAAY;YAAE;YAAO;YAAU;QAAK;QAChD,MAAM,WAAW;YAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;YAAI;QAAM;QAEpD,gBAAgB;QAChB,QAAQ;QACR,YAAY;QAEZ,uBAAuB;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,aAAa,OAAO,CAAC,wBAAwB;YAC7C,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO;YAAE,MAAM;gBAAE,MAAM;YAAS;YAAG,OAAO;QAAK;IACjD;IAEA,0EAA0E;IAC1E,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,wCAAwC;QACpD,QAAQ,GAAG,CAAC,6BAA6B,OAAO,IAAI,CAAC;QAErD,IAAI;YACF,yBAAyB;YACzB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,QAAQ,GAAG,CAAC,oBAAoB,cAAc,UAAU;YACxD,IAAI,aAAa;gBACf,MAAM,UAAU,KAAK,KAAK,CAAC;gBAC3B,QAAQ,GAAG,CAAC,eAAe,QAAQ,MAAM,EAAE;gBAC3C,MAAM,gBAAgB,QAAQ,IAAI,CAAC,CAAC;oBAClC,MAAM,aAAa,OAAO,YAAY,EAAE,UAAU;oBAClD,MAAM,cAAc,OAAO,MAAM,KAAK;oBACtC,QAAQ,GAAG,CAAC,oBAAoB,OAAO,YAAY,EAAE,YAAY,gBAAgB,YAAY,iBAAiB;oBAC9G,OAAO,cAAc;gBACvB;gBACA,IAAI,eAAe;oBACjB,QAAQ,GAAG,CAAC,+BAA+B,WAAW,cAAc,YAAY,EAAE;oBAClF,OAAO;gBACT;YACF;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,QAAQ,GAAG,CAAC,sBAAsB,gBAAgB,UAAU;YAC5D,IAAI,eAAe;gBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,QAAQ,GAAG,CAAC,eAAe,UAAU,MAAM,EAAE;gBAC7C,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAC;oBACtC,MAAM,aAAa,SAAS,YAAY,EAAE,UAAU;oBACpD,MAAM,cAAc,SAAS,MAAM,KAAK;oBACxC,QAAQ,GAAG,CAAC,sBAAsB,SAAS,YAAY,EAAE,cAAc,gBAAgB,YAAY,iBAAiB;oBACpH,QAAQ,GAAG,CAAC,+BAA+B;wBAAE,cAAc,SAAS,YAAY;wBAAE,QAAQ,SAAS,MAAM;oBAAC;oBAC1G,OAAO,cAAc;gBACvB;gBACA,IAAI,iBAAiB;oBACnB,QAAQ,GAAG,CAAC,iCAAiC,WAAW,gBAAgB,YAAY,EAAE;oBACtF,OAAO;gBACT;YACF;YAEA,2BAA2B;YAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,QAAQ,GAAG,CAAC,sBAAsB,gBAAgB,UAAU;YAC5D,IAAI,eAAe;gBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;gBAC7B,QAAQ,GAAG,CAAC,eAAe,UAAU,MAAM,EAAE;gBAC7C,MAAM,iBAAiB,UAAU,IAAI,CAAC,CAAC;oBACrC,MAAM,aAAa,QAAQ,WAAW,EAAE,YAAY;oBACpD,MAAM,cAAc,QAAQ,MAAM,KAAK;oBACvC,QAAQ,GAAG,CAAC,qBAAqB,QAAQ,WAAW,EAAE,aAAa,gBAAgB,YAAY,iBAAiB;oBAChH,OAAO,cAAc;gBACvB;gBACA,IAAI,gBAAgB;oBAClB,QAAQ,GAAG,CAAC,gCAAgC,WAAW,eAAe,WAAW,EAAE;oBACnF,OAAO;gBACT;YACF;YAEA,QAAQ,GAAG,CAAC,2BAA2B,WAAW;YAClD,OAAO,SAAS,mBAAmB;;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,SAAS,mBAAmB;;QACrC;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,8BAA8B;QAC9B,QAAQ,GAAG,CAAC,YAAY;YAAE;YAAO;QAAS;QAC1C,sCAAsC;QACtC,MAAM,WAAW;YAAE,IAAI;YAAO;QAAM;QAEpC,wCAAwC;QACxC,MAAM,eAAe,sBAAsB;QAC3C,QAAQ,GAAG,CAAC,uBAAuB,cAAc,cAAc;QAE/D,gBAAgB;QAChB,QAAQ;QACR,YAAY;QAEZ,uBAAuB;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,aAAa,OAAO,CAAC,wBAAwB;YAC7C,QAAQ,GAAG,CAAC,wDAAwD;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;QACtD;QAEA,OAAO;YAAE,MAAM;gBAAE,MAAM;YAAS;YAAG,OAAO;QAAK;IACjD;IAEA,MAAM,UAAU;QACd,cAAc;QACd,QAAQ;QACR,YAAY;QAEZ,qBAAqB;QACrB,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,QAAQ,CAAC,UAAU,OAAO;QAE/B,IAAI;YACF,IAAI,aAAa,UAAU;gBACzB,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,OAAO,QAAQ,IAAI,CAAC,CAAC,SACnB,OAAO,YAAY,EAAE,UAAU,KAAK,KAAK,IAAI,OAAO,MAAM,KAAK,KAAK,KAAK;gBAE7E;YACF,OAAO,IAAI,aAAa,YAAY;gBAClC,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,OAAO,UAAU,IAAI,CAAC,CAAC,WACrB,SAAS,YAAY,EAAE,UAAU,KAAK,KAAK,IAC3C,SAAS,MAAM,KAAK,KAAK,KAAK;gBAElC;YACF,OAAO,IAAI,aAAa,WAAW;gBACjC,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,MAAM,YAAY,KAAK,KAAK,CAAC;oBAC7B,OAAO,UAAU,IAAI,CAAC,CAAC,UACrB,QAAQ,WAAW,EAAE,YAAY,KAAK,KAAK,IAAI,QAAQ,MAAM,KAAK,KAAK,KAAK;gBAEhF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,YAAY,aAAa,OAAO,CAAC;QAC7C,QAAQ,GAAG,CAAC,cAAc,aAAa,OAAO,CAAC;QAC/C,QAAQ,GAAG,CAAC,cAAc,aAAa,OAAO,CAAC;QAE/C,IAAI;YACF,MAAM,UAAU,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,yBAAyB;YACzE,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,2BAA2B;YAC7E,MAAM,YAAY,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,2BAA2B;YAE7E,QAAQ,GAAG,CAAC,sBAAsB;YAClC,QAAQ,GAAG,CAAC,wBAAwB;YACpC,QAAQ,GAAG,CAAC,wBAAwB;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,MAAM,OAAO;YACf,QAAQ,GAAG,CAAC,yCAAyC,KAAK,KAAK;YAC/D,MAAM,UAAU,sBAAsB,KAAK,KAAK;YAChD,QAAQ,GAAG,CAAC,yBAAyB;YAErC,IAAI,YAAY,UAAU;gBACxB,YAAY;gBACZ,aAAa,OAAO,CAAC,wBAAwB;gBAC7C,QAAQ,GAAG,CAAC,uBAAuB,UAAU,MAAM;YACrD,OAAO;gBACL,QAAQ,GAAG,CAAC,qBAAqB;YACnC;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/PostsContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface Comment {\n  id: string\n  post_id: string\n  user_id: string\n  user_name: string\n  user_role: 'worker' | 'supplier' | 'company'\n  content: string\n  created_at: string\n}\n\nexport interface Post {\n  id: string\n  user_id: string\n  user_name: string\n  user_role: 'worker' | 'supplier' | 'company'\n  content: string\n  media_urls?: string[]\n  post_type: 'text' | 'image' | 'video' | 'live' | 'document' | 'mixed'\n  visibility: 'public' | 'block_only'\n  created_at: string\n  expires_at: string // Posts expire after 1 month\n  likes: number\n  comments: number\n  liked_by_user?: boolean\n  post_comments?: Comment[]\n  shared_count?: number\n}\n\ninterface PostsContextType {\n  posts: Post[]\n  addPost: (post: Omit<Post, 'id' | 'created_at' | 'expires_at' | 'likes' | 'comments'>) => void\n  likePost: (postId: string) => void\n  sharePost: (postId: string) => void\n  deletePost: (postId: string) => void\n  addComment: (postId: string, content: string, user: { id: string, name: string, role: 'worker' | 'supplier' | 'company' }) => void\n  clearAllPosts: () => void\n  resetToInitialPosts: () => void\n  cleanupExpiredPosts: () => void\n  migratePostsToFullNames: () => void\n}\n\nconst PostsContext = createContext<PostsContextType | undefined>(undefined)\n\n// Helper function to calculate expiration date (1 month from creation)\nconst getExpirationDate = (createdAt: string): string => {\n  const created = new Date(createdAt)\n  const expiration = new Date(created)\n  expiration.setMonth(expiration.getMonth() + 1) // Add 1 month\n  return expiration.toISOString()\n}\n\n// Initial mock posts\nconst initialPosts: Post[] = [\n  {\n    id: '1',\n    user_id: 'mock-user-1',\n    user_name: 'Ahmed Al-Rashid',\n    user_role: 'worker',\n    content: 'Looking for construction work in Riyadh. I have 5 years of experience in electrical work. Available immediately! #ElectricalWork #Riyadh #Available',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),\n    likes: 12,\n    comments: 3,\n    shared_count: 2,\n    post_comments: [\n      {\n        id: 'comment-1',\n        post_id: '1',\n        user_id: 'commenter-1',\n        user_name: 'Sarah Construction',\n        user_role: 'company',\n        content: 'We have openings for electrical work. Please send your CV.',\n        created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n      },\n      {\n        id: 'comment-2',\n        post_id: '1',\n        user_id: 'commenter-2',\n        user_name: 'Ali Hassan',\n        user_role: 'worker',\n        content: 'Good luck brother! I also work in electrical.',\n        created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n      }\n    ]\n  },\n  {\n    id: '2',\n    user_id: 'mock-user-2',\n    user_name: 'Saudi Construction Co.',\n    user_role: 'company',\n    content: 'We are hiring 20 skilled workers for our new project in Jeddah. Competitive salary and accommodation provided. Contact us for details. #Hiring #Jeddah #Construction',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()),\n    likes: 28,\n    comments: 15\n  },\n  {\n    id: '3',\n    user_id: 'mock-user-3',\n    user_name: 'Gulf Manpower Solutions',\n    user_role: 'supplier',\n    content: 'New batch of certified welders available for immediate deployment. All workers have valid IQAMA and safety certifications. #Welders #Certified #Available',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()),\n    likes: 18,\n    comments: 8\n  },\n  {\n    id: '4',\n    user_id: 'mock-user-4',\n    user_name: 'Mohammed Hassan',\n    user_role: 'worker',\n    content: 'Just completed my safety certification course! Ready for new opportunities in the oil and gas sector. #SafetyCertified #OilAndGas #Ready',\n    post_type: 'text',\n    visibility: 'public',\n    created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(), // 8 hours ago\n    expires_at: getExpirationDate(new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()),\n    likes: 15,\n    comments: 5\n  }\n]\n\nexport function PostsProvider({ children }: { children: ReactNode }) {\n  const [posts, setPosts] = useState<Post[]>([])\n  const [isLoaded, setIsLoaded] = useState(false)\n\n  // Load posts from localStorage on component mount\n  useEffect(() => {\n    const loadPostsFromStorage = () => {\n      try {\n        const savedPosts = localStorage.getItem('kaazmaamaa-posts')\n        if (savedPosts) {\n          const parsedPosts = JSON.parse(savedPosts)\n          console.log('Loaded posts from localStorage:', parsedPosts.length)\n\n          // Filter out expired posts when loading\n          const now = new Date()\n          const activePosts = parsedPosts.filter((post: Post) => {\n            // Handle posts that might not have expires_at field (legacy posts)\n            if (!post.expires_at) {\n              // Add expires_at to legacy posts (1 month from creation)\n              post.expires_at = getExpirationDate(post.created_at)\n            }\n            const expirationDate = new Date(post.expires_at)\n            return expirationDate > now\n          })\n\n          const expiredCount = parsedPosts.length - activePosts.length\n          if (expiredCount > 0) {\n            console.log(`Filtered out ${expiredCount} expired posts on load`)\n            // Save cleaned posts back to localStorage\n            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts))\n          }\n\n          setPosts(activePosts)\n        } else {\n          // If no saved posts, use initial posts\n          console.log('No saved posts found, using initial posts')\n          setPosts(initialPosts)\n          localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts))\n        }\n      } catch (error) {\n        console.error('Error loading posts from localStorage:', error)\n        setPosts(initialPosts)\n      } finally {\n        setIsLoaded(true)\n      }\n    }\n\n    loadPostsFromStorage()\n  }, [])\n\n  // Save posts to localStorage whenever posts change\n  useEffect(() => {\n    if (isLoaded && posts.length > 0) {\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(posts))\n        console.log('Saved posts to localStorage:', posts.length)\n      } catch (error) {\n        console.error('Error saving posts to localStorage:', error)\n      }\n    }\n  }, [posts, isLoaded])\n\n  console.log('PostsProvider initialized with posts:', posts.length)\n\n  const addPost = (newPost: Omit<Post, 'id' | 'created_at' | 'expires_at' | 'likes' | 'comments'>) => {\n    const createdAt = new Date().toISOString()\n    const post: Post = {\n      ...newPost,\n      id: Date.now().toString(),\n      created_at: createdAt,\n      expires_at: getExpirationDate(createdAt), // Posts expire after 1 month\n      likes: 0,\n      comments: 0\n    }\n    console.log('Adding post to context:', post)\n    setPosts(prevPosts => {\n      const updatedPosts = [post, ...prevPosts]\n      console.log('Updated posts array:', updatedPosts)\n\n      // Immediately save to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Post immediately saved to localStorage')\n      } catch (error) {\n        console.error('Error saving post to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const likePost = (postId: string) => {\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.map(post =>\n        post.id === postId\n          ? {\n              ...post,\n              likes: post.liked_by_user ? post.likes - 1 : post.likes + 1,\n              liked_by_user: !post.liked_by_user\n            }\n          : post\n      )\n\n      // Save likes to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Like status saved to localStorage')\n      } catch (error) {\n        console.error('Error saving like to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const sharePost = (postId: string) => {\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.map(post =>\n        post.id === postId\n          ? {\n              ...post,\n              shared_count: (post.shared_count || 0) + 1\n            }\n          : post\n      )\n\n      // Save share count to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Share count saved to localStorage')\n      } catch (error) {\n        console.error('Error saving share to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const addComment = (postId: string, content: string, user: { id: string, name: string, role: 'worker' | 'supplier' | 'company' }) => {\n    const newComment: Comment = {\n      id: Date.now().toString(),\n      post_id: postId,\n      user_id: user.id,\n      user_name: user.name,\n      user_role: user.role,\n      content: content.trim(),\n      created_at: new Date().toISOString()\n    }\n\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.map(post =>\n        post.id === postId\n          ? {\n              ...post,\n              comments: post.comments + 1,\n              post_comments: [...(post.post_comments || []), newComment]\n            }\n          : post\n      )\n\n      // Save comments to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Comment saved to localStorage')\n      } catch (error) {\n        console.error('Error saving comment to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const deletePost = (postId: string) => {\n    setPosts(prevPosts => {\n      const updatedPosts = prevPosts.filter(post => post.id !== postId)\n\n      // Save deletion to localStorage\n      try {\n        localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts))\n        console.log('Post deletion saved to localStorage')\n      } catch (error) {\n        console.error('Error saving deletion to localStorage:', error)\n      }\n\n      return updatedPosts\n    })\n  }\n\n  const clearAllPosts = () => {\n    setPosts([])\n    try {\n      localStorage.removeItem('kaazmaamaa-posts')\n      console.log('All posts cleared from localStorage')\n    } catch (error) {\n      console.error('Error clearing posts from localStorage:', error)\n    }\n  }\n\n  const resetToInitialPosts = () => {\n    setPosts(initialPosts)\n    try {\n      localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts))\n      console.log('Reset to initial posts and saved to localStorage')\n    } catch (error) {\n      console.error('Error resetting posts in localStorage:', error)\n    }\n  }\n\n  // Function to clean up expired posts (older than 1 month)\n  const cleanupExpiredPosts = useCallback(() => {\n    const now = new Date()\n    setPosts(prevPosts => {\n      const activePosts = prevPosts.filter(post => {\n        const expirationDate = new Date(post.expires_at)\n        return expirationDate > now\n      })\n\n      const expiredCount = prevPosts.length - activePosts.length\n      if (expiredCount > 0) {\n        console.log(`Cleaned up ${expiredCount} expired posts`)\n\n        // Save cleaned posts to localStorage\n        try {\n          localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts))\n          console.log('Cleaned posts saved to localStorage')\n        } catch (error) {\n          console.error('Error saving cleaned posts to localStorage:', error)\n        }\n      }\n\n      return activePosts\n    })\n  }, [])\n\n  const migratePostsToFullNames = () => {\n    console.log('Migrating posts to use full names...')\n    // Clear localStorage and reset to initial posts with proper names\n    try {\n      localStorage.removeItem('kaazmaamaa-posts')\n      console.log('Cleared old posts from localStorage')\n    } catch (error) {\n      console.error('Error clearing localStorage:', error)\n    }\n    resetToInitialPosts()\n  }\n\n  const value = {\n    posts,\n    addPost,\n    likePost,\n    sharePost,\n    deletePost,\n    addComment,\n    clearAllPosts,\n    resetToInitialPosts,\n    cleanupExpiredPosts,\n    migratePostsToFullNames\n  }\n\n  return (\n    <PostsContext.Provider value={value}>\n      {children}\n    </PostsContext.Provider>\n  )\n}\n\nexport function usePosts() {\n  const context = useContext(PostsContext)\n  if (context === undefined) {\n    throw new Error('usePosts must be used within a PostsProvider')\n  }\n  return context\n}\n\n// Utility function to format time ago\nexport function formatTimeAgo(dateString: string): string {\n  const now = new Date()\n  const postDate = new Date(dateString)\n  const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))\n\n  if (diffInMinutes < 1) return 'Just now'\n  if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) return `${diffInHours}h ago`\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) return `${diffInDays}d ago`\n\n  return postDate.toLocaleDateString()\n}\n\n// Utility function to format post expiration info\nexport function formatExpirationInfo(expiresAt: string): { text: string, isExpiringSoon: boolean } {\n  const now = new Date()\n  const expirationDate = new Date(expiresAt)\n  const diffInMs = expirationDate.getTime() - now.getTime()\n  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24))\n\n  if (diffInMs <= 0) {\n    return { text: 'Expired', isExpiringSoon: true }\n  }\n\n  if (diffInDays <= 3) {\n    return { text: `Expires in ${diffInDays} day${diffInDays !== 1 ? 's' : ''}`, isExpiringSoon: true }\n  }\n\n  if (diffInDays <= 7) {\n    return { text: `Expires in ${diffInDays} days`, isExpiringSoon: false }\n  }\n\n  const diffInWeeks = Math.floor(diffInDays / 7)\n  if (diffInWeeks < 4) {\n    return { text: `Expires in ${diffInWeeks} week${diffInWeeks !== 1 ? 's' : ''}`, isExpiringSoon: false }\n  }\n\n  return { text: 'Expires in 1 month', isExpiringSoon: false }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;AA6CA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAEjE,uEAAuE;AACvE,MAAM,oBAAoB,CAAC;IACzB,MAAM,UAAU,IAAI,KAAK;IACzB,MAAM,aAAa,IAAI,KAAK;IAC5B,WAAW,QAAQ,CAAC,WAAW,QAAQ,KAAK,GAAG,cAAc;;IAC7D,OAAO,WAAW,WAAW;AAC/B;AAEA,qBAAqB;AACrB,MAAM,eAAuB;IAC3B;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;QACV,cAAc;QACd,eAAe;YACb;gBACE,IAAI;gBACJ,SAAS;gBACT,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,SAAS;gBACT,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YACnE;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,SAAS;gBACT,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC/D;SACD;IACH;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;IACZ;IACA;QACE,IAAI;QACJ,SAAS;QACT,WAAW;QACX,WAAW;QACX,SAAS;QACT,WAAW;QACX,YAAY;QACZ,YAAY,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACjE,YAAY,kBAAkB,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;QACnF,OAAO;QACP,UAAU;IACZ;CACD;AAEM,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,MAAM,cAAc,KAAK,KAAK,CAAC;oBAC/B,QAAQ,GAAG,CAAC,mCAAmC,YAAY,MAAM;oBAEjE,wCAAwC;oBACxC,MAAM,MAAM,IAAI;oBAChB,MAAM,cAAc,YAAY,MAAM,CAAC,CAAC;wBACtC,mEAAmE;wBACnE,IAAI,CAAC,KAAK,UAAU,EAAE;4BACpB,yDAAyD;4BACzD,KAAK,UAAU,GAAG,kBAAkB,KAAK,UAAU;wBACrD;wBACA,MAAM,iBAAiB,IAAI,KAAK,KAAK,UAAU;wBAC/C,OAAO,iBAAiB;oBAC1B;oBAEA,MAAM,eAAe,YAAY,MAAM,GAAG,YAAY,MAAM;oBAC5D,IAAI,eAAe,GAAG;wBACpB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa,sBAAsB,CAAC;wBAChE,0CAA0C;wBAC1C,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;oBAC1D;oBAEA,SAAS;gBACX,OAAO;oBACL,uCAAuC;oBACvC,QAAQ,GAAG,CAAC;oBACZ,SAAS;oBACT,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBAC1D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,SAAS;YACX,SAAU;gBACR,YAAY;YACd;QACF;QAEA;IACF,GAAG,EAAE;IAEL,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;YAChC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC,gCAAgC,MAAM,MAAM;YAC1D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;QACF;IACF,GAAG;QAAC;QAAO;KAAS;IAEpB,QAAQ,GAAG,CAAC,yCAAyC,MAAM,MAAM;IAEjE,MAAM,UAAU,CAAC;QACf,MAAM,YAAY,IAAI,OAAO,WAAW;QACxC,MAAM,OAAa;YACjB,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY;YACZ,YAAY,kBAAkB;YAC9B,OAAO;YACP,UAAU;QACZ;QACA,QAAQ,GAAG,CAAC,2BAA2B;QACvC,SAAS,CAAA;YACP,MAAM,eAAe;gBAAC;mBAAS;aAAU;YACzC,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,mCAAmC;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OACjC,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,OAAO,KAAK,aAAa,GAAG,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,GAAG;oBAC1D,eAAe,CAAC,KAAK,aAAa;gBACpC,IACA;YAGN,6BAA6B;YAC7B,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OACjC,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,cAAc,CAAC,KAAK,YAAY,IAAI,CAAC,IAAI;gBAC3C,IACA;YAGN,mCAAmC;YACnC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;YACvD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,aAAa,CAAC,QAAgB,SAAiB;QACnD,MAAM,aAAsB;YAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,SAAS,KAAK,EAAE;YAChB,WAAW,KAAK,IAAI;YACpB,WAAW,KAAK,IAAI;YACpB,SAAS,QAAQ,IAAI;YACrB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,GAAG,CAAC,CAAA,OACjC,KAAK,EAAE,KAAK,SACR;oBACE,GAAG,IAAI;oBACP,UAAU,KAAK,QAAQ,GAAG;oBAC1B,eAAe;2BAAK,KAAK,aAAa,IAAI,EAAE;wBAAG;qBAAW;gBAC5D,IACA;YAGN,gCAAgC;YAChC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;YAEA,OAAO;QACT;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA;YACP,MAAM,eAAe,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAE1D,gCAAgC;YAChC,IAAI;gBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0CAA0C;YAC1D;YAEA,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB;QACpB,SAAS,EAAE;QACX,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;IACF;IAEA,MAAM,sBAAsB;QAC1B,SAAS;QACT,IAAI;YACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;YACxD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,0DAA0D;IAC1D,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,MAAM,MAAM,IAAI;QAChB,SAAS,CAAA;YACP,MAAM,cAAc,UAAU,MAAM,CAAC,CAAA;gBACnC,MAAM,iBAAiB,IAAI,KAAK,KAAK,UAAU;gBAC/C,OAAO,iBAAiB;YAC1B;YAEA,MAAM,eAAe,UAAU,MAAM,GAAG,YAAY,MAAM;YAC1D,IAAI,eAAe,GAAG;gBACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa,cAAc,CAAC;gBAEtD,qCAAqC;gBACrC,IAAI;oBACF,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;oBACxD,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,+CAA+C;gBAC/D;YACF;YAEA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,0BAA0B;QAC9B,QAAQ,GAAG,CAAC;QACZ,kEAAkE;QAClE,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;QACA;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,cAAc,UAAkB;IAC9C,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IAElF,IAAI,gBAAgB,GAAG,OAAO;IAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;IAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;IAElD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,KAAK,CAAC;IAE/C,OAAO,SAAS,kBAAkB;AACpC;AAGO,SAAS,qBAAqB,SAAiB;IACpD,MAAM,MAAM,IAAI;IAChB,MAAM,iBAAiB,IAAI,KAAK;IAChC,MAAM,WAAW,eAAe,OAAO,KAAK,IAAI,OAAO;IACvD,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAE7D,IAAI,YAAY,GAAG;QACjB,OAAO;YAAE,MAAM;YAAW,gBAAgB;QAAK;IACjD;IAEA,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,MAAM,CAAC,WAAW,EAAE,WAAW,IAAI,EAAE,eAAe,IAAI,MAAM,IAAI;YAAE,gBAAgB;QAAK;IACpG;IAEA,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,MAAM,CAAC,WAAW,EAAE,WAAW,KAAK,CAAC;YAAE,gBAAgB;QAAM;IACxE;IAEA,MAAM,cAAc,KAAK,KAAK,CAAC,aAAa;IAC5C,IAAI,cAAc,GAAG;QACnB,OAAO;YAAE,MAAM,CAAC,WAAW,EAAE,YAAY,KAAK,EAAE,gBAAgB,IAAI,MAAM,IAAI;YAAE,gBAAgB;QAAM;IACxG;IAEA,OAAO;QAAE,MAAM;QAAsB,gBAAgB;IAAM;AAC7D", "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/OnlineUsersContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface OnlineUser {\n  id: string\n  name: string\n  email: string\n  role: 'worker' | 'supplier' | 'company'\n  lastSeen: string\n  isOnline: boolean\n  avatar?: string\n}\n\ninterface OnlineUsersContextType {\n  onlineUsers: OnlineUser[]\n  totalOnlineCount: number\n  setUserOnline: (user: { id: string, name: string, email: string, role: 'worker' | 'supplier' | 'company' }) => void\n  setUserOffline: (userId: string) => void\n  isUserOnline: (userId: string) => boolean\n}\n\nconst OnlineUsersContext = createContext<OnlineUsersContextType | undefined>(undefined)\n\n// Mock online users data\nconst mockOnlineUsers: OnlineUser[] = [\n  {\n    id: 'user-1',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'worker',\n    lastSeen: new Date().toISOString(),\n    isOnline: true\n  },\n  {\n    id: 'user-2',\n    name: '<PERSON>',\n    email: '<EMAIL>',\n    role: 'company',\n    lastSeen: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago\n    isOnline: true\n  },\n  {\n    id: 'user-3',\n    name: 'Gulf Manpower',\n    email: '<EMAIL>',\n    role: 'supplier',\n    lastSeen: new Date(Date.now() - 2 * 60 * 1000).toISOString(), // 2 minutes ago\n    isOnline: true\n  },\n  {\n    id: 'user-4',\n    name: 'Mohammed Hassan',\n    email: '<EMAIL>',\n    role: 'worker',\n    lastSeen: new Date(Date.now() - 1 * 60 * 1000).toISOString(), // 1 minute ago\n    isOnline: true\n  },\n  {\n    id: 'user-5',\n    name: 'Riyadh Builders',\n    email: '<EMAIL>',\n    role: 'company',\n    lastSeen: new Date(Date.now() - 3 * 60 * 1000).toISOString(), // 3 minutes ago\n    isOnline: true\n  },\n  {\n    id: 'user-6',\n    name: 'Ali Construction',\n    email: '<EMAIL>',\n    role: 'worker',\n    lastSeen: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago\n    isOnline: false\n  }\n]\n\nexport function OnlineUsersProvider({ children }: { children: ReactNode }) {\n  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([])\n\n  useEffect(() => {\n    // Load initial online users\n    setOnlineUsers(mockOnlineUsers)\n\n    // Simulate real-time updates\n    const interval = setInterval(() => {\n      setOnlineUsers(prevUsers => {\n        return prevUsers.map(user => {\n          // Randomly update online status for demo\n          const shouldToggle = Math.random() < 0.1 // 10% chance to toggle\n          if (shouldToggle) {\n            return {\n              ...user,\n              isOnline: !user.isOnline,\n              lastSeen: new Date().toISOString()\n            }\n          }\n          return user\n        })\n      })\n    }, 30000) // Update every 30 seconds\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const setUserOnline = useCallback((user: { id: string, name: string, email: string, role: 'worker' | 'supplier' | 'company' }) => {\n    setOnlineUsers(prevUsers => {\n      const existingUserIndex = prevUsers.findIndex(u => u.id === user.id)\n      const onlineUser: OnlineUser = {\n        id: user.id,\n        name: user.name,\n        email: user.email,\n        role: user.role,\n        lastSeen: new Date().toISOString(),\n        isOnline: true\n      }\n\n      if (existingUserIndex >= 0) {\n        // Update existing user only if status changed\n        const existingUser = prevUsers[existingUserIndex]\n        if (existingUser.isOnline) {\n          // User is already online, no need to update\n          return prevUsers\n        }\n        const updatedUsers = [...prevUsers]\n        updatedUsers[existingUserIndex] = onlineUser\n        return updatedUsers\n      } else {\n        // Add new user\n        return [...prevUsers, onlineUser]\n      }\n    })\n  }, [])\n\n  const setUserOffline = useCallback((userId: string) => {\n    setOnlineUsers(prevUsers =>\n      prevUsers.map(user =>\n        user.id === userId\n          ? { ...user, isOnline: false, lastSeen: new Date().toISOString() }\n          : user\n      )\n    )\n  }, [])\n\n  const isUserOnline = useCallback((userId: string) => {\n    const user = onlineUsers.find(u => u.id === userId)\n    return user?.isOnline || false\n  }, [onlineUsers])\n\n  const totalOnlineCount = onlineUsers.filter(user => user.isOnline).length\n\n  const value = {\n    onlineUsers,\n    totalOnlineCount,\n    setUserOnline,\n    setUserOffline,\n    isUserOnline\n  }\n\n  return (\n    <OnlineUsersContext.Provider value={value}>\n      {children}\n    </OnlineUsersContext.Provider>\n  )\n}\n\nexport function useOnlineUsers() {\n  const context = useContext(OnlineUsersContext)\n  if (context === undefined) {\n    throw new Error('useOnlineUsers must be used within an OnlineUsersProvider')\n  }\n  return context\n}\n\n// Utility function to format last seen time\nexport function formatLastSeen(lastSeenString: string): string {\n  const now = new Date()\n  const lastSeen = new Date(lastSeenString)\n  const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))\n\n  if (diffInMinutes < 1) return 'Just now'\n  if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n\n  const diffInHours = Math.floor(diffInMinutes / 60)\n  if (diffInHours < 24) return `${diffInHours}h ago`\n\n  const diffInDays = Math.floor(diffInHours / 24)\n  if (diffInDays < 7) return `${diffInDays}d ago`\n\n  return lastSeen.toLocaleDateString()\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAsBA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsC;AAE7E,yBAAyB;AACzB,MAAM,kBAAgC;IACpC;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,OAAO,WAAW;QAChC,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,MAAM,WAAW;QAC1D,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,UAAU,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;QAC3D,UAAU;IACZ;CACD;AAEM,SAAS,oBAAoB,EAAE,QAAQ,EAA2B;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,eAAe;QAEf,6BAA6B;QAC7B,MAAM,WAAW,YAAY;YAC3B,eAAe,CAAA;gBACb,OAAO,UAAU,GAAG,CAAC,CAAA;oBACnB,yCAAyC;oBACzC,MAAM,eAAe,KAAK,MAAM,KAAK,IAAI,uBAAuB;;oBAChE,IAAI,cAAc;wBAChB,OAAO;4BACL,GAAG,IAAI;4BACP,UAAU,CAAC,KAAK,QAAQ;4BACxB,UAAU,IAAI,OAAO,WAAW;wBAClC;oBACF;oBACA,OAAO;gBACT;YACF;QACF,GAAG,OAAO,0BAA0B;;QAEpC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,eAAe,CAAA;YACb,MAAM,oBAAoB,UAAU,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK,EAAE;YACnE,MAAM,aAAyB;gBAC7B,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,UAAU,IAAI,OAAO,WAAW;gBAChC,UAAU;YACZ;YAEA,IAAI,qBAAqB,GAAG;gBAC1B,8CAA8C;gBAC9C,MAAM,eAAe,SAAS,CAAC,kBAAkB;gBACjD,IAAI,aAAa,QAAQ,EAAE;oBACzB,4CAA4C;oBAC5C,OAAO;gBACT;gBACA,MAAM,eAAe;uBAAI;iBAAU;gBACnC,YAAY,CAAC,kBAAkB,GAAG;gBAClC,OAAO;YACT,OAAO;gBACL,eAAe;gBACf,OAAO;uBAAI;oBAAW;iBAAW;YACnC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,eAAe,CAAA,YACb,UAAU,GAAG,CAAC,CAAA,OACZ,KAAK,EAAE,KAAK,SACR;oBAAE,GAAG,IAAI;oBAAE,UAAU;oBAAO,UAAU,IAAI,OAAO,WAAW;gBAAG,IAC/D;IAGV,GAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5C,OAAO,MAAM,YAAY;IAC3B,GAAG;QAAC;KAAY;IAEhB,MAAM,mBAAmB,YAAY,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;IAEzE,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;kBACjC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS,eAAe,cAAsB;IACnD,MAAM,MAAM,IAAI;IAChB,MAAM,WAAW,IAAI,KAAK;IAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IAElF,IAAI,gBAAgB,GAAG,OAAO;IAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;IAEtD,MAAM,cAAc,KAAK,KAAK,CAAC,gBAAgB;IAC/C,IAAI,cAAc,IAAI,OAAO,GAAG,YAAY,KAAK,CAAC;IAElD,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;IAC5C,IAAI,aAAa,GAAG,OAAO,GAAG,WAAW,KAAK,CAAC;IAE/C,OAAO,SAAS,kBAAkB;AACpC", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/WorkersContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\nexport interface WorkerDocument {\n  id: string\n  name: string\n  type: 'absher_iqama' | 'muqeem' | 'medical' | 'insurance' | 'chamber' | 'ajeer' | 'experience' | 'fitness' | 'passport' | 'visa' | 'gosi' | 'other'\n  url: string\n  uploadedAt: string\n  isValidated?: boolean\n  fileSize?: number\n  fileName?: string\n}\n\nexport interface WorkerVideo {\n  id: string\n  title: string\n  type: 'introduction' | 'skills' | 'portfolio'\n  url: string\n  thumbnail?: string\n  duration?: number\n  uploadedAt: string\n}\n\nexport interface WorkExperience {\n  id: string\n  company: string\n  position: string\n  startDate: string\n  endDate?: string\n  description: string\n  location: string\n  isCurrentJob: boolean\n}\n\nexport interface WorkerLifecycle {\n  joiningDate?: string\n  vacationStart?: string\n  vacationEnd?: string\n  leavingDate?: string\n  status: 'available' | 'working' | 'on_vacation' | 'left' | 'seeking'\n  currentEmployer?: string\n  notes?: string\n}\n\nexport interface WorkerProfile {\n  id: string // Must be unique\n  userId: string\n  personalInfo: {\n    workerName: string\n    nationality: string\n    kofilName: string\n    kofilPhone: string\n    email: string\n    phone: string\n    whatsapp: string\n    address: string\n    age?: number\n    profileImage?: string\n  }\n  identificationInfo: {\n    iqamaNumber: string\n    passportNumber: string\n    crNumber?: string // Optional for workers\n  }\n  professionalInfo: {\n    workerCategory: string\n    title: string\n    specialty: string\n    experience: number\n    skills: string[]\n    languages: string[]\n    expectedSalaryPerHour: number\n    currency: 'SAR' | 'USD' | 'EUR'\n    salaryPayingDuration: 'daily' | 'weekly' | 'monthly' | 'hourly'\n    availability: 'immediate' | 'within_week' | 'within_month' | 'negotiable'\n  }\n  documents: WorkerDocument[]\n  videos: WorkerVideo[]\n  workExperience: WorkExperience[]\n  lifecycle: WorkerLifecycle\n  preferences: {\n    workType: 'full_time' | 'part_time' | 'contract' | 'freelance'\n    sideLocationInterest: string[]\n    companyNameInterest: string[]\n    preferredLocations?: string[]\n    willingToRelocate: boolean\n    remoteWork: boolean\n    neededDocuments: string[]\n    otherRequirements: string\n  }\n  isLiveStreaming: boolean\n  liveStreamUrl?: string\n  createdAt: string\n  updatedAt: string\n  isVerified: boolean\n  rating: number\n  totalReviews: number\n}\n\ninterface WorkersContextType {\n  workers: WorkerProfile[]\n  currentWorkerProfile?: WorkerProfile\n  createWorkerProfile: (profile: Omit<WorkerProfile, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateWorkerProfile: (id: string, updates: Partial<WorkerProfile>) => void\n  updateWorker: (profile: WorkerProfile) => void\n  addDocument: (workerId: string, document: Omit<WorkerDocument, 'id' | 'uploadedAt'>) => void\n  addVideo: (workerId: string, video: Omit<WorkerVideo, 'id' | 'uploadedAt'>) => void\n  updateLifecycle: (workerId: string, lifecycle: Partial<WorkerLifecycle>) => void\n  startLiveStream: (workerId: string, streamUrl: string) => void\n  stopLiveStream: (workerId: string) => void\n  getWorkersBySpecialty: (specialty: string) => WorkerProfile[]\n  getWorkersByCategory: (category: string) => WorkerProfile[]\n  getAvailableWorkers: () => WorkerProfile[]\n  validateWorkerIdUniqueness: (workerId: string, excludeId?: string) => boolean\n  validateIqamaDocument: (iqamaNumber: string) => Promise<boolean>\n}\n\nconst WorkersContext = createContext<WorkersContextType | undefined>(undefined)\n\n// Mock data for demonstration\nconst mockWorkers: WorkerProfile[] = [\n  {\n    id: 'worker-1', // Must be unique\n    userId: 'user-1',\n    personalInfo: {\n      workerName: 'Ahmed Al-Rashid',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Ahmed Mohammed Al-Rashid',\n      kofilPhone: '+966501234567',\n      email: '<EMAIL>',\n      phone: '+966501234567',\n      whatsapp: '+966501234567',\n      address: 'King Fahd Road, Riyadh 12345, Saudi Arabia'\n    },\n    identificationInfo: {\n      iqamaNumber: '2345678901',\n      passportNumber: 'A12345678',\n      crNumber: 'CR-1010987654'\n    },\n    professionalInfo: {\n      workerCategory: 'Electrical Technician',\n      title: 'Senior Electrical Technician',\n      specialty: 'Electrical Work',\n      experience: 5,\n      skills: ['Electrical Installation', 'Maintenance', 'Troubleshooting', 'Safety Protocols'],\n      languages: ['Arabic', 'English'],\n      expectedSalaryPerHour: 85,\n      currency: 'SAR',\n      salaryPayingDuration: 'weekly',\n      availability: 'immediate'\n    },\n    documents: [\n      {\n        id: 'doc-1',\n        name: 'Iqama Document',\n        type: 'iqama',\n        url: '/documents/iqama.pdf',\n        uploadedAt: new Date().toISOString(),\n        isValidated: true\n      },\n      {\n        id: 'doc-2',\n        name: 'Visa Document',\n        type: 'visa',\n        url: '/documents/visa.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-3',\n        name: 'Passport Copy',\n        type: 'passport',\n        url: '/documents/passport.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-4',\n        name: 'Medical Certificate',\n        type: 'medical',\n        url: '/documents/medical.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-5',\n        name: 'Fitness Certificate',\n        type: 'fitness',\n        url: '/documents/fitness.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-1',\n        title: 'Professional Introduction',\n        type: 'introduction',\n        url: '/videos/intro.mp4',\n        duration: 120,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    workExperience: [\n      {\n        id: 'exp-1',\n        company: 'Saudi Electric Company',\n        position: 'Electrical Technician',\n        startDate: '2020-01-01',\n        endDate: '2023-12-31',\n        description: 'Responsible for electrical installations and maintenance',\n        location: 'Riyadh',\n        isCurrentJob: false\n      }\n    ],\n    lifecycle: {\n      status: 'seeking',\n      notes: 'Looking for new opportunities in electrical field'\n    },\n    preferences: {\n      workType: 'full_time',\n      sideLocationInterest: ['Riyadh', 'Jeddah', 'Dammam'],\n      companyNameInterest: ['Saudi Electric Company', 'ARAMCO', 'SABIC'],\n      willingToRelocate: true,\n      remoteWork: false,\n      neededDocuments: ['Work Permit', 'Safety Certificate', 'Medical Clearance'],\n      otherRequirements: 'Prefer companies with good safety standards and career growth opportunities'\n    },\n    isLiveStreaming: false,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    isVerified: true,\n    rating: 4.8,\n    totalReviews: 15\n  },\n  {\n    id: 'worker-2',\n    userId: 'user-2',\n    personalInfo: {\n      workerName: 'Mohammed Al-Zahrani',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Mohammed Abdullah Al-Zahrani',\n      kofilPhone: '+966502345678',\n      email: '<EMAIL>',\n      phone: '+966502345678',\n      whatsapp: '+966502345678',\n      address: 'Prince Sultan Road, Jeddah 21455, Saudi Arabia'\n    },\n    identificationInfo: {\n      iqamaNumber: '**********',\n      passportNumber: 'B23456789',\n      crNumber: 'CR-**********'\n    },\n    professionalInfo: {\n      workerCategory: 'Plumber',\n      title: 'Senior Plumber',\n      specialty: 'Plumbing & Water Systems',\n      experience: 8,\n      skills: ['Pipe Installation', 'Water System Repair', 'Drainage', 'Emergency Repairs'],\n      languages: ['Arabic', 'English', 'Urdu'],\n      expectedSalaryPerHour: 75,\n      currency: 'SAR',\n      salaryPayingDuration: 'daily',\n      availability: 'within_week'\n    },\n    documents: [\n      {\n        id: 'doc-6',\n        name: 'Iqama Document',\n        type: 'iqama',\n        url: '/documents/iqama2.pdf',\n        uploadedAt: new Date().toISOString(),\n        isValidated: true\n      },\n      {\n        id: 'doc-7',\n        name: 'Plumbing Certificate',\n        type: 'experience',\n        url: '/documents/plumbing-cert.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-2',\n        title: 'Plumbing Skills Demo',\n        type: 'skills',\n        url: '/videos/plumbing-demo.mp4',\n        duration: 180,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    workExperience: [\n      {\n        id: 'exp-2',\n        company: 'Saudi Water Company',\n        position: 'Senior Plumber',\n        startDate: '2018-03-01',\n        endDate: '2024-01-01',\n        description: 'Specialized in water system installations and repairs',\n        location: 'Jeddah',\n        isCurrentJob: false\n      }\n    ],\n    lifecycle: {\n      status: 'available',\n      notes: 'Available for immediate plumbing work'\n    },\n    preferences: {\n      workType: 'contract',\n      sideLocationInterest: ['Jeddah', 'Mecca', 'Taif'],\n      companyNameInterest: ['Saudi Water Company', 'National Water Company'],\n      willingToRelocate: false,\n      remoteWork: false,\n      neededDocuments: ['Work Permit', 'Plumbing License'],\n      otherRequirements: 'Prefer emergency repair contracts'\n    },\n    isLiveStreaming: false,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    isVerified: true,\n    rating: 4.6,\n    totalReviews: 23\n  },\n  {\n    id: 'worker-3',\n    userId: 'user-3',\n    personalInfo: {\n      workerName: 'Khalid Al-Mutairi',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Khalid Saad Al-Mutairi',\n      kofilPhone: '+966503456789',\n      email: '<EMAIL>',\n      phone: '+966503456789',\n      whatsapp: '+966503456789',\n      address: 'King Abdul Aziz Road, Dammam 31411, Saudi Arabia'\n    },\n    identificationInfo: {\n      iqamaNumber: '4*********',\n      passportNumber: '*********',\n      crNumber: 'CR-**********'\n    },\n    professionalInfo: {\n      workerCategory: 'Carpenter',\n      title: 'Master Carpenter',\n      specialty: 'Woodworking & Furniture',\n      experience: 12,\n      skills: ['Custom Furniture', 'Cabinet Making', 'Wood Finishing', 'Restoration'],\n      languages: ['Arabic', 'English'],\n      expectedSalaryPerHour: 95,\n      currency: 'SAR',\n      salaryPayingDuration: 'weekly',\n      availability: 'immediate'\n    },\n    documents: [\n      {\n        id: 'doc-8',\n        name: 'Iqama Document',\n        type: 'iqama',\n        url: '/documents/iqama3.pdf',\n        uploadedAt: new Date().toISOString(),\n        isValidated: true\n      },\n      {\n        id: 'doc-9',\n        name: 'Carpentry Master Certificate',\n        type: 'experience',\n        url: '/documents/carpentry-master.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-3',\n        title: 'Custom Furniture Portfolio',\n        type: 'portfolio',\n        url: '/videos/furniture-portfolio.mp4',\n        duration: 300,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    workExperience: [\n      {\n        id: 'exp-3',\n        company: 'Royal Furniture Workshop',\n        position: 'Master Carpenter',\n        startDate: '2015-01-01',\n        description: 'Creating custom furniture for luxury homes and offices',\n        location: 'Dammam',\n        isCurrentJob: true\n      }\n    ],\n    lifecycle: {\n      status: 'seeking',\n      notes: 'Looking for high-end furniture projects'\n    },\n    preferences: {\n      workType: 'freelance',\n      sideLocationInterest: ['Dammam', 'Khobar', 'Dhahran'],\n      companyNameInterest: ['Royal Furniture', 'Elite Woodworks'],\n      willingToRelocate: true,\n      remoteWork: false,\n      neededDocuments: ['Work Permit', 'Carpentry License', 'Portfolio'],\n      otherRequirements: 'Prefer luxury furniture projects'\n    },\n    isLiveStreaming: true,\n    liveStreamUrl: 'https://stream.example.com/khalid-live',\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n    isVerified: true,\n    rating: 4.9,\n    totalReviews: 31\n  }\n]\n\nexport function WorkersProvider({ children }: { children: ReactNode }) {\n  const [workers, setWorkers] = useState<WorkerProfile[]>([])\n  const [currentWorkerProfile, setCurrentWorkerProfile] = useState<WorkerProfile>()\n\n  useEffect(() => {\n    try {\n      // Load workers from localStorage or use mock data\n      const savedWorkers = localStorage.getItem('kaazmaamaa-workers')\n      if (savedWorkers) {\n        const parsedWorkers = JSON.parse(savedWorkers)\n        // Validate the data structure\n        if (Array.isArray(parsedWorkers) && parsedWorkers.length > 0) {\n          setWorkers(parsedWorkers)\n        } else {\n          // Use mock data if saved data is invalid\n          setWorkers(mockWorkers)\n          localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))\n        }\n      } else {\n        // Use mock data for first time\n        setWorkers(mockWorkers)\n        localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))\n      }\n    } catch (error) {\n      console.error('Error loading workers:', error)\n      // Fallback to mock data if there's an error\n      setWorkers(mockWorkers)\n      localStorage.setItem('kaazmaamaa-workers', JSON.stringify(mockWorkers))\n    }\n  }, [])\n\n  const saveToStorage = (updatedWorkers: WorkerProfile[]) => {\n    localStorage.setItem('kaazmaamaa-workers', JSON.stringify(updatedWorkers))\n  }\n\n  const createWorkerProfile = (profile: Omit<WorkerProfile, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProfile: WorkerProfile = {\n      ...profile,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    const updatedWorkers = [...workers, newProfile]\n    setWorkers(updatedWorkers)\n    saveToStorage(updatedWorkers)\n    setCurrentWorkerProfile(newProfile)\n  }\n\n  const updateWorkerProfile = (id: string, updates: Partial<WorkerProfile>) => {\n    const updatedWorkers = workers.map(worker =>\n      worker.id === id\n        ? { ...worker, ...updates, updatedAt: new Date().toISOString() }\n        : worker\n    )\n    setWorkers(updatedWorkers)\n    saveToStorage(updatedWorkers)\n  }\n\n  const updateWorker = (profile: WorkerProfile) => {\n    const existingWorkerIndex = workers.findIndex(w => w.userId === profile.userId)\n\n    if (existingWorkerIndex >= 0) {\n      // Update existing worker\n      const updatedWorkers = [...workers]\n      updatedWorkers[existingWorkerIndex] = {\n        ...profile,\n        updatedAt: new Date().toISOString()\n      }\n      setWorkers(updatedWorkers)\n      saveToStorage(updatedWorkers)\n    } else {\n      // Create new worker if doesn't exist\n      const newProfile: WorkerProfile = {\n        ...profile,\n        id: profile.id || Date.now().toString(),\n        createdAt: profile.createdAt || new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      }\n      const updatedWorkers = [...workers, newProfile]\n      setWorkers(updatedWorkers)\n      saveToStorage(updatedWorkers)\n    }\n  }\n\n  const addDocument = (workerId: string, document: Omit<WorkerDocument, 'id' | 'uploadedAt'>) => {\n    const newDocument: WorkerDocument = {\n      ...document,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateWorkerProfile(workerId, {\n      documents: [...(workers.find(w => w.id === workerId)?.documents || []), newDocument]\n    })\n  }\n\n  const addVideo = (workerId: string, video: Omit<WorkerVideo, 'id' | 'uploadedAt'>) => {\n    const newVideo: WorkerVideo = {\n      ...video,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateWorkerProfile(workerId, {\n      videos: [...(workers.find(w => w.id === workerId)?.videos || []), newVideo]\n    })\n  }\n\n  const updateLifecycle = (workerId: string, lifecycle: Partial<WorkerLifecycle>) => {\n    const worker = workers.find(w => w.id === workerId)\n    if (worker) {\n      updateWorkerProfile(workerId, {\n        lifecycle: { ...worker.lifecycle, ...lifecycle }\n      })\n    }\n  }\n\n  const startLiveStream = (workerId: string, streamUrl: string) => {\n    updateWorkerProfile(workerId, {\n      isLiveStreaming: true,\n      liveStreamUrl: streamUrl\n    })\n  }\n\n  const stopLiveStream = (workerId: string) => {\n    updateWorkerProfile(workerId, {\n      isLiveStreaming: false,\n      liveStreamUrl: undefined\n    })\n  }\n\n  const getWorkersBySpecialty = (specialty: string) => {\n    return workers.filter(worker =>\n      worker.professionalInfo.specialty.toLowerCase().includes(specialty.toLowerCase())\n    )\n  }\n\n  const getAvailableWorkers = () => {\n    return workers.filter(worker =>\n      worker.lifecycle.status === 'available' || worker.lifecycle.status === 'seeking'\n    )\n  }\n\n  const getWorkersByCategory = (category: string) => {\n    return workers.filter(worker =>\n      worker.professionalInfo.workerCategory.toLowerCase().includes(category.toLowerCase())\n    )\n  }\n\n  const validateWorkerIdUniqueness = (workerId: string, excludeId?: string) => {\n    return !workers.some(worker =>\n      worker.id === workerId && worker.id !== excludeId\n    )\n  }\n\n  const validateIqamaDocument = async (iqamaNumber: string): Promise<boolean> => {\n    // Mock validation - in real app, this would call government API\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        // Simple validation: check if it's 10 digits\n        const isValid = /^\\d{10}$/.test(iqamaNumber)\n        resolve(isValid)\n      }, 1000)\n    })\n  }\n\n  const value = {\n    workers,\n    currentWorkerProfile,\n    createWorkerProfile,\n    updateWorkerProfile,\n    updateWorker,\n    addDocument,\n    addVideo,\n    updateLifecycle,\n    startLiveStream,\n    stopLiveStream,\n    getWorkersBySpecialty,\n    getWorkersByCategory,\n    getAvailableWorkers,\n    validateWorkerIdUniqueness,\n    validateIqamaDocument\n  }\n\n  return (\n    <WorkersContext.Provider value={value}>\n      {children}\n    </WorkersContext.Provider>\n  )\n}\n\nexport function useWorkers() {\n  const context = useContext(WorkersContext)\n  if (context === undefined) {\n    throw new Error('useWorkers must be used within a WorkersProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuHA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAErE,8BAA8B;AAC9B,MAAM,cAA+B;IACnC;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,YAAY;YACZ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,oBAAoB;YAClB,aAAa;YACb,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,gBAAgB;YAChB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;gBAAC;gBAA2B;gBAAe;gBAAmB;aAAmB;YACzF,WAAW;gBAAC;gBAAU;aAAU;YAChC,uBAAuB;YACvB,UAAU;YACV,sBAAsB;YACtB,cAAc;QAChB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;SACD;QACD,WAAW;YACT,QAAQ;YACR,OAAO;QACT;QACA,aAAa;YACX,UAAU;YACV,sBAAsB;gBAAC;gBAAU;gBAAU;aAAS;YACpD,qBAAqB;gBAAC;gBAA0B;gBAAU;aAAQ;YAClE,mBAAmB;YACnB,YAAY;YACZ,iBAAiB;gBAAC;gBAAe;gBAAsB;aAAoB;YAC3E,mBAAmB;QACrB;QACA,iBAAiB;QACjB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,QAAQ;QACR,cAAc;IAChB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,YAAY;YACZ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,oBAAoB;YAClB,aAAa;YACb,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,gBAAgB;YAChB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;gBAAC;gBAAqB;gBAAuB;gBAAY;aAAoB;YACrF,WAAW;gBAAC;gBAAU;gBAAW;aAAO;YACxC,uBAAuB;YACvB,UAAU;YACV,sBAAsB;YACtB,cAAc;QAChB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;SACD;QACD,WAAW;YACT,QAAQ;YACR,OAAO;QACT;QACA,aAAa;YACX,UAAU;YACV,sBAAsB;gBAAC;gBAAU;gBAAS;aAAO;YACjD,qBAAqB;gBAAC;gBAAuB;aAAyB;YACtE,mBAAmB;YACnB,YAAY;YACZ,iBAAiB;gBAAC;gBAAe;aAAmB;YACpD,mBAAmB;QACrB;QACA,iBAAiB;QACjB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,QAAQ;QACR,cAAc;IAChB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,YAAY;YACZ,aAAa;YACb,WAAW;YACX,YAAY;YACZ,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,oBAAoB;YAClB,aAAa;YACb,gBAAgB;YAChB,UAAU;QACZ;QACA,kBAAkB;YAChB,gBAAgB;YAChB,OAAO;YACP,WAAW;YACX,YAAY;YACZ,QAAQ;gBAAC;gBAAoB;gBAAkB;gBAAkB;aAAc;YAC/E,WAAW;gBAAC;gBAAU;aAAU;YAChC,uBAAuB;YACvB,UAAU;YACV,sBAAsB;YACtB,cAAc;QAChB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;gBAClC,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,gBAAgB;YACd;gBACE,IAAI;gBACJ,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,cAAc;YAChB;SACD;QACD,WAAW;YACT,QAAQ;YACR,OAAO;QACT;QACA,aAAa;YACX,UAAU;YACV,sBAAsB;gBAAC;gBAAU;gBAAU;aAAU;YACrD,qBAAqB;gBAAC;gBAAmB;aAAkB;YAC3D,mBAAmB;YACnB,YAAY;YACZ,iBAAiB;gBAAC;gBAAe;gBAAqB;aAAY;YAClE,mBAAmB;QACrB;QACA,iBAAiB;QACjB,eAAe;QACf,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;QACjC,YAAY;QACZ,QAAQ;QACR,cAAc;IAChB;CACD;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,kDAAkD;YAClD,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,IAAI,cAAc;gBAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC;gBACjC,8BAA8B;gBAC9B,IAAI,MAAM,OAAO,CAAC,kBAAkB,cAAc,MAAM,GAAG,GAAG;oBAC5D,WAAW;gBACb,OAAO;oBACL,yCAAyC;oBACzC,WAAW;oBACX,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;gBAC5D;YACF,OAAO;gBACL,+BAA+B;gBAC/B,WAAW;gBACX,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAC5D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,4CAA4C;YAC5C,WAAW;YACX,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;QAC5D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;IAC5D;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,aAA4B;YAChC,GAAG,OAAO;YACV,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,iBAAiB;eAAI;YAAS;SAAW;QAC/C,WAAW;QACX,cAAc;QACd,wBAAwB;IAC1B;IAEA,MAAM,sBAAsB,CAAC,IAAY;QACvC,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SACjC,OAAO,EAAE,KAAK,KACV;gBAAE,GAAG,MAAM;gBAAE,GAAG,OAAO;gBAAE,WAAW,IAAI,OAAO,WAAW;YAAG,IAC7D;QAEN,WAAW;QACX,cAAc;IAChB;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,sBAAsB,QAAQ,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;QAE9E,IAAI,uBAAuB,GAAG;YAC5B,yBAAyB;YACzB,MAAM,iBAAiB;mBAAI;aAAQ;YACnC,cAAc,CAAC,oBAAoB,GAAG;gBACpC,GAAG,OAAO;gBACV,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,WAAW;YACX,cAAc;QAChB,OAAO;YACL,qCAAqC;YACrC,MAAM,aAA4B;gBAChC,GAAG,OAAO;gBACV,IAAI,QAAQ,EAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACrC,WAAW,QAAQ,SAAS,IAAI,IAAI,OAAO,WAAW;gBACtD,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,MAAM,iBAAiB;mBAAI;gBAAS;aAAW;YAC/C,WAAW;YACX,cAAc;QAChB;IACF;IAEA,MAAM,cAAc,CAAC,UAAkB;QACrC,MAAM,cAA8B;YAClC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,oBAAoB,UAAU;YAC5B,WAAW;mBAAK,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,aAAa,EAAE;gBAAG;aAAY;QACtF;IACF;IAEA,MAAM,WAAW,CAAC,UAAkB;QAClC,MAAM,WAAwB;YAC5B,GAAG,KAAK;YACR,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,oBAAoB,UAAU;YAC5B,QAAQ;mBAAK,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,UAAU,EAAE;gBAAG;aAAS;QAC7E;IACF;IAEA,MAAM,kBAAkB,CAAC,UAAkB;QACzC,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,QAAQ;YACV,oBAAoB,UAAU;gBAC5B,WAAW;oBAAE,GAAG,OAAO,SAAS;oBAAE,GAAG,SAAS;gBAAC;YACjD;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC,UAAkB;QACzC,oBAAoB,UAAU;YAC5B,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB,UAAU;YAC5B,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,gBAAgB,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UAAU,WAAW;IAElF;IAEA,MAAM,sBAAsB;QAC1B,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,SAAS,CAAC,MAAM,KAAK,eAAe,OAAO,SAAS,CAAC,MAAM,KAAK;IAE3E;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,OAAO,gBAAgB,CAAC,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAEtF;IAEA,MAAM,6BAA6B,CAAC,UAAkB;QACpD,OAAO,CAAC,QAAQ,IAAI,CAAC,CAAA,SACnB,OAAO,EAAE,KAAK,YAAY,OAAO,EAAE,KAAK;IAE5C;IAEA,MAAM,wBAAwB,OAAO;QACnC,gEAAgE;QAChE,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,6CAA6C;gBAC7C,MAAM,UAAU,WAAW,IAAI,CAAC;gBAChC,QAAQ;YACV,GAAG;QACL;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/SuppliersContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, ReactNode } from 'react'\n\nexport interface SupplierDocument {\n  id: string\n  name: string\n  type: 'licence' | 'cr' | 'contract' | 'other'\n  url: string\n  uploadedAt: string\n}\n\nexport interface JobDemand {\n  id: string\n  category: string\n  position: string\n  description: string\n  requirements: string[]\n  salaryPerHour: number\n  currency: 'SAR' | 'USD' | 'EUR'\n  paymentDuration: 'daily' | 'weekly' | 'monthly' | 'project_based'\n  location: string\n  urgency: 'immediate' | 'within_week' | 'within_month'\n  documentsRequired: string[]\n  createdAt: string\n}\n\nexport interface SupplierProfile {\n  id: string // Must be unique\n  userId: string\n  personalInfo: {\n    supplierName: string\n    nationality: string\n    kofilName: string\n    kofilPhone: string\n    profileImage?: string\n    address: string\n    phone: string\n    whatsapp: string\n    email: string\n  }\n  businessInfo: {\n    companyName: string\n    crNumber: string // Commercial Registration Number\n    licenceNumber: string // Must be unique\n    businessType: string\n    establishedYear: number\n    employeeCount: string\n  }\n  documents: SupplierDocument[]\n  jobDemands: JobDemand[]\n  videos: {\n    id: string\n    title: string\n    type: 'company_intro' | 'job_requirements' | 'workplace_tour'\n    url: string\n    thumbnail?: string\n    duration?: number\n    uploadedAt: string\n  }[]\n  isLiveStreaming: boolean\n  liveStreamUrl?: string\n  operatingLocations: string[]\n  preferredWorkerCategories: string[]\n  rating: number\n  totalReviews: number\n  isVerified: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface SuppliersContextType {\n  suppliers: SupplierProfile[]\n  currentSupplierProfile?: SupplierProfile\n  createSupplierProfile: (profile: Omit<SupplierProfile, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateSupplierProfile: (id: string, updates: Partial<SupplierProfile>) => void\n  addDocument: (supplierId: string, document: Omit<SupplierDocument, 'id' | 'uploadedAt'>) => void\n  addJobDemand: (supplierId: string, jobDemand: Omit<JobDemand, 'id' | 'createdAt'>) => void\n  updateJobDemand: (supplierId: string, jobDemandId: string, updates: Partial<JobDemand>) => void\n  deleteJobDemand: (supplierId: string, jobDemandId: string) => void\n  startLiveStream: (supplierId: string, streamUrl: string) => void\n  stopLiveStream: (supplierId: string) => void\n  getSuppliersByLocation: (location: string) => SupplierProfile[]\n  getJobDemandsByCategory: (category: string) => { supplier: SupplierProfile, jobDemand: JobDemand }[]\n  validateLicenceUniqueness: (licenceNumber: string, excludeId?: string) => boolean\n}\n\nconst SuppliersContext = createContext<SuppliersContextType | undefined>(undefined)\n\n// Mock data for demonstration\nconst mockSuppliers: SupplierProfile[] = [\n  {\n    id: 'supplier-1',\n    userId: 'user-supplier-1',\n    personalInfo: {\n      supplierName: 'Ahmed Construction Supplier',\n      nationality: 'Saudi Arabia',\n      kofilName: 'Ahmed Al-Rashid',\n      kofilPhone: '+966501234567',\n      address: 'King Fahd Road, Riyadh 12345',\n      phone: '+966501234567',\n      whatsapp: '+966501234567',\n      email: '<EMAIL>'\n    },\n    businessInfo: {\n      companyName: 'Al-Rashid Construction Supply Co.',\n      crNumber: 'CR-1010123456',\n      licenceNumber: 'LIC-2024-001',\n      businessType: 'Construction Supply',\n      establishedYear: 2015,\n      employeeCount: '50-100'\n    },\n    documents: [\n      {\n        id: 'doc-1',\n        name: 'Business Licence',\n        type: 'licence',\n        url: '/documents/business-licence.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-2',\n        name: 'Commercial Registration',\n        type: 'cr',\n        url: '/documents/cr-certificate.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    jobDemands: [\n      {\n        id: 'job-1',\n        category: 'Electrical Work',\n        position: 'Senior Electrician',\n        description: 'Looking for experienced electricians for large construction project',\n        requirements: ['5+ years experience', 'Electrical license', 'Safety certification'],\n        salaryPerHour: 85,\n        currency: 'SAR',\n        paymentDuration: 'weekly',\n        location: 'Riyadh',\n        urgency: 'immediate',\n        documentsRequired: ['Iqama', 'Electrical License', 'Medical Certificate'],\n        createdAt: new Date().toISOString()\n      }\n    ],\n    videos: [\n      {\n        id: 'video-1',\n        title: 'Company Introduction',\n        type: 'company_intro',\n        url: '/videos/company-intro.mp4',\n        duration: 180,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    isLiveStreaming: false,\n    operatingLocations: ['Riyadh', 'Jeddah', 'Dammam'],\n    preferredWorkerCategories: ['Electrical Work', 'Plumbing', 'Construction'],\n    rating: 4.7,\n    totalReviews: 23,\n    isVerified: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }\n]\n\nexport function SuppliersProvider({ children }: { children: ReactNode }) {\n  const [suppliers, setSuppliers] = useState<SupplierProfile[]>([])\n  const [currentSupplierProfile, setCurrentSupplierProfile] = useState<SupplierProfile>()\n\n  useEffect(() => {\n    // Load suppliers from localStorage or use mock data\n    const savedSuppliers = localStorage.getItem('kaazmaamaa-suppliers')\n    if (savedSuppliers) {\n      setSuppliers(JSON.parse(savedSuppliers))\n    } else {\n      setSuppliers(mockSuppliers)\n      localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(mockSuppliers))\n    }\n  }, [])\n\n  const saveToStorage = (updatedSuppliers: SupplierProfile[]) => {\n    localStorage.setItem('kaazmaamaa-suppliers', JSON.stringify(updatedSuppliers))\n  }\n\n  const createSupplierProfile = (profile: Omit<SupplierProfile, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProfile: SupplierProfile = {\n      ...profile,\n      id: `supplier-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    const updatedSuppliers = [...suppliers, newProfile]\n    setSuppliers(updatedSuppliers)\n    saveToStorage(updatedSuppliers)\n    setCurrentSupplierProfile(newProfile)\n  }\n\n  const updateSupplierProfile = (id: string, updates: Partial<SupplierProfile>) => {\n    const updatedSuppliers = suppliers.map(supplier =>\n      supplier.id === id\n        ? { ...supplier, ...updates, updatedAt: new Date().toISOString() }\n        : supplier\n    )\n    setSuppliers(updatedSuppliers)\n    saveToStorage(updatedSuppliers)\n  }\n\n  const addDocument = (supplierId: string, document: Omit<SupplierDocument, 'id' | 'uploadedAt'>) => {\n    const newDocument: SupplierDocument = {\n      ...document,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateSupplierProfile(supplierId, {\n      documents: [...(suppliers.find(s => s.id === supplierId)?.documents || []), newDocument]\n    })\n  }\n\n  const addJobDemand = (supplierId: string, jobDemand: Omit<JobDemand, 'id' | 'createdAt'>) => {\n    const newJobDemand: JobDemand = {\n      ...jobDemand,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString()\n    }\n\n    updateSupplierProfile(supplierId, {\n      jobDemands: [...(suppliers.find(s => s.id === supplierId)?.jobDemands || []), newJobDemand]\n    })\n  }\n\n  const updateJobDemand = (supplierId: string, jobDemandId: string, updates: Partial<JobDemand>) => {\n    const supplier = suppliers.find(s => s.id === supplierId)\n    if (supplier) {\n      const updatedJobDemands = supplier.jobDemands.map(job =>\n        job.id === jobDemandId ? { ...job, ...updates } : job\n      )\n      updateSupplierProfile(supplierId, { jobDemands: updatedJobDemands })\n    }\n  }\n\n  const deleteJobDemand = (supplierId: string, jobDemandId: string) => {\n    const supplier = suppliers.find(s => s.id === supplierId)\n    if (supplier) {\n      const updatedJobDemands = supplier.jobDemands.filter(job => job.id !== jobDemandId)\n      updateSupplierProfile(supplierId, { jobDemands: updatedJobDemands })\n    }\n  }\n\n  const startLiveStream = (supplierId: string, streamUrl: string) => {\n    updateSupplierProfile(supplierId, {\n      isLiveStreaming: true,\n      liveStreamUrl: streamUrl\n    })\n  }\n\n  const stopLiveStream = (supplierId: string) => {\n    updateSupplierProfile(supplierId, {\n      isLiveStreaming: false,\n      liveStreamUrl: undefined\n    })\n  }\n\n  const getSuppliersByLocation = (location: string) => {\n    return suppliers.filter(supplier => \n      supplier.operatingLocations.some(loc => \n        loc.toLowerCase().includes(location.toLowerCase())\n      )\n    )\n  }\n\n  const getJobDemandsByCategory = (category: string) => {\n    const results: { supplier: SupplierProfile, jobDemand: JobDemand }[] = []\n    suppliers.forEach(supplier => {\n      supplier.jobDemands.forEach(jobDemand => {\n        if (jobDemand.category.toLowerCase().includes(category.toLowerCase())) {\n          results.push({ supplier, jobDemand })\n        }\n      })\n    })\n    return results\n  }\n\n  const validateLicenceUniqueness = (licenceNumber: string, excludeId?: string) => {\n    return !suppliers.some(supplier => \n      supplier.businessInfo.licenceNumber === licenceNumber && supplier.id !== excludeId\n    )\n  }\n\n  const value = {\n    suppliers,\n    currentSupplierProfile,\n    createSupplierProfile,\n    updateSupplierProfile,\n    addDocument,\n    addJobDemand,\n    updateJobDemand,\n    deleteJobDemand,\n    startLiveStream,\n    stopLiveStream,\n    getSuppliersByLocation,\n    getJobDemandsByCategory,\n    validateLicenceUniqueness\n  }\n\n  return (\n    <SuppliersContext.Provider value={value}>\n      {children}\n    </SuppliersContext.Provider>\n  )\n}\n\nexport function useSuppliers() {\n  const context = useContext(SuppliersContext)\n  if (context === undefined) {\n    throw new Error('useSuppliers must be used within a SuppliersProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuFA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAEzE,8BAA8B;AAC9B,MAAM,gBAAmC;IACvC;QACE,IAAI;QACJ,QAAQ;QACR,cAAc;YACZ,cAAc;YACd,aAAa;YACb,WAAW;YACX,YAAY;YACZ,SAAS;YACT,OAAO;YACP,UAAU;YACV,OAAO;QACT;QACA,cAAc;YACZ,aAAa;YACb,UAAU;YACV,eAAe;YACf,cAAc;YACd,iBAAiB;YACjB,eAAe;QACjB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,YAAY;YACV;gBACE,IAAI;gBACJ,UAAU;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;oBAAC;oBAAuB;oBAAsB;iBAAuB;gBACnF,eAAe;gBACf,UAAU;gBACV,iBAAiB;gBACjB,UAAU;gBACV,SAAS;gBACT,mBAAmB;oBAAC;oBAAS;oBAAsB;iBAAsB;gBACzE,WAAW,IAAI,OAAO,WAAW;YACnC;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,iBAAiB;QACjB,oBAAoB;YAAC;YAAU;YAAU;SAAS;QAClD,2BAA2B;YAAC;YAAmB;YAAY;SAAe;QAC1E,QAAQ;QACR,cAAc;QACd,YAAY;QACZ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAChE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAEnE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,aAAa,KAAK,KAAK,CAAC;QAC1B,OAAO;YACL,aAAa;YACb,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;QAC9D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC;QACrB,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;IAC9D;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,aAA8B;YAClC,GAAG,OAAO;YACV,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;YAC5B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,mBAAmB;eAAI;YAAW;SAAW;QACnD,aAAa;QACb,cAAc;QACd,0BAA0B;IAC5B;IAEA,MAAM,wBAAwB,CAAC,IAAY;QACzC,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAA,WACrC,SAAS,EAAE,KAAK,KACZ;gBAAE,GAAG,QAAQ;gBAAE,GAAG,OAAO;gBAAE,WAAW,IAAI,OAAO,WAAW;YAAG,IAC/D;QAEN,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,cAAc,CAAC,YAAoB;QACvC,MAAM,cAAgC;YACpC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,sBAAsB,YAAY;YAChC,WAAW;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,aAAa,EAAE;gBAAG;aAAY;QAC1F;IACF;IAEA,MAAM,eAAe,CAAC,YAAoB;QACxC,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,sBAAsB,YAAY;YAChC,YAAY;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,cAAc,EAAE;gBAAG;aAAa;QAC7F;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB,aAAqB;QAChE,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,IAAI,UAAU;YACZ,MAAM,oBAAoB,SAAS,UAAU,CAAC,GAAG,CAAC,CAAA,MAChD,IAAI,EAAE,KAAK,cAAc;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAEpD,sBAAsB,YAAY;gBAAE,YAAY;YAAkB;QACpE;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB;QAC3C,MAAM,WAAW,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,IAAI,UAAU;YACZ,MAAM,oBAAoB,SAAS,UAAU,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACvE,sBAAsB,YAAY;gBAAE,YAAY;YAAkB;QACpE;IACF;IAEA,MAAM,kBAAkB,CAAC,YAAoB;QAC3C,sBAAsB,YAAY;YAChC,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,sBAAsB,YAAY;YAChC,iBAAiB;YACjB,eAAe;QACjB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAO,UAAU,MAAM,CAAC,CAAA,WACtB,SAAS,kBAAkB,CAAC,IAAI,CAAC,CAAA,MAC/B,IAAI,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAGrD;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,UAAiE,EAAE;QACzE,UAAU,OAAO,CAAC,CAAA;YAChB,SAAS,UAAU,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,UAAU,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,KAAK;oBACrE,QAAQ,IAAI,CAAC;wBAAE;wBAAU;oBAAU;gBACrC;YACF;QACF;QACA,OAAO;IACT;IAEA,MAAM,4BAA4B,CAAC,eAAuB;QACxD,OAAO,CAAC,UAAU,IAAI,CAAC,CAAA,WACrB,SAAS,YAAY,CAAC,aAAa,KAAK,iBAAiB,SAAS,EAAE,KAAK;IAE7E;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1674, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/CompaniesContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface CompanyDocument {\n  id: string\n  name: string\n  type: 'licence' | 'cr' | 'tax_certificate' | 'establishment_card' | 'insurance' | 'contract' | 'other'\n  url: string\n  uploadedAt: string\n}\n\nexport interface JobOpening {\n  id: string\n  title: string\n  department: string\n  category: string\n  description: string\n  requirements: string[]\n  salaryRange: {\n    min: number\n    max: number\n    currency: 'SAR' | 'USD' | 'EUR'\n  }\n  employmentType: 'full_time' | 'part_time' | 'contract' | 'internship'\n  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive'\n  location: string\n  isRemote: boolean\n  benefits: string[]\n  applicationDeadline?: string\n  urgency: 'immediate' | 'within_week' | 'within_month'\n  documentsRequired: string[]\n  createdAt: string\n  applicationsCount: number\n}\n\nexport interface CompanyProfile {\n  id: string // Must be unique\n  userId: string\n  companyInfo: {\n    companyName: string\n    tradeName?: string\n    industry: string\n    companySize: '1-10' | '11-50' | '51-200' | '201-500' | '501-1000' | '1000+'\n    foundedYear: number\n    headquarters: string\n    website?: string\n    description: string\n    mission?: string\n    vision?: string\n  }\n  contactInfo: {\n    hrManagerName: string\n    hrEmail: string\n    hrPhone: string\n    hrWhatsapp: string\n    companyPhone: string\n    companyEmail: string\n    address: string\n    poBox?: string\n  }\n  legalInfo: {\n    crNumber: string // Commercial Registration Number\n    taxNumber: string\n    licenceNumber: string // Must be unique\n    establishmentNumber?: string\n    chamberMembership?: string\n  }\n  documents: CompanyDocument[]\n  jobOpenings: JobOpening[]\n  videos: {\n    id: string\n    title: string\n    type: 'company_intro' | 'workplace_culture' | 'job_opportunities' | 'employee_testimonials'\n    url: string\n    thumbnail?: string\n    duration?: number\n    uploadedAt: string\n  }[]\n  isLiveStreaming: boolean\n  liveStreamUrl?: string\n  operatingLocations: string[]\n  preferredWorkerCategories: string[]\n  companyBenefits: string[]\n  workEnvironment: {\n    isRemoteFriendly: boolean\n    hasFlexibleHours: boolean\n    providesTraining: boolean\n    hasCareerGrowth: boolean\n    workLifeBalance: number // 1-5 rating\n  }\n  rating: number\n  totalReviews: number\n  totalEmployees: number\n  isVerified: boolean\n  isHiring: boolean\n  createdAt: string\n  updatedAt: string\n}\n\ninterface CompaniesContextType {\n  companies: CompanyProfile[]\n  currentCompanyProfile?: CompanyProfile\n  createCompanyProfile: (profile: Omit<CompanyProfile, 'id' | 'createdAt' | 'updatedAt'>) => void\n  updateCompanyProfile: (id: string, updates: Partial<CompanyProfile>) => void\n  addDocument: (companyId: string, document: Omit<CompanyDocument, 'id' | 'uploadedAt'>) => void\n  addJobOpening: (companyId: string, jobOpening: Omit<JobOpening, 'id' | 'createdAt' | 'applicationsCount'>) => void\n  updateJobOpening: (companyId: string, jobOpeningId: string, updates: Partial<JobOpening>) => void\n  deleteJobOpening: (companyId: string, jobOpeningId: string) => void\n  startLiveStream: (companyId: string, streamUrl: string) => void\n  stopLiveStream: (companyId: string) => void\n  getCompaniesByLocation: (location: string) => CompanyProfile[]\n  getCompaniesByIndustry: (industry: string) => CompanyProfile[]\n  getJobOpeningsByCategory: (category: string) => { company: CompanyProfile, jobOpening: JobOpening }[]\n  getHiringCompanies: () => CompanyProfile[]\n  validateLicenceUniqueness: (licenceNumber: string, excludeId?: string) => boolean\n}\n\nconst CompaniesContext = createContext<CompaniesContextType | undefined>(undefined)\n\n// Mock data for demonstration\nconst mockCompanies: CompanyProfile[] = [\n  {\n    id: 'company-1',\n    userId: 'user-company-1',\n    companyInfo: {\n      companyName: 'Saudi Tech Solutions',\n      tradeName: 'STS',\n      industry: 'Information Technology',\n      companySize: '201-500',\n      foundedYear: 2010,\n      headquarters: 'Riyadh, Saudi Arabia',\n      website: 'https://sauditechsolutions.com',\n      description: 'Leading technology solutions provider in the Kingdom of Saudi Arabia',\n      mission: 'To provide innovative technology solutions that drive digital transformation',\n      vision: 'To be the leading technology partner in the Middle East'\n    },\n    contactInfo: {\n      hrManagerName: 'Sarah Al-Mahmoud',\n      hrEmail: '<EMAIL>',\n      hrPhone: '+966112345678',\n      hrWhatsapp: '+966501234567',\n      companyPhone: '+966112345678',\n      companyEmail: '<EMAIL>',\n      address: 'King Fahd Road, Olaya District, Riyadh 12345',\n      poBox: 'P.O. Box 12345, Riyadh 11564'\n    },\n    legalInfo: {\n      crNumber: 'CR-**********',\n      taxNumber: 'TAX-300123456789003',\n      licenceNumber: 'LIC-TECH-2024-001',\n      establishmentNumber: 'EST-*********',\n      chamberMembership: 'CHAMBER-RUH-2024-456'\n    },\n    documents: [\n      {\n        id: 'doc-1',\n        name: 'Commercial Registration',\n        type: 'cr',\n        url: '/documents/company-cr.pdf',\n        uploadedAt: new Date().toISOString()\n      },\n      {\n        id: 'doc-2',\n        name: 'Business License',\n        type: 'licence',\n        url: '/documents/company-license.pdf',\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    jobOpenings: [\n      {\n        id: 'job-1',\n        title: 'Senior Software Engineer',\n        department: 'Engineering',\n        category: 'Software Development',\n        description: 'We are looking for a senior software engineer to join our growing team',\n        requirements: ['5+ years experience', 'React/Node.js', 'AWS knowledge', 'Team leadership'],\n        salaryRange: {\n          min: 15000,\n          max: 25000,\n          currency: 'SAR'\n        },\n        employmentType: 'full_time',\n        experienceLevel: 'senior',\n        location: 'Riyadh',\n        isRemote: false,\n        benefits: ['Health Insurance', 'Annual Bonus', 'Training Budget', 'Flexible Hours'],\n        urgency: 'immediate',\n        documentsRequired: ['CV', 'Portfolio', 'Certificates'],\n        createdAt: new Date().toISOString(),\n        applicationsCount: 23\n      }\n    ],\n    videos: [\n      {\n        id: 'video-1',\n        title: 'Company Culture Overview',\n        type: 'company_intro',\n        url: '/videos/company-culture.mp4',\n        duration: 240,\n        uploadedAt: new Date().toISOString()\n      }\n    ],\n    isLiveStreaming: false,\n    operatingLocations: ['Riyadh', 'Jeddah', 'Dammam'],\n    preferredWorkerCategories: ['Software Development', 'Data Analysis', 'Project Management'],\n    companyBenefits: ['Health Insurance', 'Annual Bonus', 'Training Programs', 'Career Development'],\n    workEnvironment: {\n      isRemoteFriendly: true,\n      hasFlexibleHours: true,\n      providesTraining: true,\n      hasCareerGrowth: true,\n      workLifeBalance: 4\n    },\n    rating: 4.6,\n    totalReviews: 87,\n    totalEmployees: 350,\n    isVerified: true,\n    isHiring: true,\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString()\n  }\n]\n\nexport function CompaniesProvider({ children }: { children: ReactNode }) {\n  const [companies, setCompanies] = useState<CompanyProfile[]>([])\n  const [currentCompanyProfile, setCurrentCompanyProfile] = useState<CompanyProfile>()\n\n  useEffect(() => {\n    // Load companies from localStorage or use mock data\n    const savedCompanies = localStorage.getItem('kaazmaamaa-companies')\n    if (savedCompanies) {\n      setCompanies(JSON.parse(savedCompanies))\n    } else {\n      setCompanies(mockCompanies)\n      localStorage.setItem('kaazmaamaa-companies', JSON.stringify(mockCompanies))\n    }\n  }, [])\n\n  const saveToStorage = useCallback((updatedCompanies: CompanyProfile[]) => {\n    localStorage.setItem('kaazmaamaa-companies', JSON.stringify(updatedCompanies))\n  }, [])\n\n  const createCompanyProfile = useCallback((profile: Omit<CompanyProfile, 'id' | 'createdAt' | 'updatedAt'>) => {\n    const newProfile: CompanyProfile = {\n      ...profile,\n      id: `company-${Date.now()}`,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n\n    const updatedCompanies = [...companies, newProfile]\n    setCompanies(updatedCompanies)\n    saveToStorage(updatedCompanies)\n    setCurrentCompanyProfile(newProfile)\n  }, [companies, saveToStorage])\n\n  const updateCompanyProfile = useCallback((id: string, updates: Partial<CompanyProfile>) => {\n    const updatedCompanies = companies.map(company =>\n      company.id === id\n        ? { ...company, ...updates, updatedAt: new Date().toISOString() }\n        : company\n    )\n    setCompanies(updatedCompanies)\n    saveToStorage(updatedCompanies)\n  }, [companies, saveToStorage])\n\n  const addDocument = useCallback((companyId: string, document: Omit<CompanyDocument, 'id' | 'uploadedAt'>) => {\n    const newDocument: CompanyDocument = {\n      ...document,\n      id: Date.now().toString(),\n      uploadedAt: new Date().toISOString()\n    }\n\n    updateCompanyProfile(companyId, {\n      documents: [...(companies.find(c => c.id === companyId)?.documents || []), newDocument]\n    })\n  }, [companies, updateCompanyProfile])\n\n  const addJobOpening = useCallback((companyId: string, jobOpening: Omit<JobOpening, 'id' | 'createdAt' | 'applicationsCount'>) => {\n    const newJobOpening: JobOpening = {\n      ...jobOpening,\n      id: Date.now().toString(),\n      createdAt: new Date().toISOString(),\n      applicationsCount: 0\n    }\n\n    updateCompanyProfile(companyId, {\n      jobOpenings: [...(companies.find(c => c.id === companyId)?.jobOpenings || []), newJobOpening]\n    })\n  }, [companies, updateCompanyProfile])\n\n  const updateJobOpening = useCallback((companyId: string, jobOpeningId: string, updates: Partial<JobOpening>) => {\n    const company = companies.find(c => c.id === companyId)\n    if (company) {\n      const updatedJobOpenings = company.jobOpenings.map(job =>\n        job.id === jobOpeningId ? { ...job, ...updates } : job\n      )\n      updateCompanyProfile(companyId, { jobOpenings: updatedJobOpenings })\n    }\n  }, [companies, updateCompanyProfile])\n\n  const deleteJobOpening = useCallback((companyId: string, jobOpeningId: string) => {\n    const company = companies.find(c => c.id === companyId)\n    if (company) {\n      const updatedJobOpenings = company.jobOpenings.filter(job => job.id !== jobOpeningId)\n      updateCompanyProfile(companyId, { jobOpenings: updatedJobOpenings })\n    }\n  }, [companies, updateCompanyProfile])\n\n  const startLiveStream = useCallback((companyId: string, streamUrl: string) => {\n    updateCompanyProfile(companyId, {\n      isLiveStreaming: true,\n      liveStreamUrl: streamUrl\n    })\n  }, [updateCompanyProfile])\n\n  const stopLiveStream = useCallback((companyId: string) => {\n    updateCompanyProfile(companyId, {\n      isLiveStreaming: false,\n      liveStreamUrl: undefined\n    })\n  }, [updateCompanyProfile])\n\n  const getCompaniesByLocation = useCallback((location: string) => {\n    return companies.filter(company => \n      company.operatingLocations.some(loc => \n        loc.toLowerCase().includes(location.toLowerCase())\n      )\n    )\n  }, [companies])\n\n  const getCompaniesByIndustry = useCallback((industry: string) => {\n    return companies.filter(company => \n      company.companyInfo.industry.toLowerCase().includes(industry.toLowerCase())\n    )\n  }, [companies])\n\n  const getJobOpeningsByCategory = useCallback((category: string) => {\n    const results: { company: CompanyProfile, jobOpening: JobOpening }[] = []\n    companies.forEach(company => {\n      company.jobOpenings.forEach(jobOpening => {\n        if (jobOpening.category.toLowerCase().includes(category.toLowerCase())) {\n          results.push({ company, jobOpening })\n        }\n      })\n    })\n    return results\n  }, [companies])\n\n  const getHiringCompanies = useCallback(() => {\n    return companies.filter(company => company.isHiring && company.jobOpenings.length > 0)\n  }, [companies])\n\n  const validateLicenceUniqueness = useCallback((licenceNumber: string, excludeId?: string) => {\n    return !companies.some(company => \n      company.legalInfo.licenceNumber === licenceNumber && company.id !== excludeId\n    )\n  }, [companies])\n\n  const value = {\n    companies,\n    currentCompanyProfile,\n    createCompanyProfile,\n    updateCompanyProfile,\n    addDocument,\n    addJobOpening,\n    updateJobOpening,\n    deleteJobOpening,\n    startLiveStream,\n    stopLiveStream,\n    getCompaniesByLocation,\n    getCompaniesByIndustry,\n    getJobOpeningsByCategory,\n    getHiringCompanies,\n    validateLicenceUniqueness\n  }\n\n  return (\n    <CompaniesContext.Provider value={value}>\n      {children}\n    </CompaniesContext.Provider>\n  )\n}\n\nexport function useCompanies() {\n  const context = useContext(CompaniesContext)\n  if (context === undefined) {\n    throw new Error('useCompanies must be used within a CompaniesProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAsHA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAEzE,8BAA8B;AAC9B,MAAM,gBAAkC;IACtC;QACE,IAAI;QACJ,QAAQ;QACR,aAAa;YACX,aAAa;YACb,WAAW;YACX,UAAU;YACV,aAAa;YACb,aAAa;YACb,cAAc;YACd,SAAS;YACT,aAAa;YACb,SAAS;YACT,QAAQ;QACV;QACA,aAAa;YACX,eAAe;YACf,SAAS;YACT,SAAS;YACT,YAAY;YACZ,cAAc;YACd,cAAc;YACd,SAAS;YACT,OAAO;QACT;QACA,WAAW;YACT,UAAU;YACV,WAAW;YACX,eAAe;YACf,qBAAqB;YACrB,mBAAmB;QACrB;QACA,WAAW;YACT;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,KAAK;gBACL,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,aAAa;YACX;gBACE,IAAI;gBACJ,OAAO;gBACP,YAAY;gBACZ,UAAU;gBACV,aAAa;gBACb,cAAc;oBAAC;oBAAuB;oBAAiB;oBAAiB;iBAAkB;gBAC1F,aAAa;oBACX,KAAK;oBACL,KAAK;oBACL,UAAU;gBACZ;gBACA,gBAAgB;gBAChB,iBAAiB;gBACjB,UAAU;gBACV,UAAU;gBACV,UAAU;oBAAC;oBAAoB;oBAAgB;oBAAmB;iBAAiB;gBACnF,SAAS;gBACT,mBAAmB;oBAAC;oBAAM;oBAAa;iBAAe;gBACtD,WAAW,IAAI,OAAO,WAAW;gBACjC,mBAAmB;YACrB;SACD;QACD,QAAQ;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,UAAU;gBACV,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,iBAAiB;QACjB,oBAAoB;YAAC;YAAU;YAAU;SAAS;QAClD,2BAA2B;YAAC;YAAwB;YAAiB;SAAqB;QAC1F,iBAAiB;YAAC;YAAoB;YAAgB;YAAqB;SAAqB;QAChG,iBAAiB;YACf,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;YACjB,iBAAiB;QACnB;QACA,QAAQ;QACR,cAAc;QACd,gBAAgB;QAChB,YAAY;QACZ,UAAU;QACV,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;CACD;AAEM,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC/D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAEjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oDAAoD;QACpD,MAAM,iBAAiB,aAAa,OAAO,CAAC;QAC5C,IAAI,gBAAgB;YAClB,aAAa,KAAK,KAAK,CAAC;QAC1B,OAAO;YACL,aAAa;YACb,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;QAC9D;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,aAAa,OAAO,CAAC,wBAAwB,KAAK,SAAS,CAAC;IAC9D,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,MAAM,aAA6B;YACjC,GAAG,OAAO;YACV,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,MAAM,mBAAmB;eAAI;YAAW;SAAW;QACnD,aAAa;QACb,cAAc;QACd,yBAAyB;IAC3B,GAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAY;QACpD,MAAM,mBAAmB,UAAU,GAAG,CAAC,CAAA,UACrC,QAAQ,EAAE,KAAK,KACX;gBAAE,GAAG,OAAO;gBAAE,GAAG,OAAO;gBAAE,WAAW,IAAI,OAAO,WAAW;YAAG,IAC9D;QAEN,aAAa;QACb,cAAc;IAChB,GAAG;QAAC;QAAW;KAAc;IAE7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QAClD,MAAM,cAA+B;YACnC,GAAG,QAAQ;YACX,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,qBAAqB,WAAW;YAC9B,WAAW;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,aAAa,EAAE;gBAAG;aAAY;QACzF;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACpD,MAAM,gBAA4B;YAChC,GAAG,UAAU;YACb,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,mBAAmB;QACrB;QAEA,qBAAqB,WAAW;YAC9B,aAAa;mBAAK,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,YAAY,eAAe,EAAE;gBAAG;aAAc;QAC/F;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB,cAAsB;QAC7E,MAAM,UAAU,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,SAAS;YACX,MAAM,qBAAqB,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAA,MACjD,IAAI,EAAE,KAAK,eAAe;oBAAE,GAAG,GAAG;oBAAE,GAAG,OAAO;gBAAC,IAAI;YAErD,qBAAqB,WAAW;gBAAE,aAAa;YAAmB;QACpE;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACvD,MAAM,UAAU,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC7C,IAAI,SAAS;YACX,MAAM,qBAAqB,QAAQ,WAAW,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YACxE,qBAAqB,WAAW;gBAAE,aAAa;YAAmB;QACpE;IACF,GAAG;QAAC;QAAW;KAAqB;IAEpC,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QACtD,qBAAqB,WAAW;YAC9B,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAqB;IAEzB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,qBAAqB,WAAW;YAC9B,iBAAiB;YACjB,eAAe;QACjB;IACF,GAAG;QAAC;KAAqB;IAEzB,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,OAAO,UAAU,MAAM,CAAC,CAAA,UACtB,QAAQ,kBAAkB,CAAC,IAAI,CAAC,CAAA,MAC9B,IAAI,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAGrD,GAAG;QAAC;KAAU;IAEd,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC1C,OAAO,UAAU,MAAM,CAAC,CAAA,UACtB,QAAQ,WAAW,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW;IAE5E,GAAG;QAAC;KAAU;IAEd,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5C,MAAM,UAAiE,EAAE;QACzE,UAAU,OAAO,CAAC,CAAA;YAChB,QAAQ,WAAW,CAAC,OAAO,CAAC,CAAA;gBAC1B,IAAI,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,WAAW,KAAK;oBACtE,QAAQ,IAAI,CAAC;wBAAE;wBAAS;oBAAW;gBACrC;YACF;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAU;IAEd,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,UAAU,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG;IACtF,GAAG;QAAC;KAAU;IAEd,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,eAAuB;QACpE,OAAO,CAAC,UAAU,IAAI,CAAC,CAAA,UACrB,QAAQ,SAAS,CAAC,aAAa,KAAK,iBAAiB,QAAQ,EAAE,KAAK;IAExE,GAAG;QAAC;KAAU;IAEd,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/SocialContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface UserConnection {\n  userId: string\n  userName: string\n  userRole: string\n  connectedAt: string\n  isFollowing: boolean\n  isFollowedBy: boolean\n}\n\nexport interface ProfileInteraction {\n  userId: string\n  profileViews: number\n  lastViewedAt: string\n  totalLikes: number\n  totalComments: number\n  totalShares: number\n}\n\ninterface SocialContextType {\n  connections: UserConnection[]\n  profileInteractions: { [userId: string]: ProfileInteraction }\n  followUser: (userId: string, userName: string, userRole: string) => void\n  unfollowUser: (userId: string) => void\n  isFollowing: (userId: string) => boolean\n  getFollowers: (userId: string) => UserConnection[]\n  getFollowing: (userId: string) => UserConnection[]\n  recordProfileView: (userId: string) => void\n  getProfileStats: (userId: string) => ProfileInteraction\n  updateProfileInteraction: (userId: string, type: 'like' | 'comment' | 'share') => void\n}\n\nconst SocialContext = createContext<SocialContextType | undefined>(undefined)\n\nexport function SocialProvider({ children }: { children: ReactNode }) {\n  const [connections, setConnections] = useState<UserConnection[]>([])\n  const [profileInteractions, setProfileInteractions] = useState<{ [userId: string]: ProfileInteraction }>({})\n\n  useEffect(() => {\n    // Load social data from localStorage\n    const savedConnections = localStorage.getItem('kaazmaamaa-connections')\n    const savedInteractions = localStorage.getItem('kaazmaamaa-profile-interactions')\n    \n    if (savedConnections) {\n      setConnections(JSON.parse(savedConnections))\n    }\n    \n    if (savedInteractions) {\n      setProfileInteractions(JSON.parse(savedInteractions))\n    }\n  }, [])\n\n  const saveConnections = useCallback((updatedConnections: UserConnection[]) => {\n    localStorage.setItem('kaazmaamaa-connections', JSON.stringify(updatedConnections))\n  }, [])\n\n  const saveInteractions = useCallback((updatedInteractions: { [userId: string]: ProfileInteraction }) => {\n    localStorage.setItem('kaazmaamaa-profile-interactions', JSON.stringify(updatedInteractions))\n  }, [])\n\n  const followUser = useCallback((userId: string, userName: string, userRole: string) => {\n    const newConnection: UserConnection = {\n      userId,\n      userName,\n      userRole,\n      connectedAt: new Date().toISOString(),\n      isFollowing: true,\n      isFollowedBy: false\n    }\n\n    const updatedConnections = [...connections.filter(c => c.userId !== userId), newConnection]\n    setConnections(updatedConnections)\n    saveConnections(updatedConnections)\n  }, [connections, saveConnections])\n\n  const unfollowUser = useCallback((userId: string) => {\n    const updatedConnections = connections.filter(c => c.userId !== userId)\n    setConnections(updatedConnections)\n    saveConnections(updatedConnections)\n  }, [connections, saveConnections])\n\n  const isFollowing = useCallback((userId: string) => {\n    return connections.some(c => c.userId === userId && c.isFollowing)\n  }, [connections])\n\n  const getFollowers = useCallback((userId: string) => {\n    return connections.filter(c => c.userId === userId && c.isFollowedBy)\n  }, [connections])\n\n  const getFollowing = useCallback((userId: string) => {\n    return connections.filter(c => c.isFollowing)\n  }, [connections])\n\n  const recordProfileView = useCallback((userId: string) => {\n    setProfileInteractions(prev => {\n      const current = prev[userId] || {\n        userId,\n        profileViews: 0,\n        lastViewedAt: new Date().toISOString(),\n        totalLikes: 0,\n        totalComments: 0,\n        totalShares: 0\n      }\n\n      const updated = {\n        ...prev,\n        [userId]: {\n          ...current,\n          profileViews: current.profileViews + 1,\n          lastViewedAt: new Date().toISOString()\n        }\n      }\n\n      saveInteractions(updated)\n      return updated\n    })\n  }, [saveInteractions])\n\n  const getProfileStats = useCallback((userId: string): ProfileInteraction => {\n    return profileInteractions[userId] || {\n      userId,\n      profileViews: 0,\n      lastViewedAt: new Date().toISOString(),\n      totalLikes: 0,\n      totalComments: 0,\n      totalShares: 0\n    }\n  }, [profileInteractions])\n\n  const updateProfileInteraction = useCallback((userId: string, type: 'like' | 'comment' | 'share') => {\n    setProfileInteractions(prev => {\n      const current = prev[userId] || {\n        userId,\n        profileViews: 0,\n        lastViewedAt: new Date().toISOString(),\n        totalLikes: 0,\n        totalComments: 0,\n        totalShares: 0\n      }\n\n      const updated = {\n        ...prev,\n        [userId]: {\n          ...current,\n          [`total${type.charAt(0).toUpperCase() + type.slice(1)}s`]: \n            current[`total${type.charAt(0).toUpperCase() + type.slice(1)}s` as keyof ProfileInteraction] as number + 1\n        }\n      }\n\n      saveInteractions(updated)\n      return updated\n    })\n  }, [saveInteractions])\n\n  const value = {\n    connections,\n    profileInteractions,\n    followUser,\n    unfollowUser,\n    isFollowing,\n    getFollowers,\n    getFollowing,\n    recordProfileView,\n    getProfileStats,\n    updateProfileInteraction\n  }\n\n  return (\n    <SocialContext.Provider value={value}>\n      {children}\n    </SocialContext.Provider>\n  )\n}\n\nexport function useSocial() {\n  const context = useContext(SocialContext)\n  if (context === undefined) {\n    throw new Error('useSocial must be used within a SocialProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAmCA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAiC;AAE5D,SAAS,eAAe,EAAE,QAAQ,EAA2B;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C,CAAC;IAE1G,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,MAAM,oBAAoB,aAAa,OAAO,CAAC;QAE/C,IAAI,kBAAkB;YACpB,eAAe,KAAK,KAAK,CAAC;QAC5B;QAEA,IAAI,mBAAmB;YACrB,uBAAuB,KAAK,KAAK,CAAC;QACpC;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;IAChE,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,aAAa,OAAO,CAAC,mCAAmC,KAAK,SAAS,CAAC;IACzE,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB,UAAkB;QAChE,MAAM,gBAAgC;YACpC;YACA;YACA;YACA,aAAa,IAAI,OAAO,WAAW;YACnC,aAAa;YACb,cAAc;QAChB;QAEA,MAAM,qBAAqB;eAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;YAAS;SAAc;QAC3F,eAAe;QACf,gBAAgB;IAClB,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,MAAM,qBAAqB,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAChE,eAAe;QACf,gBAAgB;IAClB,GAAG;QAAC;QAAa;KAAgB;IAEjC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,OAAO,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,WAAW;IACnE,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,YAAY;IACtE,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW;IAC9C,GAAG;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACrC,uBAAuB,CAAA;YACrB,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI;gBAC9B;gBACA,cAAc;gBACd,cAAc,IAAI,OAAO,WAAW;gBACpC,YAAY;gBACZ,eAAe;gBACf,aAAa;YACf;YAEA,MAAM,UAAU;gBACd,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,OAAO;oBACV,cAAc,QAAQ,YAAY,GAAG;oBACrC,cAAc,IAAI,OAAO,WAAW;gBACtC;YACF;YAEA,iBAAiB;YACjB,OAAO;QACT;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,OAAO,mBAAmB,CAAC,OAAO,IAAI;YACpC;YACA,cAAc;YACd,cAAc,IAAI,OAAO,WAAW;YACpC,YAAY;YACZ,eAAe;YACf,aAAa;QACf;IACF,GAAG;QAAC;KAAoB;IAExB,MAAM,2BAA2B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB;QAC5D,uBAAuB,CAAA;YACrB,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI;gBAC9B;gBACA,cAAc;gBACd,cAAc,IAAI,OAAO,WAAW;gBACpC,YAAY;gBACZ,eAAe;gBACf,aAAa;YACf;YAEA,MAAM,UAAU;gBACd,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,OAAO;oBACV,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EACvD,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,CAA6B,GAAa;gBAC7G;YACF;YAEA,iBAAiB;YACjB,OAAO;QACT;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,cAAc,QAAQ;QAAC,OAAO;kBAC5B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/PaymentContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'\n\nexport interface PaymentTransaction {\n  id: string\n  userId: string\n  profileId: string\n  profileType: 'worker' | 'supplier' | 'company'\n  amount: number\n  currency: 'SAR'\n  purpose: 'contact_access' | 'document_download' | 'premium_access'\n  status: 'pending' | 'completed' | 'failed' | 'cancelled'\n  paymentMethod: 'mada' | 'visa' | 'mastercard' | 'stc_pay' | 'apple_pay'\n  transactionId?: string\n  createdAt: string\n  completedAt?: string\n  expiresAt: string\n}\n\nexport interface AccessPermission {\n  userId: string\n  profileId: string\n  profileType: 'worker' | 'supplier' | 'company'\n  hasContactAccess: boolean\n  hasDocumentAccess: boolean\n  purchasedAt: string\n  expiresAt: string\n  transactionId: string\n}\n\ninterface PaymentContextType {\n  transactions: PaymentTransaction[]\n  permissions: AccessPermission[]\n  initiatePayment: (profileId: string, profileType: 'worker' | 'supplier' | 'company', purpose: 'contact_access' | 'document_download' | 'premium_access') => Promise<string>\n  processPayment: (transactionId: string, paymentMethod: string, paymentDetails: any) => Promise<boolean>\n  hasAccess: (profileId: string, accessType: 'contact' | 'documents') => boolean\n  getTransactionStatus: (transactionId: string) => PaymentTransaction | null\n  getUserTransactions: () => PaymentTransaction[]\n  getUserPermissions: () => AccessPermission[]\n}\n\nconst PaymentContext = createContext<PaymentContextType | undefined>(undefined)\n\n// Saudi Arabian Payment Gateway Configuration\nconst PAYMENT_CONFIG: Record<'contact_access' | 'document_download' | 'premium_access', {\n  amount: number\n  currency: 'SAR'\n  validityDays: number\n}> = {\n  contact_access: {\n    amount: 25, // 25 SAR for contact information access\n    currency: 'SAR' as const,\n    validityDays: 30\n  },\n  document_download: {\n    amount: 50, // 50 SAR for document download access\n    currency: 'SAR' as const,\n    validityDays: 90\n  },\n  premium_access: {\n    amount: 75, // 75 SAR for full premium access (contact + documents)\n    currency: 'SAR' as const,\n    validityDays: 90\n  }\n}\n\nexport function PaymentProvider({ children }: { children: ReactNode }) {\n  const [transactions, setTransactions] = useState<PaymentTransaction[]>([])\n  const [permissions, setPermissions] = useState<AccessPermission[]>([])\n  const [currentUserId, setCurrentUserId] = useState<string>('')\n\n  useEffect(() => {\n    // Load payment data from localStorage (client-side only)\n    if (typeof window !== 'undefined') {\n      const savedTransactions = localStorage.getItem('kaazmaamaa-transactions')\n      const savedPermissions = localStorage.getItem('kaazmaamaa-permissions')\n      const userId = localStorage.getItem('kaazmaamaa-current-user') || 'current-user'\n\n      setCurrentUserId(userId)\n\n      if (savedTransactions) {\n        setTransactions(JSON.parse(savedTransactions))\n      }\n\n      if (savedPermissions) {\n        setPermissions(JSON.parse(savedPermissions))\n      }\n    }\n  }, [])\n\n  const saveToStorage = useCallback(() => {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('kaazmaamaa-transactions', JSON.stringify(transactions))\n      localStorage.setItem('kaazmaamaa-permissions', JSON.stringify(permissions))\n    }\n  }, [transactions, permissions])\n\n  useEffect(() => {\n    saveToStorage()\n  }, [transactions, permissions, saveToStorage])\n\n  const initiatePayment = useCallback(async (\n    profileId: string,\n    profileType: 'worker' | 'supplier' | 'company',\n    purpose: 'contact_access' | 'document_download' | 'premium_access'\n  ): Promise<string> => {\n    try {\n      if (!profileId || !profileType || !purpose) {\n        throw new Error('Missing required payment parameters')\n      }\n\n      const config = PAYMENT_CONFIG[purpose]\n      if (!config) {\n        throw new Error(`Invalid payment purpose: ${purpose}`)\n      }\n\n      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`\n\n      const transaction: PaymentTransaction = {\n        id: transactionId,\n        userId: currentUserId,\n        profileId,\n        profileType,\n        amount: config.amount,\n        currency: config.currency,\n        purpose,\n        status: 'pending',\n        paymentMethod: 'mada', // Default to MADA\n        createdAt: new Date().toISOString(),\n        expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes expiry\n      }\n\n      setTransactions(prev => [...prev, transaction])\n      return transactionId\n    } catch (error) {\n      console.error('Error initiating payment:', error)\n      throw error\n    }\n  }, [currentUserId])\n\n  const processPayment = useCallback(async (\n    transactionId: string,\n    paymentMethod: string,\n    paymentDetails: any\n  ): Promise<boolean> => {\n    // Simulate Saudi payment gateway processing\n    return new Promise((resolve) => {\n      setTimeout(() => {\n        const transaction = transactions.find(t => t.id === transactionId)\n        if (!transaction) {\n          resolve(false)\n          return\n        }\n\n        // Simulate payment success (90% success rate)\n        const isSuccess = Math.random() > 0.1\n\n        if (isSuccess) {\n          // Update transaction status\n          setTransactions(prev => prev.map(t =>\n            t.id === transactionId\n              ? {\n                  ...t,\n                  status: 'completed' as const,\n                  paymentMethod: paymentMethod as any,\n                  completedAt: new Date().toISOString(),\n                  transactionId: `saudi_${Date.now()}`\n                }\n              : t\n          ))\n\n          // Grant access permissions\n          const config = PAYMENT_CONFIG[transaction.purpose]\n          if (!config) {\n            console.error('Invalid payment purpose in processPayment:', transaction.purpose)\n            resolve(false)\n            return\n          }\n\n          const expiryDate = new Date()\n          expiryDate.setDate(expiryDate.getDate() + config.validityDays)\n\n          const permission: AccessPermission = {\n            userId: currentUserId,\n            profileId: transaction.profileId,\n            profileType: transaction.profileType,\n            hasContactAccess: transaction.purpose === 'contact_access' || transaction.purpose === 'premium_access',\n            hasDocumentAccess: transaction.purpose === 'document_download' || transaction.purpose === 'premium_access',\n            purchasedAt: new Date().toISOString(),\n            expiresAt: expiryDate.toISOString(),\n            transactionId\n          }\n\n          setPermissions(prev => {\n            // Remove any existing permission for the same profile\n            const filtered = prev.filter(p => !(p.profileId === transaction.profileId && p.userId === currentUserId))\n            return [...filtered, permission]\n          })\n\n          resolve(true)\n        } else {\n          // Update transaction as failed\n          setTransactions(prev => prev.map(t =>\n            t.id === transactionId\n              ? { ...t, status: 'failed' as const }\n              : t\n          ))\n          resolve(false)\n        }\n      }, 2000) // Simulate 2 second processing time\n    })\n  }, [transactions, currentUserId])\n\n  const hasAccess = useCallback((profileId: string, accessType: 'contact' | 'documents'): boolean => {\n    const permission = permissions.find(p =>\n      p.profileId === profileId &&\n      p.userId === currentUserId &&\n      new Date(p.expiresAt) > new Date()\n    )\n\n    if (!permission) return false\n\n    return accessType === 'contact' ? permission.hasContactAccess : permission.hasDocumentAccess\n  }, [permissions, currentUserId])\n\n  const getTransactionStatus = useCallback((transactionId: string): PaymentTransaction | null => {\n    return transactions.find(t => t.id === transactionId) || null\n  }, [transactions])\n\n  const getUserTransactions = useCallback((): PaymentTransaction[] => {\n    return transactions.filter(t => t.userId === currentUserId)\n  }, [transactions, currentUserId])\n\n  const getUserPermissions = useCallback((): AccessPermission[] => {\n    return permissions.filter(p => p.userId === currentUserId && new Date(p.expiresAt) > new Date())\n  }, [permissions, currentUserId])\n\n  const value = {\n    transactions,\n    permissions,\n    initiatePayment,\n    processPayment,\n    hasAccess,\n    getTransactionStatus,\n    getUserTransactions,\n    getUserPermissions\n  }\n\n  return (\n    <PaymentContext.Provider value={value}>\n      {children}\n    </PaymentContext.Provider>\n  )\n}\n\nexport function usePayment() {\n  const context = useContext(PaymentContext)\n  if (context === undefined) {\n    throw new Error('usePayment must be used within a PaymentProvider')\n  }\n  return context\n}\n\n// Saudi Arabian Payment Methods\nexport const SAUDI_PAYMENT_METHODS = [\n  {\n    id: 'mada',\n    name: 'MADA',\n    icon: '💳',\n    description: 'Saudi national payment system'\n  },\n  {\n    id: 'visa',\n    name: 'Visa',\n    icon: '💳',\n    description: 'Visa credit/debit cards'\n  },\n  {\n    id: 'mastercard',\n    name: 'Mastercard',\n    icon: '💳',\n    description: 'Mastercard credit/debit cards'\n  },\n  {\n    id: 'stc_pay',\n    name: 'STC Pay',\n    icon: '📱',\n    description: 'STC digital wallet'\n  },\n  {\n    id: 'apple_pay',\n    name: 'Apple Pay',\n    icon: '🍎',\n    description: 'Apple Pay digital wallet'\n  }\n]\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AA0CA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAErE,8CAA8C;AAC9C,MAAM,iBAID;IACH,gBAAgB;QACd,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,mBAAmB;QACjB,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;IACA,gBAAgB;QACd,QAAQ;QACR,UAAU;QACV,cAAc;IAChB;AACF;AAEO,SAAS,gBAAgB,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB,EAAE;IACzE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yDAAyD;QACzD,uCAAmC;;QAcnC;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,uCAAmC;;QAGnC;IACF,GAAG;QAAC;QAAc;KAAY;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;QAAa;KAAc;IAE7C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAClC,WACA,aACA;QAEA,IAAI;YACF,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,SAAS;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,SAAS,cAAc,CAAC,QAAQ;YACtC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS;YACvD;YAEA,MAAM,gBAAgB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;YAEpF,MAAM,cAAkC;gBACtC,IAAI;gBACJ,QAAQ;gBACR;gBACA;gBACA,QAAQ,OAAO,MAAM;gBACrB,UAAU,OAAO,QAAQ;gBACzB;gBACA,QAAQ;gBACR,eAAe;gBACf,WAAW,IAAI,OAAO,WAAW;gBACjC,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW,GAAG,oBAAoB;YACrF;YAEA,gBAAgB,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;YAC9C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,eACA,eACA;QAEA,4CAA4C;QAC5C,OAAO,IAAI,QAAQ,CAAC;YAClB,WAAW;gBACT,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACpD,IAAI,CAAC,aAAa;oBAChB,QAAQ;oBACR;gBACF;gBAEA,8CAA8C;gBAC9C,MAAM,YAAY,KAAK,MAAM,KAAK;gBAElC,IAAI,WAAW;oBACb,4BAA4B;oBAC5B,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,gBACL;gCACE,GAAG,CAAC;gCACJ,QAAQ;gCACR,eAAe;gCACf,aAAa,IAAI,OAAO,WAAW;gCACnC,eAAe,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;4BACtC,IACA;oBAGN,2BAA2B;oBAC3B,MAAM,SAAS,cAAc,CAAC,YAAY,OAAO,CAAC;oBAClD,IAAI,CAAC,QAAQ;wBACX,QAAQ,KAAK,CAAC,8CAA8C,YAAY,OAAO;wBAC/E,QAAQ;wBACR;oBACF;oBAEA,MAAM,aAAa,IAAI;oBACvB,WAAW,OAAO,CAAC,WAAW,OAAO,KAAK,OAAO,YAAY;oBAE7D,MAAM,aAA+B;wBACnC,QAAQ;wBACR,WAAW,YAAY,SAAS;wBAChC,aAAa,YAAY,WAAW;wBACpC,kBAAkB,YAAY,OAAO,KAAK,oBAAoB,YAAY,OAAO,KAAK;wBACtF,mBAAmB,YAAY,OAAO,KAAK,uBAAuB,YAAY,OAAO,KAAK;wBAC1F,aAAa,IAAI,OAAO,WAAW;wBACnC,WAAW,WAAW,WAAW;wBACjC;oBACF;oBAEA,eAAe,CAAA;wBACb,sDAAsD;wBACtD,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,SAAS,KAAK,YAAY,SAAS,IAAI,EAAE,MAAM,KAAK,aAAa;wBACvG,OAAO;+BAAI;4BAAU;yBAAW;oBAClC;oBAEA,QAAQ;gBACV,OAAO;oBACL,+BAA+B;oBAC/B,gBAAgB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC/B,EAAE,EAAE,KAAK,gBACL;gCAAE,GAAG,CAAC;gCAAE,QAAQ;4BAAkB,IAClC;oBAEN,QAAQ;gBACV;YACF,GAAG,MAAM,oCAAoC;;QAC/C;IACF,GAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,WAAmB;QAChD,MAAM,aAAa,YAAY,IAAI,CAAC,CAAA,IAClC,EAAE,SAAS,KAAK,aAChB,EAAE,MAAM,KAAK,iBACb,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI;QAG9B,IAAI,CAAC,YAAY,OAAO;QAExB,OAAO,eAAe,YAAY,WAAW,gBAAgB,GAAG,WAAW,iBAAiB;IAC9F,GAAG;QAAC;QAAa;KAAc;IAE/B,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,OAAO,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB;IAC3D,GAAG;QAAC;KAAa;IAEjB,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,OAAO,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAC/C,GAAG;QAAC;QAAc;KAAc;IAEhC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,iBAAiB,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI;IAC3F,GAAG;QAAC;QAAa;KAAc;IAE/B,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,MAAM,wBAAwB;IACnC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 2418, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/contexts/BartaContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { useAuth } from './AuthContext'\nimport { useWorkers } from './WorkersContext'\nimport { useSuppliers } from './SuppliersContext'\nimport { useCompanies } from './CompaniesContext'\n\n// Types for Barta messaging system\nexport interface BartaUser {\n  id: string\n  name: string\n  email: string\n  role: 'worker' | 'supplier' | 'company'\n  avatar?: string\n  isOnline: boolean\n  lastSeen: string\n}\n\nexport interface BartaMessage {\n  id: string\n  conversationId: string\n  senderId: string\n  senderName: string\n  senderRole: 'worker' | 'supplier' | 'company'\n  content: string\n  type: 'text' | 'image' | 'file' | 'emoji'\n  timestamp: string\n  isRead: boolean\n  isDelivered: boolean\n  replyTo?: string // For message replies\n  replyToMessage?: BartaMessage // Full message being replied to\n  reactions?: {\n    [emoji: string]: {\n      users: string[] // User IDs who reacted\n      count: number\n    }\n  }\n  attachments?: {\n    type: 'image' | 'file'\n    url: string\n    name: string\n    size?: number\n  }[]\n  isEdited?: boolean\n  editedAt?: string\n}\n\nexport interface BartaConversation {\n  id: string\n  participants: string[] // User IDs\n  participantDetails: BartaUser[]\n  lastMessage?: BartaMessage\n  lastActivity: string\n  unreadCount: number\n  isGroup: boolean\n  groupName?: string\n  groupAvatar?: string\n  createdAt: string\n  updatedAt: string\n}\n\ninterface BartaContextType {\n  // State\n  conversations: BartaConversation[]\n  messages: { [conversationId: string]: BartaMessage[] }\n  activeConversation: string | null\n  isTyping: { [conversationId: string]: string[] } // User IDs who are typing\n  onlineUsers: BartaUser[]\n\n  // Actions\n  sendMessage: (conversationId: string, content: string, type?: 'text' | 'image' | 'file' | 'emoji', replyTo?: string) => void\n  createConversation: (participantId: string) => string\n  setActiveConversation: (conversationId: string | null) => void\n  markAsRead: (conversationId: string) => void\n  setTyping: (conversationId: string, isTyping: boolean) => void\n  searchUsers: (query: string) => BartaUser[]\n  getConversationWithUser: (userId: string) => BartaConversation | null\n  deleteMessage: (messageId: string, conversationId: string) => void\n  editMessage: (messageId: string, newContent: string) => void\n  addReaction: (messageId: string, conversationId: string, emoji: string) => void\n  removeReaction: (messageId: string, conversationId: string, emoji: string) => void\n\n  // Utilities\n  getUserById: (userId: string) => BartaUser | null\n  getUnreadCount: () => number\n  formatLastSeen: (timestamp: string) => string\n}\n\nconst BartaContext = createContext<BartaContextType | undefined>(undefined)\n\nexport function BartaProvider({ children }: { children: ReactNode }) {\n  const { user, userRole } = useAuth()\n  const { workers } = useWorkers()\n  const { suppliers } = useSuppliers()\n  const { companies } = useCompanies()\n\n  // State\n  const [conversations, setConversations] = useState<BartaConversation[]>([])\n  const [messages, setMessages] = useState<{ [conversationId: string]: BartaMessage[] }>({})\n  const [activeConversation, setActiveConversation] = useState<string | null>(null)\n  const [isTyping, setIsTyping] = useState<{ [conversationId: string]: string[] }>({})\n  const [onlineUsers, setOnlineUsers] = useState<BartaUser[]>([])\n\n  // Load data from localStorage on mount\n  useEffect(() => {\n    try {\n      const savedConversations = localStorage.getItem('kaazmaamaa-barta-conversations')\n      const savedMessages = localStorage.getItem('kaazmaamaa-barta-messages')\n      \n      if (savedConversations) {\n        setConversations(JSON.parse(savedConversations))\n      }\n      \n      if (savedMessages) {\n        setMessages(JSON.parse(savedMessages))\n      }\n      \n      console.log('📱 Barta: Loaded conversations and messages from localStorage')\n    } catch (error) {\n      console.error('📱 Barta: Error loading data from localStorage:', error)\n    }\n  }, [])\n\n  // Save conversations to localStorage whenever they change\n  useEffect(() => {\n    if (conversations.length > 0) {\n      localStorage.setItem('kaazmaamaa-barta-conversations', JSON.stringify(conversations))\n    }\n  }, [conversations])\n\n  // Save messages to localStorage whenever they change\n  useEffect(() => {\n    if (Object.keys(messages).length > 0) {\n      localStorage.setItem('kaazmaamaa-barta-messages', JSON.stringify(messages))\n    }\n  }, [messages])\n\n  // Build online users list from all profile types\n  useEffect(() => {\n    const allUsers: BartaUser[] = []\n    \n    // Add workers\n    workers.forEach(worker => {\n      if (worker.personalInfo?.workerName && worker.personalInfo?.email) {\n        allUsers.push({\n          id: worker.userId,\n          name: worker.personalInfo.workerName,\n          email: worker.personalInfo.email,\n          role: 'worker',\n          isOnline: Math.random() > 0.5, // Mock online status\n          lastSeen: new Date().toISOString()\n        })\n      }\n    })\n    \n    // Add suppliers\n    suppliers.forEach(supplier => {\n      if (supplier.personalInfo?.supplierName && supplier.personalInfo?.email) {\n        allUsers.push({\n          id: supplier.userId,\n          name: supplier.personalInfo.supplierName,\n          email: supplier.personalInfo.email,\n          role: 'supplier',\n          isOnline: Math.random() > 0.5, // Mock online status\n          lastSeen: new Date().toISOString()\n        })\n      }\n    })\n    \n    // Add companies\n    companies.forEach(company => {\n      if (company.companyInfo?.companyName && company.contactInfo?.hrEmail) {\n        allUsers.push({\n          id: company.userId,\n          name: company.companyInfo.companyName,\n          email: company.contactInfo.hrEmail,\n          role: 'company',\n          isOnline: Math.random() > 0.5, // Mock online status\n          lastSeen: new Date().toISOString()\n        })\n      }\n    })\n    \n    setOnlineUsers(allUsers)\n    console.log('📱 Barta: Updated online users list:', allUsers.length, 'users')\n  }, [workers, suppliers, companies])\n\n  // Generate unique ID for messages and conversations\n  const generateId = () => {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9)\n  }\n\n  // Get user by ID from all profile types\n  const getUserById = (userId: string): BartaUser | null => {\n    return onlineUsers.find(user => user.id === userId) || null\n  }\n\n  // Search users across all profile types\n  const searchUsers = (query: string): BartaUser[] => {\n    if (!query.trim()) return onlineUsers\n    \n    const lowercaseQuery = query.toLowerCase()\n    return onlineUsers.filter(user => \n      user.name.toLowerCase().includes(lowercaseQuery) ||\n      user.email.toLowerCase().includes(lowercaseQuery) ||\n      user.role.toLowerCase().includes(lowercaseQuery)\n    )\n  }\n\n  // Get existing conversation with a user\n  const getConversationWithUser = (userId: string): BartaConversation | null => {\n    return conversations.find(conv => \n      !conv.isGroup && \n      conv.participants.includes(userId) && \n      conv.participants.includes(user?.id || '')\n    ) || null\n  }\n\n  // Create new conversation\n  const createConversation = (participantId: string): string => {\n    if (!user?.id) return ''\n    \n    // Check if conversation already exists\n    const existingConv = getConversationWithUser(participantId)\n    if (existingConv) {\n      return existingConv.id\n    }\n    \n    const participant = getUserById(participantId)\n    if (!participant) return ''\n    \n    const conversationId = generateId()\n    const currentUserDetails = getUserById(user.id)\n    \n    const newConversation: BartaConversation = {\n      id: conversationId,\n      participants: [user.id, participantId],\n      participantDetails: [currentUserDetails, participant].filter(Boolean) as BartaUser[],\n      lastActivity: new Date().toISOString(),\n      unreadCount: 0,\n      isGroup: false,\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString()\n    }\n    \n    setConversations(prev => [newConversation, ...prev])\n    console.log('📱 Barta: Created new conversation:', conversationId)\n    \n    return conversationId\n  }\n\n  // Send message\n  const sendMessage = (conversationId: string, content: string, type: 'text' | 'image' | 'file' | 'emoji' = 'text', replyTo?: string) => {\n    if (!user?.id || !content.trim()) return\n\n    const messageId = generateId()\n    const currentUserDetails = getUserById(user.id)\n\n    // Get reply message if replying\n    let replyToMessage: BartaMessage | undefined\n    if (replyTo) {\n      const conversationMessages = messages[conversationId] || []\n      replyToMessage = conversationMessages.find(msg => msg.id === replyTo)\n    }\n\n    const newMessage: BartaMessage = {\n      id: messageId,\n      conversationId,\n      senderId: user.id,\n      senderName: currentUserDetails?.name || user.email || 'Unknown User',\n      senderRole: (userRole as 'worker' | 'supplier' | 'company') || 'worker',\n      content: content.trim(),\n      type,\n      timestamp: new Date().toISOString(),\n      isRead: false,\n      isDelivered: true,\n      replyTo,\n      replyToMessage,\n      reactions: {}\n    }\n    \n    // Add message to messages\n    setMessages(prev => ({\n      ...prev,\n      [conversationId]: [...(prev[conversationId] || []), newMessage]\n    }))\n    \n    // Update conversation's last message and activity\n    setConversations(prev => prev.map(conv => {\n      if (conv.id === conversationId) {\n        return {\n          ...conv,\n          lastMessage: newMessage,\n          lastActivity: new Date().toISOString(),\n          updatedAt: new Date().toISOString()\n        }\n      }\n      return conv\n    }))\n    \n    console.log('📱 Barta: Message sent:', messageId)\n  }\n\n  // Mark conversation as read\n  const markAsRead = (conversationId: string) => {\n    setMessages(prev => ({\n      ...prev,\n      [conversationId]: (prev[conversationId] || []).map(msg => ({\n        ...msg,\n        isRead: true\n      }))\n    }))\n    \n    setConversations(prev => prev.map(conv => \n      conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv\n    ))\n  }\n\n  // Set typing status\n  const setTypingStatus = (conversationId: string, isTypingNow: boolean) => {\n    if (!user?.id) return\n    \n    setIsTyping(prev => {\n      const currentTypers = prev[conversationId] || []\n      \n      if (isTypingNow) {\n        if (!currentTypers.includes(user.id)) {\n          return {\n            ...prev,\n            [conversationId]: [...currentTypers, user.id]\n          }\n        }\n      } else {\n        return {\n          ...prev,\n          [conversationId]: currentTypers.filter(id => id !== user.id)\n        }\n      }\n      \n      return prev\n    })\n  }\n\n  // Delete message\n  const deleteMessage = (messageId: string, conversationId: string) => {\n    setMessages(prev => ({\n      ...prev,\n      [conversationId]: (prev[conversationId] || []).filter(msg => msg.id !== messageId)\n    }))\n  }\n\n  // Edit message\n  const editMessage = (messageId: string, newContent: string) => {\n    setMessages(prev => {\n      const updatedMessages = { ...prev }\n\n      Object.keys(updatedMessages).forEach(convId => {\n        updatedMessages[convId] = updatedMessages[convId].map(msg =>\n          msg.id === messageId ? {\n            ...msg,\n            content: newContent,\n            isEdited: true,\n            editedAt: new Date().toISOString()\n          } : msg\n        )\n      })\n\n      return updatedMessages\n    })\n  }\n\n  // Add reaction to message\n  const addReaction = (messageId: string, conversationId: string, emoji: string) => {\n    if (!user?.id) return\n\n    setMessages(prev => ({\n      ...prev,\n      [conversationId]: (prev[conversationId] || []).map(msg => {\n        if (msg.id === messageId) {\n          const reactions = { ...msg.reactions }\n\n          if (reactions[emoji]) {\n            // Add user to existing reaction\n            if (!reactions[emoji].users.includes(user.id)) {\n              reactions[emoji].users.push(user.id)\n              reactions[emoji].count++\n            }\n          } else {\n            // Create new reaction\n            reactions[emoji] = {\n              users: [user.id],\n              count: 1\n            }\n          }\n\n          return { ...msg, reactions }\n        }\n        return msg\n      })\n    }))\n  }\n\n  // Remove reaction from message\n  const removeReaction = (messageId: string, conversationId: string, emoji: string) => {\n    if (!user?.id) return\n\n    setMessages(prev => ({\n      ...prev,\n      [conversationId]: (prev[conversationId] || []).map(msg => {\n        if (msg.id === messageId) {\n          const reactions = { ...msg.reactions }\n\n          if (reactions[emoji]) {\n            reactions[emoji].users = reactions[emoji].users.filter(id => id !== user.id)\n            reactions[emoji].count = reactions[emoji].users.length\n\n            // Remove reaction if no users left\n            if (reactions[emoji].count === 0) {\n              delete reactions[emoji]\n            }\n          }\n\n          return { ...msg, reactions }\n        }\n        return msg\n      })\n    }))\n  }\n\n  // Get total unread count\n  const getUnreadCount = (): number => {\n    return conversations.reduce((total, conv) => total + conv.unreadCount, 0)\n  }\n\n  // Format last seen time\n  const formatLastSeen = (timestamp: string): string => {\n    const now = new Date()\n    const lastSeen = new Date(timestamp)\n    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'Just now'\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`\n    return `${Math.floor(diffInMinutes / 1440)}d ago`\n  }\n\n  const value: BartaContextType = {\n    // State\n    conversations,\n    messages,\n    activeConversation,\n    isTyping,\n    onlineUsers,\n\n    // Actions\n    sendMessage,\n    createConversation,\n    setActiveConversation,\n    markAsRead,\n    setTyping: setTypingStatus,\n    searchUsers,\n    getConversationWithUser,\n    deleteMessage,\n    editMessage,\n    addReaction,\n    removeReaction,\n\n    // Utilities\n    getUserById,\n    getUnreadCount,\n    formatLastSeen\n  }\n\n  return (\n    <BartaContext.Provider value={value}>\n      {children}\n    </BartaContext.Provider>\n  )\n}\n\nexport function useBarta() {\n  const context = useContext(BartaContext)\n  if (context === undefined) {\n    throw new Error('useBarta must be used within a BartaProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAyFA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IACjC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD;IAEjC,QAAQ;IACR,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgD,CAAC;IACxF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0C,CAAC;IAClF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAE9D,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;YACF,MAAM,qBAAqB,aAAa,OAAO,CAAC;YAChD,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAE3C,IAAI,oBAAoB;gBACtB,iBAAiB,KAAK,KAAK,CAAC;YAC9B;YAEA,IAAI,eAAe;gBACjB,YAAY,KAAK,KAAK,CAAC;YACzB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;QACnE;IACF,GAAG,EAAE;IAEL,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,aAAa,OAAO,CAAC,kCAAkC,KAAK,SAAS,CAAC;QACxE;IACF,GAAG;QAAC;KAAc;IAElB,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;YACpC,aAAa,OAAO,CAAC,6BAA6B,KAAK,SAAS,CAAC;QACnE;IACF,GAAG;QAAC;KAAS;IAEb,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAwB,EAAE;QAEhC,cAAc;QACd,QAAQ,OAAO,CAAC,CAAA;YACd,IAAI,OAAO,YAAY,EAAE,cAAc,OAAO,YAAY,EAAE,OAAO;gBACjE,SAAS,IAAI,CAAC;oBACZ,IAAI,OAAO,MAAM;oBACjB,MAAM,OAAO,YAAY,CAAC,UAAU;oBACpC,OAAO,OAAO,YAAY,CAAC,KAAK;oBAChC,MAAM;oBACN,UAAU,KAAK,MAAM,KAAK;oBAC1B,UAAU,IAAI,OAAO,WAAW;gBAClC;YACF;QACF;QAEA,gBAAgB;QAChB,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI,SAAS,YAAY,EAAE,gBAAgB,SAAS,YAAY,EAAE,OAAO;gBACvE,SAAS,IAAI,CAAC;oBACZ,IAAI,SAAS,MAAM;oBACnB,MAAM,SAAS,YAAY,CAAC,YAAY;oBACxC,OAAO,SAAS,YAAY,CAAC,KAAK;oBAClC,MAAM;oBACN,UAAU,KAAK,MAAM,KAAK;oBAC1B,UAAU,IAAI,OAAO,WAAW;gBAClC;YACF;QACF;QAEA,gBAAgB;QAChB,UAAU,OAAO,CAAC,CAAA;YAChB,IAAI,QAAQ,WAAW,EAAE,eAAe,QAAQ,WAAW,EAAE,SAAS;gBACpE,SAAS,IAAI,CAAC;oBACZ,IAAI,QAAQ,MAAM;oBAClB,MAAM,QAAQ,WAAW,CAAC,WAAW;oBACrC,OAAO,QAAQ,WAAW,CAAC,OAAO;oBAClC,MAAM;oBACN,UAAU,KAAK,MAAM,KAAK;oBAC1B,UAAU,IAAI,OAAO,WAAW;gBAClC;YACF;QACF;QAEA,eAAe;QACf,QAAQ,GAAG,CAAC,wCAAwC,SAAS,MAAM,EAAE;IACvE,GAAG;QAAC;QAAS;QAAW;KAAU;IAElC,oDAAoD;IACpD,MAAM,aAAa;QACjB,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;IACtE;IAEA,wCAAwC;IACxC,MAAM,cAAc,CAAC;QACnB,OAAO,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,WAAW;IACzD;IAEA,wCAAwC;IACxC,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,MAAM,IAAI,IAAI,OAAO;QAE1B,MAAM,iBAAiB,MAAM,WAAW;QACxC,OAAO,YAAY,MAAM,CAAC,CAAA,OACxB,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,mBACjC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,mBAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAErC;IAEA,wCAAwC;IACxC,MAAM,0BAA0B,CAAC;QAC/B,OAAO,cAAc,IAAI,CAAC,CAAA,OACxB,CAAC,KAAK,OAAO,IACb,KAAK,YAAY,CAAC,QAAQ,CAAC,WAC3B,KAAK,YAAY,CAAC,QAAQ,CAAC,MAAM,MAAM,QACpC;IACP;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,MAAM,IAAI,OAAO;QAEtB,uCAAuC;QACvC,MAAM,eAAe,wBAAwB;QAC7C,IAAI,cAAc;YAChB,OAAO,aAAa,EAAE;QACxB;QAEA,MAAM,cAAc,YAAY;QAChC,IAAI,CAAC,aAAa,OAAO;QAEzB,MAAM,iBAAiB;QACvB,MAAM,qBAAqB,YAAY,KAAK,EAAE;QAE9C,MAAM,kBAAqC;YACzC,IAAI;YACJ,cAAc;gBAAC,KAAK,EAAE;gBAAE;aAAc;YACtC,oBAAoB;gBAAC;gBAAoB;aAAY,CAAC,MAAM,CAAC;YAC7D,cAAc,IAAI,OAAO,WAAW;YACpC,aAAa;YACb,SAAS;YACT,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QAEA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;QACnD,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,OAAO;IACT;IAEA,eAAe;IACf,MAAM,cAAc,CAAC,gBAAwB,SAAiB,OAA4C,MAAM,EAAE;QAChH,IAAI,CAAC,MAAM,MAAM,CAAC,QAAQ,IAAI,IAAI;QAElC,MAAM,YAAY;QAClB,MAAM,qBAAqB,YAAY,KAAK,EAAE;QAE9C,gCAAgC;QAChC,IAAI;QACJ,IAAI,SAAS;YACX,MAAM,uBAAuB,QAAQ,CAAC,eAAe,IAAI,EAAE;YAC3D,iBAAiB,qBAAqB,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/D;QAEA,MAAM,aAA2B;YAC/B,IAAI;YACJ;YACA,UAAU,KAAK,EAAE;YACjB,YAAY,oBAAoB,QAAQ,KAAK,KAAK,IAAI;YACtD,YAAY,AAAC,YAAkD;YAC/D,SAAS,QAAQ,IAAI;YACrB;YACA,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;YACR,aAAa;YACb;YACA;YACA,WAAW,CAAC;QACd;QAEA,0BAA0B;QAC1B,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE;uBAAK,IAAI,CAAC,eAAe,IAAI,EAAE;oBAAG;iBAAW;YACjE,CAAC;QAED,kDAAkD;QAClD,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAChC,IAAI,KAAK,EAAE,KAAK,gBAAgB;oBAC9B,OAAO;wBACL,GAAG,IAAI;wBACP,aAAa;wBACb,cAAc,IAAI,OAAO,WAAW;wBACpC,WAAW,IAAI,OAAO,WAAW;oBACnC;gBACF;gBACA,OAAO;YACT;QAEA,QAAQ,GAAG,CAAC,2BAA2B;IACzC;IAEA,4BAA4B;IAC5B,MAAM,aAAa,CAAC;QAClB,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;wBACzD,GAAG,GAAG;wBACN,QAAQ;oBACV,CAAC;YACH,CAAC;QAED,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OAChC,KAAK,EAAE,KAAK,iBAAiB;oBAAE,GAAG,IAAI;oBAAE,aAAa;gBAAE,IAAI;IAE/D;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,gBAAwB;QAC/C,IAAI,CAAC,MAAM,IAAI;QAEf,YAAY,CAAA;YACV,MAAM,gBAAgB,IAAI,CAAC,eAAe,IAAI,EAAE;YAEhD,IAAI,aAAa;gBACf,IAAI,CAAC,cAAc,QAAQ,CAAC,KAAK,EAAE,GAAG;oBACpC,OAAO;wBACL,GAAG,IAAI;wBACP,CAAC,eAAe,EAAE;+BAAI;4BAAe,KAAK,EAAE;yBAAC;oBAC/C;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,eAAe,EAAE,cAAc,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,EAAE;gBAC7D;YACF;YAEA,OAAO;QACT;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC,WAAmB;QACxC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAC1E,CAAC;IACH;IAEA,eAAe;IACf,MAAM,cAAc,CAAC,WAAmB;QACtC,YAAY,CAAA;YACV,MAAM,kBAAkB;gBAAE,GAAG,IAAI;YAAC;YAElC,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAC,CAAA;gBACnC,eAAe,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,MACpD,IAAI,EAAE,KAAK,YAAY;wBACrB,GAAG,GAAG;wBACN,SAAS;wBACT,UAAU;wBACV,UAAU,IAAI,OAAO,WAAW;oBAClC,IAAI;YAER;YAEA,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,MAAM,cAAc,CAAC,WAAmB,gBAAwB;QAC9D,IAAI,CAAC,MAAM,IAAI;QAEf,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA;oBACjD,IAAI,IAAI,EAAE,KAAK,WAAW;wBACxB,MAAM,YAAY;4BAAE,GAAG,IAAI,SAAS;wBAAC;wBAErC,IAAI,SAAS,CAAC,MAAM,EAAE;4BACpB,gCAAgC;4BAChC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG;gCAC7C,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE;gCACnC,SAAS,CAAC,MAAM,CAAC,KAAK;4BACxB;wBACF,OAAO;4BACL,sBAAsB;4BACtB,SAAS,CAAC,MAAM,GAAG;gCACjB,OAAO;oCAAC,KAAK,EAAE;iCAAC;gCAChB,OAAO;4BACT;wBACF;wBAEA,OAAO;4BAAE,GAAG,GAAG;4BAAE;wBAAU;oBAC7B;oBACA,OAAO;gBACT;YACF,CAAC;IACH;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB,CAAC,WAAmB,gBAAwB;QACjE,IAAI,CAAC,MAAM,IAAI;QAEf,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA;oBACjD,IAAI,IAAI,EAAE,KAAK,WAAW;wBACxB,MAAM,YAAY;4BAAE,GAAG,IAAI,SAAS;wBAAC;wBAErC,IAAI,SAAS,CAAC,MAAM,EAAE;4BACpB,SAAS,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,KAAK,EAAE;4BAC3E,SAAS,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM;4BAEtD,mCAAmC;4BACnC,IAAI,SAAS,CAAC,MAAM,CAAC,KAAK,KAAK,GAAG;gCAChC,OAAO,SAAS,CAAC,MAAM;4BACzB;wBACF;wBAEA,OAAO;4BAAE,GAAG,GAAG;4BAAE;wBAAU;oBAC7B;oBACA,OAAO;gBACT;YACF,CAAC;IACH;IAEA,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,OAAO,cAAc,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,WAAW,EAAE;IACzE;IAEA,wBAAwB;IACxB,MAAM,iBAAiB,CAAC;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,KAAK;QAC1B,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,SAAS,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAElF,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;QACtD,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IACnD;IAEA,MAAM,QAA0B;QAC9B,QAAQ;QACR;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA,WAAW;QACX;QACA;QACA;QACA;QACA;QACA;QAEA,YAAY;QACZ;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;kBAC3B;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2812, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/KAAZMAAMAA%20AUGMENT/kaazmaamaa-app/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}]}