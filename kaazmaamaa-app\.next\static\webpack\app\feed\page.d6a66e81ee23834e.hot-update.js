"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/contexts/PostsContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/PostsContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostsProvider: () => (/* binding */ PostsProvider),\n/* harmony export */   formatExpirationInfo: () => (/* binding */ formatExpirationInfo),\n/* harmony export */   formatTimeAgo: () => (/* binding */ formatTimeAgo),\n/* harmony export */   usePosts: () => (/* binding */ usePosts)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ PostsProvider,usePosts,formatTimeAgo,formatExpirationInfo auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst PostsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Helper function to calculate expiration date (1 month from creation)\nconst getExpirationDate = (createdAt)=>{\n    const created = new Date(createdAt);\n    const expiration = new Date(created);\n    expiration.setMonth(expiration.getMonth() + 1) // Add 1 month\n    ;\n    return expiration.toISOString();\n};\n// Initial mock posts\nconst initialPosts = [\n    {\n        id: '1',\n        user_id: 'mock-user-1',\n        user_name: 'Ahmed Al-Rashid',\n        user_role: 'worker',\n        content: 'Looking for construction work in Riyadh. I have 5 years of experience in electrical work. Available immediately! #ElectricalWork #Riyadh #Available',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()),\n        likes: 12,\n        comments: 3,\n        shared_count: 2,\n        post_comments: [\n            {\n                id: 'comment-1',\n                post_id: '1',\n                user_id: 'commenter-1',\n                user_name: 'Sarah Construction',\n                user_role: 'company',\n                content: 'We have openings for electrical work. Please send your CV.',\n                created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n            },\n            {\n                id: 'comment-2',\n                post_id: '1',\n                user_id: 'commenter-2',\n                user_name: 'Ali Hassan',\n                user_role: 'worker',\n                content: 'Good luck brother! I also work in electrical.',\n                created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString()\n            }\n        ]\n    },\n    {\n        id: '2',\n        user_id: 'mock-user-2',\n        user_name: 'Saudi Construction Co.',\n        user_role: 'company',\n        content: 'We are hiring 20 skilled workers for our new project in Jeddah. Competitive salary and accommodation provided. Contact us for details. #Hiring #Jeddah #Construction',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()),\n        likes: 28,\n        comments: 15\n    },\n    {\n        id: '3',\n        user_id: 'mock-user-3',\n        user_name: 'Gulf Manpower Solutions',\n        user_role: 'supplier',\n        content: 'New batch of certified welders available for immediate deployment. All workers have valid IQAMA and safety certifications. #Welders #Certified #Available',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()),\n        likes: 18,\n        comments: 8\n    },\n    {\n        id: '4',\n        user_id: 'mock-user-4',\n        user_name: 'Mohammed Hassan',\n        user_role: 'worker',\n        content: 'Just completed my safety certification course! Ready for new opportunities in the oil and gas sector. #SafetyCertified #OilAndGas #Ready',\n        post_type: 'text',\n        visibility: 'public',\n        created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n        expires_at: getExpirationDate(new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()),\n        likes: 15,\n        comments: 5\n    }\n];\nfunction PostsProvider(param) {\n    let { children } = param;\n    _s();\n    const [posts, setPosts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load posts from localStorage on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PostsProvider.useEffect\": ()=>{\n            const loadPostsFromStorage = {\n                \"PostsProvider.useEffect.loadPostsFromStorage\": ()=>{\n                    try {\n                        const savedPosts = localStorage.getItem('kaazmaamaa-posts');\n                        if (savedPosts) {\n                            const parsedPosts = JSON.parse(savedPosts);\n                            console.log('Loaded posts from localStorage:', parsedPosts.length);\n                            // Filter out expired posts when loading\n                            const now = new Date();\n                            const activePosts = parsedPosts.filter({\n                                \"PostsProvider.useEffect.loadPostsFromStorage.activePosts\": (post)=>{\n                                    // Handle posts that might not have expires_at field (legacy posts)\n                                    if (!post.expires_at) {\n                                        // Add expires_at to legacy posts (1 month from creation)\n                                        post.expires_at = getExpirationDate(post.created_at);\n                                    }\n                                    const expirationDate = new Date(post.expires_at);\n                                    return expirationDate > now;\n                                }\n                            }[\"PostsProvider.useEffect.loadPostsFromStorage.activePosts\"]);\n                            const expiredCount = parsedPosts.length - activePosts.length;\n                            if (expiredCount > 0) {\n                                console.log(\"Filtered out \".concat(expiredCount, \" expired posts on load\"));\n                                // Save cleaned posts back to localStorage\n                                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts));\n                            }\n                            setPosts(activePosts);\n                        } else {\n                            // If no saved posts, use initial posts\n                            console.log('No saved posts found, using initial posts');\n                            setPosts(initialPosts);\n                            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts));\n                        }\n                    } catch (error) {\n                        console.error('Error loading posts from localStorage:', error);\n                        setPosts(initialPosts);\n                    } finally{\n                        setIsLoaded(true);\n                    }\n                }\n            }[\"PostsProvider.useEffect.loadPostsFromStorage\"];\n            loadPostsFromStorage();\n        }\n    }[\"PostsProvider.useEffect\"], []);\n    // Save posts to localStorage whenever posts change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PostsProvider.useEffect\": ()=>{\n            if (isLoaded && posts.length > 0) {\n                try {\n                    localStorage.setItem('kaazmaamaa-posts', JSON.stringify(posts));\n                    console.log('Saved posts to localStorage:', posts.length);\n                } catch (error) {\n                    console.error('Error saving posts to localStorage:', error);\n                }\n            }\n        }\n    }[\"PostsProvider.useEffect\"], [\n        posts,\n        isLoaded\n    ]);\n    console.log('PostsProvider initialized with posts:', posts.length);\n    const addPost = (newPost)=>{\n        const createdAt = new Date().toISOString();\n        const post = {\n            ...newPost,\n            id: Date.now().toString(),\n            created_at: createdAt,\n            expires_at: getExpirationDate(createdAt),\n            likes: 0,\n            comments: 0\n        };\n        console.log('Adding post to context:', post);\n        setPosts((prevPosts)=>{\n            const updatedPosts = [\n                post,\n                ...prevPosts\n            ];\n            console.log('Updated posts array:', updatedPosts);\n            // Immediately save to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Post immediately saved to localStorage');\n            } catch (error) {\n                console.error('Error saving post to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const likePost = (postId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {\n                    ...post,\n                    likes: post.liked_by_user ? post.likes - 1 : post.likes + 1,\n                    liked_by_user: !post.liked_by_user\n                } : post);\n            // Save likes to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Like status saved to localStorage');\n            } catch (error) {\n                console.error('Error saving like to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const sharePost = (postId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {\n                    ...post,\n                    shared_count: (post.shared_count || 0) + 1\n                } : post);\n            // Save share count to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Share count saved to localStorage');\n            } catch (error) {\n                console.error('Error saving share to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const addComment = (postId, content, user)=>{\n        const newComment = {\n            id: Date.now().toString(),\n            post_id: postId,\n            user_id: user.id,\n            user_name: user.name,\n            user_role: user.role,\n            content: content.trim(),\n            created_at: new Date().toISOString()\n        };\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.map((post)=>post.id === postId ? {\n                    ...post,\n                    comments: post.comments + 1,\n                    post_comments: [\n                        ...post.post_comments || [],\n                        newComment\n                    ]\n                } : post);\n            // Save comments to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Comment saved to localStorage');\n            } catch (error) {\n                console.error('Error saving comment to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const deletePost = (postId)=>{\n        setPosts((prevPosts)=>{\n            const updatedPosts = prevPosts.filter((post)=>post.id !== postId);\n            // Save deletion to localStorage\n            try {\n                localStorage.setItem('kaazmaamaa-posts', JSON.stringify(updatedPosts));\n                console.log('Post deletion saved to localStorage');\n            } catch (error) {\n                console.error('Error saving deletion to localStorage:', error);\n            }\n            return updatedPosts;\n        });\n    };\n    const clearAllPosts = ()=>{\n        setPosts([]);\n        try {\n            localStorage.removeItem('kaazmaamaa-posts');\n            console.log('All posts cleared from localStorage');\n        } catch (error) {\n            console.error('Error clearing posts from localStorage:', error);\n        }\n    };\n    const resetToInitialPosts = ()=>{\n        setPosts(initialPosts);\n        try {\n            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(initialPosts));\n            console.log('Reset to initial posts and saved to localStorage');\n        } catch (error) {\n            console.error('Error resetting posts in localStorage:', error);\n        }\n    };\n    // Function to clean up expired posts (older than 1 month)\n    const cleanupExpiredPosts = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PostsProvider.useCallback[cleanupExpiredPosts]\": ()=>{\n            const now = new Date();\n            setPosts({\n                \"PostsProvider.useCallback[cleanupExpiredPosts]\": (prevPosts)=>{\n                    const activePosts = prevPosts.filter({\n                        \"PostsProvider.useCallback[cleanupExpiredPosts].activePosts\": (post)=>{\n                            const expirationDate = new Date(post.expires_at);\n                            return expirationDate > now;\n                        }\n                    }[\"PostsProvider.useCallback[cleanupExpiredPosts].activePosts\"]);\n                    const expiredCount = prevPosts.length - activePosts.length;\n                    if (expiredCount > 0) {\n                        console.log(\"Cleaned up \".concat(expiredCount, \" expired posts\"));\n                        // Save cleaned posts to localStorage\n                        try {\n                            localStorage.setItem('kaazmaamaa-posts', JSON.stringify(activePosts));\n                            console.log('Cleaned posts saved to localStorage');\n                        } catch (error) {\n                            console.error('Error saving cleaned posts to localStorage:', error);\n                        }\n                    }\n                    return activePosts;\n                }\n            }[\"PostsProvider.useCallback[cleanupExpiredPosts]\"]);\n        }\n    }[\"PostsProvider.useCallback[cleanupExpiredPosts]\"], []);\n    const migratePostsToFullNames = ()=>{\n        console.log('Migrating posts to use full names...');\n        // Clear localStorage and reset to initial posts with proper names\n        try {\n            localStorage.removeItem('kaazmaamaa-posts');\n            console.log('Cleared old posts from localStorage');\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n        }\n        resetToInitialPosts();\n    };\n    const value = {\n        posts,\n        addPost,\n        likePost,\n        sharePost,\n        deletePost,\n        addComment,\n        clearAllPosts,\n        resetToInitialPosts,\n        cleanupExpiredPosts,\n        migratePostsToFullNames\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostsContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\contexts\\\\PostsContext.tsx\",\n        lineNumber: 433,\n        columnNumber: 5\n    }, this);\n}\n_s(PostsProvider, \"grrvA51HETDAPO/ii/ehM7XMTSc=\");\n_c = PostsProvider;\nfunction usePosts() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PostsContext);\n    if (context === undefined) {\n        throw new Error('usePosts must be used within a PostsProvider');\n    }\n    return context;\n}\n_s1(usePosts, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Utility function to format time ago\nfunction formatTimeAgo(dateString) {\n    const now = new Date();\n    const postDate = new Date(dateString);\n    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n    if (diffInMinutes < 1) return 'Just now';\n    if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m ago\");\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) return \"\".concat(diffInHours, \"h ago\");\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) return \"\".concat(diffInDays, \"d ago\");\n    return postDate.toLocaleDateString();\n}\n// Utility function to format post expiration info\nfunction formatExpirationInfo(expiresAt) {\n    const now = new Date();\n    const expirationDate = new Date(expiresAt);\n    const diffInMs = expirationDate.getTime() - now.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n    if (diffInMs <= 0) {\n        return {\n            text: 'Expired',\n            isExpiringSoon: true\n        };\n    }\n    if (diffInDays <= 3) {\n        return {\n            text: \"Expires in \".concat(diffInDays, \" day\").concat(diffInDays !== 1 ? 's' : ''),\n            isExpiringSoon: true\n        };\n    }\n    if (diffInDays <= 7) {\n        return {\n            text: \"Expires in \".concat(diffInDays, \" days\"),\n            isExpiringSoon: false\n        };\n    }\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n        return {\n            text: \"Expires in \".concat(diffInWeeks, \" week\").concat(diffInWeeks !== 1 ? 's' : ''),\n            isExpiringSoon: false\n        };\n    }\n    return {\n        text: 'Expires in 1 month',\n        isExpiringSoon: false\n    };\n}\nvar _c;\n$RefreshReg$(_c, \"PostsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/PostsContext.tsx\n"));

/***/ })

});