"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/feed/page",{

/***/ "(app-pages-browser)/./src/app/feed/page.tsx":
/*!*******************************!*\
  !*** ./src/app/feed/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeedPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/PostsContext */ \"(app-pages-browser)/./src/contexts/PostsContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction FeedPage() {\n    _s();\n    const { user, userRole, getUserProfile, loading, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { posts, addPost, likePost, addComment, sharePost } = (0,_contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Create Post States\n    const [showCreatePost, setShowCreatePost] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [postText, setPostText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedFeeling, setSelectedFeeling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImages, setSelectedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedLocation, setSelectedLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [taggedUsers, setTaggedUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [postPrivacy, setPostPrivacy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('public');\n    // Comments States\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [commentTexts, setCommentTexts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [showAllComments, setShowAllComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Share States\n    const [showShareModal, setShowShareModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [shareText, setShareText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Other States\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeStory, setActiveStory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEmojiPicker, setShowEmojiPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLocationPicker, setShowLocationPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTagPicker, setShowTagPicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Get user profile data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (user && userRole) {\n                const profile = getUserProfile();\n                setUserProfile(profile);\n                console.log('User profile loaded:', profile);\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        userRole,\n        getUserProfile\n    ]);\n    // Redirect to home if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeedPage.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/');\n            }\n        }\n    }[\"FeedPage.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    // Helper function to get user's display name\n    const getUserDisplayName = ()=>{\n        if (!userProfile) return 'User';\n        if (userRole === 'worker') {\n            var _userProfile_personalInfo;\n            return ((_userProfile_personalInfo = userProfile.personalInfo) === null || _userProfile_personalInfo === void 0 ? void 0 : _userProfile_personalInfo.workerName) || 'Worker';\n        } else if (userRole === 'supplier') {\n            var _userProfile_personalInfo1;\n            return ((_userProfile_personalInfo1 = userProfile.personalInfo) === null || _userProfile_personalInfo1 === void 0 ? void 0 : _userProfile_personalInfo1.supplierName) || 'Supplier';\n        } else if (userRole === 'company') {\n            var _userProfile_companyInfo;\n            return ((_userProfile_companyInfo = userProfile.companyInfo) === null || _userProfile_companyInfo === void 0 ? void 0 : _userProfile_companyInfo.companyName) || 'Company';\n        }\n        return 'User';\n    };\n    // Helper function to get user's avatar/initial\n    const getUserInitial = ()=>{\n        const name = getUserDisplayName();\n        return name.charAt(0).toUpperCase();\n    };\n    // Helper function to get user's role emoji\n    const getUserRoleEmoji = ()=>{\n        switch(userRole){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Helper functions for Facebook-style features\n    const handleLikePost = (postId)=>{\n        likePost(postId);\n    };\n    const handleCommentSubmit = (postId)=>{\n        const commentText = commentTexts[postId];\n        if (!(commentText === null || commentText === void 0 ? void 0 : commentText.trim()) || !user) return;\n        addComment(postId, commentText, {\n            id: user.email,\n            name: getUserDisplayName(),\n            role: userRole || 'worker'\n        });\n        setCommentTexts((prev)=>({\n                ...prev,\n                [postId]: ''\n            }));\n    };\n    const handleSharePost = (postId)=>{\n        if (!shareText.trim()) {\n            sharePost(postId);\n        } else {\n            // Create a new post that shares the original\n            const originalPost = posts.find((p)=>p.id === postId);\n            if (originalPost) {\n                const newPost = {\n                    user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                    user_name: getUserDisplayName(),\n                    user_role: userRole || 'worker',\n                    content: shareText,\n                    post_type: 'shared',\n                    visibility: 'public'\n                };\n                addPost(newPost);\n            }\n        }\n        setShowShareModal(null);\n        setShareText('');\n    };\n    const toggleComments = (postId)=>{\n        setShowComments((prev)=>({\n                ...prev,\n                [postId]: !prev[postId]\n            }));\n    };\n    const formatTimeAgo = (dateString)=>{\n        const now = new Date();\n        const postDate = new Date(dateString);\n        const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60));\n        if (diffInMinutes < 1) return 'Just now';\n        if (diffInMinutes < 60) return \"\".concat(diffInMinutes, \"m\");\n        const diffInHours = Math.floor(diffInMinutes / 60);\n        if (diffInHours < 24) return \"\".concat(diffInHours, \"h\");\n        const diffInDays = Math.floor(diffInHours / 24);\n        if (diffInDays < 7) return \"\".concat(diffInDays, \"d\");\n        const diffInWeeks = Math.floor(diffInDays / 7);\n        return \"\".concat(diffInWeeks, \"w\");\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case 'worker':\n                return '#2563eb';\n            case 'supplier':\n                return '#059669';\n            case 'company':\n                return '#7c3aed';\n            default:\n                return '#6b7280';\n        }\n    };\n    const getRoleEmoji = (role)=>{\n        switch(role){\n            case 'worker':\n                return '🔧';\n            case 'supplier':\n                return '🏭';\n            case 'company':\n                return '🏢';\n            default:\n                return '👤';\n        }\n    };\n    // Show loading if still authenticating\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: '#f0f2f5'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '2rem',\n                            marginBottom: '1rem'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#65676b'\n                        },\n                        children: \"Loading your feed...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n            lineNumber: 167,\n            columnNumber: 7\n        }, this);\n    }\n    // Redirect if not authenticated\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: '100vh',\n            backgroundColor: '#f0f2f5'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundColor: '#1877f2',\n                    padding: '0.75rem 1rem',\n                    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: '1200px',\n                        margin: '0 auto',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '1rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    style: {\n                                        fontSize: '1.5rem',\n                                        fontWeight: 'bold',\n                                        color: 'white',\n                                        margin: 0\n                                    },\n                                    children: \"KAAZMAAMAA\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'rgba(255,255,255,0.2)',\n                                        borderRadius: '20px',\n                                        padding: '0.5rem 1rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: \"Search KAAZMAAMAA...\",\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            color: 'white',\n                                            outline: 'none',\n                                            fontSize: '0.875rem'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '1rem',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '0.75rem',\n                                        color: 'white'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                borderRadius: '50%',\n                                                width: '2rem',\n                                                height: '2rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '0.875rem',\n                                                fontWeight: 'bold'\n                                            },\n                                            children: getUserInitial()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                const profilePath = userRole === 'worker' ? '/workers/profile' : userRole === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                router.push(profilePath);\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Go Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                await signOut();\n                                                router.push('/');\n                                            },\n                                            style: {\n                                                backgroundColor: 'rgba(255,255,255,0.2)',\n                                                color: 'white',\n                                                border: '1px solid rgba(255,255,255,0.3)',\n                                                padding: '0.5rem 1rem',\n                                                borderRadius: '0.25rem',\n                                                fontSize: '0.875rem',\n                                                fontWeight: '500',\n                                                cursor: 'pointer'\n                                            },\n                                            children: \"Sign Out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    maxWidth: '1200px',\n                    margin: '0 auto',\n                    display: 'grid',\n                    gridTemplateColumns: '1fr 2fr 1fr',\n                    gap: '1rem',\n                    padding: '1rem'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Quick Access\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/workers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e3f2fd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDD27\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Workers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/suppliers\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#e8f5e8',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFED\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Suppliers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/companies\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#f3e8ff',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83C\\uDFE2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Companies\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/barta\",\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem',\n                                            textDecoration: 'none',\n                                            color: '#1c1e21',\n                                            padding: '0.5rem',\n                                            borderRadius: '6px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: '#fff3cd',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: \"\\uD83D\\uDCAC\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontWeight: '500'\n                                                },\n                                                children: \"Barta Messenger\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            flexDirection: 'column',\n                            gap: '1rem'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        gap: '0.75rem',\n                                        overflowX: 'auto',\n                                        paddingBottom: '0.5rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                minWidth: '120px',\n                                                height: '200px',\n                                                borderRadius: '12px',\n                                                background: 'linear-gradient(45deg, #1877f2, #42a5f5)',\n                                                position: 'relative',\n                                                cursor: 'pointer',\n                                                display: 'flex',\n                                                flexDirection: 'column',\n                                                justifyContent: 'flex-end',\n                                                padding: '1rem',\n                                                color: 'white'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: 'absolute',\n                                                        top: '1rem',\n                                                        left: '1rem',\n                                                        backgroundColor: 'rgba(255,255,255,0.3)',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    children: \"+\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '0.875rem',\n                                                        fontWeight: '600'\n                                                    },\n                                                    children: \"Create Story\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            'Ahmed Al-Rashid',\n                                            'Saudi Building Co.',\n                                            'Tech Solutions',\n                                            'Construction Pro'\n                                        ].map((name, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    minWidth: '120px',\n                                                    height: '200px',\n                                                    borderRadius: '12px',\n                                                    background: \"linear-gradient(45deg, \".concat([\n                                                        '#ff6b6b',\n                                                        '#4ecdc4',\n                                                        '#45b7d1',\n                                                        '#96ceb4'\n                                                    ][index], \", \").concat([\n                                                        '#ffa726',\n                                                        '#26a69a',\n                                                        '#42a5f5',\n                                                        '#81c784'\n                                                    ][index], \")\"),\n                                                    position: 'relative',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    justifyContent: 'space-between',\n                                                    padding: '1rem',\n                                                    color: 'white'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: 'rgba(255,255,255,0.3)',\n                                                            borderRadius: '50%',\n                                                            width: '2.5rem',\n                                                            height: '2.5rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: name.charAt(0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: '600'\n                                                        },\n                                                        children: name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    padding: '1rem',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            gap: '0.75rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                                    borderRadius: '50%',\n                                                    width: '2.5rem',\n                                                    height: '2.5rem',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center',\n                                                    color: 'white',\n                                                    fontWeight: 'bold'\n                                                },\n                                                children: getUserInitial()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: \"What's on your mind, \".concat(getUserDisplayName(), \"?\"),\n                                                onClick: ()=>setShowCreatePost(true),\n                                                readOnly: true,\n                                                style: {\n                                                    flex: 1,\n                                                    backgroundColor: '#f0f2f5',\n                                                    border: 'none',\n                                                    borderRadius: '20px',\n                                                    padding: '0.75rem 1rem',\n                                                    outline: 'none',\n                                                    fontSize: '1rem',\n                                                    cursor: 'pointer'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            justifyContent: 'space-around',\n                                            paddingTop: '0.75rem',\n                                            borderTop: '1px solid #e4e6ea'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDCF7 Photo/Video\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowCreatePost(true),\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '0.5rem',\n                                                    backgroundColor: 'transparent',\n                                                    border: 'none',\n                                                    padding: '0.5rem 1rem',\n                                                    borderRadius: '6px',\n                                                    cursor: 'pointer',\n                                                    color: '#65676b'\n                                                },\n                                                children: \"\\uD83D\\uDE0A Feeling/Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            posts.map((post)=>{\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: 'white',\n                                        borderRadius: '8px',\n                                        boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '1rem',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.75rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        backgroundColor: getRoleColor(post.user_role),\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        color: 'white',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: post.user_name.charAt(0).toUpperCase()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                // Navigate to user's profile based on their role\n                                                                const profilePath = post.user_role === 'worker' ? '/workers/profile' : post.user_role === 'supplier' ? '/suppliers/profile' : '/companies/profile';\n                                                                router.push(\"\".concat(profilePath, \"?user=\").concat(encodeURIComponent(post.user_id)));\n                                                            },\n                                                            style: {\n                                                                background: 'none',\n                                                                border: 'none',\n                                                                padding: 0,\n                                                                fontWeight: '600',\n                                                                color: '#1c1e21',\n                                                                cursor: 'pointer',\n                                                                fontSize: 'inherit',\n                                                                textAlign: 'left'\n                                                            },\n                                                            onMouseEnter: (e)=>e.target.style.textDecoration = 'underline',\n                                                            onMouseLeave: (e)=>e.target.style.textDecoration = 'none',\n                                                            children: post.user_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                color: '#65676b'\n                                                            },\n                                                            children: [\n                                                                getTimeAgo(post.created_at),\n                                                                \" • \",\n                                                                getRoleEmoji(post.user_role),\n                                                                \" \",\n                                                                post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 473,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                paddingLeft: '1rem',\n                                                paddingRight: '1rem',\n                                                marginBottom: '1rem'\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#1c1e21',\n                                                    lineHeight: '1.5',\n                                                    margin: 0\n                                                },\n                                                children: post.content\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this),\n                                        post.media_urls && post.media_urls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f2f5',\n                                                height: '250px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                color: '#65676b',\n                                                fontSize: '3rem'\n                                            },\n                                            children: \"\\uD83D\\uDCF7\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                padding: '0.75rem 1rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-between',\n                                                        alignItems: 'center',\n                                                        marginBottom: '0.75rem'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#65676b',\n                                                            fontSize: '0.875rem'\n                                                        },\n                                                        children: [\n                                                            \"\\uD83D\\uDC4D \",\n                                                            post.likes,\n                                                            \" likes • \\uD83D\\uDCAC \",\n                                                            post.comments,\n                                                            \" comments\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        display: 'flex',\n                                                        justifyContent: 'space-around',\n                                                        paddingTop: '0.75rem',\n                                                        borderTop: '1px solid #e4e6ea'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>likePost(post.id),\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: post.liked_by_user ? '#1877f2' : '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDC4D Like\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDCAC Comment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            style: {\n                                                                display: 'flex',\n                                                                alignItems: 'center',\n                                                                gap: '0.5rem',\n                                                                backgroundColor: 'transparent',\n                                                                border: 'none',\n                                                                padding: '0.5rem 1rem',\n                                                                borderRadius: '6px',\n                                                                cursor: 'pointer',\n                                                                color: '#65676b',\n                                                                fontWeight: '500'\n                                                            },\n                                                            children: \"\\uD83D\\uDCE4 Share\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, post.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 15\n                                }, this);\n                            }),\n                            posts.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: 'white',\n                                    borderRadius: '8px',\n                                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',\n                                    padding: '3rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCDD\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#1c1e21',\n                                            marginBottom: '0.5rem'\n                                        },\n                                        children: \"No posts yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 534,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: \"Be the first to share something with your professional network!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            backgroundColor: 'white',\n                            borderRadius: '8px',\n                            padding: '1rem',\n                            height: 'fit-content',\n                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    fontSize: '1.125rem',\n                                    fontWeight: '600',\n                                    color: '#1c1e21',\n                                    marginBottom: '1rem'\n                                },\n                                children: \"Online Now\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 542,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '0.75rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#7c3aed',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"M\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Modern Tech Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#2563eb',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"F\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Fatima Al-Zahra\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 556,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '0.75rem'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: 'relative'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            backgroundColor: '#059669',\n                                                            borderRadius: '50%',\n                                                            width: '2rem',\n                                                            height: '2rem',\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            color: 'white',\n                                                            fontSize: '0.875rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"K\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            position: 'absolute',\n                                                            bottom: '-2px',\n                                                            right: '-2px',\n                                                            backgroundColor: '#42b883',\n                                                            borderRadius: '50%',\n                                                            width: '0.75rem',\n                                                            height: '0.75rem',\n                                                            border: '2px solid white'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    fontSize: '0.875rem',\n                                                    color: '#1c1e21'\n                                                },\n                                                children: \"Khalid Construction\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 566,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    marginTop: '2rem',\n                                    paddingTop: '1rem',\n                                    borderTop: '1px solid #e4e6ea'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    style: {\n                                        color: '#1877f2',\n                                        textDecoration: 'none',\n                                        fontSize: '0.875rem'\n                                    },\n                                    children: \"← Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                        lineNumber: 541,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            showCreatePost && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(255, 255, 255, 0.8)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    zIndex: 1000\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: 'white',\n                        borderRadius: '8px',\n                        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',\n                        width: '500px',\n                        maxHeight: '90vh',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderBottom: '1px solid #e4e6ea',\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'center'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '1.25rem',\n                                        fontWeight: '600',\n                                        color: '#1c1e21',\n                                        margin: 0\n                                    },\n                                    children: \"Create Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCreatePost(false),\n                                    style: {\n                                        backgroundColor: '#f0f2f5',\n                                        border: 'none',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        fontSize: '1.25rem',\n                                        color: '#65676b'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 606,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.75rem'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',\n                                        borderRadius: '50%',\n                                        width: '2.5rem',\n                                        height: '2.5rem',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        justifyContent: 'center',\n                                        color: 'white',\n                                        fontWeight: 'bold'\n                                    },\n                                    children: getUserInitial()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 638,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: getUserDisplayName()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 652,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '0.875rem',\n                                                color: '#65676b',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '0.25rem'\n                                            },\n                                            children: [\n                                                getUserRoleEmoji(),\n                                                \" \",\n                                                userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User',\n                                                \" • \\uD83C\\uDF0D Public\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 651,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                value: postText,\n                                onChange: (e)=>setPostText(e.target.value),\n                                placeholder: \"What's on your mind?\",\n                                style: {\n                                    width: '100%',\n                                    minHeight: '120px',\n                                    border: 'none',\n                                    outline: 'none',\n                                    fontSize: '1.5rem',\n                                    color: '#1c1e21',\n                                    resize: 'none',\n                                    fontFamily: 'inherit'\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 660,\n                            columnNumber: 13\n                        }, this),\n                        selectedFeeling && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '20px',\n                                    padding: '0.5rem 1rem',\n                                    display: 'inline-flex',\n                                    alignItems: 'center',\n                                    gap: '0.5rem'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: selectedFeeling\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedFeeling(''),\n                                        style: {\n                                            backgroundColor: 'transparent',\n                                            border: 'none',\n                                            cursor: 'pointer',\n                                            color: '#65676b'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 680,\n                            columnNumber: 15\n                        }, this),\n                        selectedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '0 1rem',\n                                marginBottom: '1rem'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: 'relative',\n                                    backgroundColor: '#f0f2f5',\n                                    borderRadius: '8px',\n                                    padding: '2rem',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '1rem'\n                                        },\n                                        children: \"\\uD83D\\uDCF7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 715,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            color: '#65676b'\n                                        },\n                                        children: [\n                                            \"Image Preview (\",\n                                            selectedImages.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 716,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setSelectedImages([]),\n                                        style: {\n                                            position: 'absolute',\n                                            top: '0.5rem',\n                                            right: '0.5rem',\n                                            backgroundColor: 'rgba(0,0,0,0.5)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '50%',\n                                            width: '2rem',\n                                            height: '2rem',\n                                            cursor: 'pointer'\n                                        },\n                                        children: \"✕\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                        lineNumber: 717,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 707,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                padding: '1rem',\n                                borderTop: '1px solid #e4e6ea'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        justifyContent: 'space-between',\n                                        alignItems: 'center',\n                                        marginBottom: '1rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontWeight: '600',\n                                                color: '#1c1e21'\n                                            },\n                                            children: \"Add to your post\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 741,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                gap: '0.5rem'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedImages([\n                                                            'image'\n                                                        ]),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Photo/Video\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 743,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setSelectedFeeling('😊 feeling happy'),\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Feeling/Activity\",\n                                                    children: \"\\uD83D\\uDE0A\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 761,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Tag People\",\n                                                    children: \"\\uD83D\\uDC65\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 779,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    style: {\n                                                        backgroundColor: 'transparent',\n                                                        border: 'none',\n                                                        borderRadius: '50%',\n                                                        width: '2.5rem',\n                                                        height: '2.5rem',\n                                                        cursor: 'pointer',\n                                                        display: 'flex',\n                                                        alignItems: 'center',\n                                                        justifyContent: 'center',\n                                                        fontSize: '1.25rem'\n                                                    },\n                                                    title: \"Check In\",\n                                                    children: \"\\uD83D\\uDCCD\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                                    lineNumber: 796,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 740,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        if (!postText.trim()) return;\n                                        // Create the post using the posts context\n                                        const newPost = {\n                                            user_id: (user === null || user === void 0 ? void 0 : user.email) || '',\n                                            user_name: getUserDisplayName(),\n                                            user_role: userRole || 'worker',\n                                            content: postText,\n                                            media_urls: selectedImage ? [\n                                                selectedImage\n                                            ] : undefined,\n                                            post_type: selectedImage ? 'image' : 'text',\n                                            visibility: 'public'\n                                        };\n                                        addPost(newPost);\n                                        // Reset form\n                                        setPostText('');\n                                        setSelectedFeeling('');\n                                        setSelectedImages([]);\n                                        setSelectedLocation('');\n                                        setTaggedUsers([]);\n                                        setShowCreatePost(false);\n                                        // Show success message\n                                        alert('Post created successfully!');\n                                    },\n                                    disabled: !postText.trim(),\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',\n                                        color: postText.trim() ? 'white' : '#bcc0c4',\n                                        border: 'none',\n                                        borderRadius: '6px',\n                                        padding: '0.75rem',\n                                        fontSize: '1rem',\n                                        fontWeight: '600',\n                                        cursor: postText.trim() ? 'pointer' : 'not-allowed'\n                                    },\n                                    children: \"Post\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                            lineNumber: 739,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                    lineNumber: 597,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n                lineNumber: 585,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\KAAZMAAMAA AUGMENT\\\\kaazmaamaa-app\\\\src\\\\app\\\\feed\\\\page.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s(FeedPage, \"mG1is2bfnznMFmJKz7POH9TiagY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _contexts_PostsContext__WEBPACK_IMPORTED_MODULE_3__.usePosts,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = FeedPage;\nvar _c;\n$RefreshReg$(_c, \"FeedPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/feed/page.tsx\n"));

/***/ })

});