'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { usePosts } from '@/contexts/PostsContext'
import { useRouter } from 'next/navigation'

export default function FeedPage() {
  const { user, userRole, getUserProfile, loading, signOut } = useAuth()
  const { posts, addPost, likePost, addComment, sharePost } = usePosts()
  const router = useRouter()

  // Create Post States
  const [showCreatePost, setShowCreatePost] = useState(false)
  const [postText, setPostText] = useState('')
  const [selectedFeeling, setSelectedFeeling] = useState('')
  const [selectedImages, setSelectedImages] = useState<string[]>([])
  const [selectedLocation, setSelectedLocation] = useState('')
  const [taggedUsers, setTaggedUsers] = useState<string[]>([])
  const [postPrivacy, setPostPrivacy] = useState<'public' | 'friends' | 'private'>('public')

  // Comments States
  const [showComments, setShowComments] = useState<{[key: string]: boolean}>({})
  const [commentTexts, setCommentTexts] = useState<{[key: string]: string}>({})
  const [showAllComments, setShowAllComments] = useState<{[key: string]: boolean}>({})

  // Share States
  const [showShareModal, setShowShareModal] = useState<string | null>(null)
  const [shareText, setShareText] = useState('')

  // Other States
  const [userProfile, setUserProfile] = useState<any>(null)
  const [activeStory, setActiveStory] = useState<string | null>(null)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [showLocationPicker, setShowLocationPicker] = useState(false)
  const [showTagPicker, setShowTagPicker] = useState(false)

  // Get user profile data
  useEffect(() => {
    if (user && userRole) {
      const profile = getUserProfile()
      setUserProfile(profile)
      console.log('User profile loaded:', profile)
    }
  }, [user, userRole, getUserProfile])

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
    }
  }, [user, loading, router])

  // Helper function to get user's display name
  const getUserDisplayName = () => {
    if (!userProfile) return 'User'

    if (userRole === 'worker') {
      return userProfile.personalInfo?.workerName || 'Worker'
    } else if (userRole === 'supplier') {
      return userProfile.personalInfo?.supplierName || 'Supplier'
    } else if (userRole === 'company') {
      return userProfile.companyInfo?.companyName || 'Company'
    }
    return 'User'
  }

  // Helper function to get user's avatar/initial
  const getUserInitial = () => {
    const name = getUserDisplayName()
    return name.charAt(0).toUpperCase()
  }

  // Helper function to get user's role emoji
  const getUserRoleEmoji = () => {
    switch (userRole) {
      case 'worker': return '🔧'
      case 'supplier': return '🏭'
      case 'company': return '🏢'
      default: return '👤'
    }
  }

  // Helper functions for Facebook-style features
  const handleLikePost = (postId: string) => {
    likePost(postId)
  }

  const handleCommentSubmit = (postId: string) => {
    const commentText = commentTexts[postId]
    if (!commentText?.trim() || !user) return

    addComment(postId, commentText, {
      id: user.email,
      name: getUserDisplayName(),
      role: userRole || 'worker'
    })

    setCommentTexts(prev => ({ ...prev, [postId]: '' }))
  }

  const handleSharePost = (postId: string) => {
    if (!shareText.trim()) {
      sharePost(postId)
    } else {
      // Create a new post that shares the original
      const originalPost = posts.find(p => p.id === postId)
      if (originalPost) {
        const newPost = {
          user_id: user?.email || '',
          user_name: getUserDisplayName(),
          user_role: (userRole || 'worker') as 'worker' | 'supplier' | 'company',
          content: shareText,
          post_type: 'shared' as const,
          visibility: 'public' as const
        }
        addPost(newPost)
      }
    }
    setShowShareModal(null)
    setShareText('')
  }

  const toggleComments = (postId: string) => {
    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }))
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h`

    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d`

    const diffInWeeks = Math.floor(diffInDays / 7)
    return `${diffInWeeks}w`
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'worker': return '#2563eb'
      case 'supplier': return '#059669'
      case 'company': return '#7c3aed'
      default: return '#6b7280'
    }
  }

  const getRoleEmoji = (role: string) => {
    switch (role) {
      case 'worker': return '🔧'
      case 'supplier': return '🏭'
      case 'company': return '🏢'
      default: return '👤'
    }
  }

  // Show loading if still authenticating
  if (loading) {
    return (
      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f0f2f5' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>
          <div style={{ color: '#65676b' }}>Loading your feed...</div>
        </div>
      </div>
    )
  }

  // Redirect if not authenticated
  if (!user) {
    return null
  }
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      {/* Facebook-style Header */}
      <div style={{ backgroundColor: '#1877f2', padding: '0.75rem 1rem', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white', margin: 0 }}>
              KAAZMAAMAA
            </h1>
            <div style={{ backgroundColor: 'rgba(255,255,255,0.2)', borderRadius: '20px', padding: '0.5rem 1rem' }}>
              <input 
                type="text" 
                placeholder="Search KAAZMAAMAA..." 
                style={{ 
                  backgroundColor: 'transparent', 
                  border: 'none', 
                  color: 'white', 
                  outline: 'none',
                  fontSize: '0.875rem'
                }}
              />
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', color: 'white' }}>
              <div style={{
                backgroundColor: 'rgba(255,255,255,0.2)',
                borderRadius: '50%',
                width: '2rem',
                height: '2rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.875rem',
                fontWeight: 'bold'
              }}>
                {getUserInitial()}
              </div>
              <span style={{ fontSize: '0.875rem', fontWeight: '500' }}>
                {getUserDisplayName()}
              </span>
            </div>

            <div style={{ display: 'flex', gap: '0.75rem' }}>
              <button
                onClick={() => {
                  const profilePath = userRole === 'worker' ? '/workers/profile' :
                                    userRole === 'supplier' ? '/suppliers/profile' :
                                    '/companies/profile'
                  router.push(profilePath)
                }}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  border: '1px solid rgba(255,255,255,0.3)',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.25rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Go Profile
              </button>

              <button
                onClick={async () => {
                  await signOut()
                  router.push('/')
                }}
                style={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  border: '1px solid rgba(255,255,255,0.3)',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.25rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Feed Layout */}
      <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'grid', gridTemplateColumns: '1fr 2fr 1fr', gap: '1rem', padding: '1rem' }}>
        
        {/* Left Sidebar */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>
            Quick Access
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <a href="/workers" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#e3f2fd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                🔧
              </div>
              <span style={{ fontWeight: '500' }}>Workers</span>
            </a>
            
            <a href="/suppliers" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#e8f5e8', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                🏭
              </div>
              <span style={{ fontWeight: '500' }}>Suppliers</span>
            </a>
            
            <a href="/companies" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#f3e8ff', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                🏢
              </div>
              <span style={{ fontWeight: '500' }}>Companies</span>
            </a>
            
            <a href="/barta" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#fff3cd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                💬
              </div>
              <span style={{ fontWeight: '500' }}>Barta Messenger</span>
            </a>
          </div>
        </div>

        {/* Center Feed */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>

          {/* Stories Section */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', gap: '0.75rem', overflowX: 'auto', paddingBottom: '0.5rem' }}>
              {/* Create Story */}
              <div style={{
                minWidth: '120px',
                height: '200px',
                borderRadius: '12px',
                background: 'linear-gradient(45deg, #1877f2, #42a5f5)',
                position: 'relative',
                cursor: 'pointer',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-end',
                padding: '1rem',
                color: 'white'
              }}>
                <div style={{
                  position: 'absolute',
                  top: '1rem',
                  left: '1rem',
                  backgroundColor: 'rgba(255,255,255,0.3)',
                  borderRadius: '50%',
                  width: '2.5rem',
                  height: '2.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.25rem'
                }}>
                  +
                </div>
                <div style={{ fontSize: '0.875rem', fontWeight: '600' }}>Create Story</div>
              </div>

              {/* Sample Stories */}
              {['Ahmed Al-Rashid', 'Saudi Building Co.', 'Tech Solutions', 'Construction Pro'].map((name, index) => (
                <div key={index} style={{
                  minWidth: '120px',
                  height: '200px',
                  borderRadius: '12px',
                  background: `linear-gradient(45deg, ${['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'][index]}, ${['#ffa726', '#26a69a', '#42a5f5', '#81c784'][index]})`,
                  position: 'relative',
                  cursor: 'pointer',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  padding: '1rem',
                  color: 'white'
                }}>
                  <div style={{
                    backgroundColor: 'rgba(255,255,255,0.3)',
                    borderRadius: '50%',
                    width: '2.5rem',
                    height: '2.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '1rem',
                    fontWeight: 'bold'
                  }}>
                    {name.charAt(0)}
                  </div>
                  <div style={{ fontSize: '0.875rem', fontWeight: '600' }}>{name}</div>
                </div>
              ))}
            </div>
          </div>

          {/* Create Post */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', gap: '0.75rem', marginBottom: '1rem' }}>
              <div style={{
                backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',
                borderRadius: '50%',
                width: '2.5rem',
                height: '2.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold'
              }}>
                {getUserInitial()}
              </div>
              <input
                type="text"
                placeholder={`What's on your mind, ${getUserDisplayName()}?`}
                onClick={() => setShowCreatePost(true)}
                readOnly
                style={{
                  flex: 1,
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '20px',
                  padding: '0.75rem 1rem',
                  outline: 'none',
                  fontSize: '1rem',
                  cursor: 'pointer'
                }}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>
              <button
                onClick={() => setShowCreatePost(true)}
                style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}
              >
                📷 Photo/Video
              </button>
              <button
                onClick={() => setShowCreatePost(true)}
                style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}
              >
                😊 Feeling/Activity
              </button>
            </div>
          </div>

          {/* Dynamic Posts from Context */}
          {posts.map((post) => {

            return (
              <div key={post.id} style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
                {/* Post Header */}
                <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                  <div style={{
                    backgroundColor: getRoleColor(post.user_role),
                    borderRadius: '50%',
                    width: '2.5rem',
                    height: '2.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold'
                  }}>
                    {post.user_name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <button
                      onClick={() => {
                        // Navigate to user's profile based on their role
                        const profilePath = post.user_role === 'worker' ? '/workers/profile' :
                                          post.user_role === 'supplier' ? '/suppliers/profile' :
                                          '/companies/profile'
                        router.push(`${profilePath}?user=${encodeURIComponent(post.user_id)}`)
                      }}
                      style={{
                        background: 'none',
                        border: 'none',
                        padding: 0,
                        fontWeight: '600',
                        color: '#1c1e21',
                        cursor: 'pointer',
                        fontSize: 'inherit',
                        textAlign: 'left'
                      }}
                      onMouseEnter={(e) => (e.target as HTMLElement).style.textDecoration = 'underline'}
                      onMouseLeave={(e) => (e.target as HTMLElement).style.textDecoration = 'none'}
                    >
                      {post.user_name}
                    </button>
                    <div style={{ fontSize: '0.8rem', color: '#65676b' }}>
                      {formatTimeAgo(post.created_at)} • {getRoleEmoji(post.user_role)} {post.user_role.charAt(0).toUpperCase() + post.user_role.slice(1)}
                    </div>
                  </div>
                </div>

                {/* Post Content */}
                <div style={{ paddingLeft: '1rem', paddingRight: '1rem', marginBottom: '1rem' }}>
                  <p style={{ color: '#1c1e21', lineHeight: '1.5', margin: 0 }}>
                    {post.content}
                  </p>
                </div>

                {/* Post Media (if any) */}
                {post.media_urls && post.media_urls.length > 0 && (
                  <div style={{ backgroundColor: '#f0f2f5', height: '250px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#65676b', fontSize: '3rem' }}>
                    📷
                  </div>
                )}

                {/* Post Reactions Summary */}
                <div style={{ padding: '0.75rem 1rem' }}>
                  {(post.likes > 0 || post.comments > 0) && (
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                        {post.likes > 0 && (
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                            <div style={{ display: 'flex', gap: '0.125rem' }}>
                              <div style={{ backgroundColor: '#1877f2', borderRadius: '50%', width: '1.25rem', height: '1.25rem', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.75rem' }}>👍</div>
                              <div style={{ backgroundColor: '#e74c3c', borderRadius: '50%', width: '1.25rem', height: '1.25rem', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.75rem' }}>❤️</div>
                              <div style={{ backgroundColor: '#f39c12', borderRadius: '50%', width: '1.25rem', height: '1.25rem', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '0.75rem' }}>😂</div>
                            </div>
                            <span style={{ color: '#65676b', fontSize: '0.875rem' }}>{post.likes}</span>
                          </div>
                        )}
                      </div>
                      <div style={{ display: 'flex', gap: '1rem', color: '#65676b', fontSize: '0.875rem' }}>
                        {post.comments > 0 && (
                          <button
                            onClick={() => toggleComments(post.id)}
                            style={{ background: 'none', border: 'none', color: '#65676b', cursor: 'pointer', fontSize: '0.875rem' }}
                          >
                            {post.comments} comments
                          </button>
                        )}
                        {(post.shared_count || 0) > 0 && <span>{post.shared_count} shares</span>}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>
                    <button
                      onClick={() => handleLikePost(post.id)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        backgroundColor: 'transparent',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        color: post.liked_by_user ? '#1877f2' : '#65676b',
                        fontWeight: '500',
                        flex: 1,
                        justifyContent: 'center'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f0f2f5'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <span style={{ fontSize: '1.25rem' }}>👍</span> Like
                    </button>
                    <button
                      onClick={() => toggleComments(post.id)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        backgroundColor: 'transparent',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        color: '#65676b',
                        fontWeight: '500',
                        flex: 1,
                        justifyContent: 'center'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f0f2f5'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <span style={{ fontSize: '1.25rem' }}>💬</span> Comment
                    </button>
                    <button
                      onClick={() => setShowShareModal(post.id)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.5rem',
                        backgroundColor: 'transparent',
                        border: 'none',
                        padding: '0.5rem 1rem',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        color: '#65676b',
                        fontWeight: '500',
                        flex: 1,
                        justifyContent: 'center'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f0f2f5'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                    >
                      <span style={{ fontSize: '1.25rem' }}>📤</span> Share
                    </button>
                  </div>

                  {/* Comments Section */}
                  {showComments[post.id] && (
                    <div style={{ marginTop: '1rem', borderTop: '1px solid #e4e6ea', paddingTop: '1rem' }}>
                      {/* Existing Comments */}
                      {(post.post_comments || []).slice(0, showAllComments[post.id] ? undefined : 3).map((comment) => (
                        <div key={comment.id} style={{ display: 'flex', gap: '0.75rem', marginBottom: '1rem' }}>
                          <div style={{
                            backgroundColor: getRoleColor(comment.user_role),
                            borderRadius: '50%',
                            width: '2rem',
                            height: '2rem',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            fontSize: '0.875rem',
                            fontWeight: 'bold',
                            flexShrink: 0
                          }}>
                            {comment.user_name.charAt(0).toUpperCase()}
                          </div>
                          <div style={{ flex: 1 }}>
                            <div style={{ backgroundColor: '#f0f2f5', borderRadius: '1rem', padding: '0.75rem 1rem' }}>
                              <div style={{ fontWeight: '600', fontSize: '0.875rem', marginBottom: '0.25rem' }}>
                                {comment.user_name}
                              </div>
                              <div style={{ fontSize: '0.875rem', lineHeight: '1.4' }}>
                                {comment.content}
                              </div>
                            </div>
                            <div style={{ display: 'flex', gap: '1rem', marginTop: '0.25rem', paddingLeft: '1rem' }}>
                              <button style={{ background: 'none', border: 'none', color: '#65676b', fontSize: '0.75rem', cursor: 'pointer', fontWeight: '600' }}>
                                Like
                              </button>
                              <button style={{ background: 'none', border: 'none', color: '#65676b', fontSize: '0.75rem', cursor: 'pointer', fontWeight: '600' }}>
                                Reply
                              </button>
                              <span style={{ color: '#65676b', fontSize: '0.75rem' }}>
                                {formatTimeAgo(comment.created_at)}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Show More Comments */}
                      {(post.post_comments || []).length > 3 && !showAllComments[post.id] && (
                        <button
                          onClick={() => setShowAllComments(prev => ({ ...prev, [post.id]: true }))}
                          style={{ background: 'none', border: 'none', color: '#65676b', fontSize: '0.875rem', cursor: 'pointer', marginBottom: '1rem' }}
                        >
                          View {(post.post_comments || []).length - 3} more comments
                        </button>
                      )}

                      {/* Add Comment */}
                      <div style={{ display: 'flex', gap: '0.75rem', alignItems: 'center' }}>
                        <div style={{
                          backgroundColor: getRoleColor(userRole || 'worker'),
                          borderRadius: '50%',
                          width: '2rem',
                          height: '2rem',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          color: 'white',
                          fontSize: '0.875rem',
                          fontWeight: 'bold',
                          flexShrink: 0
                        }}>
                          {getUserInitial()}
                        </div>
                        <div style={{ flex: 1, position: 'relative' }}>
                          <input
                            type="text"
                            placeholder="Write a comment..."
                            value={commentTexts[post.id] || ''}
                            onChange={(e) => setCommentTexts(prev => ({ ...prev, [post.id]: e.target.value }))}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                handleCommentSubmit(post.id)
                              }
                            }}
                            style={{
                              width: '100%',
                              backgroundColor: '#f0f2f5',
                              border: 'none',
                              borderRadius: '1rem',
                              padding: '0.75rem 1rem',
                              outline: 'none',
                              fontSize: '0.875rem'
                            }}
                          />
                          {commentTexts[post.id] && (
                            <button
                              onClick={() => handleCommentSubmit(post.id)}
                              style={{
                                position: 'absolute',
                                right: '0.5rem',
                                top: '50%',
                                transform: 'translateY(-50%)',
                                background: 'none',
                                border: 'none',
                                color: '#1877f2',
                                cursor: 'pointer',
                                fontSize: '0.875rem',
                                fontWeight: '600'
                              }}
                            >
                              Post
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )
          })}

          {/* No Posts Message */}
          {posts.length === 0 && (
            <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)', padding: '3rem', textAlign: 'center' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📝</div>
              <h3 style={{ color: '#1c1e21', marginBottom: '0.5rem' }}>No posts yet</h3>
              <p style={{ color: '#65676b' }}>Be the first to share something with your professional network!</p>
            </div>
          )}
        </div>

        {/* Right Sidebar */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>
            Online Now
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ position: 'relative' }}>
                <div style={{ backgroundColor: '#7c3aed', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>
                  M
                </div>
                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>
              </div>
              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Modern Tech Solutions</span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ position: 'relative' }}>
                <div style={{ backgroundColor: '#2563eb', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>
                  F
                </div>
                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>
              </div>
              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Fatima Al-Zahra</span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ position: 'relative' }}>
                <div style={{ backgroundColor: '#059669', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>
                  K
                </div>
                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>
              </div>
              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Khalid Construction</span>
            </div>
          </div>
          
          <div style={{ marginTop: '2rem', paddingTop: '1rem', borderTop: '1px solid #e4e6ea' }}>
            <a href="/" style={{ color: '#1877f2', textDecoration: 'none', fontSize: '0.875rem' }}>← Back to Home</a>
          </div>
        </div>
      </div>

      {/* Create Post Modal */}
      {showCreatePost && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',
            width: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '1rem',
              borderBottom: '1px solid #e4e6ea',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1c1e21', margin: 0 }}>
                Create Post
              </h2>
              <button
                onClick={() => setShowCreatePost(false)}
                style={{
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '50%',
                  width: '2.5rem',
                  height: '2.5rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.25rem',
                  color: '#65676b'
                }}
              >
                ✕
              </button>
            </div>

            {/* User Info */}
            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{
                backgroundColor: userRole === 'worker' ? '#2563eb' : userRole === 'supplier' ? '#059669' : '#7c3aed',
                borderRadius: '50%',
                width: '2.5rem',
                height: '2.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold'
              }}>
                {getUserInitial()}
              </div>
              <div>
                <div style={{ fontWeight: '600', color: '#1c1e21' }}>{getUserDisplayName()}</div>
                <div style={{ fontSize: '0.875rem', color: '#65676b', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  {getUserRoleEmoji()} {userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User'} • 🌍 Public
                </div>
              </div>
            </div>

            {/* Post Content */}
            <div style={{ padding: '0 1rem' }}>
              <textarea
                value={postText}
                onChange={(e) => setPostText(e.target.value)}
                placeholder="What's on your mind?"
                style={{
                  width: '100%',
                  minHeight: '120px',
                  border: 'none',
                  outline: 'none',
                  fontSize: '1.5rem',
                  color: '#1c1e21',
                  resize: 'none',
                  fontFamily: 'inherit'
                }}
              />
            </div>

            {/* Feeling/Activity */}
            {selectedFeeling && (
              <div style={{ padding: '0 1rem', marginBottom: '1rem' }}>
                <div style={{
                  backgroundColor: '#f0f2f5',
                  borderRadius: '20px',
                  padding: '0.5rem 1rem',
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <span>{selectedFeeling}</span>
                  <button
                    onClick={() => setSelectedFeeling('')}
                    style={{
                      backgroundColor: 'transparent',
                      border: 'none',
                      cursor: 'pointer',
                      color: '#65676b'
                    }}
                  >
                    ✕
                  </button>
                </div>
              </div>
            )}

            {/* Image Preview */}
            {selectedImages.length > 0 && (
              <div style={{ padding: '0 1rem', marginBottom: '1rem' }}>
                <div style={{
                  position: 'relative',
                  backgroundColor: '#f0f2f5',
                  borderRadius: '8px',
                  padding: '2rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📷</div>
                  <div style={{ color: '#65676b' }}>Image Preview ({selectedImages.length})</div>
                  <button
                    onClick={() => setSelectedImages([])}
                    style={{
                      position: 'absolute',
                      top: '0.5rem',
                      right: '0.5rem',
                      backgroundColor: 'rgba(0,0,0,0.5)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '50%',
                      width: '2rem',
                      height: '2rem',
                      cursor: 'pointer'
                    }}
                  >
                    ✕
                  </button>
                </div>
              </div>
            )}

            {/* Add to Post */}
            <div style={{ padding: '1rem', borderTop: '1px solid #e4e6ea' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                <span style={{ fontWeight: '600', color: '#1c1e21' }}>Add to your post</span>
                <div style={{ display: 'flex', gap: '0.5rem' }}>
                  <button
                    onClick={() => setSelectedImages(['image'])}
                    style={{
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderRadius: '50%',
                      width: '2.5rem',
                      height: '2.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.25rem'
                    }}
                    title="Photo/Video"
                  >
                    📷
                  </button>
                  <button
                    onClick={() => setSelectedFeeling('😊 feeling happy')}
                    style={{
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderRadius: '50%',
                      width: '2.5rem',
                      height: '2.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.25rem'
                    }}
                    title="Feeling/Activity"
                  >
                    😊
                  </button>
                  <button
                    style={{
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderRadius: '50%',
                      width: '2.5rem',
                      height: '2.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.25rem'
                    }}
                    title="Tag People"
                  >
                    👥
                  </button>
                  <button
                    style={{
                      backgroundColor: 'transparent',
                      border: 'none',
                      borderRadius: '50%',
                      width: '2.5rem',
                      height: '2.5rem',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '1.25rem'
                    }}
                    title="Check In"
                  >
                    📍
                  </button>
                </div>
              </div>

              {/* Post Button */}
              <button
                onClick={() => {
                  if (!postText.trim()) return

                  // Create the post using the posts context
                  const newPost = {
                    user_id: user?.email || '',
                    user_name: getUserDisplayName(),
                    user_role: (userRole || 'worker') as 'worker' | 'supplier' | 'company',
                    content: postText,
                    media_urls: selectedImages.length > 0 ? selectedImages : undefined,
                    post_type: (selectedImages.length > 0 ? 'image' : 'text') as 'text' | 'image' | 'video' | 'live' | 'document' | 'mixed',
                    visibility: 'public' as 'public' | 'block_only'
                  }

                  addPost(newPost)

                  // Reset form
                  setPostText('')
                  setSelectedFeeling('')
                  setSelectedImages([])
                  setSelectedLocation('')
                  setTaggedUsers([])
                  setShowCreatePost(false)

                  // Show success message
                  alert('Post created successfully!')
                }}
                disabled={!postText.trim()}
                style={{
                  width: '100%',
                  backgroundColor: postText.trim() ? '#1877f2' : '#e4e6ea',
                  color: postText.trim() ? 'white' : '#bcc0c4',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.75rem',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: postText.trim() ? 'pointer' : 'not-allowed'
                }}
              >
                Post
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Share Modal */}
      {showShareModal && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',
            width: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '1rem',
              borderBottom: '1px solid #e4e6ea',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1c1e21', margin: 0 }}>
                Share Post
              </h2>
              <button
                onClick={() => setShowShareModal(null)}
                style={{
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '50%',
                  width: '2.5rem',
                  height: '2.5rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.25rem',
                  color: '#65676b'
                }}
              >
                ✕
              </button>
            </div>

            {/* Share Options */}
            <div style={{ padding: '1rem' }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem', marginBottom: '1rem' }}>
                <button
                  onClick={() => handleSharePost(showShareModal)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.75rem',
                    padding: '1rem',
                    backgroundColor: '#f0f2f5',
                    border: 'none',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    fontSize: '1rem'
                  }}
                >
                  <span style={{ fontSize: '1.5rem' }}>📤</span>
                  Share Now
                </button>

                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  padding: '1rem',
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem'
                }}>
                  <span style={{ fontSize: '1.5rem' }}>💬</span>
                  Send in Message
                </button>

                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  padding: '1rem',
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem'
                }}>
                  <span style={{ fontSize: '1.5rem' }}>📋</span>
                  Copy Link
                </button>

                <button style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  padding: '1rem',
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  fontSize: '1rem'
                }}>
                  <span style={{ fontSize: '1.5rem' }}>📱</span>
                  Share External
                </button>
              </div>

              {/* Share with Comment */}
              <div style={{ borderTop: '1px solid #e4e6ea', paddingTop: '1rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '1rem' }}>
                  <div style={{
                    backgroundColor: getRoleColor(userRole || 'worker'),
                    borderRadius: '50%',
                    width: '2.5rem',
                    height: '2.5rem',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold'
                  }}>
                    {getUserInitial()}
                  </div>
                  <div>
                    <div style={{ fontWeight: '600', color: '#1c1e21' }}>{getUserDisplayName()}</div>
                    <div style={{ fontSize: '0.875rem', color: '#65676b' }}>
                      {getRoleEmoji(userRole || 'worker')} {userRole ? userRole.charAt(0).toUpperCase() + userRole.slice(1) : 'User'} • 🌍 Public
                    </div>
                  </div>
                </div>

                <textarea
                  value={shareText}
                  onChange={(e) => setShareText(e.target.value)}
                  placeholder="Say something about this..."
                  style={{
                    width: '100%',
                    minHeight: '80px',
                    border: '1px solid #e4e6ea',
                    borderRadius: '8px',
                    padding: '0.75rem',
                    outline: 'none',
                    fontSize: '1rem',
                    resize: 'vertical',
                    marginBottom: '1rem'
                  }}
                />

                <button
                  onClick={() => handleSharePost(showShareModal)}
                  style={{
                    width: '100%',
                    backgroundColor: '#1877f2',
                    color: 'white',
                    border: 'none',
                    borderRadius: '6px',
                    padding: '0.75rem',
                    fontSize: '1rem',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  Share Post
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
