import React from 'react'

export default function FeedPage() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f0f2f5' }}>
      {/* Facebook-style Header */}
      <div style={{ backgroundColor: '#1877f2', padding: '0.75rem 1rem', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h1 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: 'white', margin: 0 }}>
              KAAZMAAMAA
            </h1>
            <div style={{ backgroundColor: 'rgba(255,255,255,0.2)', borderRadius: '20px', padding: '0.5rem 1rem' }}>
              <input 
                type="text" 
                placeholder="Search KAAZMAAMAA..." 
                style={{ 
                  backgroundColor: 'transparent', 
                  border: 'none', 
                  color: 'white', 
                  outline: 'none',
                  fontSize: '0.875rem'
                }}
              />
            </div>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <a 
              href="/auth/login"
              style={{ 
                color: 'white', 
                textDecoration: 'none',
                padding: '0.5rem 1rem',
                border: '1px solid white',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Sign In
            </a>
            
            <a 
              href="/auth/signup"
              style={{ 
                backgroundColor: 'white', 
                color: '#1877f2', 
                textDecoration: 'none',
                padding: '0.5rem 1rem', 
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Register
            </a>
          </div>
        </div>
      </div>

      {/* Main Feed Layout */}
      <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'grid', gridTemplateColumns: '1fr 2fr 1fr', gap: '1rem', padding: '1rem' }}>
        
        {/* Left Sidebar */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>
            Quick Access
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <a href="/workers" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#e3f2fd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                🔧
              </div>
              <span style={{ fontWeight: '500' }}>Workers</span>
            </a>
            
            <a href="/suppliers" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#e8f5e8', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                🏭
              </div>
              <span style={{ fontWeight: '500' }}>Suppliers</span>
            </a>
            
            <a href="/companies" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#f3e8ff', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                🏢
              </div>
              <span style={{ fontWeight: '500' }}>Companies</span>
            </a>
            
            <a href="/barta" style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', textDecoration: 'none', color: '#1c1e21', padding: '0.5rem', borderRadius: '6px' }}>
              <div style={{ backgroundColor: '#fff3cd', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                💬
              </div>
              <span style={{ fontWeight: '500' }}>Barta Messenger</span>
            </a>
          </div>
        </div>

        {/* Center Feed */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
          
          {/* Create Post */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
            <div style={{ display: 'flex', gap: '0.75rem', marginBottom: '1rem' }}>
              <div style={{ backgroundColor: '#1877f2', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>
                U
              </div>
              <input 
                type="text" 
                placeholder="What's on your mind?" 
                style={{ 
                  flex: 1, 
                  backgroundColor: '#f0f2f5', 
                  border: 'none', 
                  borderRadius: '20px', 
                  padding: '0.75rem 1rem',
                  outline: 'none',
                  fontSize: '1rem'
                }}
              />
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>
              <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}>
                📷 Photo/Video
              </button>
              <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b' }}>
                😊 Feeling/Activity
              </button>
            </div>
          </div>

          {/* Sample Post 1 */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ backgroundColor: '#2563eb', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>
                A
              </div>
              <div>
                <div style={{ fontWeight: '600', color: '#1c1e21' }}>Ahmed Al-Rashid</div>
                <div style={{ fontSize: '0.8rem', color: '#65676b' }}>2 hours ago • 🔧 Worker</div>
              </div>
            </div>
            
            <div style={{ paddingLeft: '1rem', paddingRight: '1rem', marginBottom: '1rem' }}>
              <p style={{ color: '#1c1e21', lineHeight: '1.5', margin: 0 }}>
                Just completed a major electrical installation project in Riyadh! 💡 Looking for new opportunities in commercial electrical work. 
                Feel free to reach out if you need a certified electrician. #ElectricalWork #Riyadh #Professional
              </p>
            </div>
            
            <div style={{ backgroundColor: '#f0f2f5', height: '300px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#65676b', fontSize: '3rem' }}>
              🔌⚡
            </div>
            
            <div style={{ padding: '0.75rem 1rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>
                <span style={{ color: '#65676b', fontSize: '0.875rem' }}>👍 15 likes • 💬 3 comments</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>
                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>
                  👍 Like
                </button>
                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>
                  💬 Comment
                </button>
                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>
                  📤 Share
                </button>
              </div>
            </div>
          </div>

          {/* Sample Post 2 */}
          <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ backgroundColor: '#059669', borderRadius: '50%', width: '2.5rem', height: '2.5rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontWeight: 'bold' }}>
                S
              </div>
              <div>
                <div style={{ fontWeight: '600', color: '#1c1e21' }}>Saudi Building Supplies Co.</div>
                <div style={{ fontSize: '0.8rem', color: '#65676b' }}>5 hours ago • 🏭 Supplier</div>
              </div>
            </div>
            
            <div style={{ paddingLeft: '1rem', paddingRight: '1rem', marginBottom: '1rem' }}>
              <p style={{ color: '#1c1e21', lineHeight: '1.5', margin: 0 }}>
                🏗️ New shipment of premium construction materials just arrived! High-quality cement, steel, and building supplies now available. 
                Contact us for bulk orders and competitive pricing. #Construction #BuildingSupplies #Quality
              </p>
            </div>
            
            <div style={{ backgroundColor: '#f0f2f5', height: '250px', display: 'flex', alignItems: 'center', justifyContent: 'center', color: '#65676b', fontSize: '3rem' }}>
              🏗️🧱
            </div>
            
            <div style={{ padding: '0.75rem 1rem' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.75rem' }}>
                <span style={{ color: '#65676b', fontSize: '0.875rem' }}>👍 28 likes • 💬 7 comments</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e4e6ea' }}>
                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>
                  👍 Like
                </button>
                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>
                  💬 Comment
                </button>
                <button style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#65676b', fontWeight: '500' }}>
                  📤 Share
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', padding: '1rem', height: 'fit-content', boxShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
          <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#1c1e21', marginBottom: '1rem' }}>
            Online Now
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ position: 'relative' }}>
                <div style={{ backgroundColor: '#7c3aed', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>
                  M
                </div>
                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>
              </div>
              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Modern Tech Solutions</span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ position: 'relative' }}>
                <div style={{ backgroundColor: '#2563eb', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>
                  F
                </div>
                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>
              </div>
              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Fatima Al-Zahra</span>
            </div>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{ position: 'relative' }}>
                <div style={{ backgroundColor: '#059669', borderRadius: '50%', width: '2rem', height: '2rem', display: 'flex', alignItems: 'center', justifyContent: 'center', color: 'white', fontSize: '0.875rem', fontWeight: 'bold' }}>
                  K
                </div>
                <div style={{ position: 'absolute', bottom: '-2px', right: '-2px', backgroundColor: '#42b883', borderRadius: '50%', width: '0.75rem', height: '0.75rem', border: '2px solid white' }}></div>
              </div>
              <span style={{ fontSize: '0.875rem', color: '#1c1e21' }}>Khalid Construction</span>
            </div>
          </div>
          
          <div style={{ marginTop: '2rem', paddingTop: '1rem', borderTop: '1px solid #e4e6ea' }}>
            <a href="/" style={{ color: '#1877f2', textDecoration: 'none', fontSize: '0.875rem' }}>← Back to Home</a>
          </div>
        </div>
      </div>
    </div>
  )
}
