import React from 'react'

export default function FeedPage() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      {/* Top Header with Sign In and Register buttons */}
      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#f59e0b', margin: 0 }}>
              KAAZMAAMAA
            </h2>
            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Social Feed</span>
          </div>
          
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
            <a 
              href="/auth/login"
              style={{ 
                color: '#f59e0b', 
                textDecoration: 'none',
                padding: '0.5rem 1rem',
                border: '1px solid #f59e0b',
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Sign In
            </a>
            
            <a 
              href="/auth/signup"
              style={{ 
                backgroundColor: '#f59e0b', 
                color: 'white', 
                textDecoration: 'none',
                padding: '0.5rem 1rem', 
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500'
              }}
            >
              Register
            </a>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '2rem' }}>
        <div style={{ maxWidth: '900px', margin: '0 auto' }}>
          {/* Feed Header */}
          <div style={{ textAlign: 'center', marginBottom: '3rem' }}>
            <div style={{ fontSize: '4rem', marginBottom: '1rem' }}>📱</div>
            <h1 style={{ fontSize: '3rem', fontWeight: 'bold', color: '#f59e0b', marginBottom: '1rem' }}>
              Professional Social Feed
            </h1>
            <p style={{ fontSize: '1.25rem', color: '#6b7280', marginBottom: '2rem' }}>
              Connect, Share, and Engage with Your Business Network
            </p>
          </div>

          {/* Social Features */}
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem', marginBottom: '3rem' }}>
            {/* Share Posts */}
            <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>
              <div style={{ backgroundColor: '#fef3c7', borderRadius: '50%', width: '4rem', height: '4rem', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto 1rem' }}>
                <span style={{ fontSize: '1.5rem' }}>📝</span>
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#92400e', marginBottom: '0.75rem' }}>
                Share Professional Updates
              </h3>
              <p style={{ color: '#78350f', marginBottom: '1.5rem', lineHeight: '1.5' }}>
                Post about your work, achievements, projects, and professional insights to engage with your network.
              </p>
              <button style={{ 
                backgroundColor: '#f59e0b', 
                color: 'white', 
                padding: '0.75rem 1.5rem', 
                border: 'none', 
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontWeight: '500'
              }}>
                Create Post
              </button>
            </div>

            {/* Engage & Network */}
            <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>
              <div style={{ backgroundColor: '#dbeafe', borderRadius: '50%', width: '4rem', height: '4rem', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto 1rem' }}>
                <span style={{ fontSize: '1.5rem' }}>💬</span>
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1e40af', marginBottom: '0.75rem' }}>
                Engage & Comment
              </h3>
              <p style={{ color: '#1e3a8a', marginBottom: '1.5rem', lineHeight: '1.5' }}>
                Like, comment, and share posts from workers, suppliers, and companies in your professional network.
              </p>
              <button style={{ 
                backgroundColor: '#2563eb', 
                color: 'white', 
                padding: '0.75rem 1.5rem', 
                border: 'none', 
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontWeight: '500'
              }}>
                Browse Feed
              </button>
            </div>

            {/* Build Network */}
            <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', textAlign: 'center' }}>
              <div style={{ backgroundColor: '#dcfce7', borderRadius: '50%', width: '4rem', height: '4rem', display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '0 auto 1rem' }}>
                <span style={{ fontSize: '1.5rem' }}>🔗</span>
              </div>
              <h3 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#166534', marginBottom: '0.75rem' }}>
                Build Your Network
              </h3>
              <p style={{ color: '#14532d', marginBottom: '1.5rem', lineHeight: '1.5' }}>
                Connect with professionals, follow industry leaders, and expand your business relationships.
              </p>
              <button style={{ 
                backgroundColor: '#059669', 
                color: 'white', 
                padding: '0.75rem 1.5rem', 
                border: 'none', 
                borderRadius: '0.5rem',
                cursor: 'pointer',
                fontWeight: '500'
              }}>
                Find Connections
              </button>
            </div>
          </div>

          {/* Quick Access to Directories */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.75rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '3rem' }}>
            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#374151', marginBottom: '1.5rem', textAlign: 'center' }}>
              Connect with Professionals
            </h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              <a 
                href="/workers"
                style={{ 
                  display: 'block',
                  padding: '1rem', 
                  backgroundColor: '#eff6ff', 
                  borderRadius: '0.5rem', 
                  textDecoration: 'none',
                  textAlign: 'center',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🔧</div>
                <div style={{ fontWeight: '600', color: '#2563eb' }}>Workers</div>
                <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>Skilled Professionals</div>
              </a>
              
              <a 
                href="/suppliers"
                style={{ 
                  display: 'block',
                  padding: '1rem', 
                  backgroundColor: '#f0fdf4', 
                  borderRadius: '0.5rem', 
                  textDecoration: 'none',
                  textAlign: 'center',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🏭</div>
                <div style={{ fontWeight: '600', color: '#059669' }}>Suppliers</div>
                <div style={{ fontSize: '0.875rem', color: '#047857' }}>Reliable Vendors</div>
              </a>
              
              <a 
                href="/companies"
                style={{ 
                  display: 'block',
                  padding: '1rem', 
                  backgroundColor: '#faf5ff', 
                  borderRadius: '0.5rem', 
                  textDecoration: 'none',
                  textAlign: 'center',
                  transition: 'all 0.2s'
                }}
              >
                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🏢</div>
                <div style={{ fontWeight: '600', color: '#7c3aed' }}>Companies</div>
                <div style={{ fontSize: '0.875rem', color: '#6d28d9' }}>Business Partners</div>
              </a>
            </div>
          </div>

          {/* Barta Messaging */}
          <div style={{ backgroundColor: '#f59e0b', borderRadius: '0.75rem', padding: '2rem', textAlign: 'center', color: 'white' }}>
            <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>💬</div>
            <h3 style={{ fontSize: '1.5rem', fontWeight: '600', marginBottom: '1rem' }}>
              Barta Messenger
            </h3>
            <p style={{ marginBottom: '2rem', color: '#fef3c7', fontSize: '1.125rem' }}>
              Start private conversations, share files, and build stronger professional relationships.
            </p>
            <a 
              href="/barta"
              style={{ 
                display: 'inline-block',
                backgroundColor: 'white', 
                color: '#f59e0b', 
                padding: '1rem 2rem', 
                borderRadius: '0.5rem', 
                textDecoration: 'none',
                fontSize: '1.125rem',
                fontWeight: '600'
              }}
            >
              Open Barta Messenger
            </a>
          </div>

          {/* Navigation Footer */}
          <div style={{ marginTop: '3rem', textAlign: 'center', padding: '2rem' }}>
            <p style={{ color: '#9ca3af' }}>
              <a href="/" style={{ color: '#f59e0b', textDecoration: 'none' }}>← Back to Home</a> | 
              <a href="/workers" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Workers</a> | 
              <a href="/suppliers" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Suppliers</a> | 
              <a href="/companies" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Companies</a> | 
              <a href="/barta" style={{ color: '#f59e0b', textDecoration: 'none', marginLeft: '0.5rem' }}>Barta</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
