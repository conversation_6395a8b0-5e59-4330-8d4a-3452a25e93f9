export default function FeedPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto p-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">KAAZMAAMAA Feed</h1>
          <p className="text-xl text-gray-600 mb-8">Your Social Business Network</p>

          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Welcome to Your Feed!</h2>
            <p className="text-gray-600 mb-6">Connect with workers, suppliers, and companies in your network.</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="p-6 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-600 mb-2">Workers</h3>
                <p className="text-gray-600 mb-4">Find skilled professionals</p>
                <a href="/workers" className="inline-block bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                  Browse Workers
                </a>
              </div>

              <div className="p-6 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-semibold text-green-600 mb-2">Suppliers</h3>
                <p className="text-gray-600 mb-4">Connect with suppliers</p>
                <a href="/suppliers" className="inline-block bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                  Browse Suppliers
                </a>
              </div>

              <div className="p-6 border border-gray-200 rounded-lg">
                <h3 className="text-lg font-semibold text-purple-600 mb-2">Companies</h3>
                <p className="text-gray-600 mb-4">Network with businesses</p>
                <a href="/companies" className="inline-block bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700">
                  Browse Companies
                </a>
              </div>
            </div>

            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Barta Messaging</h3>
              <p className="text-gray-600 mb-4">Start conversations with other users</p>
              <a href="/barta" className="inline-block bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                Open Barta Messenger
              </a>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-gray-500">
                <a href="/" className="text-blue-600 hover:underline">← Back to Home</a> |
                <a href="/auth/login" className="text-blue-600 hover:underline ml-2">Sign In</a> |
                <a href="/invoice" className="text-blue-600 hover:underline ml-2">Invoices</a>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

  // Show sign-in screen if no user
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-4xl mx-auto p-4">
          <div className="text-center py-12">
            <MessageCircle className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">KAAZMAAMAA Feed</h1>
            <p className="text-gray-600 mb-6">Please sign in to access your personalized feed and connect with other users.</p>

            <div className="space-y-4">
              <Button
                onClick={() => router.push('/auth/login')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Sign In
              </Button>

              <Button
                variant="outline"
                onClick={() => router.push('/')}
              >
                Go to Home
              </Button>
            </div>

            <div className="mt-8 p-4 bg-white rounded-lg shadow">
              <h2 className="text-lg font-semibold mb-2">Feed Features:</h2>
              <ul className="text-left space-y-1 text-gray-600">
                <li>• Share posts and updates</li>
                <li>• Connect with workers, suppliers, and companies</li>
                <li>• Like and comment on posts</li>
                <li>• Real-time messaging with Barta</li>
                <li>• Professional networking</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Main feed interface for authenticated users
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">KAAZMAAMAA Feed</h1>
              <div className="hidden md:flex items-center space-x-2">
                <span className="text-sm text-gray-600">Welcome back,</span>
                <span className="text-sm font-medium text-blue-600">{user.email}</span>
                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                  {userRole || 'user'}
                </span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/')}
                className="hover:bg-gray-100"
              >
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/workers')}
                className="hover:bg-blue-50 hover:text-blue-600"
              >
                <Wrench className="h-4 w-4 mr-2" />
                Workers
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/suppliers')}
                className="hover:bg-green-50 hover:text-green-600"
              >
                <Building className="h-4 w-4 mr-2" />
                Suppliers
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/companies')}
                className="hover:bg-purple-50 hover:text-purple-600"
              >
                <Users className="h-4 w-4 mr-2" />
                Companies
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/barta')}
                className="hover:bg-blue-50 hover:text-blue-600"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Barta
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto p-4">
        <div className="text-center py-12">
          <MessageCircle className="h-16 w-16 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to Your Feed!</h2>
          <p className="text-gray-600 mb-6">Your personalized social feed is ready. Start connecting with other users!</p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
            <div className="p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow">
              <Wrench className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Connect with Workers</h3>
              <p className="text-sm text-gray-600 mb-4">Find skilled professionals and workers</p>
              <Button
                size="sm"
                onClick={() => router.push('/workers')}
                className="w-full"
              >
                Browse Workers
              </Button>
            </div>

            <div className="p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow">
              <Building className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Find Suppliers</h3>
              <p className="text-sm text-gray-600 mb-4">Connect with reliable suppliers</p>
              <Button
                size="sm"
                onClick={() => router.push('/suppliers')}
                className="w-full"
              >
                Browse Suppliers
              </Button>
            </div>

            <div className="p-6 bg-white rounded-lg shadow hover:shadow-md transition-shadow">
              <Users className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Partner with Companies</h3>
              <p className="text-sm text-gray-600 mb-4">Network with businesses</p>
              <Button
                size="sm"
                onClick={() => router.push('/companies')}
                className="w-full"
              >
                Browse Companies
              </Button>
            </div>
          </div>

          <div className="mt-8 p-6 bg-blue-50 rounded-lg">
            <MessageCircle className="h-8 w-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold text-gray-900 mb-2">Start Messaging with Barta</h3>
            <p className="text-sm text-gray-600 mb-4">Connect instantly with other users using our messaging system</p>
            <Button
              onClick={() => router.push('/barta')}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Open Barta Messenger
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

  // Debug user role detection
  console.log('🔍 Feed page - Loading state:', loading)
  console.log('🔍 Feed page - Current user:', user)
  console.log('🔍 Feed page - Detected userRole:', userRole)
  console.log('🔍 Feed page - Available profiles:', {
    workers: workers.length,
    suppliers: suppliers.length,
    companies: companies.length
  })

  // State for CREATE POST modal
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [modalPostContent, setModalPostContent] = useState('')
  const [isModalCreatingPost, setIsModalCreatingPost] = useState(false)

  // State for media attachments
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [selectedVideos, setSelectedVideos] = useState<File[]>([])
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [postType, setPostType] = useState<'text' | 'image' | 'video' | 'mixed'>('text')

  // State for comments
  const [showComments, setShowComments] = useState<{ [postId: string]: boolean }>({})
  const [commentContent, setCommentContent] = useState<{ [postId: string]: string }>({})
  const [isAddingComment, setIsAddingComment] = useState<{ [postId: string]: boolean }>({})

  // Ref to track if user has been set online to prevent unnecessary updates
  const userOnlineSetRef = useRef<string | null>(null)

  // Utility function to get user's full name based on their actual profile type
  const getUserFullName = (userId: string, fallbackEmail?: string): string => {
    console.log('🔍 getUserFullName called with:', { userId, fallbackEmail, currentUserRole: userRole })

    // For current user, use their actual profile based on their role
    if (userId === user?.id || userId === user?.email) {
      console.log('🔍 Getting name for current user with role:', userRole)

      if (userRole === 'worker') {
        const workerProfile = workers.find(w => w.userId === user?.email || w.userId === user?.id)
        if (workerProfile?.personalInfo.workerName) {
          console.log('✅ Found current user worker profile:', workerProfile.personalInfo.workerName)
          return workerProfile.personalInfo.workerName
        }
      } else if (userRole === 'supplier') {
        const supplierProfile = suppliers.find(s => s.userId === user?.email || s.userId === user?.id)
        if (supplierProfile?.personalInfo?.supplierName) {
          console.log('✅ Found current user supplier profile:', supplierProfile.personalInfo.supplierName)
          return supplierProfile.personalInfo.supplierName
        }
      } else if (userRole === 'company') {
        const companyProfile = companies.find(c => c.userId === user?.email || c.userId === user?.id)
        if (companyProfile?.companyInfo?.companyName) {
          console.log('✅ Found current user company profile:', companyProfile.companyInfo.companyName)
          return companyProfile.companyInfo.companyName
        }
      }
    }

    // For other users, check all profile types
    // Check workers
    const workerProfile = workers.find(w => w.userId === userId)
    if (workerProfile?.personalInfo.workerName) {
      console.log('✅ Found worker profile:', workerProfile.personalInfo.workerName)
      return workerProfile.personalInfo.workerName
    }

    // Check suppliers
    const supplierProfile = suppliers.find(s => s.userId === userId)
    if (supplierProfile?.personalInfo?.supplierName) {
      console.log('✅ Found supplier profile:', supplierProfile.personalInfo.supplierName)
      return supplierProfile.personalInfo.supplierName
    }

    // Check companies
    const companyProfile = companies.find(c => c.userId === userId)
    if (companyProfile?.companyInfo?.companyName) {
      console.log('✅ Found company profile:', companyProfile.companyInfo.companyName)
      return companyProfile.companyInfo.companyName
    }

    // Then try to find in online users
    const onlineUser = onlineUsers.find(u => u.id === userId)
    if (onlineUser?.name && !onlineUser.name.includes('@')) {
      console.log('✅ Found online user:', onlineUser.name)
      return onlineUser.name
    }

    // Fallback to email-based name but make it more readable
    if (fallbackEmail) {
      const emailName = fallbackEmail.split('@')[0] || 'Unknown User'
      // Convert email-based names to more readable format
      const readableName = emailName
        .replace(/[0-9]/g, '') // Remove numbers
        .replace(/[._-]/g, ' ') // Replace special chars with spaces
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize
        .join(' ')
        .trim()

      const finalName = readableName || emailName
      console.log('Using email fallback:', finalName)
      return finalName
    }

    console.log('Returning Unknown User')
    return 'Unknown User'
  }

  // Remove the redirect useEffect - let the conditional render handle it instead
  // This prevents conflicts between redirect and conditional rendering

  useEffect(() => {
    // Set user as online when they access the feed (only if not already set)
    if (user && userRole) {
      const currentUserId = user.id
      if (userOnlineSetRef.current !== currentUserId) {
        setUserOnline({
          id: currentUserId,
          name: getUserFullName(currentUserId, user.email),
          email: user.email,
          role: userRole
        })
        userOnlineSetRef.current = currentUserId
      }
    }
  }, [user?.id, user?.email, userRole, setUserOnline])

  // Automatic cleanup of expired posts on app load and periodically
  useEffect(() => {
    // Run cleanup when component mounts
    cleanupExpiredPosts()

    // Set up periodic cleanup every hour
    const cleanupInterval = setInterval(() => {
      cleanupExpiredPosts()
    }, 60 * 60 * 1000) // 1 hour in milliseconds

    // Cleanup interval on unmount
    return () => clearInterval(cleanupInterval)
  }, [cleanupExpiredPosts]) // Now safe to include since it's memoized with useCallback

  // Function to toggle comments visibility
  const toggleComments = (postId: string) => {
    setShowComments(prev => ({
      ...prev,
      [postId]: !prev[postId]
    }))
  }

  // Function to handle comment submission
  const handleAddComment = (postId: string) => {
    const content = commentContent[postId]?.trim()
    if (!content) {
      toast.error('Please write a comment!')
      return
    }

    setIsAddingComment(prev => ({ ...prev, [postId]: true }))

    try {
      addComment(postId, content, {
        id: user?.id || 'current-user',
        name: getUserFullName(user?.id || 'current-user', user?.email),
        role: userRole || 'worker'
      })

      // Clear comment input
      setCommentContent(prev => ({ ...prev, [postId]: '' }))
      toast.success('Comment added! 💬')
    } catch (error) {
      console.error('Error adding comment:', error)
      toast.error('Failed to add comment')
    } finally {
      setIsAddingComment(prev => ({ ...prev, [postId]: false }))
    }
  }

  // Helper functions for media handling
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    const imageFiles = files.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length > 5) {
      toast.error('Maximum 5 images allowed per post')
      return
    }

    setSelectedImages(prev => [...prev, ...imageFiles].slice(0, 5))
    updatePostType([...selectedImages, ...imageFiles], selectedVideos)
    toast.success(`${imageFiles.length} image(s) added!`)
  }

  const handleVideoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    const videoFiles = files.filter(file => file.type.startsWith('video/'))

    if (videoFiles.length > 2) {
      toast.error('Maximum 2 videos allowed per post')
      return
    }

    setSelectedVideos(prev => [...prev, ...videoFiles].slice(0, 2))
    updatePostType(selectedImages, [...selectedVideos, ...videoFiles])
    toast.success(`${videoFiles.length} video(s) added!`)
  }

  const updatePostType = (images: File[], videos: File[]) => {
    if (images.length > 0 && videos.length > 0) {
      setPostType('mixed')
    } else if (images.length > 0) {
      setPostType('image')
    } else if (videos.length > 0) {
      setPostType('video')
    } else {
      setPostType('text')
    }
  }

  const removeImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index)
    setSelectedImages(newImages)
    updatePostType(newImages, selectedVideos)
    toast.info('Image removed')
  }

  const removeVideo = (index: number) => {
    const newVideos = selectedVideos.filter((_, i) => i !== index)
    setSelectedVideos(newVideos)
    updatePostType(selectedImages, newVideos)
    toast.info('Video removed')
  }

  const addEmoji = (emoji: string) => {
    setModalPostContent(prev => prev + emoji)
    setShowEmojiPicker(false)
    toast.success(`${emoji} added!`)
  }

  const clearAllMedia = () => {
    setSelectedImages([])
    setSelectedVideos([])
    setPostType('text')
    toast.info('All media cleared')
  }

  // Function to create a new post
  const handleCreatePost = () => {
    if (!modalPostContent.trim() && selectedImages.length === 0 && selectedVideos.length === 0) {
      toast.error('Please add some content, images, or videos to post!')
      return
    }

    setIsModalCreatingPost(true)

    try {
      // Create URLs for media files (handle both real files and mock files)
      const imageUrls = selectedImages.map(file => {
        // Check if it's a mock file with preview URL
        if ((file as any).preview) {
          return (file as any).preview
        }
        // Otherwise create object URL for real file
        return URL.createObjectURL(file)
      })

      const videoUrls = selectedVideos.map(file => {
        // Check if it's a mock file with preview URL
        if ((file as any).preview) {
          return (file as any).preview
        }
        // Otherwise create object URL for real file
        return URL.createObjectURL(file)
      })

      const mediaUrls = [...imageUrls, ...videoUrls]

      const newPost = {
        user_id: user?.id || 'current-user',
        user_name: getUserFullName(user?.id || 'current-user', user?.email),
        user_role: userRole || 'worker',
        content: modalPostContent.trim(),
        post_type: postType,
        visibility: 'public' as const,
        media_urls: mediaUrls.length > 0 ? mediaUrls : undefined
      }

      addPost(newPost)

      // Clear all form data
      setModalPostContent('')
      setSelectedImages([])
      setSelectedVideos([])
      setPostType('text')
      setShowEmojiPicker(false)
      setShowCreateModal(false)

      toast.success('Post created successfully! 🎉')
    } catch (error) {
      console.error('Error creating post:', error)
      toast.error('Failed to create post')
    } finally {
      setIsModalCreatingPost(false)
    }
  }

  // Show loading screen while checking authentication
  if (loading) {
    console.log('🔄 Feed: Still loading, showing loading screen')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Loading Feed...</h1>
          <p className="text-gray-600">Checking your authentication status</p>
          <p className="text-xs text-gray-400 mt-2">Loading: {loading ? 'true' : 'false'}, User: {user ? 'found' : 'not found'}</p>
        </div>
      </div>
    )
  }

  // TEMPORARY: Force show feed even without user for debugging
  console.log('🔍 Feed: Loading complete, user state:', user)
  console.log('🔍 Feed: About to render feed interface')

  // TEMPORARY: Comment out user check for debugging
  /*
  if (!user) {
    console.log('🔍 Feed: Showing access denied screen - no user found')
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="mb-6">
            <MessageCircle className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome to KAAZMAAMAA Feed</h1>
            <p className="text-gray-600 mb-6">Please sign in to access your personalized feed and connect with other users.</p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={() => router.push('/auth/login')}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Sign In
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/')}
              className="w-full"
            >
              Go to Home
            </Button>
          </div>

          <p className="text-sm text-gray-500 mt-4">
            Don't have an account? <button
              onClick={() => router.push('/auth/signup')}
              className="text-blue-600 hover:underline"
            >
              Sign up here
            </button>
          </p>
        </div>
      </div>
    )
  }
  */



  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'worker':
        return <Wrench className="h-4 w-4 text-blue-600" />
      case 'supplier':
        return <Users className="h-4 w-4 text-green-600" />
      case 'company':
        return <Building className="h-4 w-4 text-purple-600" />
      default:
        return <Users className="h-4 w-4 text-gray-600" />
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center space-x-2 lg:space-x-4 flex-shrink-0">
              <h1 className="text-xl lg:text-2xl font-bold text-blue-600">KAAZMAAMAA</h1>
              <nav className="hidden lg:flex space-x-2 xl:space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="bg-blue-50 text-blue-600 flex-shrink-0"
                >
                  <Home className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Feed</span>
                  <span className="xl:hidden">Feed</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Network button clicked!')
                    toast.info('Navigating to Network...')
                    router.push('/network')
                  }}
                  className="hover:bg-green-50 hover:text-green-600 transition-colors flex-shrink-0"
                >
                  <Users className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Network</span>
                  <span className="xl:hidden">Network</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Workers button clicked!')
                    toast.info('Navigating to Workers Block...')
                    router.push('/workers')
                  }}
                  className="hover:bg-blue-50 hover:text-blue-600 transition-colors flex-shrink-0"
                >
                  <Wrench className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Workers</span>
                  <span className="xl:hidden">Workers</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Suppliers button clicked!')
                    toast.info('Navigating to Suppliers Block...')
                    router.push('/suppliers')
                  }}
                  className="hover:bg-green-50 hover:text-green-600 transition-colors flex-shrink-0"
                >
                  <Building className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Suppliers</span>
                  <span className="xl:hidden">Suppliers</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Companies button clicked!')
                    toast.info('Navigating to Companies Block...')
                    router.push('/companies')
                  }}
                  className="hover:bg-purple-50 hover:text-purple-600 transition-colors flex-shrink-0"
                >
                  <Building className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Companies</span>
                  <span className="xl:hidden">Companies</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Barta button clicked!')
                    toast.success('Opening Barta Messenger...')
                    router.push('/barta')
                  }}
                  className="hover:bg-blue-50 hover:text-blue-600 transition-colors flex-shrink-0 relative"
                >
                  <MessageCircle className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Barta</span>
                  <span className="xl:hidden">Barta</span>
                  {getUnreadCount() > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {getUnreadCount() > 9 ? '9+' : getUnreadCount()}
                    </span>
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Directory button clicked!')
                    toast.info('Navigating to Directory...')
                    router.push('/directory')
                  }}
                  className="hover:bg-gray-50 hover:text-gray-600 transition-colors flex-shrink-0"
                >
                  <Users className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Directory</span>
                  <span className="xl:hidden">Directory</span>
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    console.log('Invoice button clicked!')
                    toast.info('Navigating to ZATCA Invoice System...')
                    router.push('/invoice')
                  }}
                  className="hover:bg-green-50 hover:text-green-600 transition-colors flex-shrink-0"
                >
                  <Receipt className="h-4 w-4 mr-1 xl:mr-2" />
                  <span className="hidden xl:inline">Invoice</span>
                  <span className="xl:hidden">Invoice</span>
                </Button>
              </nav>
            </div>
            <div className="flex items-center space-x-1 lg:space-x-3 flex-shrink-0">
              <span className="hidden xl:block text-sm text-gray-600 truncate max-w-[200px]">
                Welcome, {getUserFullName(user?.id || 'current-user', user?.email)}
              </span>
              <span className="hidden lg:block text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded">
                Posts: {posts.length}
              </span>
              <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                <span className="hidden sm:inline">{totalOnlineCount} Online</span>
                <span className="sm:hidden">{totalOnlineCount}</span>
              </span>
              <span className="hidden md:block text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                💾 Persistent
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  toast.success('🔗 Opening your profile...')
                  router.push(`/profile/${user?.id || 'current-user'}`)
                }}
                className="flex-shrink-0 border-blue-200 text-blue-600 hover:bg-blue-50"
              >
                <User className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">My Profile</span>
                <span className="sm:hidden">Profile</span>
              </Button>
              <Button variant="outline" size="sm" onClick={signOut} className="flex-shrink-0">
                <span className="hidden sm:inline">Sign Out</span>
                <span className="sm:hidden">Out</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation */}
      <div className="lg:hidden bg-white border-b px-2 py-2">
        <div className="flex justify-start space-x-1 overflow-x-auto pb-1 scrollbar-hide">
          <Button
            variant="ghost"
            size="sm"
            className="bg-blue-50 text-blue-600 text-xs flex-shrink-0 min-w-fit"
          >
            <Home className="h-3 w-3 mr-1" />
            Feed
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Network button clicked!')
              toast.info('Navigating to Network...')
              router.push('/network')
            }}
            className="hover:bg-green-50 hover:text-green-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <Users className="h-3 w-3 mr-1" />
            Network
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Workers button clicked!')
              toast.info('Navigating to Workers...')
              router.push('/workers')
            }}
            className="hover:bg-blue-50 hover:text-blue-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <Wrench className="h-3 w-3 mr-1" />
            Workers
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Suppliers button clicked!')
              toast.info('Navigating to Suppliers...')
              router.push('/suppliers')
            }}
            className="hover:bg-green-50 hover:text-green-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <Building className="h-3 w-3 mr-1" />
            Suppliers
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Companies button clicked!')
              toast.info('Navigating to Companies...')
              router.push('/companies')
            }}
            className="hover:bg-purple-50 hover:text-purple-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <Building className="h-3 w-3 mr-1" />
            Companies
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Directory button clicked!')
              toast.info('Navigating to Directory...')
              router.push('/directory')
            }}
            className="hover:bg-gray-50 hover:text-gray-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <Users className="h-3 w-3 mr-1" />
            Directory
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Invoice button clicked!')
              toast.info('Navigating to Invoice...')
              router.push('/invoice')
            }}
            className="hover:bg-green-50 hover:text-green-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <Receipt className="h-3 w-3 mr-1" />
            Invoice
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Barta button clicked!')
              toast.success('Opening Barta Messenger...')
              router.push('/barta')
            }}
            className="hover:bg-blue-50 hover:text-blue-600 transition-colors text-xs flex-shrink-0 min-w-fit relative"
          >
            <MessageCircle className="h-3 w-3 mr-1" />
            Barta
            {getUnreadCount() > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {getUnreadCount() > 9 ? '9+' : getUnreadCount()}
              </span>
            )}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              console.log('Mobile Profile button clicked!')
              toast.success('🔗 Opening your profile...')
              router.push(`/profile/${user?.id || 'current-user'}`)
            }}
            className="hover:bg-blue-50 hover:text-blue-600 transition-colors text-xs flex-shrink-0 min-w-fit"
          >
            <User className="h-3 w-3 mr-1" />
            My Profile
          </Button>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-2 sm:px-4 py-4 lg:py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Sidebar - Online Users */}
          <div className="lg:col-span-1 space-y-4">
            {/* Your Profile */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Your Profile</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3 mb-4">
                  <div className="relative">
                    <Avatar className="h-12 w-12 bg-blue-100 flex items-center justify-center">
                      {getRoleIcon(userRole || 'worker')}
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium">{getUserFullName(user?.id || 'current-user', user?.email)}</p>
                    <p className="text-sm text-gray-500 capitalize">{userRole}</p>
                    <p className="text-xs text-green-600">● Online</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Button
                    className="w-full"
                    size="sm"
                    onClick={() => {
                      toast.success('🔗 Opening your profile...')
                      router.push(`/profile/${user?.id || 'current-user'}`)
                    }}
                  >
                    View My Profile
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    size="sm"
                    onClick={() => router.push('/profile/setup')}
                  >
                    Edit Profile
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Online Users - Horizontal Layout */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>Who's Online</span>
                  <span className="text-sm font-normal bg-green-100 text-green-700 px-2 py-1 rounded-full">
                    {totalOnlineCount} online
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Horizontal scrollable container */}
                <div className="flex space-x-4 overflow-x-auto pb-2 scrollbar-hide">
                  {onlineUsers
                    .filter(onlineUser => onlineUser.isOnline && onlineUser.id !== user?.id)
                    .slice(0, 15) // Show max 15 users horizontally
                    .map((onlineUser) => (
                      <div
                        key={onlineUser.id}
                        className="flex-shrink-0 text-center p-2 hover:bg-blue-50 rounded-lg cursor-pointer transition-all duration-200 min-w-[80px] hover:shadow-lg hover:scale-105 border border-transparent hover:border-blue-200"
                        onClick={() => {
                          console.log('Clicking on user:', onlineUser.name)
                          toast.success(`🔗 Opening ${onlineUser.name}'s profile...`)
                          router.push(`/profile/${onlineUser.id}`)
                        }}
                        title={`Click to view ${onlineUser.name}'s profile`}
                      >
                        <div className="relative mx-auto mb-2">
                          <Avatar className="h-12 w-12 bg-gray-100 flex items-center justify-center hover:scale-105 transition-transform">
                            {getRoleIcon(onlineUser.role)}
                          </Avatar>
                          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
                        </div>
                        <div className="text-center">
                          <p className="font-medium text-xs truncate max-w-[70px] hover:text-blue-600">{onlineUser.name}</p>
                          <p className="text-xs text-gray-500 capitalize">{onlineUser.role}</p>
                          <p className="text-xs text-green-600">● {formatLastSeen(onlineUser.lastSeen)}</p>
                        </div>
                      </div>
                    ))}

                  {/* Show more button if there are more users */}
                  {onlineUsers.filter(u => u.isOnline && u.id !== user?.id).length > 15 && (
                    <div className="flex-shrink-0 text-center p-2 min-w-[80px]">
                      <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-2 cursor-pointer hover:bg-gray-300 transition-colors">
                        <span className="text-sm font-bold text-gray-600">+{onlineUsers.filter(u => u.isOnline && u.id !== user?.id).length - 15}</span>
                      </div>
                      <p className="text-xs text-gray-500">More</p>
                    </div>
                  )}
                </div>

                {onlineUsers.filter(u => u.isOnline && u.id !== user?.id).length === 0 && (
                  <div className="text-center py-6 text-gray-500">
                    <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm">No other users online</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Feed */}
          <div className="lg:col-span-2 space-y-6">
            {/* Main CREATE POST Card */}
            <Card className="border-2 border-dashed border-blue-300 hover:border-blue-400 transition-colors">
              <CardContent className="pt-6">
                <div className="text-center space-y-4">
                  <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Plus className="h-8 w-8 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Share Something</h3>
                    <p className="text-gray-600">Create a post to share with the community</p>
                  </div>

                  {/* Main CREATE POST Button */}
                  <Button
                    onClick={() => setShowCreateModal(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3"
                    size="lg"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    CREATE POST
                  </Button>

                  {/* Quick Media Buttons */}
                  <div className="flex justify-center space-x-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowCreateModal(true)}
                      className="hover:bg-blue-50 hover:text-blue-600"
                    >
                      <Image className="h-4 w-4 mr-2" />
                      📸 Add Photos
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowCreateModal(true)}
                      className="hover:bg-purple-50 hover:text-purple-600"
                    >
                      <Video className="h-4 w-4 mr-2" />
                      🎥 Add Videos
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowCreateModal(true)}
                      className="hover:bg-yellow-50 hover:text-yellow-600"
                    >
                      <Smile className="h-4 w-4 mr-2" />
                      😀 Add Emojis
                    </Button>
                  </div>

                  {/* Quick Actions */}
                  <div className="flex justify-center space-x-2 pt-2 flex-wrap gap-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        const testPost = {
                          user_id: user?.id || 'quick-test',
                          user_name: user?.email?.split('@')[0] || 'Quick User',
                          user_role: userRole || 'worker',
                          content: `Quick test post from ${userRole || 'worker'} at ${new Date().toLocaleTimeString()}! 🚀`,
                          post_type: 'text' as const,
                          visibility: 'public' as const
                        }
                        addPost(testPost)
                        toast.success('Quick post created!')
                      }}
                    >
                      ⚡ Quick Test
                    </Button>

                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        const emojiPost = {
                          user_id: user?.id || 'emoji-test',
                          user_name: user?.email?.split('@')[0] || 'Emoji User',
                          user_role: userRole || 'worker',
                          content: `🎉 Exciting news! Just completed a major project! 💼✨ Looking forward to new opportunities! 🚀 #WorkLife #Success #KAAZMAAMAA 😊👷‍♂️💪`,
                          post_type: 'text' as const,
                          visibility: 'public' as const
                        }
                        addPost(emojiPost)
                        toast.success('Emoji post created! 🎉')
                      }}
                    >
                      😀 Emoji Test
                    </Button>

                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        // Create a mock image post
                        const imagePost = {
                          user_id: user?.id || 'image-test',
                          user_name: getUserFullName(user?.id || 'image-test', user?.email),
                          user_role: userRole || 'worker',
                          content: `Check out this amazing project I just completed! 📸✨ #WorkShowcase #Professional`,
                          post_type: 'image' as const,
                          visibility: 'public' as const,
                          media_urls: [
                            'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=500&h=300&fit=crop',
                            'https://images.unsplash.com/photo-1581092795442-8d6c0b8b5b8e?w=500&h=300&fit=crop'
                          ]
                        }
                        addPost(imagePost)
                        toast.success('Image post created! 📸')
                      }}
                    >
                      📸 Image Test
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('🔄 Refreshing user role...')
                        refreshUserRole()
                        toast.success('User role refreshed! Check console for details.')
                      }}
                      className="border-orange-200 text-orange-600 hover:bg-orange-50"
                    >
                      🔄 Refresh Role
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('Network test button clicked!')
                        toast.success('Testing Network navigation...')
                        router.push('/network')
                      }}
                      className="hover:bg-green-50 hover:text-green-600"
                    >
                      🌐 Network
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.success('Opening Workers Block...')
                        router.push('/workers')
                      }}
                      className="hover:bg-blue-50 hover:text-blue-600"
                    >
                      👷 Workers
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.success('Opening Suppliers Block...')
                        router.push('/suppliers')
                      }}
                      className="hover:bg-green-50 hover:text-green-600"
                    >
                      🏭 Suppliers
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.success('Opening Companies Block...')
                        router.push('/companies')
                      }}
                      className="hover:bg-purple-50 hover:text-purple-600"
                    >
                      🏢 Companies
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.success('Opening Directory...')
                        router.push('/directory')
                      }}
                      className="hover:bg-gray-50 hover:text-gray-600"
                    >
                      📋 Directory
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.success('Opening ZATCA Invoice System...')
                        router.push('/invoice')
                      }}
                      className="hover:bg-green-50 hover:text-green-600"
                    >
                      🧾 Invoice
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push('/create-post')}
                    >
                      📝 Advanced
                    </Button>
                  </div>

                  {/* Debug Media Features */}
                  <div className="flex justify-center space-x-2 pt-2 border-t mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowCreateModal(true)
                        // Auto-open emoji picker for testing
                        setTimeout(() => setShowEmojiPicker(true), 100)
                        toast.info('Opening create modal with emoji picker!')
                      }}
                    >
                      😀 Test Emoji Picker
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowCreateModal(true)
                        toast.info('Test file upload by clicking "Add Images" or "Add Videos"!')
                      }}
                    >
                      📁 Test File Upload
                    </Button>

                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        clearAllPosts()
                        toast.success('All posts cleared! Refresh to test persistence.')
                      }}
                    >
                      🗑️ Clear All
                    </Button>

                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        resetToInitialPosts()
                        toast.success('Reset to initial posts!')
                      }}
                    >
                      🔄 Reset
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        cleanupExpiredPosts()
                        toast.success('Expired posts cleaned up! 🧹')
                      }}
                      className="border-orange-200 text-orange-600 hover:bg-orange-50"
                    >
                      🧹 Cleanup Expired
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        migratePostsToFullNames()
                        toast.success('Posts migrated to use full names! 👤')
                      }}
                      className="border-blue-200 text-blue-600 hover:bg-blue-50"
                    >
                      👤 Fix Names
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('=== DEBUG INFO ===')
                        console.log('Posts:', posts)
                        console.log('Posts user names:', posts.map(p => ({ id: p.user_id, name: p.user_name })))
                        console.log('Workers:', workers)
                        console.log('Online Users:', onlineUsers)
                        console.log('Online Users names:', onlineUsers.map(u => ({ id: u.id, name: u.name })))
                        console.log('Current User:', user)
                        console.log('User Role:', userRole)
                        toast.info('Check console for debug info! 🐛')
                      }}
                      className="border-purple-200 text-purple-600 hover:bg-purple-50"
                    >
                      🐛 Debug
                    </Button>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        console.log('Testing profile redirect to mock-user-1...')
                        toast.success('🔗 Testing redirect to Ahmed Al-Rashid profile...')
                        router.push('/profile/mock-user-1')
                      }}
                      className="border-green-200 text-green-600 hover:bg-green-50"
                    >
                      🧪 Test Profile
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>


            {/* Posts */}
            {posts.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <p className="text-gray-500 mb-4">No posts yet. Be the first to share something!</p>
                  <Button onClick={() => router.push('/create-post')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Post
                  </Button>
                </CardContent>
              </Card>
            ) : (
              posts.map((post) => (
                <Card key={post.id}>
                  <CardHeader>
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <Avatar className="h-10 w-10 bg-gray-100 flex items-center justify-center">
                          {getRoleIcon(post.user_role)}
                        </Avatar>
                        {onlineUsers.find(u => u.id === post.user_id)?.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <p
                            className="font-medium hover:text-blue-600 cursor-pointer transition-colors hover:underline"
                            onClick={() => {
                              console.log('Profile click:', {
                                user_name: post.user_name,
                                user_id: post.user_id,
                                post: post
                              })
                              toast.success(`🔗 Opening ${post.user_name}'s profile...`)
                              router.push(`/profile/${post.user_id}`)
                            }}
                            title={`Click to view ${post.user_name}'s profile`}
                          >
                            {post.user_name}
                          </p>
                          {onlineUsers.find(u => u.id === post.user_id)?.isOnline && (
                            <span className="text-xs text-green-600 bg-green-50 px-1.5 py-0.5 rounded">● Online</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <span className="capitalize">{post.user_role}</span>
                          <span>•</span>
                          <span>{formatTimeAgo(post.created_at)}</span>
                          {post.expires_at && (
                            <>
                              <span>•</span>
                              <span className={`${
                                formatExpirationInfo(post.expires_at).isExpiringSoon
                                  ? 'text-orange-600 font-medium'
                                  : 'text-gray-400'
                              }`}>
                                {formatExpirationInfo(post.expires_at).text}
                              </span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-800 mb-4">{post.content}</p>

                    {/* Media Display */}
                    {post.media_urls && post.media_urls.length > 0 && (
                      <div className="mb-4">
                        {/* Post Type Badge */}
                        <div className="flex items-center space-x-2 mb-3">
                          {post.post_type === 'image' && (
                            <div className="flex items-center space-x-1 text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
                              <Image className="h-4 w-4" />
                              <span>{post.media_urls.length} Image{post.media_urls.length > 1 ? 's' : ''}</span>
                            </div>
                          )}
                          {post.post_type === 'video' && (
                            <div className="flex items-center space-x-1 text-sm text-purple-600 bg-purple-50 px-2 py-1 rounded">
                              <Video className="h-4 w-4" />
                              <span>{post.media_urls.length} Video{post.media_urls.length > 1 ? 's' : ''}</span>
                            </div>
                          )}
                          {post.post_type === 'mixed' && (
                            <div className="flex items-center space-x-1 text-sm text-green-600 bg-green-50 px-2 py-1 rounded">
                              <Camera className="h-4 w-4" />
                              <span>{post.media_urls.length} Media Files</span>
                            </div>
                          )}
                        </div>

                        {/* Media Grid */}
                        <div className={`grid gap-2 ${
                          post.media_urls.length === 1 ? 'grid-cols-1' :
                          post.media_urls.length === 2 ? 'grid-cols-2' :
                          post.media_urls.length === 3 ? 'grid-cols-2 md:grid-cols-3' :
                          'grid-cols-2 md:grid-cols-3'
                        }`}>
                          {post.media_urls.map((url, index) => {
                            // Determine if it's an image or video based on URL or file extension
                            const isVideo = url.includes('video') || url.includes('.mp4') || url.includes('.webm') || url.includes('.mov')

                            return (
                              <div key={index} className="relative rounded-lg overflow-hidden group">
                                {isVideo ? (
                                  <video
                                    src={url}
                                    controls
                                    className="w-full h-auto max-h-64 object-cover"
                                    poster="" // You can add a poster image here
                                  />
                                ) : (
                                  <img
                                    src={url}
                                    alt={`Post media ${index + 1}`}
                                    className="w-full h-auto max-h-64 object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                    onClick={() => {
                                      // Open image in modal/lightbox (can be implemented later)
                                      toast.info('Image viewer coming soon!')
                                    }}
                                  />
                                )}

                                {/* Media Type Indicator */}
                                <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                  {isVideo ? (
                                    <div className="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center">
                                      <Film className="h-3 w-3 mr-1" />
                                      Video
                                    </div>
                                  ) : (
                                    <div className="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-xs flex items-center">
                                      <Image className="h-3 w-3 mr-1" />
                                      Image
                                    </div>
                                  )}
                                </div>
                              </div>
                            )
                          })}
                        </div>

                        {/* Show more indicator if there are many media files */}
                        {post.media_urls.length > 4 && (
                          <div className="mt-2 text-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-700"
                              onClick={() => toast.info('View all media feature coming soon!')}
                            >
                              View all {post.media_urls.length} media files
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Post Actions */}
                    <div className="flex items-center space-x-6 pt-4 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`text-gray-600 hover:text-red-600 ${
                          post.liked_by_user ? 'text-red-600' : ''
                        }`}
                        onClick={() => {
                          likePost(post.id)
                          toast.success(post.liked_by_user ? 'Unliked post' : 'Liked post!')
                        }}
                      >
                        <Heart className={`h-4 w-4 mr-2 ${post.liked_by_user ? 'fill-current' : ''}`} />
                        {post.likes}
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className={`text-gray-600 hover:text-blue-600 ${
                          showComments[post.id] ? 'text-blue-600 bg-blue-50' : ''
                        }`}
                        onClick={() => toggleComments(post.id)}
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        {post.comments} Comments
                      </Button>

                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-green-600"
                        onClick={() => {
                          sharePost(post.id)
                          toast.success(`Post shared! Total shares: ${(post.shared_count || 0) + 1}`)
                        }}
                      >
                        <Share className="h-4 w-4 mr-2" />
                        Share {post.shared_count ? `(${post.shared_count})` : ''}
                      </Button>
                    </div>

                    {/* Comments Section */}
                    {showComments[post.id] && (
                      <div className="mt-4 pt-4 border-t bg-gray-50 rounded-lg p-4">
                        {/* Existing Comments */}
                        {post.post_comments && post.post_comments.length > 0 && (
                          <div className="space-y-3 mb-4">
                            <h4 className="font-medium text-gray-900">Comments ({post.post_comments.length})</h4>
                            {post.post_comments.map((comment) => (
                              <div key={comment.id} className="flex space-x-3">
                                <div className="relative">
                                  <Avatar className="h-8 w-8 bg-gray-100 flex items-center justify-center">
                                    {getRoleIcon(comment.user_role)}
                                  </Avatar>
                                  {onlineUsers.find(u => u.id === comment.user_id)?.isOnline && (
                                    <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 border border-white rounded-full"></div>
                                  )}
                                </div>
                                <div className="flex-1 bg-white rounded-lg p-3">
                                  <div className="flex items-center space-x-2 mb-1">
                                    <span
                                      className="font-medium text-sm hover:text-blue-600 cursor-pointer transition-colors"
                                      onClick={() => {
                                        toast.info(`Opening ${comment.user_name}'s profile...`)
                                        router.push(`/profile/${comment.user_id}`)
                                      }}
                                    >
                                      {comment.user_name}
                                    </span>
                                    {onlineUsers.find(u => u.id === comment.user_id)?.isOnline && (
                                      <span className="text-xs text-green-600">● Online</span>
                                    )}
                                    <span className="text-xs text-gray-500 capitalize">{comment.user_role}</span>
                                    <span className="text-xs text-gray-400">
                                      {formatTimeAgo(comment.created_at)}
                                    </span>
                                  </div>
                                  <p className="text-sm text-gray-800">{comment.content}</p>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Add Comment Form */}
                        <div className="flex space-x-3">
                          <Avatar className="h-8 w-8 bg-blue-100 flex items-center justify-center">
                            {getRoleIcon(userRole || 'worker')}
                          </Avatar>
                          <div className="flex-1">
                            <Textarea
                              placeholder="Write a comment..."
                              value={commentContent[post.id] || ''}
                              onChange={(e) => setCommentContent(prev => ({
                                ...prev,
                                [post.id]: e.target.value
                              }))}
                              className="min-h-[60px] resize-none text-sm"
                              onKeyDown={(e) => {
                                if (e.key === 'Enter' && !e.shiftKey) {
                                  e.preventDefault()
                                  handleAddComment(post.id)
                                }
                              }}
                            />
                            <div className="flex justify-between items-center mt-2">
                              <span className="text-xs text-gray-500">
                                Press Enter to post, Shift+Enter for new line
                              </span>
                              <Button
                                size="sm"
                                onClick={() => handleAddComment(post.id)}
                                disabled={!commentContent[post.id]?.trim() || isAddingComment[post.id]}
                                className="bg-blue-600 hover:bg-blue-700"
                              >
                                {isAddingComment[post.id] ? 'Posting...' : 'Post Comment'}
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Floating CREATE POST Button (Mobile) */}
      <div className="fixed bottom-6 right-6 md:hidden z-40">
        <Button
          onClick={() => setShowCreateModal(true)}
          className="h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
          size="sm"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>

      {/* CREATE POST Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold flex items-center">
              <Plus className="h-5 w-5 mr-2 text-blue-600" />
              Create New Post
            </DialogTitle>
            <DialogDescription>
              Share your thoughts, job opportunities, or professional updates with the community.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            {/* User Info */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <Avatar className="h-10 w-10 bg-blue-100 flex items-center justify-center">
                {getRoleIcon(userRole || 'worker')}
              </Avatar>
              <div>
                <p className="font-medium">{getUserFullName(user?.id || 'current-user', user?.email)}</p>
                <p className="text-sm text-gray-500 capitalize">Posting as {userRole}</p>
              </div>
            </div>

            {/* Post Content */}
            <div className="space-y-2">
              <div className="relative">
                <Textarea
                  placeholder="What's on your mind? Share your thoughts, job opportunities, or professional updates..."
                  value={modalPostContent}
                  onChange={(e) => {
                    if (e.target.value.length <= 500) {
                      setModalPostContent(e.target.value)
                    }
                  }}
                  className="min-h-[150px] resize-none text-base pr-12"
                  maxLength={500}
                />
                {/* Emoji Button */}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  className="absolute top-2 right-2 h-8 w-8 p-0"
                >
                  <Smile className="h-4 w-4" />
                </Button>
              </div>

              {/* Emoji Picker */}
              {showEmojiPicker && (
                <div className="bg-white border rounded-lg p-3 shadow-lg">
                  <div className="grid grid-cols-8 gap-2">
                    {['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥', '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐', '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑', '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻', '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸', '😹', '😻', '😼', '😽', '🙀', '😿', '😾'].map((emoji) => (
                      <button
                        key={emoji}
                        onClick={() => addEmoji(emoji)}
                        className="text-lg hover:bg-gray-100 rounded p-1 transition-colors"
                      >
                        {emoji}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-between text-sm">
                <span className="text-gray-500">
                  {modalPostContent.length}/500 characters
                </span>
                <span className={`${modalPostContent.length > 450 ? 'text-orange-500' : 'text-gray-400'}`}>
                  {modalPostContent.length > 450 && 'Character limit approaching'}
                </span>
              </div>
            </div>

            {/* Media Upload Section */}
            <div className="space-y-4">
              {/* Upload Buttons */}
              <div className="flex flex-wrap gap-2">
                {/* Image Upload */}
                <div className="relative">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    id="image-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="flex items-center relative pointer-events-none"
                  >
                    <Image className="h-4 w-4 mr-2" />
                    Add Images ({selectedImages.length}/5)
                  </Button>
                </div>

                {/* Video Upload */}
                <div className="relative">
                  <input
                    type="file"
                    multiple
                    accept="video/*"
                    onChange={handleVideoUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    id="video-upload"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="flex items-center relative pointer-events-none"
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Add Videos ({selectedVideos.length}/2)
                  </Button>
                </div>

                {/* Clear All Button */}
                {(selectedImages.length > 0 || selectedVideos.length > 0) && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={clearAllMedia}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Clear All
                  </Button>
                )}

                {/* Test Upload Button */}
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    // Simulate adding test images
                    const testImageUrls = [
                      'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=400&h=300&fit=crop',
                      'https://images.unsplash.com/photo-1581092795442-8d6c0b8b5b8e?w=400&h=300&fit=crop'
                    ]

                    // Create mock File objects for testing
                    const mockFiles = testImageUrls.map((url, index) => {
                      const blob = new Blob(['test'], { type: 'image/jpeg' })
                      const file = new File([blob], `test-image-${index + 1}.jpg`, { type: 'image/jpeg' })
                      Object.defineProperty(file, 'preview', { value: url })
                      return file
                    })

                    setSelectedImages(mockFiles)
                    setPostType('image')
                    toast.success('Test images added! 📸')
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  🧪 Test Images
                </Button>

                {/* Test Emoji Button */}
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    const testEmojis = '🎉✨🚀💼👷‍♂️💪🔥⭐'
                    setModalPostContent(prev => prev + testEmojis)
                    toast.success('Test emojis added! 😀')
                  }}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  😀 Test Emojis
                </Button>
              </div>

              {/* Post Type Indicator */}
              {postType !== 'text' && (
                <div className="flex items-center space-x-2 text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-lg">
                  {postType === 'image' && <Image className="h-4 w-4" />}
                  {postType === 'video' && <Video className="h-4 w-4" />}
                  {postType === 'mixed' && <Camera className="h-4 w-4" />}
                  <span className="capitalize">Post Type: {postType === 'mixed' ? 'Images & Videos' : postType}</span>
                </div>
              )}

              {/* Image Previews */}
              {selectedImages.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Selected Images:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {selectedImages.map((image, index) => {
                      // Handle both real files and mock files with preview URLs
                      const imageUrl = (image as any).preview || URL.createObjectURL(image)

                      return (
                        <div key={index} className="relative group">
                          <img
                            src={imageUrl}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                            onError={(e) => {
                              // Fallback for broken images
                              (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlPC90ZXh0Pjwvc3ZnPg=='
                            }}
                          />
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X className="h-3 w-3" />
                          </button>
                          <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                            {image.name.length > 10 ? image.name.substring(0, 10) + '...' : image.name}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}

              {/* Video Previews */}
              {selectedVideos.length > 0 && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium text-gray-700">Selected Videos:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {selectedVideos.map((video, index) => (
                      <div key={index} className="relative group">
                        <video
                          src={URL.createObjectURL(video)}
                          className="w-full h-32 object-cover rounded-lg"
                          controls
                        />
                        <button
                          onClick={() => removeVideo(index)}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X className="h-3 w-3" />
                        </button>
                        <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded flex items-center">
                          <Film className="h-3 w-3 mr-1" />
                          {video.name.length > 10 ? video.name.substring(0, 10) + '...' : video.name}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center pt-4 border-t">
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                <span>Visibility: <span className="font-medium">Public</span></span>
                {(selectedImages.length > 0 || selectedVideos.length > 0) && (
                  <span className="flex items-center space-x-1">
                    <Camera className="h-4 w-4" />
                    <span>{selectedImages.length + selectedVideos.length} media files</span>
                  </span>
                )}
              </div>
              <div className="flex space-x-3">
                <Button
                  variant="outline"
                  onClick={() => {
                    setModalPostContent('')
                    setSelectedImages([])
                    setSelectedVideos([])
                    setPostType('text')
                    setShowEmojiPicker(false)
                    setShowCreateModal(false)
                  }}
                  disabled={isModalCreatingPost}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreatePost}
                  disabled={(!modalPostContent.trim() && selectedImages.length === 0 && selectedVideos.length === 0) || isModalCreatingPost}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isModalCreatingPost ? (
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Posting...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Upload className="h-4 w-4 mr-2" />
                      Share Post
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
