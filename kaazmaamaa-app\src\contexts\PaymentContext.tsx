'use client'

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react'

export interface PaymentTransaction {
  id: string
  userId: string
  profileId: string
  profileType: 'worker' | 'supplier' | 'company'
  amount: number
  currency: 'SAR'
  purpose: 'contact_access' | 'document_download' | 'premium_access'
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod: 'mada' | 'visa' | 'mastercard' | 'stc_pay' | 'apple_pay'
  transactionId?: string
  createdAt: string
  completedAt?: string
  expiresAt: string
}

export interface AccessPermission {
  userId: string
  profileId: string
  profileType: 'worker' | 'supplier' | 'company'
  hasContactAccess: boolean
  hasDocumentAccess: boolean
  purchasedAt: string
  expiresAt: string
  transactionId: string
}

interface PaymentContextType {
  transactions: PaymentTransaction[]
  permissions: AccessPermission[]
  initiatePayment: (profileId: string, profileType: 'worker' | 'supplier' | 'company', purpose: 'contact_access' | 'document_download' | 'premium_access') => Promise<string>
  processPayment: (transactionId: string, paymentMethod: string, paymentDetails: any) => Promise<boolean>
  hasAccess: (profileId: string, accessType: 'contact' | 'documents') => boolean
  getTransactionStatus: (transactionId: string) => PaymentTransaction | null
  getUserTransactions: () => PaymentTransaction[]
  getUserPermissions: () => AccessPermission[]
}

const PaymentContext = createContext<PaymentContextType | undefined>(undefined)

// Saudi Arabian Payment Gateway Configuration
const PAYMENT_CONFIG: Record<'contact_access' | 'document_download' | 'premium_access', {
  amount: number
  currency: 'SAR'
  validityDays: number
}> = {
  contact_access: {
    amount: 25, // 25 SAR for contact information access
    currency: 'SAR' as const,
    validityDays: 30
  },
  document_download: {
    amount: 50, // 50 SAR for document download access
    currency: 'SAR' as const,
    validityDays: 90
  },
  premium_access: {
    amount: 75, // 75 SAR for full premium access (contact + documents)
    currency: 'SAR' as const,
    validityDays: 90
  }
}

export function PaymentProvider({ children }: { children: ReactNode }) {
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([])
  const [permissions, setPermissions] = useState<AccessPermission[]>([])
  const [currentUserId, setCurrentUserId] = useState<string>('')

  useEffect(() => {
    // Load payment data from localStorage (client-side only)
    if (typeof window !== 'undefined') {
      const savedTransactions = localStorage.getItem('kaazmaamaa-transactions')
      const savedPermissions = localStorage.getItem('kaazmaamaa-permissions')
      const userId = localStorage.getItem('kaazmaamaa-current-user') || 'current-user'

      setCurrentUserId(userId)

      if (savedTransactions) {
        setTransactions(JSON.parse(savedTransactions))
      }

      if (savedPermissions) {
        setPermissions(JSON.parse(savedPermissions))
      }
    }
  }, [])

  const saveToStorage = useCallback(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('kaazmaamaa-transactions', JSON.stringify(transactions))
      localStorage.setItem('kaazmaamaa-permissions', JSON.stringify(permissions))
    }
  }, [transactions, permissions])

  useEffect(() => {
    saveToStorage()
  }, [transactions, permissions, saveToStorage])

  const initiatePayment = useCallback(async (
    profileId: string,
    profileType: 'worker' | 'supplier' | 'company',
    purpose: 'contact_access' | 'document_download' | 'premium_access'
  ): Promise<string> => {
    try {
      if (!profileId || !profileType || !purpose) {
        throw new Error('Missing required payment parameters')
      }

      const config = PAYMENT_CONFIG[purpose]
      if (!config) {
        throw new Error(`Invalid payment purpose: ${purpose}`)
      }

      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const transaction: PaymentTransaction = {
        id: transactionId,
        userId: currentUserId,
        profileId,
        profileType,
        amount: config.amount,
        currency: config.currency,
        purpose,
        status: 'pending',
        paymentMethod: 'mada', // Default to MADA
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes expiry
      }

      setTransactions(prev => [...prev, transaction])
      return transactionId
    } catch (error) {
      console.error('Error initiating payment:', error)
      throw error
    }
  }, [currentUserId])

  const processPayment = useCallback(async (
    transactionId: string,
    paymentMethod: string,
    paymentDetails: any
  ): Promise<boolean> => {
    // Simulate Saudi payment gateway processing
    return new Promise((resolve) => {
      setTimeout(() => {
        const transaction = transactions.find(t => t.id === transactionId)
        if (!transaction) {
          resolve(false)
          return
        }

        // Simulate payment success (90% success rate)
        const isSuccess = Math.random() > 0.1

        if (isSuccess) {
          // Update transaction status
          setTransactions(prev => prev.map(t =>
            t.id === transactionId
              ? {
                  ...t,
                  status: 'completed' as const,
                  paymentMethod: paymentMethod as any,
                  completedAt: new Date().toISOString(),
                  transactionId: `saudi_${Date.now()}`
                }
              : t
          ))

          // Grant access permissions
          const config = PAYMENT_CONFIG[transaction.purpose]
          if (!config) {
            console.error('Invalid payment purpose in processPayment:', transaction.purpose)
            resolve(false)
            return
          }

          const expiryDate = new Date()
          expiryDate.setDate(expiryDate.getDate() + config.validityDays)

          const permission: AccessPermission = {
            userId: currentUserId,
            profileId: transaction.profileId,
            profileType: transaction.profileType,
            hasContactAccess: transaction.purpose === 'contact_access' || transaction.purpose === 'premium_access',
            hasDocumentAccess: transaction.purpose === 'document_download' || transaction.purpose === 'premium_access',
            purchasedAt: new Date().toISOString(),
            expiresAt: expiryDate.toISOString(),
            transactionId
          }

          setPermissions(prev => {
            // Remove any existing permission for the same profile
            const filtered = prev.filter(p => !(p.profileId === transaction.profileId && p.userId === currentUserId))
            return [...filtered, permission]
          })

          resolve(true)
        } else {
          // Update transaction as failed
          setTransactions(prev => prev.map(t =>
            t.id === transactionId
              ? { ...t, status: 'failed' as const }
              : t
          ))
          resolve(false)
        }
      }, 2000) // Simulate 2 second processing time
    })
  }, [transactions, currentUserId])

  const hasAccess = useCallback((profileId: string, accessType: 'contact' | 'documents'): boolean => {
    const permission = permissions.find(p =>
      p.profileId === profileId &&
      p.userId === currentUserId &&
      new Date(p.expiresAt) > new Date()
    )

    if (!permission) return false

    return accessType === 'contact' ? permission.hasContactAccess : permission.hasDocumentAccess
  }, [permissions, currentUserId])

  const getTransactionStatus = useCallback((transactionId: string): PaymentTransaction | null => {
    return transactions.find(t => t.id === transactionId) || null
  }, [transactions])

  const getUserTransactions = useCallback((): PaymentTransaction[] => {
    return transactions.filter(t => t.userId === currentUserId)
  }, [transactions, currentUserId])

  const getUserPermissions = useCallback((): AccessPermission[] => {
    return permissions.filter(p => p.userId === currentUserId && new Date(p.expiresAt) > new Date())
  }, [permissions, currentUserId])

  const value = {
    transactions,
    permissions,
    initiatePayment,
    processPayment,
    hasAccess,
    getTransactionStatus,
    getUserTransactions,
    getUserPermissions
  }

  return (
    <PaymentContext.Provider value={value}>
      {children}
    </PaymentContext.Provider>
  )
}

export function usePayment() {
  const context = useContext(PaymentContext)
  if (context === undefined) {
    throw new Error('usePayment must be used within a PaymentProvider')
  }
  return context
}

// Saudi Arabian Payment Methods
export const SAUDI_PAYMENT_METHODS = [
  {
    id: 'mada',
    name: 'MADA',
    icon: '💳',
    description: 'Saudi national payment system'
  },
  {
    id: 'visa',
    name: 'Visa',
    icon: '💳',
    description: 'Visa credit/debit cards'
  },
  {
    id: 'mastercard',
    name: 'Mastercard',
    icon: '💳',
    description: 'Mastercard credit/debit cards'
  },
  {
    id: 'stc_pay',
    name: 'STC Pay',
    icon: '📱',
    description: 'STC digital wallet'
  },
  {
    id: 'apple_pay',
    name: 'Apple Pay',
    icon: '🍎',
    description: 'Apple Pay digital wallet'
  }
]
