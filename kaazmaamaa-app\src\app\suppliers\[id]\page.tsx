'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useSuppliers } from '@/contexts/SuppliersContext'
import { usePayment } from '@/contexts/PaymentContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState, use } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  ArrowLeft,
  MapPin,
  Star,
  MessageCircle,
  Phone,
  Download,
  Play,
  Radio,
  CheckCircle,
  Calendar,
  Briefcase,
  DollarSign,
  Globe,
  FileText,
  Video,
  Building,
  Mail,
  Users,
  Award,
  Target,
  TrendingUp,
  Clock,
  Factory,
  Lock,
  CreditCard
} from 'lucide-react'
import PaymentGateway from '@/components/PaymentGateway'
import { toast } from 'sonner'

interface SupplierProfilePageProps {
  params: Promise<{
    id: string
  }>
}

export default function SupplierProfilePage({ params }: SupplierProfilePageProps) {
  const { user } = useAuth()
  const { suppliers } = useSuppliers()
  const { hasAccess } = usePayment()
  const router = useRouter()
  const resolvedParams = use(params)
  const [supplier, setSupplier] = useState(suppliers.find(s => s.id === resolvedParams.id))
  const [showPaymentGateway, setShowPaymentGateway] = useState(false)
  const [paymentAccessType, setPaymentAccessType] = useState<'contact' | 'documents' | 'premium'>('contact')

  useEffect(() => {
    if (!user) {
      router.push('/')
    }

    const foundSupplier = suppliers.find(s => s.id === resolvedParams.id)
    if (!foundSupplier) {
      router.push('/suppliers')
    } else {
      setSupplier(foundSupplier)
    }
  }, [user, resolvedParams.id, suppliers, router])

  const handleWhatsAppContact = () => {
    if (supplier?.personalInfo?.whatsapp) {
      const message = `Hello ${supplier.personalInfo?.supplierName}, I found your company on KAAZMAAMAA Suppliers Block and would like to discuss job opportunities.`
      const whatsappUrl = `https://wa.me/${supplier.personalInfo.whatsapp.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`
      window.open(whatsappUrl, '_blank')
    } else {
      toast.error('WhatsApp number not available')
    }
  }

  const handleContactAccess = () => {
    if (hasAccess(resolvedParams.id, 'contact')) {
      return true
    } else {
      setPaymentAccessType('contact')
      setShowPaymentGateway(true)
      return false
    }
  }

  const handleDocumentDownload = (doc: any) => {
    if (hasAccess(resolvedParams.id, 'documents')) {
      toast.success(`Downloading ${doc.name}...`)
    } else {
      setPaymentAccessType('documents')
      setShowPaymentGateway(true)
    }
  }

  const handlePaymentSuccess = () => {
    setShowPaymentGateway(false)
    toast.success('Access granted! You can now view contact information and download documents.')
  }

  if (!supplier) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Supplier Not Found</h1>
          <Button onClick={() => router.push('/suppliers')}>
            Back to Suppliers
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push('/suppliers')}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Suppliers
              </Button>
              <h1 className="text-2xl font-bold text-green-600">Supplier Profile</h1>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Supplier Header */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-6">
              <div className="relative">
                <Avatar className="h-32 w-32 bg-green-100 flex items-center justify-center">
                  <Factory className="h-16 w-16 text-green-600" />
                </Avatar>
                {supplier.isLiveStreaming && (
                  <div className="absolute -top-2 -right-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                    <Radio className="h-3 w-3 mr-1" />
                    LIVE
                  </div>
                )}
                {supplier.isVerified && (
                  <div className="absolute -bottom-2 -right-2 bg-green-500 text-white rounded-full p-2">
                    <CheckCircle className="h-4 w-4" />
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="flex flex-col md:flex-row md:items-start md:justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">{supplier.personalInfo?.supplierName || 'Unknown Supplier'}</h1>
                    <p className="text-xl text-green-600 font-semibold mt-1">{supplier.businessInfo?.companyName || 'No Company'}</p>
                    <p className="text-gray-600 mt-1">{supplier.businessInfo?.businessType || 'Business'}</p>

                    <div className="flex items-center space-x-4 mt-3">
                      <Badge className="bg-green-100 text-green-800">
                        Supplier
                      </Badge>
                      <div className="flex items-center text-yellow-500">
                        <Star className="h-5 w-5 fill-current" />
                        <span className="ml-1 font-semibold">{supplier.rating || 0}</span>
                        <span className="text-gray-500 ml-1">({supplier.totalReviews || 0} reviews)</span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                      <div className="flex items-center text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        {supplier.personalInfo?.address || 'Address not specified'}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Building className="h-4 w-4 mr-2" />
                        CR: {supplier.businessInfo?.crNumber || 'Not provided'}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Award className="h-4 w-4 mr-2" />
                        License: {supplier.businessInfo?.licenceNumber || 'Not provided'}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Briefcase className="h-4 w-4 mr-2" />
                        {(supplier.jobDemands || []).length} job demands
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2 mt-4 md:mt-0">
                    <Button onClick={handleWhatsAppContact} className="bg-green-600 hover:bg-green-700">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Contact on WhatsApp
                    </Button>
                    {hasAccess(resolvedParams.id, 'contact') ? (
                      <>
                        <Button variant="outline">
                          <Phone className="h-4 w-4 mr-2" />
                          Call: {supplier.personalInfo?.phone}
                        </Button>
                        <Button variant="outline">
                          <Mail className="h-4 w-4 mr-2" />
                          Email: {supplier.personalInfo?.email}
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="outline"
                          onClick={handleContactAccess}
                          className="border-orange-300 text-orange-600 hover:bg-orange-50"
                        >
                          <Lock className="h-4 w-4 mr-2" />
                          Unlock Contact Info (25 SAR)
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setPaymentAccessType('premium')
                            setShowPaymentGateway(true)
                          }}
                          className="border-purple-300 text-purple-600 hover:bg-purple-50"
                        >
                          <CreditCard className="h-4 w-4 mr-2" />
                          Premium Access (75 SAR)
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Information Tabs */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Job Demands</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="videos">Videos</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building className="h-5 w-5 mr-2" />
                    Business Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Company Name</label>
                      <p className="text-gray-900">{supplier.businessInfo?.companyName || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Business Type</label>
                      <p className="text-gray-900">{supplier.businessInfo?.businessType || 'Not specified'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">CR Number</label>
                      <p className="text-gray-900">{supplier.businessInfo?.crNumber || 'Not provided'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">License Number</label>
                      <p className="text-gray-900">{supplier.businessInfo?.licenceNumber || 'Not provided'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <MapPin className="h-5 w-5 mr-2" />
                    Operating Locations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {(supplier.operatingLocations || []).map((location, index) => (
                      <Badge key={index} variant="secondary">{location}</Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="jobs">
            <div className="space-y-4">
              {(supplier.jobDemands || []).length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Job Demands</h3>
                    <p className="text-gray-600">This supplier has no active job demands.</p>
                  </CardContent>
                </Card>
              ) : (
                (supplier.jobDemands || []).map((job) => (
                  <Card key={job.id} className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-xl font-semibold">{job.position}</h3>
                          <p className="text-green-600 font-medium">{job.category}</p>
                          <div className="flex items-center space-x-2 mt-2">
                            <Badge>{job.urgency.replace('_', ' ')}</Badge>
                            <Badge variant="secondary">{job.paymentDuration}</Badge>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold text-green-600">
                            {job.salaryPerHour} {job.currency}/hr
                          </p>
                          <p className="text-sm text-gray-500">{job.location}</p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div>
                          <h4 className="font-medium mb-2">Job Description</h4>
                          <p className="text-gray-700">{job.description}</p>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Requirements</h4>
                          <ul className="list-disc list-inside space-y-1">
                            {job.requirements.map((req, index) => (
                              <li key={index} className="text-gray-700 text-sm">{req}</li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="font-medium mb-2">Required Documents</h4>
                          <div className="flex flex-wrap gap-2">
                            {job.documentsRequired.map((doc, index) => (
                              <Badge key={index} variant="outline">{doc}</Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="documents">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Documents ({(supplier.documents || []).length})
                </CardTitle>
                {!hasAccess(resolvedParams.id, 'documents') && (
                  <CardDescription className="bg-orange-50 border border-orange-200 rounded-lg p-3 mt-2">
                    <Lock className="h-4 w-4 inline mr-2" />
                    Document downloads require payment. Pay 50 SAR for document access or 75 SAR for premium access (contact + documents).
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(supplier.documents || []).map((doc) => (
                    <div key={doc.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{doc.name}</h4>
                          <p className="text-sm text-gray-500 capitalize">{doc.type.replace('_', ' ')}</p>
                          <p className="text-xs text-gray-400 mt-1">
                            Uploaded: {new Date(doc.uploadedAt).toLocaleDateString()}
                          </p>
                        </div>
                        {hasAccess(resolvedParams.id, 'documents') ? (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDocumentDownload(doc)}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDocumentDownload(doc)}
                            className="border-orange-300 text-orange-600 hover:bg-orange-50"
                          >
                            <Lock className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="videos">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Video className="h-5 w-5 mr-2" />
                  Videos ({(supplier.videos || []).length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {(supplier.videos || []).map((video) => (
                    <div key={video.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="aspect-video bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                        <Button
                          variant="ghost"
                          size="lg"
                          onClick={() => toast.success(`Playing ${video.title}...`)}
                        >
                          <Play className="h-8 w-8" />
                        </Button>
                      </div>
                      <h4 className="font-medium">{video.title}</h4>
                      <p className="text-sm text-gray-500 capitalize">{video.type.replace('_', ' ')}</p>
                      {video.duration && (
                        <p className="text-xs text-gray-400">Duration: {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')}</p>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contact">
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {hasAccess(resolvedParams.id, 'contact') ? (
                  <>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Supplier Name</label>
                      <p className="text-gray-900">{supplier.personalInfo?.supplierName}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Phone</label>
                      <p className="text-gray-900">{supplier.personalInfo?.phone}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Email</label>
                      <p className="text-gray-900">{supplier.personalInfo?.email}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-500">Address</label>
                      <p className="text-gray-900">{supplier.personalInfo?.address}</p>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <Lock className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Contact Information Protected</h3>
                    <p className="text-gray-600 mb-4">
                      Pay 25 SAR to access contact information or 75 SAR for premium access.
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button onClick={handleContactAccess}>
                        Unlock Contact (25 SAR)
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setPaymentAccessType('premium')
                          setShowPaymentGateway(true)
                        }}
                      >
                        Premium Access (75 SAR)
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Payment Gateway Modal */}
      {showPaymentGateway && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <PaymentGateway
                profileId={resolvedParams.id}
                profileType="supplier"
                profileName={supplier?.personalInfo?.supplierName || 'Supplier'}
                accessType={paymentAccessType}
                onSuccess={handlePaymentSuccess}
                onCancel={() => setShowPaymentGateway(false)}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
