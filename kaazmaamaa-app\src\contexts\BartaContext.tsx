'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useAuth } from './AuthContext'
import { useWorkers } from './WorkersContext'
import { useSuppliers } from './SuppliersContext'
import { useCompanies } from './CompaniesContext'

// Types for Barta messaging system
export interface BartaUser {
  id: string
  name: string
  email: string
  role: 'worker' | 'supplier' | 'company'
  avatar?: string
  isOnline: boolean
  lastSeen: string
}

export interface BartaMessage {
  id: string
  conversationId: string
  senderId: string
  senderName: string
  senderRole: 'worker' | 'supplier' | 'company'
  content: string
  type: 'text' | 'image' | 'file' | 'emoji'
  timestamp: string
  isRead: boolean
  isDelivered: boolean
  replyTo?: string // For message replies
  attachments?: {
    type: 'image' | 'file'
    url: string
    name: string
    size?: number
  }[]
}

export interface BartaConversation {
  id: string
  participants: string[] // User IDs
  participantDetails: BartaUser[]
  lastMessage?: BartaMessage
  lastActivity: string
  unreadCount: number
  isGroup: boolean
  groupName?: string
  groupAvatar?: string
  createdAt: string
  updatedAt: string
}

interface BartaContextType {
  // State
  conversations: BartaConversation[]
  messages: { [conversationId: string]: BartaMessage[] }
  activeConversation: string | null
  isTyping: { [conversationId: string]: string[] } // User IDs who are typing
  onlineUsers: BartaUser[]
  
  // Actions
  sendMessage: (conversationId: string, content: string, type?: 'text' | 'image' | 'file' | 'emoji') => void
  createConversation: (participantId: string) => string
  setActiveConversation: (conversationId: string | null) => void
  markAsRead: (conversationId: string) => void
  setTyping: (conversationId: string, isTyping: boolean) => void
  searchUsers: (query: string) => BartaUser[]
  getConversationWithUser: (userId: string) => BartaConversation | null
  deleteMessage: (messageId: string, conversationId: string) => void
  editMessage: (messageId: string, newContent: string) => void
  
  // Utilities
  getUserById: (userId: string) => BartaUser | null
  getUnreadCount: () => number
  formatLastSeen: (timestamp: string) => string
}

const BartaContext = createContext<BartaContextType | undefined>(undefined)

export function BartaProvider({ children }: { children: ReactNode }) {
  const { user, userRole } = useAuth()
  const { workers } = useWorkers()
  const { suppliers } = useSuppliers()
  const { companies } = useCompanies()

  // State
  const [conversations, setConversations] = useState<BartaConversation[]>([])
  const [messages, setMessages] = useState<{ [conversationId: string]: BartaMessage[] }>({})
  const [activeConversation, setActiveConversation] = useState<string | null>(null)
  const [isTyping, setIsTyping] = useState<{ [conversationId: string]: string[] }>({})
  const [onlineUsers, setOnlineUsers] = useState<BartaUser[]>([])

  // Load data from localStorage on mount
  useEffect(() => {
    try {
      const savedConversations = localStorage.getItem('kaazmaamaa-barta-conversations')
      const savedMessages = localStorage.getItem('kaazmaamaa-barta-messages')
      
      if (savedConversations) {
        setConversations(JSON.parse(savedConversations))
      }
      
      if (savedMessages) {
        setMessages(JSON.parse(savedMessages))
      }
      
      console.log('📱 Barta: Loaded conversations and messages from localStorage')
    } catch (error) {
      console.error('📱 Barta: Error loading data from localStorage:', error)
    }
  }, [])

  // Save conversations to localStorage whenever they change
  useEffect(() => {
    if (conversations.length > 0) {
      localStorage.setItem('kaazmaamaa-barta-conversations', JSON.stringify(conversations))
    }
  }, [conversations])

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (Object.keys(messages).length > 0) {
      localStorage.setItem('kaazmaamaa-barta-messages', JSON.stringify(messages))
    }
  }, [messages])

  // Build online users list from all profile types
  useEffect(() => {
    const allUsers: BartaUser[] = []
    
    // Add workers
    workers.forEach(worker => {
      if (worker.personalInfo?.workerName && worker.personalInfo?.email) {
        allUsers.push({
          id: worker.userId,
          name: worker.personalInfo.workerName,
          email: worker.personalInfo.email,
          role: 'worker',
          isOnline: Math.random() > 0.5, // Mock online status
          lastSeen: new Date().toISOString()
        })
      }
    })
    
    // Add suppliers
    suppliers.forEach(supplier => {
      if (supplier.personalInfo?.supplierName && supplier.personalInfo?.email) {
        allUsers.push({
          id: supplier.userId,
          name: supplier.personalInfo.supplierName,
          email: supplier.personalInfo.email,
          role: 'supplier',
          isOnline: Math.random() > 0.5, // Mock online status
          lastSeen: new Date().toISOString()
        })
      }
    })
    
    // Add companies
    companies.forEach(company => {
      if (company.companyInfo?.companyName && company.contactInfo?.hrEmail) {
        allUsers.push({
          id: company.userId,
          name: company.companyInfo.companyName,
          email: company.contactInfo.hrEmail,
          role: 'company',
          isOnline: Math.random() > 0.5, // Mock online status
          lastSeen: new Date().toISOString()
        })
      }
    })
    
    setOnlineUsers(allUsers)
    console.log('📱 Barta: Updated online users list:', allUsers.length, 'users')
  }, [workers, suppliers, companies])

  // Generate unique ID for messages and conversations
  const generateId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }

  // Get user by ID from all profile types
  const getUserById = (userId: string): BartaUser | null => {
    return onlineUsers.find(user => user.id === userId) || null
  }

  // Search users across all profile types
  const searchUsers = (query: string): BartaUser[] => {
    if (!query.trim()) return onlineUsers
    
    const lowercaseQuery = query.toLowerCase()
    return onlineUsers.filter(user => 
      user.name.toLowerCase().includes(lowercaseQuery) ||
      user.email.toLowerCase().includes(lowercaseQuery) ||
      user.role.toLowerCase().includes(lowercaseQuery)
    )
  }

  // Get existing conversation with a user
  const getConversationWithUser = (userId: string): BartaConversation | null => {
    return conversations.find(conv => 
      !conv.isGroup && 
      conv.participants.includes(userId) && 
      conv.participants.includes(user?.id || '')
    ) || null
  }

  // Create new conversation
  const createConversation = (participantId: string): string => {
    if (!user?.id) return ''
    
    // Check if conversation already exists
    const existingConv = getConversationWithUser(participantId)
    if (existingConv) {
      return existingConv.id
    }
    
    const participant = getUserById(participantId)
    if (!participant) return ''
    
    const conversationId = generateId()
    const currentUserDetails = getUserById(user.id)
    
    const newConversation: BartaConversation = {
      id: conversationId,
      participants: [user.id, participantId],
      participantDetails: [currentUserDetails, participant].filter(Boolean) as BartaUser[],
      lastActivity: new Date().toISOString(),
      unreadCount: 0,
      isGroup: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    setConversations(prev => [newConversation, ...prev])
    console.log('📱 Barta: Created new conversation:', conversationId)
    
    return conversationId
  }

  // Send message
  const sendMessage = (conversationId: string, content: string, type: 'text' | 'image' | 'file' | 'emoji' = 'text') => {
    if (!user?.id || !content.trim()) return
    
    const messageId = generateId()
    const currentUserDetails = getUserById(user.id)
    
    const newMessage: BartaMessage = {
      id: messageId,
      conversationId,
      senderId: user.id,
      senderName: currentUserDetails?.name || user.email || 'Unknown User',
      senderRole: (userRole as 'worker' | 'supplier' | 'company') || 'worker',
      content: content.trim(),
      type,
      timestamp: new Date().toISOString(),
      isRead: false,
      isDelivered: true
    }
    
    // Add message to messages
    setMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), newMessage]
    }))
    
    // Update conversation's last message and activity
    setConversations(prev => prev.map(conv => {
      if (conv.id === conversationId) {
        return {
          ...conv,
          lastMessage: newMessage,
          lastActivity: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      return conv
    }))
    
    console.log('📱 Barta: Message sent:', messageId)
  }

  // Mark conversation as read
  const markAsRead = (conversationId: string) => {
    setMessages(prev => ({
      ...prev,
      [conversationId]: (prev[conversationId] || []).map(msg => ({
        ...msg,
        isRead: true
      }))
    }))
    
    setConversations(prev => prev.map(conv => 
      conv.id === conversationId ? { ...conv, unreadCount: 0 } : conv
    ))
  }

  // Set typing status
  const setTypingStatus = (conversationId: string, isTypingNow: boolean) => {
    if (!user?.id) return
    
    setIsTyping(prev => {
      const currentTypers = prev[conversationId] || []
      
      if (isTypingNow) {
        if (!currentTypers.includes(user.id)) {
          return {
            ...prev,
            [conversationId]: [...currentTypers, user.id]
          }
        }
      } else {
        return {
          ...prev,
          [conversationId]: currentTypers.filter(id => id !== user.id)
        }
      }
      
      return prev
    })
  }

  // Delete message
  const deleteMessage = (messageId: string, conversationId: string) => {
    setMessages(prev => ({
      ...prev,
      [conversationId]: (prev[conversationId] || []).filter(msg => msg.id !== messageId)
    }))
  }

  // Edit message
  const editMessage = (messageId: string, newContent: string) => {
    setMessages(prev => {
      const updatedMessages = { ...prev }
      
      Object.keys(updatedMessages).forEach(convId => {
        updatedMessages[convId] = updatedMessages[convId].map(msg => 
          msg.id === messageId ? { ...msg, content: newContent } : msg
        )
      })
      
      return updatedMessages
    })
  }

  // Get total unread count
  const getUnreadCount = (): number => {
    return conversations.reduce((total, conv) => total + conv.unreadCount, 0)
  }

  // Format last seen time
  const formatLastSeen = (timestamp: string): string => {
    const now = new Date()
    const lastSeen = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - lastSeen.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const value: BartaContextType = {
    // State
    conversations,
    messages,
    activeConversation,
    isTyping,
    onlineUsers,
    
    // Actions
    sendMessage,
    createConversation,
    setActiveConversation,
    markAsRead,
    setTyping: setTypingStatus,
    searchUsers,
    getConversationWithUser,
    deleteMessage,
    editMessage,
    
    // Utilities
    getUserById,
    getUnreadCount,
    formatLastSeen
  }

  return (
    <BartaContext.Provider value={value}>
      {children}
    </BartaContext.Provider>
  )
}

export function useBarta() {
  const context = useContext(BartaContext)
  if (context === undefined) {
    throw new Error('useBarta must be used within a BartaProvider')
  }
  return context
}
