'use client'

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { usePosts } from '@/contexts/PostsContext'
import { useRouter, useSearchParams } from 'next/navigation'

export default function WorkerProfilePage() {
  const { user, userRole, getUserProfile, loading } = useAuth()
  const { posts, addPost } = usePosts()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [userProfile, setUserProfile] = useState<any>(null)
  const [isOwnProfile, setIsOwnProfile] = useState(true)
  const targetUserId = searchParams.get('user')
  const [showCreatePost, setShowCreatePost] = useState(false)
  const [postText, setPostText] = useState('')

  // Get user profile data
  useEffect(() => {
    if (user && userRole) {
      const profile = getUserProfile()
      setUserProfile(profile)
      
      // Check if viewing own profile or someone else's
      if (targetUserId && targetUserId !== user.email) {
        setIsOwnProfile(false)
        // In a real app, you'd fetch the target user's profile here
        // For now, we'll show a placeholder
      } else {
        setIsOwnProfile(true)
      }
    }
  }, [user, userRole, getUserProfile, targetUserId])

  // Redirect to home if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
    }
  }, [user, loading, router])

  // Show loading if still authenticating
  if (loading) {
    return (
      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f3f4f6' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>⏳</div>
          <div style={{ color: '#6b7280' }}>Loading profile...</div>
        </div>
      </div>
    )
  }

  // Redirect if not authenticated
  if (!user) {
    return null
  }

  // Helper function to get user's display name
  const getUserDisplayName = () => {
    if (!userProfile) return 'Worker'
    return userProfile.personalInfo?.workerName || 'Worker'
  }

  // Helper function to get user's initial
  const getUserInitial = () => {
    const name = getUserDisplayName()
    return name.charAt(0).toUpperCase()
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f3f4f6' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 2rem' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#2563eb', margin: 0 }}>
              KAAZMAAMAA
            </h2>
            <span style={{ color: '#6b7280', fontSize: '0.875rem' }}>Worker Profile</span>
          </div>
          
          <div style={{ display: 'flex', gap: '0.75rem' }}>
            <button 
              onClick={() => router.push('/feed')}
              style={{ 
                backgroundColor: '#2563eb', 
                color: 'white', 
                border: 'none',
                padding: '0.5rem 1rem', 
                borderRadius: '0.25rem',
                fontSize: '0.875rem',
                fontWeight: '500',
                cursor: 'pointer'
              }}
            >
              Back to Feed
            </button>
            
            {isOwnProfile && (
              <button 
                onClick={() => {
                  router.push('/workers/edit')
                }}
                style={{ 
                  backgroundColor: '#059669', 
                  color: 'white', 
                  border: 'none',
                  padding: '0.5rem 1rem', 
                  borderRadius: '0.25rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}
              >
                Edit Profile
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ padding: '2rem' }}>
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          
          {/* Profile Header */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '2rem', marginBottom: '2rem' }}>
              <div style={{ 
                backgroundColor: '#2563eb', 
                borderRadius: '50%', 
                width: '5rem', 
                height: '5rem', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center', 
                color: 'white', 
                fontSize: '2rem',
                fontWeight: 'bold' 
              }}>
                {getUserInitial()}
              </div>
              
              <div>
                <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0, marginBottom: '0.5rem' }}>
                  {getUserDisplayName()}
                </h1>
                <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', color: '#6b7280', marginBottom: '0.5rem' }}>
                  <span>🔧</span>
                  <span>Professional Worker</span>
                </div>
                {userProfile?.personalInfo?.email && (
                  <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                    📧 {userProfile.personalInfo.email}
                  </div>
                )}
              </div>
            </div>
            
            {!isOwnProfile && (
              <div style={{ display: 'flex', gap: '1rem' }}>
                <button style={{ 
                  backgroundColor: '#2563eb', 
                  color: 'white', 
                  border: 'none',
                  padding: '0.75rem 1.5rem', 
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  Connect
                </button>
                <button style={{ 
                  backgroundColor: '#059669', 
                  color: 'white', 
                  border: 'none',
                  padding: '0.75rem 1.5rem', 
                  borderRadius: '0.25rem',
                  fontSize: '1rem',
                  fontWeight: '500',
                  cursor: 'pointer'
                }}>
                  Message
                </button>
              </div>
            )}
          </div>

          {/* Profile Information */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>
              Profile Information
            </h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1.5rem' }}>
              {userProfile?.personalInfo?.workerName && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Full Name
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.personalInfo.workerName}</div>
                </div>
              )}
              
              {userProfile?.personalInfo?.email && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Email
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.personalInfo.email}</div>
                </div>
              )}
              
              {userProfile?.personalInfo?.phone && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Phone
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.personalInfo.phone}</div>
                </div>
              )}
              
              {userProfile?.personalInfo?.nationality && (
                <div>
                  <label style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151', display: 'block', marginBottom: '0.25rem' }}>
                    Nationality
                  </label>
                  <div style={{ color: '#111827' }}>{userProfile.personalInfo.nationality}</div>
                </div>
              )}
            </div>
          </div>

          {/* Create Post Section */}
          {isOwnProfile && (
            <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem', marginBottom: '2rem' }}>
              <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>
                Share an Update
              </h2>

              <div style={{ display: 'flex', gap: '0.75rem', marginBottom: '1rem' }}>
                <div style={{
                  backgroundColor: '#2563eb',
                  borderRadius: '50%',
                  width: '2.5rem',
                  height: '2.5rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontWeight: 'bold'
                }}>
                  {getUserInitial()}
                </div>
                <input
                  type="text"
                  placeholder={`Share your professional update, ${getUserDisplayName()}...`}
                  onClick={() => setShowCreatePost(true)}
                  readOnly
                  style={{
                    flex: 1,
                    backgroundColor: '#f3f4f6',
                    border: '1px solid #d1d5db',
                    borderRadius: '20px',
                    padding: '0.75rem 1rem',
                    outline: 'none',
                    fontSize: '1rem',
                    cursor: 'pointer'
                  }}
                />
              </div>

              <div style={{ display: 'flex', justifyContent: 'space-around', paddingTop: '0.75rem', borderTop: '1px solid #e5e7eb' }}>
                <button
                  onClick={() => setShowCreatePost(true)}
                  style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#6b7280' }}
                >
                  📷 Photo/Video
                </button>
                <button
                  onClick={() => setShowCreatePost(true)}
                  style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', backgroundColor: 'transparent', border: 'none', padding: '0.5rem 1rem', borderRadius: '6px', cursor: 'pointer', color: '#6b7280' }}
                >
                  💼 Work Update
                </button>
              </div>
            </div>
          )}

          {/* Professional Skills */}
          <div style={{ backgroundColor: 'white', borderRadius: '0.5rem', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '2rem' }}>
            <h2 style={{ fontSize: '1.5rem', fontWeight: '600', color: '#111827', marginBottom: '1.5rem' }}>
              Professional Skills & Experience
            </h2>

            <div style={{ textAlign: 'center', padding: '2rem', color: '#6b7280' }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🔧</div>
              <p>Professional skills and experience information will be displayed here.</p>
              {isOwnProfile && (
                <button
                  onClick={() => router.push('/workers/edit')}
                  style={{
                    backgroundColor: '#2563eb',
                    color: 'white',
                    border: 'none',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.25rem',
                    fontSize: '1rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    marginTop: '1rem'
                  }}
                >
                  Add Skills & Experience
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Create Post Modal */}
      {showCreatePost && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.1)',
            width: '500px',
            maxHeight: '90vh',
            overflow: 'auto'
          }}>
            {/* Modal Header */}
            <div style={{
              padding: '1rem',
              borderBottom: '1px solid #e4e6ea',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <h2 style={{ fontSize: '1.25rem', fontWeight: '600', color: '#1c1e21', margin: 0 }}>
                Create Post
              </h2>
              <button
                onClick={() => setShowCreatePost(false)}
                style={{
                  backgroundColor: '#f0f2f5',
                  border: 'none',
                  borderRadius: '50%',
                  width: '2.5rem',
                  height: '2.5rem',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1.25rem',
                  color: '#65676b'
                }}
              >
                ✕
              </button>
            </div>

            {/* User Info */}
            <div style={{ padding: '1rem', display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
              <div style={{
                backgroundColor: '#2563eb',
                borderRadius: '50%',
                width: '2.5rem',
                height: '2.5rem',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontWeight: 'bold'
              }}>
                {getUserInitial()}
              </div>
              <div>
                <div style={{ fontWeight: '600', color: '#1c1e21' }}>{getUserDisplayName()}</div>
                <div style={{ fontSize: '0.875rem', color: '#65676b', display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                  🔧 Worker • 🌍 Public
                </div>
              </div>
            </div>

            {/* Post Content */}
            <div style={{ padding: '0 1rem' }}>
              <textarea
                value={postText}
                onChange={(e) => setPostText(e.target.value)}
                placeholder="Share your professional update..."
                style={{
                  width: '100%',
                  minHeight: '120px',
                  border: 'none',
                  outline: 'none',
                  fontSize: '1.5rem',
                  color: '#1c1e21',
                  resize: 'none',
                  fontFamily: 'inherit'
                }}
              />
            </div>

            {/* Post Button */}
            <div style={{ padding: '1rem', borderTop: '1px solid #e4e6ea' }}>
              <button
                onClick={() => {
                  if (!postText.trim()) return

                  // Create the post using the posts context
                  const newPost = {
                    user_id: user?.email || '',
                    user_name: getUserDisplayName(),
                    user_role: 'worker' as const,
                    content: postText,
                    post_type: 'text' as const,
                    visibility: 'public' as const
                  }

                  addPost(newPost)

                  // Reset form
                  setPostText('')
                  setShowCreatePost(false)

                  // Show success message
                  alert('Post created successfully!')
                }}
                disabled={!postText.trim()}
                style={{
                  width: '100%',
                  backgroundColor: postText.trim() ? '#2563eb' : '#e4e6ea',
                  color: postText.trim() ? 'white' : '#bcc0c4',
                  border: 'none',
                  borderRadius: '6px',
                  padding: '0.75rem',
                  fontSize: '1rem',
                  fontWeight: '600',
                  cursor: postText.trim() ? 'pointer' : 'not-allowed'
                }}
              >
                Post
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
